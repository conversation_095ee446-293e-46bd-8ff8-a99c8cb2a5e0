﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public void PetActionPack(byte[] packetData, int packetSize)
	{
		if (MapID == 801 || MapID == 40101 || MapID == 42001 || MapID == 9001 || MapID == 9101 || MapID == 9201)
		{
			HeThongNhacNho("Không thể triệu hồi Linh Thú tại bản đồ này!");
			return;
		}
		if (World.BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu == 1)
		{
			HeThongNhacNho("Không thể thực hiện vào thời điểm này!!", 10, "Thiên cơ các");
			return;
		}
		var array = new byte[2];
		Buffer.BlockCopy(packetData, 10, array, 0, 1);
		PetAction(BitConverter.ToInt16(array, 0));
	}

	public void SummonPets(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			if (MapID == 801 || MapID == 40101 || MapID == 9001 || MapID == 9101 || MapID == 9201 || MapID == 42001)
			{
				HeThongNhacNho("Không thể triệu hồi Linh Thú tại bản đồ này!");
				return;
			}
			var num = BitConverter.ToInt32(Item_Wear[14].VatPham_ID, 0);
			int num2 = BitConverter.ToInt16(packetData, 10);
			if (num == 0 || !World.ItemList.ContainsKey(num))
			{
				return;
			}
			var num3 = BitConverter.ToInt64(Item_Wear[14].ItemGlobal_ID, 0);
			var itmeClass = World.ItemList[num];
			if (itmeClass.FLD_RESIDE2 != 15)
			{
				HeThongNhacNho("Có điều bất thường tại nơi đây, đại hiệp cần cẩn thận!!!", 20, "Thiên cơ các");
			}
			else if (CharacterBeast == null)
			{
				ReadOutTheBeastData(num3, this);
				if (CharacterBeast != null && CharacterBeast.ZrName == CharacterName)
				{
					if (CharacterBeast.FLD_LEVEL < 99)
					{
						CharacterBeast.FLD_LEVEL = 99;
						CharacterBeast.FLD_ZCD = 2000;
					}
					if (CharacterBeast.FLD_JOB_LEVEL < 5)
					{
						CharacterBeast.FLD_JOB_LEVEL = 5;
					}
					CharacterBeast.Playe = this;
					if (CharacterBeast.FLD_ZCD < 100)
					{
						if (num2 != 100)
						{
							SummoningReminder(0, 3);
							ClearTheBeastState();
							return;
						}
						if (Player_Money < 1000000)
						{
							HeThongNhacNho("Ngân lượng không đủ 1,000,000 lượng, không thể triệu hoán sủng vật!");
							SummoningReminder(0, 6);
							ClearTheBeastState();
							return;
						}
						Player_Money -= 10000000L;
						UpdateMoneyAndWeight();
						CharacterBeast.FLD_ZCD = 200;
					}
					Item_Wear[14].Lock_Move = true;
					SummoningReminder(0, 1);
					if (Item_Wear[14].GetVatPham_ID == 1000001011)
					{
						CharacterBeast.Bs = 0;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001377)
					{
						CharacterBeast.Bs = 1;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001378)
					{
						CharacterBeast.Bs = 2;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001379)
					{
						CharacterBeast.Bs = 3;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001380)
					{
						CharacterBeast.Bs = 4;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001381)
					{
						CharacterBeast.Bs = 5;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001382)
					{
						CharacterBeast.Bs = 0;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001383)
					{
						CharacterBeast.Bs = 1;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001384)
					{
						CharacterBeast.Bs = 2;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000001385)
					{
						CharacterBeast.Bs = 3;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000002006)
					{
						CharacterBeast.Bs = 4;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000002001)
					{
						CharacterBeast.Bs = 0;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000002002)
					{
						CharacterBeast.Bs = 1;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000002003)
					{
						CharacterBeast.Bs = 2;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000002004)
					{
						CharacterBeast.Bs = 3;
					}
					else if (Item_Wear[14].GetVatPham_ID == 1000002005)
					{
						CharacterBeast.Bs = 0;
					}
					CharacterBeast.NhanVatToaDo_X = PosX;
					CharacterBeast.NhanVatToaDo_Y = PosY;
					CharacterBeast.NhanVatToaDo_Z = PosZ;
					CharacterBeast.NhanVatToaDo_MAP = MapID;
					SummonUpdateShowsEquippedItems(this);
					CharacterBeast.TinhToan_ThuCung_TrangBi_SoLieu();
					UpdateSpiritBeastHP_MP_SP();
					UpdateTheSpiritBeastExperienceAndTrainExperience();
					UpdateSpiritBeastMartialArtsAndStatus();
					UpdateTheWeightOfTheBeast();
					UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					SendSpiritBeastData(num3, CharacterBeast);
					return;
				}
				_namePackagePetId = num3;
				if (itmeClass.FLD_PID == 1000000065)
				{
					_namePackPetType = 1;
				}
				else if (itmeClass.FLD_PID == 1000000066)
				{
					_namePackPetType = 2;
				}
				else if (itmeClass.FLD_PID == 1000000067)
				{
					_namePackPetType = 3;
				}
				else if (itmeClass.FLD_PID == 1000000068)
				{
					_namePackPetType = 4;
				}
				else if (itmeClass.FLD_PID != 1000001011 && itmeClass.FLD_PID != 1000001377 && itmeClass.FLD_PID != 1000001378 && itmeClass.FLD_PID != 1000001379)
				{
					if (itmeClass.FLD_PID == 1000001380)
					{
						_namePackPetType = 5;
					}
					else if (itmeClass.FLD_PID == 1000001381)
					{
						_namePackPetType = 5;
					}
					else if (itmeClass.FLD_PID == 1000001382 || itmeClass.FLD_PID == 1000001383 || itmeClass.FLD_PID == 1000001384 || itmeClass.FLD_PID == 1000001385 || itmeClass.FLD_PID == 1000002006)
					{
						_namePackPetType = 6;
					}
					else if (itmeClass.FLD_PID == 1000000083)
					{
						_namePackPetType = 1;
					}
					else if (itmeClass.FLD_PID == 1000000084)
					{
						_namePackPetType = 2;
					}
					else if (itmeClass.FLD_PID == 1000000085)
					{
						_namePackPetType = 3;
					}
					else if (itmeClass.FLD_PID == 1000000086)
					{
						_namePackPetType = 4;
					}
					else if (itmeClass.FLD_PID == 1000002001 || itmeClass.FLD_PID == 1000002002 || itmeClass.FLD_PID == 1000002003 || itmeClass.FLD_PID == 1000002004)
					{
						_namePackPetType = 7;
					}
					else if (itmeClass.FLD_PID == 1000002005)
					{
						_namePackPetType = 8;
					}
				}
				else
				{
					_namePackPetType = 5;
				}
				Item_Wear[14].Lock_Move = true;
				SummoningReminder(0, 100);
			}
			else
			{
				ClearTheBeastState();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Summon Pets có lỗi !!! - " + ex.Message);
		}
	}

	public async Task PetNamePack(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = new byte[14];
			Buffer.BlockCopy(packetData, 14, array, 0, 14);
			var text = Encoding.Default.GetString(array).Replace("\0", string.Empty);
			//var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_Cw  WHERE  ItmeId  ={_namePackagePetId}");
			var dBToDataTable = await GameDb.GetPlayerPet(_namePackagePetId);
			if (text.Length > 12)
			{
				NameReminder(3);
				Item_Wear[14].Lock_Move = false;
			}
			else if (dBToDataTable != null)
			{
				//DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Cw  SET  ZrName='{1}',Name='{2}',FLD_ZCD={3}  WHERE  ItmeId={0}", _namePackagePetId, CharacterName, text, 100)).GetAwaiter().GetResult();
				await GameDb.UpdatePlayerPet(CharacterName, text, 100, _namePackagePetId);
				if (CharacterBeast != null)
				{
					CharacterBeast.Name = text;
					CharacterBeast.ZrName = CharacterName;
					CharacterBeast.FLD_ZCD = 100;
					ClearTheBeastState();
				}
			}
			else if (await GameDb.CreatePet(text, CharacterName, _namePackPetType, _namePackagePetId))
			{
				SummonPets(packetData, packetSize);
			}
			else
			{
				NameReminder(5);
				Item_Wear[14].Lock_Move = false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đặt tên cho thú cưng của bạn error: [" + SessionID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}

	public void SummoningReminder(int kg, int id)
	{
		var array = Converter.HexStringToByte("AA5512000A005110050000000000030000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(kg), 0, array, 10, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 14, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void NameReminder(int id)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(id);
		sendingClass.Write4(0);
		Client?.SendPak(sendingClass, 22288, SessionID);
	}

	public void PetAction(int id)
	{
		switch (id)
		{
			case 3:
				CharacterBeast.CuoiThu = 1;
				UpdateMovementSpeed();
				break;
			case 4:
				CharacterBeast.CuoiThu = 0;
				UpdateMovementSpeed();
				break;
			case 5:
			case 6:
				FLD_Pet_ThemVao_PhanTramKinhNghiem = 0.0;
				FLD_Pet_ThemVao_PhongNgu = 0;
				FLD_Pet_ThemVao_LonNhatHP = 0;
				FLD_Pet_ThemVao_CongKich = 0;
				CapNhat_HP_MP_SP();
				UpdateMartialArtsAndStatus();
				ClearTheListOfBeastStatus();
				if (CharacterBeast.AttackList != null)
				{
					CharacterBeast.AttackList.Clear();
					CharacterBeast.AttackList = null;
				}
				break;
			case 8:
				{
					CharacterBeast.ThuBay = 1;
					var array2 = Converter.HexStringToByte("AA551C002E010C050E0001000000000016430000803F0100000000000000000055AA");
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
					Client?.Send_Map_Data(array2, array2.Length);
					SendCurrentRangeBroadcastData(array2, array2.Length);
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					break;
				}
			case 9:
				{
					CharacterBeast.ThuBay = 0;
					var array = Converter.HexStringToByte("AA551C002E010C050E0000000000000016430000803F0100000000000000000055AA");
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
					Client?.Send_Map_Data(array, array.Length);
					SendCurrentRangeBroadcastData(array, array.Length);
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					break;
				}
		}
		var array3 = Converter.HexStringToByte("AA550F00549C5510010001000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array3, 10, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(CharacterBeastFullServiceID), 0, array3, 4, 2);
		Client?.Send_Map_Data(array3, array3.Length);
		SendCurrentRangeBroadcastData(array3, array3.Length);
	}

	public void SummonUpdateShowsTheDataOfTheBeast(Players thisPlay)
	{
		var array = Converter.HexStringToByte("AA556700549C6400580001000000549C000000000000000000000000000000000000000000003203040000000000FA590544000070417F86C64465000000E69EA42770920F24000000008425A627000100009CBBFF43D322BDC370F9CD440000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlay.CharacterBeastFullServiceID), 0, array, 14, 2);
		var bytes = Encoding.Default.GetBytes(thisPlay.CharacterBeast.Name);
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlay.CharacterBeast.FLD_LEVEL), 0, array, 38, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlay.CharacterBeast.FLD_JOB_LEVEL), 0, array, 40, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlay.CharacterBeast.FLD_JOB), 0, array, 41, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlay.CharacterBeast.Bs), 0, array, 42, 1);
		for (var i = 0; i < 4; i++)
		{
			byte[] src;
			try
			{
				src = CharacterBeast.ThuCungVaTrangBi[i].VatPham_ID;
			}
			catch
			{
				src = new byte[4];
			}
			Buffer.BlockCopy(src, 0, array, 44 + i * 4, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlay.CharacterBeastFullServiceID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
		Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
	}

	public void SummonUpdateShowsEquippedItems(Players play)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(1);
		sendingClass.Write4(play.CharacterBeastFullServiceID);
		sendingClass.WriteName(play.CharacterBeast.Name);
		sendingClass.Write(0);
		sendingClass.Write4(0);
		sendingClass.Write2(play.CharacterBeast.FLD_LEVEL);
		sendingClass.Write(play.CharacterBeast.FLD_JOB_LEVEL);
		sendingClass.Write(play.CharacterBeast.FLD_JOB);
		sendingClass.Write(play.CharacterBeast.Bs);
		sendingClass.Write(0);
		for (var i = 0; i < 5; i++)
		{
			if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[i].VatPhamSoLuong, 0) == 0)
			{
				CharacterBeast.ThuCungVaTrangBi[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
			sendingClass.Write(CharacterBeast.ThuCungVaTrangBi[i].GetByte(), 0, World.Item_Byte_Length_92);
		}
		Client.SendPak(sendingClass, 40960, play.CharacterBeastFullServiceID);
	}

	public void UpdateSpiritBeastHP_MP_SP()
	{
		if (CharacterBeast.FLD_ZCD > 2000)
		{
			CharacterBeast.FLD_ZCD = 2000;
		}
		FLD_Pet_ThemVao_PhanTramKinhNghiem = CalculateThePercentageOfPetsAdditionalExperience(CharacterBeast.FLD_ZCD);
		SendingClass sendingClass = new();
		sendingClass.Write4(CharacterBeast.FLD_ZCD);
		sendingClass.Write4(0);
		sendingClass.Write4(2000);
		sendingClass.Write4(0);
		Client?.SendPak(sendingClass, 26880, CharacterBeastFullServiceID);
	}

	public double CalculateThePercentageOfPetsAdditionalExperience(int doTrungThanh)
	{
		if (doTrungThanh >= 0 && doTrungThanh <= 200)
		{
			return 0.01;
		}
		if (doTrungThanh > 200 && doTrungThanh <= 500)
		{
			return 0.01;
		}
		if (doTrungThanh > 500 && doTrungThanh <= 800)
		{
			return 0.01;
		}
		if (doTrungThanh > 800 && doTrungThanh <= 1000)
		{
			return 0.01;
		}
		if (doTrungThanh > 1000 && doTrungThanh <= 1400)
		{
			return 0.0;
		}
		if (doTrungThanh > 1400 && doTrungThanh <= 1700)
		{
			return 0.03;
		}
		if (doTrungThanh > 1700 && doTrungThanh <= 1900)
		{
			return 0.06;
		}
		if (doTrungThanh > 1900 && doTrungThanh <= 2000)
		{
			return 0.09;
		}
		return 0.0;
	}

	public void UpdateTheSpiritBeastExperienceAndTrainExperience()
	{
		double num = Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL]) - Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL - 1]);
		double num2 = CharacterBeast.FLD_EXP - Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL - 1]);
		if (num2 < 1.0)
		{
			CharacterBeast.FLD_EXP = Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL - 1]);
			num2 = 0.0;
		}
		SendingClass sendingClass = new();
		sendingClass.Write8((long)num2);
		sendingClass.Write8((long)num);
		sendingClass.Write4(0);
		Client?.SendPak(sendingClass, 27136, CharacterBeastFullServiceID);
	}

	public void UpdateSpiritBeastMartialArtsAndStatus()
	{
		SendingClass sendingClass = new();
		sendingClass.Write2(CharacterBeast.FLD_LEVEL);
		sendingClass.Write2(CharacterBeast.SpiritBeastCoBanCongKich);
		sendingClass.Write2(CharacterBeast.SpiritBeastCoBanPhongNgu);
		sendingClass.Write2(CharacterBeast.SpiritBeastCoBanTrungDich);
		for (var i = 0; i < 4; i++)
		{
			if (CharacterBeast.VoCongMoi[0, i] != null)
			{
				sendingClass.Write4(CharacterBeast.VoCongMoi[0, i].FLD_PID);
			}
			else
			{
				sendingClass.Write4(0);
			}
		}
		for (var j = 0; j < 4; j++)
		{
			if (CharacterBeast.VoCongMoi[1, j] != null)
			{
				sendingClass.Write4(CharacterBeast.VoCongMoi[1, j].FLD_PID);
			}
			else
			{
				sendingClass.Write4(0);
			}
		}
		for (var k = 0; k < 24; k++)
		{
			sendingClass.Write4(0);
		}
		sendingClass.Write4(CharacterBeast.FLD_MAGIC1);
		sendingClass.Write4(CharacterBeast.FLD_MAGIC2);
		sendingClass.Write4(CharacterBeast.FLD_MAGIC3);
		sendingClass.Write4(CharacterBeast.FLD_MAGIC4);
		sendingClass.Write4(CharacterBeast.FLD_MAGIC5);
		Client?.SendPak(sendingClass, 27392, CharacterBeastFullServiceID);
	}

	public void UpdateTheWeightOfTheBeast()
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		sendingClass.Write4(CharacterBeast.FLD_TrongLuong);
		sendingClass.Write4(CharacterBeast.FLD_TrongLuong_MAX);
		Client?.SendPak(sendingClass, 31744, CharacterBeastFullServiceID);
	}
	
	public void ClearTheListOfBeastStatus()
	{
		if (AppendStatusList != null)
		{
			if (GetAddState(1000000167))
			{
				AppendStatusList[1000000167].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000168))
			{
				AppendStatusList[1000000168].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000170))
			{
				AppendStatusList[1000000170].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000171))
			{
				AppendStatusList[1000000171].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000173))
			{
				AppendStatusList[1000000173].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000174))
			{
				AppendStatusList[1000000174].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000176))
			{
				AppendStatusList[1000000176].ThoiGianKetThucSuKien();
			}
			if (GetAddState(1000000177))
			{
				AppendStatusList[1000000177].ThoiGianKetThucSuKien();
			}
			if (GetAddState(700201))
			{
				AppendStatusList[700201].ThoiGianKetThucSuKien();
			}
			if (GetAddState(700202))
			{
				AppendStatusList[700202].ThoiGianKetThucSuKien();
			}
			if (GetAddState(700203))
			{
				AppendStatusList[700203].ThoiGianKetThucSuKien();
			}
			if (GetAddState(700301))
			{
				AppendStatusList[700301].ThoiGianKetThucSuKien();
			}
			if (GetAddState(700302))
			{
				AppendStatusList[700302].ThoiGianKetThucSuKien();
			}
			if (GetAddState(700303))
			{
				AppendStatusList[700303].ThoiGianKetThucSuKien();
			}
		}
	}

	public void ClearTheBeastState()
	{
		if (CharacterBeast != null)
		{
			CharacterBeast.SaveSoLieu();
			FLD_Pet_ThemVao_PhanTramKinhNghiem = 0.0;
			FLD_Pet_ThemVao_CongKich = 0;
			FLD_Pet_ThemVao_PhongNgu = 0;
			FLD_Pet_ThemVao_LonNhatHP = 0;
			if (!Exiting && Client.Running)
			{
				CapNhat_HP_MP_SP();
				UpdateMartialArtsAndStatus();
			}
			ClearTheListOfBeastStatus();
			if (CharacterBeast.AttackList != null)
			{
				CharacterBeast.AttackList.Clear();
				CharacterBeast.AttackList = null;
			}
			SummoningReminder(1, 1);
			Uncall(this, this);
			CharacterBeast.Dispose();
			CharacterBeast = null;
			Item_Wear[14].Lock_Move = false;
		}
	}

	public void DeadPet()
	{
		CharacterBeast.TuVong = true;
		PetAction(5);
	}

	public void SpiritBeastTransformation(byte[] packetData, int packetSize)
	{
		try
		{
			if (Item_Wear[14].GetVatPham_ID == 1000001011 || Item_Wear[14].GetVatPham_ID == 1000001377 || Item_Wear[14].GetVatPham_ID == 1000001378 || Item_Wear[14].GetVatPham_ID == 1000001379)
			{
				return;
			}
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 10, array, 0, 1);
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 12, array2, 0, 2);
			var array3 = Converter.HexStringToByte("AA552200F4045B1014000100050038FE0D108546940181DC143C00000100000000000000E4A255AA");
			if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) == 1008000129)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
				var array4 = Converter.HexStringToByte("AA5514002C015C100600989E00000100000000000000000055AA");
				Buffer.BlockCopy(array2, 0, array4, 14, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(CharacterBeastFullServiceID), 0, array4, 10, 2);
				Client?.Send_Map_Data(array3, array3.Length);
				Client?.Send_Map_Data(array4, array4.Length);
				SendCurrentRangeBroadcastData(array4, array4.Length);
				if (CharacterBeast != null)
				{
					CharacterBeast.Bs = BitConverter.ToInt16(array2, 0);
				}
				SubtractItem(BitConverter.ToInt32(array, 0), 1);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Spirit Beast Biến đổi: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

}

