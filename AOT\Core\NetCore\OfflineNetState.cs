using System;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer;

namespace HeroYulgang.Core.NetCore
{
    /// <summary>
    /// Offline network state for players who are not connected
    /// Provides compatibility interface for offline player operations
    /// </summary>
    public class OfflineNetState 
    {
        #region Fields

        private readonly int _sessionId;
        private volatile bool _disposed = false;
        
        // Player state
        private string _account = string.Empty;
        private string _characterName = string.Empty;
        private uint _userId = 0;
        private bool _treoMay = false;

        #endregion

        #region Properties

        public int SessionId => _sessionId;
        public string Account => _account;
        public string CharacterName => _characterName;
        public uint UserId => _userId;
        public bool TreoMay 
        { 
            get => _treoMay; 
            set => _treoMay = value; 
        }
        
        // Compatibility properties
        public bool IsActive => false; // Always false for offline state
        public bool Login => false; // Always false for offline state
        public bool Online => false; // Always false for offline state

        public Players Player;

        #endregion

        #region Constructor

        public OfflineNetState(int sessionId, string account = "", string characterName = "", uint userId = 0)
        {
            _sessionId = sessionId;
            _account = account ?? string.Empty;
            _characterName = characterName ?? string.Empty;
            _userId = userId;

            Logger.Instance.Debug($"OfflineNetState created for session {sessionId}, account: {account}");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Set player information
        /// </summary>
        public void SetPlayerInfo(string account, string characterName, uint userId)
        {
            _account = account ?? string.Empty;
            _characterName = characterName ?? string.Empty;
            _userId = userId;
        }

        /// <summary>
        /// Send packet - No-op for offline state, logs warning
        /// </summary>
        public void SendPak(SendingClass pak, int id, int wordid, bool bypass = false, bool enableBatching = false, int sourceNpcId = 0)
        {
            Logger.Instance.Warning($"Attempted to send packet to offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Send map data - No-op for offline state, logs warning
        /// </summary>
        public void Send_Map_Data(byte[] data, int length)
        {
            Logger.Instance.Warning($"Attempted to send map data to offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Send packet - No-op for offline state, logs warning
        /// </summary>
        public void SendPacket(byte[] packetData)
        {
            Logger.Instance.Warning($"Attempted to send packet to offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Send encrypted packet - No-op for offline state, logs warning
        /// </summary>
        public void SendEncryptedPacket(byte[] packetData)
        {
            Logger.Instance.Warning($"Attempted to send encrypted packet to offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Send multiple package with encryption bypass - No-op for offline state
        /// </summary>
        public void SendMultiplePackageEncryptionByPass(byte[] data, int length, int count)
        {
            Logger.Instance.Warning($"Attempted to send bypass packet to offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Send multiple package with new encryption - No-op for offline state
        /// </summary>
        public void SendMultiplePackageEncryptionNew(byte[] data, int length, int count)
        {
            Logger.Instance.Warning($"Attempted to send encrypted packet to offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Disconnect - No-op for offline state
        /// </summary>
        public void Disconnect()
        {
            Logger.Instance.Debug($"Disconnect called on offline player {_account} (session {_sessionId})");
            // No-op for offline state
        }

        /// <summary>
        /// Check if player is inactive (always true for offline)
        /// </summary>
        public bool IsInactive(TimeSpan timeout)
        {
            return true; // Always inactive for offline state
        }

        /// <summary>
        /// Update heartbeat - No-op for offline state
        /// </summary>
        public void UpdateHeartbeat()
        {
            // No-op for offline state
        }

        /// <summary>
        /// Set authentication - Updates player info
        /// </summary>
        public void SetAuthentication(string account, uint userId, int clusterId = 0, int channelId = 0)
        {
            _account = account ?? string.Empty;
            _userId = userId;
            
            Logger.Instance.Debug($"OfflineNetState authentication set: Account={account}, UserId={userId}");
        }

        /// <summary>
        /// Set character name
        /// </summary>
        public void SetCharacterName(string characterName)
        {
            _characterName = characterName ?? string.Empty;
            Logger.Instance.Debug($"OfflineNetState character name set: {characterName}");
        }

        /// <summary>
        /// Set login status - No-op for offline state
        /// </summary>
        public void SetLoginStatus(bool status)
        {
            // No-op for offline state - always false
        }

        /// <summary>
        /// Get string representation
        /// </summary>
        public override string ToString()
        {
            return $"OfflineNetState[Session={_sessionId}, Account={_account}, Character={_characterName}, UserId={_userId}]";
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// Create offline state for a player
        /// </summary>
        public static OfflineNetState CreateForPlayer(string account, string characterName, uint userId)
        {
            var sessionId = Environment.TickCount + account.GetHashCode();
            return new OfflineNetState(sessionId, account, characterName, userId);
        }

        /// <summary>
        /// Create offline state with session ID
        /// </summary>
        public static OfflineNetState CreateWithSessionId(int sessionId)
        {
            return new OfflineNetState(sessionId);
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                _disposed = true;
                Logger.Instance.Debug($"OfflineNetState disposed for session {_sessionId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing OfflineNetState {_sessionId}: {ex.Message}");
            }
        }

        #endregion
    }
}
