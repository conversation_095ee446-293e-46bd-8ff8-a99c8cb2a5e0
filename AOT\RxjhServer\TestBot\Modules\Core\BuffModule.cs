using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using RxjhServer.TestBot.Core;

namespace RxjhServer.TestBot.Modules.Core
{
    /// <summary>
    /// Module quản lý buff và support skills
    /// </summary>
    public class BuffModule : BaseBotModule
    {
        public override string ModuleName => "BuffModule";
        public override int Priority => 3;
        
        protected override int UpdateInterval => 5000; // Check every 5 seconds
        
        private DateTime _lastBuffTime = DateTime.MinValue;
        private DateTime _lastSupportTime = DateTime.MinValue;
        
        // Danh sách buff ID cho từng job
        private readonly Dictionary<int, List<int>> _jobBuffs = new Dictionary<int, List<int>>
        {
            // Cung 2 buff
            [4] = new List<int> { },
            // Đại <PERSON> (job 5) buffs
            [5] = new List<int> { 501502, 501601, 501501, 501603, 501602 },
            // Ninja skill né và tốc đánh
            [6] = new List<int> { 801201, 801302 },
        };
        
        protected override bool OnCanExecute()
        {
            return Config.AutoBuffEnabled && base.OnCanExecute();
        }
        
        protected override void OnUpdate()
        {
            // Xử lý auto buff
            HandleAutoBuffs();
            
            // Xử lý support functions (pills, pet, etc.)
            HandleSupportFunctions();
        }
        
        /// <summary>
        /// Xử lý auto buff
        /// </summary>
        private void HandleAutoBuffs()
        {
            try
            {
                if (!IsTimeToAct(_lastBuffTime, Config.BuffInterval))
                    return;
                    
                // Buff theo job cụ thể
                BuffByJob();
                
                // Buff theo config
                BuffByConfig();
                
                _lastBuffTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                LogError($"Error in auto buffs: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Buff theo job cụ thể
        /// </summary>
        private void BuffByJob()
        {
            try
            {
                // Xử lý đặc biệt cho Đại Phu (job 5)
                if (Player.Player_Job == 5 && Player.offline_buff)
                {
                    HandleDaiPhuBuffs();
                    return;
                }

                // Xử lý buff cho các job khác
                if (_jobBuffs.TryGetValue(Player.Player_Job, out var jobBuffs))
                {
                    foreach (var buffId in jobBuffs)
                    {
                        if (ShouldApplyBuff(buffId))
                        {
                            ApplyBuff(buffId);
                        }
                    }
                }
                HandleCommonBuff();
            }
            catch (Exception ex)
            {
                LogError($"Error in job-specific buffs: {ex.Message}");
            }
        }

        private void HandleCommonBuff()
        {
            try
            {
                // var buffId = Player.Player_Level >= 100 && !Player.GetAddState(601103) ? 601103 :
                var buffId = Player.Player_Level switch
                {
                    >= 100 => 601103,
                    >= 60 => 601102,
                    >= 15 => 601101,
                    _ => 0,
                };
                if (ShouldApplyBuff(buffId))
                {
                    ApplyBuff(buffId);
                }
            }
            catch (Exception ex)
            {
                LogError($"handleCommonBuff error {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Xử lý buff cho Đại Phu (tương tự logic cũ)
        /// </summary>
        private void HandleDaiPhuBuffs()
        {
            try
            {
                // Set skill ID cho combat
                if (X_Vo_Cong_Loai.GetsfeWg(Player, 501203))
                {
                    Player.OfflineTreoMaySkill_ID = 501203;
                }

                // Kiểm tra và apply các buff
                var buffsToCheck = new[]
                {
                    (buffId: 501502, statusId: 501303, playerBuffField: "offline_buff_1"),
                    (buffId: 501601, statusId: 501403, playerBuffField: "offline_buff_2"),
                    (buffId: 501501, statusId: 501301, playerBuffField: "offline_buff_3"),
                    (buffId: 501603, statusId: 501402, playerBuffField: "offline_buff_4"),
                    (buffId: 501602, statusId: 501401, playerBuffField: "offline_buff_5")
                };

                foreach (var (buffId, statusId, playerBuffField) in buffsToCheck)
                {
                    if (X_Vo_Cong_Loai.GetsfeWg(Player, buffId))
                    {
                        // Set buff ID vào player field tương ứng
                        SetPlayerBuffField(playerBuffField, buffId);

                        // Kiểm tra xem có cần apply buff không
                        if (!Player.AppendStatusList.ContainsKey(buffId) &&
                            !Player.AppendStatusList.ContainsKey(statusId))
                        {
                            Player.OfflineTreoMaySkill_ID = buffId;
                            LogDebug($"Applied Đại Phu buff: {buffId}");
                            break; // Chỉ apply 1 buff mỗi lần
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in Đại Phu buffs: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Set buff ID vào player field
        /// </summary>
        /// <param name="fieldName">Tên field</param>
        /// <param name="buffId">Buff ID</param>
        private void SetPlayerBuffField(string fieldName, int buffId)
        {
            try
            {
                switch (fieldName)
                {
                    case "offline_buff_1":
                        Player.offline_buff_1 = buffId;
                        break;
                    case "offline_buff_2":
                        Player.offline_buff_2 = buffId;
                        break;
                    case "offline_buff_3":
                        Player.offline_buff_3 = buffId;
                        break;
                    case "offline_buff_4":
                        Player.offline_buff_4 = buffId;
                        break;
                    case "offline_buff_5":
                        Player.offline_buff_5 = buffId;
                        break;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error setting player buff field {fieldName}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Buff theo config
        /// </summary>
        private void BuffByConfig()
        {
            try
            {
                foreach (var buffId in Config.AutoBuffIds)
                {
                    if (ShouldApplyBuff(buffId))
                    {
                        ApplyBuff(buffId);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in config buffs: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Kiểm tra xem có nên apply buff không
        /// </summary>
        /// <param name="buffId">Buff ID</param>
        /// <returns>True nếu nên apply</returns>
        private bool ShouldApplyBuff(int buffId)
        {
            try
            {
                // Kiểm tra xem player có học skill này không
                if (!X_Vo_Cong_Loai.GetsfeWg(Player, buffId))
                    return false;
                    
                // Kiểm tra xem buff đã có chưa
                if (Player.AppendStatusList.ContainsKey(buffId))
                    return false;
                    
                // Kiểm tra MP có đủ không
                var skill = World.MagicList.TryGetValue(buffId, out var skillData) ? skillData : null;
                if (skill != null && Player.NhanVat_MP < skill.FLD_MP)
                    return false;
                    
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Apply buff
        /// </summary>
        /// <param name="buffId">Buff ID</param>
        private void ApplyBuff(int buffId)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"ApplyBuff {buffId} for {Player.CharacterName}");
                Player.HandleSkillOnSelf(buffId);
            }
            catch (Exception ex)
            {
                LogError($"Error applying buff {buffId}: {ex.Message}");
            }
        }

        private void ApplyBuffOnOther(int buffId)
        {
            try
            {
                
            }
            catch (Exception ex)
            {
                LogError($"Error ApplyBuffOnOther {buffId}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Xử lý support functions (pills, pet, etc.)
        /// </summary>
        private void HandleSupportFunctions()
        {
            try
            {
                if (!IsTimeToAct(_lastSupportTime, 60000)) // Every minute
                    return;

                if (Player.ChucNang_Auto_ThucHien == 1)
                {
                    // Auto sử dụng pill
                    Player.Auto_SuDungPill();

                    // Buff thích khách
                    Player.BuffThichKhach();

                    // Buff cảm sự
                    Player.Buff_CamSu();

                    // Auto cho pet ăn
                    Player.Auto_cho_pet_an();

                    // Buff cung
                    // Player.Buff_Cung();

                    LogDebug("Applied support functions");
                }

                _lastSupportTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                LogError($"Error in support functions: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Thêm buff ID vào danh sách auto buff
        /// </summary>
        /// <param name="buffId">Buff ID</param>
        public void AddAutoBuff(int buffId)
        {
            if (!Config.AutoBuffIds.Contains(buffId))
            {
                Config.AutoBuffIds.Add(buffId);
                LogInfo($"Added auto buff: {buffId}");
            }
        }
        
        /// <summary>
        /// Xóa buff ID khỏi danh sách auto buff
        /// </summary>
        /// <param name="buffId">Buff ID</param>
        public void RemoveAutoBuff(int buffId)
        {
            if (Config.AutoBuffIds.Remove(buffId))
            {
                LogInfo($"Removed auto buff: {buffId}");
            }
        }
        
        /// <summary>
        /// Force apply buff ngay lập tức
        /// </summary>
        /// <param name="buffId">Buff ID</param>
        public void ForceApplyBuff(int buffId)
        {
            try
            {
                if (ShouldApplyBuff(buffId))
                {
                    ApplyBuff(buffId);
                    LogInfo($"Force applied buff: {buffId}");
                }
                else
                {
                    LogWarning($"Cannot apply buff {buffId} - conditions not met");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error force applying buff {buffId}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Lấy danh sách buff hiện tại
        /// </summary>
        /// <returns>Danh sách buff ID</returns>
        public List<int> GetActiveBuffs()
        {
            try
            {
                return new List<int>(Player.AppendStatusList.Keys);
            }
            catch
            {
                return new List<int>();
            }
        }
    }
}
