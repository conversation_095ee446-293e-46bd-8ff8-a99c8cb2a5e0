-- =====================================================
-- SETUP CUMULATIVE REWARD SYSTEM
-- Run this script to create tables and insert sample data
-- =====================================================

-- Run the main table creation script
\i cumulative_reward_tables.sql

-- Insert sample data for testing
-- Template cho tháng 12/2024
INSERT INTO tbl_cumulative_reward_templates (
    name, month, year, is_active, start_date, end_date,
    milestone_1_cash, milestone_2_cash, milestone_3_cash, milestone_4_cash, milestone_5_cash,
    milestone_6_cash, milestone_7_cash, milestone_8_cash, milestone_9_cash, milestone_10_cash
) VALUES (
    'Cumulative Reward December 2024', 12, 2024, true, 
    '2024-12-01 00:00:00', '2024-12-31 23:59:59',
    5000, 10000, 20000, 35000, 50000, 75000, 100000, 150000, 200000, 300000
) ON CONFLICT (month, year) DO UPDATE SET
    is_active = EXCLUDED.is_active,
    start_date = EXCLUDED.start_date,
    end_date = EXCLUDED.end_date,
    milestone_1_cash = EXCLUDED.milestone_1_cash,
    milestone_2_cash = EXCLUDED.milestone_2_cash,
    milestone_3_cash = EXCLUDED.milestone_3_cash,
    milestone_4_cash = EXCLUDED.milestone_4_cash,
    milestone_5_cash = EXCLUDED.milestone_5_cash,
    milestone_6_cash = EXCLUDED.milestone_6_cash,
    milestone_7_cash = EXCLUDED.milestone_7_cash,
    milestone_8_cash = EXCLUDED.milestone_8_cash,
    milestone_9_cash = EXCLUDED.milestone_9_cash,
    milestone_10_cash = EXCLUDED.milestone_10_cash;

-- Get template ID for inserting rewards
DO $$
DECLARE
    template_id_var INTEGER;
BEGIN
    SELECT id INTO template_id_var FROM tbl_cumulative_reward_templates WHERE month = 12 AND year = 2024;
    
    -- Clear existing rewards for this template
    DELETE FROM tbl_cumulative_reward_items WHERE template_id = template_id_var;
    
    -- Milestone 1 rewards (5000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 1, 1, 34604, 1),  -- Sample item 1
    (template_id_var, 1, 2, 34605, 5);  -- Sample item 2
    
    -- Milestone 2 rewards (10000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 2, 1, 34606, 1),  -- Sample item 3
    (template_id_var, 2, 2, 34607, 3),  -- Sample item 4
    (template_id_var, 2, 3, 34608, 10); -- Sample item 5
    
    -- Milestone 3 rewards (20000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 3, 1, 34609, 1),  -- Sample item 6
    (template_id_var, 3, 2, 34610, 2),  -- Sample item 7
    (template_id_var, 3, 3, 34611, 15); -- Sample item 8
    
    -- Milestone 4 rewards (35000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 4, 1, 34612, 1),  -- Sample item 9
    (template_id_var, 4, 2, 34613, 3),  -- Sample item 10
    (template_id_var, 4, 3, 34614, 20), -- Sample item 11
    (template_id_var, 4, 4, 34615, 1);  -- Sample item 12
    
    -- Milestone 5 rewards (50000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 5, 1, 34616, 1),  -- Sample item 13
    (template_id_var, 5, 2, 34617, 5),  -- Sample item 14
    (template_id_var, 5, 3, 34618, 25), -- Sample item 15
    (template_id_var, 5, 4, 34619, 2);  -- Sample item 16
    
    -- Milestone 6 rewards (75000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 6, 1, 34620, 1),  -- Sample item 17
    (template_id_var, 6, 2, 34621, 5),  -- Sample item 18
    (template_id_var, 6, 3, 34622, 30), -- Sample item 19
    (template_id_var, 6, 4, 34623, 2),  -- Sample item 20
    (template_id_var, 6, 5, 34624, 1);  -- Sample item 21
    
    -- Milestone 7 rewards (100000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 7, 1, 34625, 1),  -- Sample item 22
    (template_id_var, 7, 2, 34626, 10), -- Sample item 23
    (template_id_var, 7, 3, 34627, 35), -- Sample item 24
    (template_id_var, 7, 4, 34628, 3),  -- Sample item 25
    (template_id_var, 7, 5, 34629, 1);  -- Sample item 26
    
    -- Milestone 8 rewards (150000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 8, 1, 34630, 1),  -- Sample item 27
    (template_id_var, 8, 2, 34631, 10), -- Sample item 28
    (template_id_var, 8, 3, 34632, 40), -- Sample item 29
    (template_id_var, 8, 4, 34633, 3),  -- Sample item 30
    (template_id_var, 8, 5, 34634, 2),  -- Sample item 31
    (template_id_var, 8, 6, 34635, 1);  -- Sample item 32
    
    -- Milestone 9 rewards (200000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 9, 1, 34636, 1),  -- Sample item 33
    (template_id_var, 9, 2, 34637, 15), -- Sample item 34
    (template_id_var, 9, 3, 34638, 50), -- Sample item 35
    (template_id_var, 9, 4, 34639, 5),  -- Sample item 36
    (template_id_var, 9, 5, 34640, 2),  -- Sample item 37
    (template_id_var, 9, 6, 34641, 1);  -- Sample item 38
    
    -- Milestone 10 rewards (300000 cash)
    INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) VALUES
    (template_id_var, 10, 1, 34642, 1),  -- Sample item 39
    (template_id_var, 10, 2, 34643, 20), -- Sample item 40
    (template_id_var, 10, 3, 34644, 100),-- Sample item 41
    (template_id_var, 10, 4, 34645, 10), -- Sample item 42
    (template_id_var, 10, 5, 34646, 5),  -- Sample item 43
    (template_id_var, 10, 6, 34647, 1);  -- Sample item 44
    
    RAISE NOTICE 'Successfully inserted cumulative reward data for template ID: %', template_id_var;
END $$;

-- Verify data
SELECT 
    t.name,
    t.month,
    t.year,
    t.is_active,
    COUNT(i.id) as total_rewards
FROM tbl_cumulative_reward_templates t
LEFT JOIN tbl_cumulative_reward_items i ON t.id = i.template_id
WHERE t.month = 12 AND t.year = 2024
GROUP BY t.id, t.name, t.month, t.year, t.is_active;

-- Show milestone breakdown
SELECT 
    milestone_number,
    COUNT(*) as reward_count,
    STRING_AGG(item_id::text || ' x' || item_amount::text, ', ') as rewards
FROM tbl_cumulative_reward_items i
JOIN tbl_cumulative_reward_templates t ON i.template_id = t.id
WHERE t.month = 12 AND t.year = 2024
GROUP BY milestone_number
ORDER BY milestone_number;
