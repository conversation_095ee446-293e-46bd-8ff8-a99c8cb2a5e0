﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Account {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class doanhthu {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('doanhthu_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? stt { get; set; }

		[JsonProperty]
		public long? tongtien { get; set; }

		[JsonProperty]
		public int? sothe { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string ti { get; set; }

		[JsonProperty]
		public long? thoigian { get; set; }

	}

}
