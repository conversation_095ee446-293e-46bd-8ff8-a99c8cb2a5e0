﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class thangthienkhicong {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('thangthienkhicong_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? khicongid { get; set; }

		[JsonProperty]
		public double? fld_bonusratevalueperpoint { get; set; }

		[JsonProperty]
		public int? vatpham_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string khicongten { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep1 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep2 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep3 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep4 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep5 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep6 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep7 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep8 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep9 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep10 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep11 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep12 { get; set; }

		[JsonProperty]
		public int? nhanvatnghenghiep13 { get; set; }

	}

}
