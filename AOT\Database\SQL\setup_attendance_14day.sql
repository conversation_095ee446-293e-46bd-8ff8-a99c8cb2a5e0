-- =====================================================
-- SETUP ATTENDANCE 14 DAY SCRIPT
-- Tạo attendance template 14 ngày với phần thưởng hấp dẫn
-- =====================================================

-- 1. Đảm bảo migration đã chạy
-- Kiểm tra xem các trường mới đã tồn tại chưa
DO $$
BEGIN
    -- Kiểm tra trường attendance_type
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'tbl_attendance_templates' 
        AND column_name = 'attendance_type'
    ) THEN
        RAISE EXCEPTION 'Migration chưa chạy! Vui lòng chạy attendance_14day_migration.sql trước';
    END IF;
END $$;

-- 2. Tạo attendance template 14 ngày
INSERT INTO tbl_attendance_templates (
    name, 
    month, 
    year, 
    is_active, 
    start_date, 
    end_date,
    attendance_type,
    eligible_after_days
) VALUES (
    'Điểm danh trở lại - Tháng ' || EXTRACT(MONTH FROM CURRENT_DATE) || '/' || EXTRACT(YEAR FROM CURRENT_DATE),
    EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER,
    EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER,
    TRUE, -- Active ngay
    DATE_TRUNC('month', CURRENT_DATE),
    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day',
    'comeback_14day',
    14
) ON CONFLICT (month, year) DO UPDATE SET
    name = EXCLUDED.name,
    attendance_type = EXCLUDED.attendance_type,
    eligible_after_days = EXCLUDED.eligible_after_days,
    is_active = EXCLUDED.is_active;

-- 3. Lấy ID của template vừa tạo và thêm rewards
DO $$
DECLARE
    template_id INTEGER;
BEGIN
    -- Lấy ID của template comeback 14 day
    SELECT id INTO template_id
    FROM tbl_attendance_templates
    WHERE attendance_type = 'comeback_14day'
    AND month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER
    AND year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER;
    
    IF template_id IS NOT NULL THEN
        -- Xóa rewards cũ nếu có
        DELETE FROM tbl_attendance_rewards WHERE attendance_id = template_id;
        
        -- Thêm rewards mới cho 14 ngày (phần thưởng hấp dẫn cho player trở lại)
        
        -- Ngày 1: Vàng khởi động
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 1, 1, 500000); -- 500k vàng
        
        -- Ngày 2: Kinh nghiệm
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 2, 2, 100000); -- 100k exp
        
        -- Ngày 3: Thuốc hồi máu lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 3, 100, 20); -- 20 thuốc hồi máu lớn
        
        -- Ngày 4: Thuốc hồi mana lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 4, 101, 20); -- 20 thuốc hồi mana lớn
        
        -- Ngày 5: Vàng bonus
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 5, 1, 750000); -- 750k vàng
        
        -- Ngày 6: Kinh nghiệm bonus
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 6, 2, 150000); -- 150k exp
        
        -- Ngày 7: Phần thưởng tuần đầu - Trang bị đặc biệt
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 7, 1000, 1); -- Trang bị đặc biệt
        
        -- Ngày 8: Thuốc tăng sức mạnh
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 8, 200, 10); -- 10 thuốc tăng sức mạnh
        
        -- Ngày 9: Vàng lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 9, 1, 1000000); -- 1M vàng
        
        -- Ngày 10: Kinh nghiệm lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 10, 2, 200000); -- 200k exp
        
        -- Ngày 11: Đá quý
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 11, 300, 5); -- 5 đá quý
        
        -- Ngày 12: Thuốc hiếm
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 12, 400, 3); -- 3 thuốc hiếm
        
        -- Ngày 13: Vàng siêu lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 13, 1, 1500000); -- 1.5M vàng
        
        -- Ngày 14: Phần thưởng cuối - Trang bị siêu hiếm
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 14, 2000, 1); -- Trang bị siêu hiếm
        
        RAISE NOTICE 'Đã thêm % rewards cho attendance 14 ngày (Template ID: %)', 14, template_id;
    ELSE
        RAISE EXCEPTION 'Không tìm thấy attendance template 14 ngày!';
    END IF;
END $$;

-- 4. Verify kết quả
SELECT 
    t.id,
    t.name,
    t.attendance_type,
    t.eligible_after_days,
    t.is_active,
    t.start_date,
    t.end_date,
    COUNT(r.id) as total_rewards
FROM tbl_attendance_templates t
LEFT JOIN tbl_attendance_rewards r ON t.id = r.attendance_id
WHERE t.attendance_type = 'comeback_14day'
AND t.month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER
AND t.year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER
GROUP BY t.id, t.name, t.attendance_type, t.eligible_after_days, t.is_active, t.start_date, t.end_date;

-- 5. Hiển thị tất cả rewards
SELECT 
    r.day_number,
    r.item_id,
    r.item_amount,
    '0x' || UPPER(TO_HEX(r.item_id)) as item_id_hex,
    CASE 
        WHEN r.day_number = 1 THEN 'Vàng khởi động'
        WHEN r.day_number = 2 THEN 'Kinh nghiệm'
        WHEN r.day_number = 3 THEN 'Thuốc hồi máu lớn'
        WHEN r.day_number = 4 THEN 'Thuốc hồi mana lớn'
        WHEN r.day_number = 5 THEN 'Vàng bonus'
        WHEN r.day_number = 6 THEN 'Kinh nghiệm bonus'
        WHEN r.day_number = 7 THEN 'Trang bị đặc biệt'
        WHEN r.day_number = 8 THEN 'Thuốc tăng sức mạnh'
        WHEN r.day_number = 9 THEN 'Vàng lớn'
        WHEN r.day_number = 10 THEN 'Kinh nghiệm lớn'
        WHEN r.day_number = 11 THEN 'Đá quý'
        WHEN r.day_number = 12 THEN 'Thuốc hiếm'
        WHEN r.day_number = 13 THEN 'Vàng siêu lớn'
        WHEN r.day_number = 14 THEN 'Trang bị siêu hiếm'
        ELSE 'Unknown'
    END as reward_description
FROM tbl_attendance_rewards r
JOIN tbl_attendance_templates t ON r.attendance_id = t.id
WHERE t.attendance_type = 'comeback_14day'
AND t.month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER
AND t.year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER
ORDER BY r.day_number;

-- 6. Thông báo hoàn thành
SELECT 'Attendance 14 ngày đã được setup thành công!' as status;
