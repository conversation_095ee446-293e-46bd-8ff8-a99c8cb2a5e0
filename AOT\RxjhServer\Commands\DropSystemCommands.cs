using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Public;
using RxjhServer.Services;

namespace RxjhServer.Commands
{
    /// <summary>
    /// Console commands for managing the new drop system
    /// </summary>
    public static class DropSystemCommands
    {
        /// <summary>
        /// Process drop system console commands
        /// </summary>
        /// <param name="command">Command string</param>
        /// <param name="player">Player executing the command (optional)</param>
        public static void ProcessDropCommand(string command, Players player = null)
        {
            try
            {
                var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 0) return;

                var cmd = parts[0].ToLower();

                switch (cmd)
                {
                    case "enablenewdrop":
                        EnableNewDropSystem();
                        break;

                    case "disablenewdrop":
                        DisableNewDropSystem();
                        break;

                    case "dropmultiplier":
                        if (parts.Length > 1 && decimal.TryParse(parts[1], out var multiplier))
                        {
                            SetDropMultiplier(multiplier);
                        }
                        else
                        {
                            LogHelper.WriteLine(LogLevel.Info, "Usage: dropmultiplier <value> (e.g., dropmultiplier 2.0)");
                        }
                        break;

                    case "dropdebug":
                        var debugMode = parts.Length > 1 ? parts[1].ToLower() : "toggle";
                        ToggleDropDebug(debugMode);
                        break;

                    case "refreshdrops":
                        RefreshDropConfiguration();
                        break;

                    case "dropstatus":
                        ShowDropSystemStatus();
                        break;

                    case "testdrop":
                        if (parts.Length > 2 && int.TryParse(parts[1], out var npcId) && int.TryParse(parts[2], out var npcLevel))
                        {
                            TestDropSystem(npcId, npcLevel, player);
                        }
                        else
                        {
                            LogHelper.WriteLine(LogLevel.Info, "Usage: testdrop <npcId> <npcLevel> (e.g., testdrop 15100 50)");
                        }
                        break;

                    case "dropstats":
                        ShowDropStatistics();
                        break;

                    case "drophelp":
                        ShowDropCommandHelp();
                        break;

                    default:
                        LogHelper.WriteLine(LogLevel.Info, $"Unknown drop command: {cmd}. Use 'drophelp' for available commands.");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing drop command: {ex.Message}");
            }
        }

        #region Command Implementations

        private static void EnableNewDropSystem()
        {
            UpdateDropConfig("new_drop_system_enabled", "true");
            LogHelper.WriteLine(LogLevel.Info, "✓ New drop system enabled");
        }

        private static void DisableNewDropSystem()
        {
            UpdateDropConfig("new_drop_system_enabled", "false");
            LogHelper.WriteLine(LogLevel.Info, "✓ New drop system disabled (using legacy system)");
        }

        private static void SetDropMultiplier(decimal multiplier)
        {
            if (multiplier < 0.1m || multiplier > 10.0m)
            {
                LogHelper.WriteLine(LogLevel.Warning, "Drop multiplier should be between 0.1 and 10.0");
                return;
            }

            UpdateDropConfig("global_drop_multiplier", multiplier.ToString("F2"));
            LogHelper.WriteLine(LogLevel.Info, $"✓ Drop multiplier set to {multiplier:F2}x");
        }

        private static void ToggleDropDebug(string mode)
        {
            var currentDebug = GetConfigBool("debug_drop_logging", false);
            var newDebug = mode switch
            {
                "on" or "true" or "1" => true,
                "off" or "false" or "0" => false,
                "toggle" => !currentDebug,
                _ => !currentDebug
            };

            UpdateDropConfig("debug_drop_logging", newDebug.ToString().ToLower());
            LogHelper.WriteLine(LogLevel.Info, $"✓ Drop debug logging: {(newDebug ? "ENABLED" : "DISABLED")}");
        }

        private static void RefreshDropConfiguration()
        {
            try
            {
                PublicDb.LoadNewDrops();
                PublicDb.LoadDropConfig();
                LogHelper.WriteLine(LogLevel.Info, "✓ Drop configuration refreshed from database");
                ShowDropSystemStatus();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to refresh drop configuration: {ex.Message}");
            }
        }

        private static void ShowDropSystemStatus()
        {
            var isEnabled = GetConfigBool("new_drop_system_enabled", false);
            var multiplier = GetConfigDecimal("global_drop_multiplier", 1.0m);
            var debugEnabled = GetConfigBool("debug_drop_logging", false);
            var maxDrops = GetConfigInt("max_drops_per_kill", 5);

            LogHelper.WriteLine(LogLevel.Info, "=== Drop System Status ===");
            LogHelper.WriteLine(LogLevel.Info, $"System Status: {(isEnabled ? "ENABLED" : "DISABLED")}");
            LogHelper.WriteLine(LogLevel.Info, $"Global Multiplier: {multiplier:F2}x");
            LogHelper.WriteLine(LogLevel.Info, $"Debug Logging: {(debugEnabled ? "ON" : "OFF")}");
            LogHelper.WriteLine(LogLevel.Info, $"Max Drops Per Kill: {maxDrops}");
            LogHelper.WriteLine(LogLevel.Info, $"Total Drop Rules: {World.NewDrops.Count}");
            LogHelper.WriteLine(LogLevel.Info, $"Active Drop Rules: {World.NewDrops.Count(r => true)}"); // All loaded rules are active
            LogHelper.WriteLine(LogLevel.Info, "========================");
        }

        private static void TestDropSystem(int npcId, int npcLevel, Players player)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Testing Drop System ===");
                LogHelper.WriteLine(LogLevel.Info, $"NPC ID: {npcId}, Level: {npcLevel}");

                if (player == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "No player specified, using null player for test");
                }

                // Test the drop system
                var activeQuests = player?.GetActiveQuestIds() ?? new List<int>();
                LogHelper.WriteLine(LogLevel.Info, $"Active Quests: [{string.Join(", ", activeQuests)}]");

                var drops = NewDropService.GetDrops(npcId, npcLevel, player, activeQuests);

                LogHelper.WriteLine(LogLevel.Info, $"Drop Results: {drops.Count} items");
                
                foreach (var drop in drops)
                {
                    LogHelper.WriteLine(LogLevel.Info, 
                        $"  - Item {drop.FLD_PID}: {drop.FLD_NAME} " +
                        $"(Magic: {drop.FLD_MAGIC0}/{drop.FLD_MAGIC1}/{drop.FLD_MAGIC2}/{drop.FLD_MAGIC3}/{drop.FLD_MAGIC4})");
                }

                if (drops.Count == 0)
                {
                    LogHelper.WriteLine(LogLevel.Info, "  No items dropped");
                }

                LogHelper.WriteLine(LogLevel.Info, "=========================");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error testing drop system: {ex.Message}");
            }
        }

        private static void ShowDropStatistics()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Drop Statistics ===");

                var levelRangeDrops = World.NewDrops.Where(d => d.SourceType == "level_range").ToList();
                var npcSpecificDrops = World.NewDrops.Where(d => d.SourceType == "npc_specific").ToList();
                var questBasedDrops = World.NewDrops.Where(d => d.SourceType == "quest_based").ToList();

                LogHelper.WriteLine(LogLevel.Info, $"Level Range Drops: {levelRangeDrops.Count}");
                LogHelper.WriteLine(LogLevel.Info, $"NPC Specific Drops: {npcSpecificDrops.Count}");
                LogHelper.WriteLine(LogLevel.Info, $"Quest Based Drops: {questBasedDrops.Count}");

                if (levelRangeDrops.Any())
                {
                    var avgRate = levelRangeDrops.Average(d => (double)d.DropRate);
                    LogHelper.WriteLine(LogLevel.Info, $"Average Level Drop Rate: {avgRate:P4}");
                }

                if (npcSpecificDrops.Any())
                {
                    var avgRate = npcSpecificDrops.Average(d => (double)d.DropRate);
                    LogHelper.WriteLine(LogLevel.Info, $"Average Boss Drop Rate: {avgRate:P4}");
                }

                LogHelper.WriteLine(LogLevel.Info, "=====================");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error showing drop statistics: {ex.Message}");
            }
        }

        private static void ShowDropCommandHelp()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Drop System Commands ===");
            LogHelper.WriteLine(LogLevel.Info, "enablenewdrop          - Enable new drop system");
            LogHelper.WriteLine(LogLevel.Info, "disablenewdrop         - Disable new drop system");
            LogHelper.WriteLine(LogLevel.Info, "dropmultiplier <value> - Set global drop multiplier (0.1-10.0)");
            LogHelper.WriteLine(LogLevel.Info, "dropdebug [on/off]     - Toggle debug logging");
            LogHelper.WriteLine(LogLevel.Info, "refreshdrops           - Reload drop configuration from database");
            LogHelper.WriteLine(LogLevel.Info, "dropstatus             - Show current system status");
            LogHelper.WriteLine(LogLevel.Info, "testdrop <npc> <level> - Test drop system for specific NPC");
            LogHelper.WriteLine(LogLevel.Info, "dropstats              - Show drop statistics");
            LogHelper.WriteLine(LogLevel.Info, "drophelp               - Show this help");
            LogHelper.WriteLine(LogLevel.Info, "===========================");
        }

        #endregion

        #region Helper Methods

        private static void UpdateDropConfig(string key, string value)
        {
            try
            {
                // Update database
                var config = PublicDb._freeSql.Select<tbl_drop_config>()
                    .Where(c => c.config_key == key)
                    .First();

                if (config != null)
                {
                    PublicDb._freeSql.Update<tbl_drop_config>()
                        .Set(c => c.config_value, value)
                        .Set(c => c.updated_at, DateTime.Now)
                        .Where(c => c.config_key == key)
                        .ExecuteAffrows();
                }
                else
                {
                    // Insert new config
                    PublicDb._freeSql.Insert(new tbl_drop_config
                    {
                        config_key = key,
                        config_value = value,
                        description = $"Auto-created config for {key}",
                        updated_at = DateTime.Now
                    }).ExecuteAffrows();
                }

                // Update cache
                World.DropConfig[key] = value;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to update drop config: {ex.Message}");
            }
        }

        private static bool GetConfigBool(string key, bool defaultValue)
        {
            if (World.DropConfig.TryGetValue(key, out var value))
            {
                return bool.TryParse(value, out var result) ? result : defaultValue;
            }
            return defaultValue;
        }

        private static decimal GetConfigDecimal(string key, decimal defaultValue)
        {
            if (World.DropConfig.TryGetValue(key, out var value))
            {
                return decimal.TryParse(value, out var result) ? result : defaultValue;
            }
            return defaultValue;
        }

        private static int GetConfigInt(string key, int defaultValue)
        {
            if (World.DropConfig.TryGetValue(key, out var value))
            {
                return int.TryParse(value, out var result) ? result : defaultValue;
            }
            return defaultValue;
        }

        #endregion
    }
}
