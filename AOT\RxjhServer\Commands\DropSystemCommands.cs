using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Public;
using RxjhServer.Services;

namespace RxjhServer.Commands
{
    /// <summary>
    /// Console commands for managing the new drop system
    /// </summary>
    public static class DropSystemCommands
    {
        /// <summary>
        /// Process drop system console commands
        /// </summary>
        /// <param name="command">Command string</param>
        /// <param name="player">Player executing the command (optional)</param>
        public static void ProcessDropCommand(string command, Players player = null)
        {
            try
            {
                var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 0) return;

                var cmd = parts[0].ToLower();

                switch (cmd)
                {
                    case "enablenewdrop":
                        EnableNewDropSystem();
                        break;

                    case "disablenewdrop":
                        DisableNewDropSystem();
                        break;

                    case "dropmultiplier":
                        if (parts.Length > 1 && decimal.TryParse(parts[1], out var multiplier))
                        {
                            SetDropMultiplier(multiplier);
                        }
                        else
                        {
                            LogHelper.WriteLine(LogLevel.Info, "Usage: dropmultiplier <value> (e.g., dropmultiplier 2.0)");
                        }
                        break;

                    case "dropdebug":
                        var debugMode = parts.Length > 1 ? parts[1].ToLower() : "toggle";
                        ToggleDropDebug(debugMode);
                        break;

                    case "refreshdrops":
                        RefreshDropConfiguration();
                        break;

                    case "dropstatus":
                        ShowDropSystemStatus();
                        break;

                    case "testdrop":
                        if (parts.Length > 2 && int.TryParse(parts[1], out var npcId) && int.TryParse(parts[2], out var npcLevel))
                        {
                            TestDropSystem(npcId, npcLevel, player);
                        }
                        else
                        {
                            LogHelper.WriteLine(LogLevel.Info, "Usage: testdrop <npcId> <npcLevel> (e.g., testdrop 15100 50)");
                        }
                        break;

                    case "dropstats":
                        ShowDropStatistics();
                        break;

                    case "migratedrops":
                        if (parts.Length > 1 && parts[1].ToLower() == "confirm")
                        {
                            await MigrateAllDropsFromOldSystem();
                        }
                        else
                        {
                            LogHelper.WriteLine(LogLevel.Info, "This will migrate ALL drops from tbl_xwwl_drop to tbl_new_drops");
                            LogHelper.WriteLine(LogLevel.Info, "Use 'migratedrops confirm' to proceed");
                        }
                        break;

                    case "drophelp":
                        ShowDropCommandHelp();
                        break;

                    default:
                        LogHelper.WriteLine(LogLevel.Info, $"Unknown drop command: {cmd}. Use 'drophelp' for available commands.");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing drop command: {ex.Message}");
            }
        }

        #region Command Implementations

        private static void EnableNewDropSystem()
        {
            UpdateDropConfig("new_drop_system_enabled", "true");
            LogHelper.WriteLine(LogLevel.Info, "✓ New drop system enabled");
        }

        private static void DisableNewDropSystem()
        {
            UpdateDropConfig("new_drop_system_enabled", "false");
            LogHelper.WriteLine(LogLevel.Info, "✓ New drop system disabled (using legacy system)");
        }

        private static void SetDropMultiplier(decimal multiplier)
        {
            if (multiplier < 0.1m || multiplier > 10.0m)
            {
                LogHelper.WriteLine(LogLevel.Warning, "Drop multiplier should be between 0.1 and 10.0");
                return;
            }

            UpdateDropConfig("global_drop_multiplier", multiplier.ToString("F2"));
            LogHelper.WriteLine(LogLevel.Info, $"✓ Drop multiplier set to {multiplier:F2}x");
        }

        private static void ToggleDropDebug(string mode)
        {
            var currentDebug = GetConfigBool("debug_drop_logging", false);
            var newDebug = mode switch
            {
                "on" or "true" or "1" => true,
                "off" or "false" or "0" => false,
                "toggle" => !currentDebug,
                _ => !currentDebug
            };

            UpdateDropConfig("debug_drop_logging", newDebug.ToString().ToLower());
            LogHelper.WriteLine(LogLevel.Info, $"✓ Drop debug logging: {(newDebug ? "ENABLED" : "DISABLED")}");
        }

        private static void RefreshDropConfiguration()
        {
            try
            {
                PublicDb.LoadNewDrops();
                PublicDb.LoadDropConfig();
                LogHelper.WriteLine(LogLevel.Info, "✓ Drop configuration refreshed from database");
                ShowDropSystemStatus();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to refresh drop configuration: {ex.Message}");
            }
        }

        private static void ShowDropSystemStatus()
        {
            var isEnabled = GetConfigBool("new_drop_system_enabled", false);
            var multiplier = GetConfigDecimal("global_drop_multiplier", 1.0m);
            var debugEnabled = GetConfigBool("debug_drop_logging", false);
            var maxDrops = GetConfigInt("max_drops_per_kill", 5);

            LogHelper.WriteLine(LogLevel.Info, "=== Drop System Status ===");
            LogHelper.WriteLine(LogLevel.Info, $"System Status: {(isEnabled ? "ENABLED" : "DISABLED")}");
            LogHelper.WriteLine(LogLevel.Info, $"Global Multiplier: {multiplier:F2}x");
            LogHelper.WriteLine(LogLevel.Info, $"Debug Logging: {(debugEnabled ? "ON" : "OFF")}");
            LogHelper.WriteLine(LogLevel.Info, $"Max Drops Per Kill: {maxDrops}");
            LogHelper.WriteLine(LogLevel.Info, $"Total Drop Rules: {World.NewDrops.Count}");
            LogHelper.WriteLine(LogLevel.Info, $"Active Drop Rules: {World.NewDrops.Count(r => true)}"); // All loaded rules are active
            LogHelper.WriteLine(LogLevel.Info, "========================");
        }

        private static void TestDropSystem(int npcId, int npcLevel, Players player)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Testing Drop System ===");
                LogHelper.WriteLine(LogLevel.Info, $"NPC ID: {npcId}, Level: {npcLevel}");

                if (player == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "No player specified, using null player for test");
                }

                // Test the drop system
                var activeQuests = player?.GetActiveQuestIds() ?? new List<int>();
                LogHelper.WriteLine(LogLevel.Info, $"Active Quests: [{string.Join(", ", activeQuests)}]");

                var drops = NewDropService.GetDrops(npcId, npcLevel, player, activeQuests);

                LogHelper.WriteLine(LogLevel.Info, $"Drop Results: {drops.Count} items");
                
                foreach (var drop in drops)
                {
                    LogHelper.WriteLine(LogLevel.Info, 
                        $"  - Item {drop.FLD_PID}: {drop.FLD_NAME} " +
                        $"(Magic: {drop.FLD_MAGIC0}/{drop.FLD_MAGIC1}/{drop.FLD_MAGIC2}/{drop.FLD_MAGIC3}/{drop.FLD_MAGIC4})");
                }

                if (drops.Count == 0)
                {
                    LogHelper.WriteLine(LogLevel.Info, "  No items dropped");
                }

                LogHelper.WriteLine(LogLevel.Info, "=========================");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error testing drop system: {ex.Message}");
            }
        }

        private static void ShowDropStatistics()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Drop Statistics ===");

                var levelRangeDrops = World.NewDrops.Where(d => d.SourceType == "level_range").ToList();
                var npcSpecificDrops = World.NewDrops.Where(d => d.SourceType == "npc_specific").ToList();
                var questBasedDrops = World.NewDrops.Where(d => d.SourceType == "quest_based").ToList();

                LogHelper.WriteLine(LogLevel.Info, $"Level Range Drops: {levelRangeDrops.Count}");
                LogHelper.WriteLine(LogLevel.Info, $"NPC Specific Drops: {npcSpecificDrops.Count}");
                LogHelper.WriteLine(LogLevel.Info, $"Quest Based Drops: {questBasedDrops.Count}");

                if (levelRangeDrops.Any())
                {
                    var avgRate = levelRangeDrops.Average(d => (double)d.DropRate);
                    LogHelper.WriteLine(LogLevel.Info, $"Average Level Drop Rate: {avgRate:P4}");
                }

                if (npcSpecificDrops.Any())
                {
                    var avgRate = npcSpecificDrops.Average(d => (double)d.DropRate);
                    LogHelper.WriteLine(LogLevel.Info, $"Average Boss Drop Rate: {avgRate:P4}");
                }

                LogHelper.WriteLine(LogLevel.Info, "=====================");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error showing drop statistics: {ex.Message}");
            }
        }

        private static void ShowDropCommandHelp()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Drop System Commands ===");
            LogHelper.WriteLine(LogLevel.Info, "enablenewdrop          - Enable new drop system");
            LogHelper.WriteLine(LogLevel.Info, "disablenewdrop         - Disable new drop system");
            LogHelper.WriteLine(LogLevel.Info, "dropmultiplier <value> - Set global drop multiplier (0.1-10.0)");
            LogHelper.WriteLine(LogLevel.Info, "dropdebug [on/off]     - Toggle debug logging");
            LogHelper.WriteLine(LogLevel.Info, "refreshdrops           - Reload drop configuration from database");
            LogHelper.WriteLine(LogLevel.Info, "dropstatus             - Show current system status");
            LogHelper.WriteLine(LogLevel.Info, "testdrop <npc> <level> - Test drop system for specific NPC");
            LogHelper.WriteLine(LogLevel.Info, "dropstats              - Show drop statistics");
            LogHelper.WriteLine(LogLevel.Info, "migratedrops confirm   - Migrate all drops from old system");
            LogHelper.WriteLine(LogLevel.Info, "drophelp               - Show this help");
            LogHelper.WriteLine(LogLevel.Info, "===========================");
        }

        /// <summary>
        /// Migrate all drops from tbl_xwwl_drop to tbl_new_drops
        /// </summary>
        private static async Task MigrateAllDropsFromOldSystem()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Starting Drop Migration ===");

                // Execute the migration SQL script
                var migrationSql = @"
                    -- Backup old data
                    DO $$
                    BEGIN
                        EXECUTE 'CREATE TABLE IF NOT EXISTS tbl_xwwl_drop_backup_' ||
                                to_char(now(), 'YYYYMMDD_HH24MISS') ||
                                ' AS SELECT * FROM tbl_xwwl_drop';
                    END $$;

                    -- Migration
                    INSERT INTO tbl_new_drops (
                        source_type, source_value, item_id, drop_rate,
                        quantity_min, quantity_max,
                        magic0, magic1, magic2, magic3, magic4,
                        expire_days, is_active, priority,
                        created_at, updated_at
                    )
                    SELECT
                        'level_range' as source_type,
                        CASE
                            WHEN fld_level1 IS NULL OR fld_level1 < 1 THEN '1'
                            ELSE fld_level1::text
                        END || '-' ||
                        CASE
                            WHEN fld_level2 IS NULL OR fld_level2 < fld_level1 THEN
                                CASE
                                    WHEN fld_level1 IS NULL OR fld_level1 < 1 THEN '100'
                                    ELSE GREATEST(fld_level1, 100)::text
                                END
                            WHEN fld_level2 > 300 THEN '300'
                            ELSE fld_level2::text
                        END as source_value,
                        fld_pid as item_id,
                        -- Convert FLD_PP to drop rate
                        -- Old logic: random(1-8000), drop if random <= FLD_PP
                        -- Real rate = FLD_PP / 8000
                        CASE
                            WHEN fld_pp IS NULL OR fld_pp <= 0 THEN 0.000001
                            WHEN fld_pp >= 8000 THEN 1.000000
                            ELSE ROUND(CAST(fld_pp AS DECIMAL(10,6)) / 8000.0, 6)
                        END as drop_rate,
                        1 as quantity_min, 1 as quantity_max,
                        COALESCE(fld_magic0, 0), COALESCE(fld_magic1, 0),
                        COALESCE(fld_magic2, 0), COALESCE(fld_magic3, 0), COALESCE(fld_magic4, 0),
                        COALESCE(fld_days, 0) as expire_days,
                        true as is_active,
                        CASE
                            WHEN fld_pp >= 4000 THEN 120
                            WHEN fld_pp >= 1600 THEN 110
                            WHEN fld_pp >= 400 THEN 100
                            ELSE 90
                        END as priority,
                        NOW(), NOW()
                    FROM tbl_xwwl_drop
                    WHERE fld_pid IS NOT NULL AND fld_pid > 0
                      AND fld_pp IS NOT NULL AND fld_pp > 0
                      AND (fld_level1 IS NULL OR fld_level1 >= 1)
                      AND (fld_level2 IS NULL OR fld_level2 <= 300)
                      AND (fld_level1 IS NULL OR fld_level2 IS NULL OR fld_level2 >= fld_level1)
                    ON CONFLICT (source_type, source_value, item_id) DO UPDATE SET
                        drop_rate = EXCLUDED.drop_rate,
                        magic0 = EXCLUDED.magic0, magic1 = EXCLUDED.magic1,
                        magic2 = EXCLUDED.magic2, magic3 = EXCLUDED.magic3, magic4 = EXCLUDED.magic4,
                        expire_days = EXCLUDED.expire_days, priority = EXCLUDED.priority,
                        updated_at = NOW();

                    -- Update config
                    INSERT INTO tbl_drop_config (config_key, config_value, description, updated_at)
                    VALUES ('migration_completed', 'true', 'Migration completed via console', NOW())
                    ON CONFLICT (config_key) DO UPDATE SET
                        config_value = EXCLUDED.config_value, updated_at = EXCLUDED.updated_at;
                ";

                await PublicDb._freeSql.Ado.ExecuteNonQueryAsync(migrationSql);

                // Get migration statistics
                var oldCount = await PublicDb._freeSql.Select<tbl_xwwl_drop>()
                    .Where(d => d.fld_pid > 0 && d.fld_pp > 0)
                    .CountAsync();

                var newCount = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.source_type == "level_range")
                    .CountAsync();

                LogHelper.WriteLine(LogLevel.Info, $"✓ Migration completed successfully!");
                LogHelper.WriteLine(LogLevel.Info, $"Old system records: {oldCount}");
                LogHelper.WriteLine(LogLevel.Info, $"Migrated records: {newCount}");
                LogHelper.WriteLine(LogLevel.Info, $"Success rate: {(double)newCount / oldCount * 100:F1}%");

                // Reload drop configuration
                PublicDb.LoadNewDrops();
                PublicDb.LoadDropConfig();

                LogHelper.WriteLine(LogLevel.Info, "✓ Drop configuration reloaded");
                LogHelper.WriteLine(LogLevel.Info, "Use 'enablenewdrop' to activate the new system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"✗ Migration failed: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private static void UpdateDropConfig(string key, string value)
        {
            try
            {
                // Update database
                var config = PublicDb._freeSql.Select<tbl_drop_config>()
                    .Where(c => c.config_key == key)
                    .First();

                if (config != null)
                {
                    PublicDb._freeSql.Update<tbl_drop_config>()
                        .Set(c => c.config_value, value)
                        .Set(c => c.updated_at, DateTime.Now)
                        .Where(c => c.config_key == key)
                        .ExecuteAffrows();
                }
                else
                {
                    // Insert new config
                    PublicDb._freeSql.Insert(new tbl_drop_config
                    {
                        config_key = key,
                        config_value = value,
                        description = $"Auto-created config for {key}",
                        updated_at = DateTime.Now
                    }).ExecuteAffrows();
                }

                // Update cache
                World.DropConfig[key] = value;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to update drop config: {ex.Message}");
            }
        }

        private static bool GetConfigBool(string key, bool defaultValue)
        {
            if (World.DropConfig.TryGetValue(key, out var value))
            {
                return bool.TryParse(value, out var result) ? result : defaultValue;
            }
            return defaultValue;
        }

        private static decimal GetConfigDecimal(string key, decimal defaultValue)
        {
            if (World.DropConfig.TryGetValue(key, out var value))
            {
                return decimal.TryParse(value, out var result) ? result : defaultValue;
            }
            return defaultValue;
        }

        private static int GetConfigInt(string key, int defaultValue)
        {
            if (World.DropConfig.TryGetValue(key, out var value))
            {
                return int.TryParse(value, out var result) ? result : defaultValue;
            }
            return defaultValue;
        }

        #endregion
    }
}
