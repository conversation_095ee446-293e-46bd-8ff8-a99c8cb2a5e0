﻿
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.HeroBoss;
public class WorldBossContributeClass
{
    public int ID;
    public NpcClass Boss;

    // Local contributions (từ server hiện tại)
    public Dictionary<(int,int), DamageContribute> Contribute;

    // Cross-server contributions (từ tất cả servers)
    public ConcurrentDictionary<(int,int), CrossServerDamageContribute> CrossServerContributions;


    private string _KilledBy;
    public bool Rewarded { get; set; }
    private WorldBossReward[] _reward;
    public WorldBossReward[] Reward
    {
        get => _reward;
        set => _reward = value;
    }

    // Thông tin cross-server
    public bool IsCrossServer { get; set; }
    public int OriginServerId { get; set; }
    public long TotalCrossServerDamage { get; private set; }
    public int TotalCrossServerParticipants { get; private set; }

    public string KilledBy
    {
        get => _KilledBy;
        set => _KilledBy = value;
    }

    public WorldBossContributeClass()
    {
        Contribute = new Dictionary<(int, int), DamageContribute>();
        CrossServerContributions = new ConcurrentDictionary<(int, int), CrossServerDamageContribute>();
    }

    public void UpdateContribute(int worldId,int SessionID,string playerName, long damage, int attackCount, string accountId)
    {
        // Check and add if player is not in the dictionary
        LogHelper.WriteLine(LogLevel.Error,"Cập nhật contribute cho "+playerName + " DMG : "+ damage);
        if (!Contribute.ContainsKey((World.ServerID,SessionID)))
        {
            Contribute.Add((World.ServerID,SessionID), new DamageContribute(worldId,SessionID,playerName, damage, attackCount, accountId));
        }
        else
        {
            Contribute[(World.ServerID,SessionID)].Damage += damage;
            Contribute[(World.ServerID,SessionID)].AttackCount += attackCount;
        }

        // Nếu là cross-server boss, cập nhật vào CrossServerBossManager
        if (IsCrossServer)
        {
            CrossServerBossManager.Instance.UpdateContribution(ID, worldId, SessionID, playerName, damage, attackCount);
        }
    }

    /// <summary>
    /// Cập nhật cross-server contribution từ server khác
    /// </summary>
    public void UpdateCrossServerContribute(int serverId, int sessionId, string playerName, long damage, int attackCount)
    {
        try
        {
            var key = (serverId, sessionId);
            CrossServerContributions.AddOrUpdate(key,
                new CrossServerDamageContribute(serverId, sessionId, playerName, damage, attackCount),
                (k, existing) =>
                {
                    existing.UpdateDamage(damage, attackCount);
                    return existing;
                });

            // Cập nhật tổng thống kê
            RecalculateCrossServerStats();

            LogHelper.WriteLine(LogLevel.Debug, $"Updated cross-server contribution: {playerName} from server {serverId}, damage: {damage}");
        }
        catch (System.Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error updating cross-server contribution: {ex.Message}");
        }
    }

    /// <summary>
    /// Tính lại thống kê cross-server
    /// </summary>
    private void RecalculateCrossServerStats()
    {
        TotalCrossServerDamage = CrossServerContributions.Values.Sum(c => c.Damage);
        TotalCrossServerParticipants = CrossServerContributions.Count;
    }

    /// <summary>
    /// Lấy tất cả contributions (local + cross-server)
    /// </summary>
    public List<CrossServerDamageContribute> GetAllContributions()
    {
        var allContributions = new List<CrossServerDamageContribute>();

        // Thêm local contributions
        foreach (var localContrib in Contribute.Values)
        {
            allContributions.Add(new CrossServerDamageContribute(
                localContrib.WorldID,
                localContrib.SessionID,
                localContrib.PlayerName,
                localContrib.Damage,
                localContrib.AttackCount
            ));
        }

        // Thêm cross-server contributions
        allContributions.AddRange(CrossServerContributions.Values);

        return allContributions.OrderByDescending(c => c.Damage).ToList();
    }

    /// <summary>
    /// Lấy top contributors
    /// </summary>
    public List<CrossServerDamageContribute> GetTopContributors(int count = 10)
    {
        return GetAllContributions().Take(count).ToList();
    }

    /// <summary>
    /// Lấy total damage từ tất cả servers
    /// </summary>
    public long GetTotalDamage()
    {
        var localDamage = Contribute.Values.Sum(c => c.Damage);
        return localDamage + TotalCrossServerDamage;
    }

    /// <summary>
    /// Lấy total participants từ tất cả servers
    /// </summary>
    public int GetTotalParticipants()
    {
        return Contribute.Count + TotalCrossServerParticipants;
    }

    public DamageContribute GetContributeByPlayer(Players player)
    {
        if (Contribute.ContainsKey((World.ServerID,player.SessionID)))
        {
            return Contribute[(World.ServerID,player.SessionID)];
        }
        return null;
    }
}

public class WorldBossReward
{
    public int ID;
    public int RewardID;
    public int RewardCount;
    public Players player;
}
