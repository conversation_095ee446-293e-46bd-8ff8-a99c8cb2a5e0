using System.Collections.Generic;
using System.Linq;
using static RxjhServer.NpcManager.NpcManager;

namespace RxjhServer;

public class MapClass
{
	// New tuple-based dictionary for better scalability
	public Dictionary<(int MapID, int NPC_SessionID), NpcClass> npcTemplate = new();

	// Helper methods for backward compatibility
	/// <summary>
	/// Get NPC by SessionID only (for backward compatibility)
	/// </summary>
	public NpcClass GetNpcBySessionId(int sessionId)
	{
		return npcTemplate.Values.FirstOrDefault(npc => npc.NPC_SessionID == sessionId);
	}

	/// <summary>
	/// Get all NPCs as Dictionary with SessionID key (for backward compatibility)
	/// </summary>
	public Dictionary<int, NpcClass> GetNpcTemplateAsLegacyFormat()
	{
		var result = new Dictionary<int, NpcClass>();
		foreach (var kvp in npcTemplate)
		{
			if (!result.ContainsKey(kvp.Value.NPC_SessionID))
			{
				result.Add(kvp.Value.NPC_SessionID, kvp.Value);
			}
		}
		return result;
	}

	/// <summary>
	/// Check if NPC exists by SessionID only
	/// </summary>
	public bool ContainsNpcBySessionId(int sessionId)
	{
		return npcTemplate.Values.Any(npc => npc.NPC_SessionID == sessionId);
	}

	private int int_0;

	private int maxcall;

	public int MapID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public static int GetNpcConn()
	{
		var num = 0;
		foreach (var value in World.MapList.Values)
		{
			num += value.npcTemplate.Count;
		}
		return num;
	}

	public static Dictionary<int, NpcClass> GetnpcTemplate(int int_1)
	{
		if (World.MapList.TryGetValue(int_1, out var value))
		{
			return value.GetNpcTemplateAsLegacyFormat();
		}
		return new();
	}

	public static Dictionary<int, NpcClass> GetnpcPID(int mapid, int pid)
	{
		Dictionary<int, NpcClass> dictionary = new();
		if (World.MapList.TryGetValue(mapid, out var value))
		{
			foreach (var value2 in value.npcTemplate.Values)
			{
				if (value2.FLD_PID == pid)
				{
					dictionary.Add(value2.NPC_SessionID, value2);
				}
			}
		}
		return dictionary;
	}

	public static NpcClass GetNpc(int mapId, int npcSession)
	{
		if (!World.MapList.TryGetValue(mapId, out var value))
		{
			return null;
		}
		if (value.npcTemplate.TryGetValue((mapId, npcSession), out var value2))
		{
			return value2;
		}
		return null;
	}

	public static void delnpc(int int_1, int int_2)
	{
		if (World.MapList.TryGetValue(int_1, out var value))
		{
			value.RemoveNpcFromMapClass(int_2);
		}
	}

	public void RemoveNpcFromMapClass(int npcSessionId)
	{
		using (new Lock(npcTemplate, "MapClass-del"))
		{
			// Find and remove by SessionID (backward compatibility)
			var keyToRemove = npcTemplate.Keys.FirstOrDefault(k => k.NPC_SessionID == npcSessionId);
			if (keyToRemove != default)
			{
				npcTemplate.Remove(keyToRemove);
			}
		}
	}

	/// <summary>
	/// Remove NPC using tuple key (new method)
	/// </summary>
	public void RemoveNpcFromMapClass(int mapId, int npcSessionId)
	{
		using (new Lock(npcTemplate, "MapClass-del"))
		{
			npcTemplate.Remove((mapId, npcSessionId));
		}
	}

	public void AddNpcToMapClass(NpcClass npcClass_0)
	{
		npcClass_0.NPC_SessionID = World.GetNextMonsterSessionId(MapID);
		// Register with NpcManager instead of creating individual timers
		var moveInterval = RNG.Next(1000, 5000);
		RegisterNPC(npcClass_0, moveInterval);
		var tupleKey = (MapID, npcClass_0.NPC_SessionID);
		npcTemplate.TryAdd(tupleKey, npcClass_0);
	}
}
