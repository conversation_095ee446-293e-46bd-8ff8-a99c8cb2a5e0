using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using RxjhServer.AOI;
using RxjhServer.NpcManager;
using HeroYulgang.Utils;
using HeroYulgang.Constants;
using RxjhServer.GroupQuest;

namespace RxjhServer;

public partial class NpcClass : IDisposable
{

	public int ID { get; set; }
	public bool IsWorldBoss { get; set; }
	private bool isDoingMech = false;

	// Optimized player target list
	private readonly OptimizedPlayerTargetList _optimizedTargetList = new();

	// Legacy compatibility - will delegate to optimized list
	public List<DamageContributeClass> PlayerTargetList => _optimizedTargetList.GetAllTargets();

	public int MarkedType = 0;
	public double DisPoseTime { get; internal set; }
	public bool NPC_Removed = false;
	public DateTime timeNpcRevival = DateTime.MinValue;
	public DateTime timeNpcDie = DateTime.Now;

	/// <summary>
	/// Compatibility property for AutomaticMove.Enabled
	/// </summary>
	private bool _automaticMoveEnabled = true;
	public bool AutomaticMoveEnabled
	{
		get => _automaticMoveEnabled;
		set => _automaticMoveEnabled = value;
	}

	/// <summary>
	/// Compatibility property for AutomaticAttack.Enabled
	/// </summary>
	private bool _automaticAttackEnabled = false;
	public bool AutomaticAttackEnabled
	{
		get => _automaticAttackEnabled;
		set => _automaticAttackEnabled = value;
	}

	/// <summary>
	/// Compatibility property for AutoRespawn.Enabled
	/// When set to true, schedules NPC for respawn using NpcManager
	/// </summary>
	private bool _autoRespawnEnabled = true;
	public bool AutoRespawnEnabled
	{
		get => _autoRespawnEnabled;
		set
		{
			_autoRespawnEnabled = value;
			if (value && NPCDeath)
			{
				// Schedule respawn using NpcManager
				var respawnTime = FLD_NEWTIME > 0 ? FLD_NEWTIME * 1000 : 10000;
				NpcManager.NpcManager.EnableRespawn(NPC_SessionID, respawnTime);
			}
		}
	}

	private static Random Ran;

	public int dame_gayboi_thannu_khidanhbom = 0;

	public Dictionary<int, X_Di_Thuong_Trang_Thai_Loai> TrangThai_BatThuong;

	public Dictionary<int, X_Di_Thuong_Mat_Mau_Trang_Thai_Loai> TrangThai_MatMau_BatThuong;

	private X_Linh_Thu_Loai _PlayCw;

	private float _FLD_FACE1;

	private float _FLD_FACE2;

	private int _IsNpc;

	private string _Name;

	private int _FLD_INDEX;

	private int _FLD_PID;

	private double _FLD_AT;

	private float _Rxjh_X;

	private float _Rxjh_Y;

	private float _Rxjh_Z;

	private float _Rxjh_cs_X;

	private float _Rxjh_cs_Y;

	private float _Rxjh_cs_Z;

	private int _Rxjh_Map;

	private int _Rxjh_Exp;

	private int _Max_Rxjh_HP;

	private int _Rxjh_HP;

	private int _Level;

	private double _FLD_DF;

	private string _WorldId;

	private int _FLD_AUTO;

	private int _FLD_BOSS;

	private int _FLD_NEWTIME;

	private bool _NPCDeath;

	private bool _QuaiXuatHien_DuyNhatMotLan;

	public int FLD_HieuUngGiamDef_Ninja = 0;

	private bool DiTinh_MissDame;

	public int 怪物数字;

	public Dictionary<int, Players> templayer = new();

	public DateTime timeNpc_HoiSinh = DateTime.MinValue;

	public int dch_towerflag;

	private bool isCastingMagic;
	private int attackCount;

	public int DCH_Tower
	{
		get
		{
			return dch_towerflag;
		}
		set
		{
			dch_towerflag = value;
		}
	}

	public X_Linh_Thu_Loai PlayCw
	{
		get
		{
			return _PlayCw;
		}
		set
		{
			_PlayCw = value;
		}
	}


	public List<DamageContributeClass> DamageContributeList
	{
		get
		{
			return _optimizedTargetList.GetAllTargets();
		}
		set
		{
			// For compatibility, clear and add all items
			_optimizedTargetList.Clear();
			if (value != null)
			{
				foreach (var item in value)
				{
					_optimizedTargetList.AddDamage(item.PlayID, item.DamageDealt, item.TeamID);
				}
			}
		}
	}

	public int PlayerWid
	{
		get
		{
			try
			{
				var topPlayer = _optimizedTargetList.GetTopPlayer();
				return topPlayer?.PlayID ?? 0;
			}
			catch (Exception)
			{
				return 0;
			}
		}
	}
	public System.Timers.Timer AutomaticDisPose;

	public int BossPlayerWid
	{
		get
		{
			try
			{
				var allTargets = _optimizedTargetList.GetAllTargets();
				if (allTargets.Count <= 0)
				{
					return 0;
				}

				// Find players with high damage (>= 100000)
				var highDamagePlayers = allTargets.Where(p => p.DamageDealt >= 100000).ToList();

				if (highDamagePlayers.Count > 0)
				{
					// Randomly select from high damage players
					var randomIndex = RNG.Next(0, highDamagePlayers.Count);
					return highDamagePlayers[randomIndex].PlayID;
				}

				// Fallback to top player
				var topPlayer = _optimizedTargetList.GetTopPlayer();
				return topPlayer?.PlayID ?? 0;
			}
			catch (Exception)
			{
				return 0;
			}
		}
	}

	public float FLD_FACE1
	{
		get
		{
			return _FLD_FACE1;
		}
		set
		{
			_FLD_FACE1 = value;
		}
	}

	public float FLD_FACE2
	{
		get
		{
			return _FLD_FACE2;
		}
		set
		{
			_FLD_FACE2 = value;
		}
	}

	public int IsNpc
	{
		get
		{
			return _IsNpc;
		}
		set
		{
			_IsNpc = value;
		}
	}

	public string Name
	{
		get
		{
			return _Name;
		}
		set
		{
			_Name = value;
		}
	}

	public int NPC_SessionID
	{
		get
		{
			return _FLD_INDEX;
		}
		set
		{
			_FLD_INDEX = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_AT
	{
		get
		{
			return _FLD_AT;
		}
		set
		{
			_FLD_AT = value;
		}
	}

	public float Rxjh_X
	{
		get
		{
			return _Rxjh_X;
		}
		set
		{
			_Rxjh_X = value;
		}
	}

	public float Rxjh_Y
	{
		get
		{
			return _Rxjh_Y;
		}
		set
		{
			_Rxjh_Y = value;
		}
	}

	public float Rxjh_Z
	{
		get
		{
			return _Rxjh_Z;
		}
		set
		{
			_Rxjh_Z = value;
		}
	}

	public float Rxjh_cs_X
	{
		get
		{
			return _Rxjh_cs_X;
		}
		set
		{
			_Rxjh_cs_X = value;
		}
	}

	public float Rxjh_cs_Y
	{
		get
		{
			return _Rxjh_cs_Y;
		}
		set
		{
			_Rxjh_cs_Y = value;
		}
	}

	public float Rxjh_cs_Z
	{
		get
		{
			return _Rxjh_cs_Z;
		}
		set
		{
			_Rxjh_cs_Z = value;
		}
	}

	public int Rxjh_Map
	{
		get
		{
			return _Rxjh_Map;
		}
		set
		{
			_Rxjh_Map = value;
		}
	}

	public int Rxjh_Exp
	{
		get
		{
			return _Rxjh_Exp;
		}
		set
		{
			_Rxjh_Exp = value;
		}
	}

	public int Max_Rxjh_HP
	{
		get
		{
			return _Max_Rxjh_HP;
		}
		set
		{
			_Max_Rxjh_HP = value;
		}
	}

	public int Rxjh_HP
	{
		get
		{
			return _Rxjh_HP;
		}
		set
		{
			_Rxjh_HP = value;
		}
	}

	public int Level
	{
		get
		{
			return _Level;
		}
		set
		{
			_Level = value;
		}
	}

	public double FLD_DF
	{
		get
		{
			return _FLD_DF;
		}
		set
		{
			_FLD_DF = value;
		}
	}

	public int FLD_AUTO
	{
		get
		{
			return _FLD_AUTO;
		}
		set
		{
			_FLD_AUTO = value;
		}
	}

	public int FLD_FreeDrop { get; set; }

	public double Rxjh_Accuracy { get; set; }

	public double Rxjh_Evasion { get; set; }

	public int FLD_BOSS
	{
		get
		{
			return _FLD_BOSS;
		}
		set
		{
			_FLD_BOSS = value;
		}
	}

	public int FLD_NEWTIME
	{
		get
		{
			return _FLD_NEWTIME;
		}
		set
		{
			_FLD_NEWTIME = value;
		}
	}

	public bool NPCDeath
	{
		get
		{
			return _NPCDeath;
		}
		set
		{
			_NPCDeath = value;
		}
	}

	public bool QuaiXuatHien_DuyNhatMotLan
	{
		get
		{
			return _QuaiXuatHien_DuyNhatMotLan;
		}
		set
		{
			_QuaiXuatHien_DuyNhatMotLan = value;
		}
	}

	public void SetMaxHP(int value) { _Max_Rxjh_HP = value; }

	public void SetHp(int value)
	{
		if (value >= _Max_Rxjh_HP)
			value = _Max_Rxjh_HP;
		_Rxjh_HP = value;
	}
	public NpcClass()
	{
		ID++;
		Ran = new(DateTime.Now.Millisecond);
		TrangThai_BatThuong = [];
		TrangThai_MatMau_BatThuong = [];
		_WorldId = World.ServerID.ToString(); // Khởi tạo WorldId với ServerID hiện tại
	}

	public void Cw_Add(X_Linh_Thu_Loai SpiritBeast)
	{
		foreach (var playGj in PlayerTargetList)
		{
			if (playGj != null && playGj.PlayID == SpiritBeast.FullServiceID)
			{
				playGj.Gjsl++;
				return;
			}
		}
		PlayerTargetList.Add(new()
		{
			Gjsl = 1,
			PlayID = SpiritBeast.FullServiceID
		});
		PlayCw = SpiritBeast;
	}



	public bool Contains(Players payer)
	{
		return IsPlayerInRange(payer, 300); // Use AOI Grid system
	}

	public DamageContributeClass FindMaxDame(List<DamageContributeClass> list)
	{
		try
		{
			// Use optimized target list instead of parameter for better performance
			var topPlayer = _optimizedTargetList.GetTopPlayer();
			if (topPlayer != null && topPlayer.PlayID > 0)
			{
				return topPlayer;
			}

			// Fallback to default if no players
			return new DamageContributeClass
			{
				PlayID = 0,
				DamageDealt = 0
			};
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"FindMaxDame error: {ex.Message}");
			return new DamageContributeClass
			{
				PlayID = 0,
				DamageDealt = 0
			};
		}
	}


	public TeamDamageContributeClass FindMaxDameTeam(List<DamageContributeClass> list)
	{
		try
		{
			// Use optimized target list instead of parameter for better performance
			var topTeam = _optimizedTargetList.GetTopTeam();
			if (topTeam != null && topTeam.TeamID > 0)
			{
				return topTeam;
			}

			// Fallback to default if no teams
			return new TeamDamageContributeClass
			{
				Gjxl_team = 0,
				TeamID = 0
			};
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"FindMaxDameTeam error: {ex.Message}");
			return new TeamDamageContributeClass
			{
				Gjxl_team = 0,
				TeamID = 0
			};
		}
	}

	public void ClearTargetList()
	{
		// Clear optimized target list
		_optimizedTargetList.Clear();
	}

	public void ScanNearbyPlayer()
	{
		try
		{
			// Use optimized scanning method
			OptimizedScanNearbyPlayers();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ScanNearbyPlayer Error： " + ex);
		}
	}


	public void InitiateDisposeTime()
	{
		LogHelper.WriteLine(LogLevel.Error, "Initial Dispose Time " + DisPoseTime);
		if (DisPoseTime > 0)
		{
			AutomaticDisPose = new System.Timers.Timer(DisPoseTime)
			{
				Enabled = true
			};
			AutomaticDisPose.Elapsed += AutomaticDisposeEvent;
			AutomaticDisPose.AutoReset = false;
		}
	}

	private void AutomaticDisposeEvent(object sender, ElapsedEventArgs e)
	{
		LogHelper.WriteLine(LogLevel.Info, "Tự động xóa NPC");
		//GuiDuLieu_TuVong_MotLanCuaQuaiVat();
		GuiDiTuVongSoLieuWrapper();
	}

	public void Dispose()
	{
		int num = 0;
		try
		{
			BossStage = 0;
			MapClass.delnpc(Rxjh_Map, NPC_SessionID);

			// Unregister from NpcManager instead of disposing individual timers
			NpcManager.NpcManager.UnregisterNPC(NPC_SessionID);
			if (AutomaticDisPose != null)
			{
				AutomaticDisPose.Close();
				AutomaticDisPose.Dispose();
				AutomaticDisPose = null;
			}
			num = 1;
			ClearTargetList();
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			num = 2;
			GetRange_Players_GuiDiBienMatSoLieu_Package();
			templayer?.Clear();
			num = 3;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NPC Khép kín So Lieu Dispose()  {num}  error：" + ex);
		}
	}

	public void GetRange_Players_GuiDiBienMatSoLieu_Package()
	{
		try
		{
			ForEachNearbyPlayer(player =>
		   {
			   if (player.Client != null)
				   player.GetReviewScopeNpc();
		   });
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC Lấy phạm vi người chơi gửi đi [biến mất] số liệu [gói 3] lỗi： " + ex);
		}
	}

	~NpcClass()
	{
	}

	public bool ContainsKeyInAbnormalState(int Key)
	{
		X_Di_Thuong_Trang_Thai_Loai value;
		if (TrangThai_BatThuong != null && TrangThai_BatThuong.Count != 0)
		{
			return TrangThai_BatThuong.TryGetValue(Key, out value);
		}
		return false;
	}

	public int ThuDuocKinhNghiem()
	{
		try
		{
			var num = Rxjh_Exp * World.KinhNghiem_BoiSo;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				_ = Rxjh_Exp;
				num = 0;
			}
			else
			{
				num = Rxjh_Exp * World.KinhNghiem_BoiSo;
			}
			if (num < 0)
			{
				num = 0;
				var minValue = num / World.Random_Rate_KinhNghiem;
				return new Random(DateTime.Now.Millisecond).Next(minValue, num);
			}
			var num2 = num / World.Random_Rate_KinhNghiem;
			var num3 = 0;
			if (num + num2 > 2147483000)
			{
				num3 = 2147483000;
			}
			else
			{
				num3 = num + num2;
				if (num3 < 0)
				{
					num3 = 0;
				}
			}
			var num4 = new Random(DateTime.Now.Millisecond).Next(num - num2, num3);
			if (num4 < 0)
			{
				num = 0;
				var minValue2 = num / World.Random_Rate_KinhNghiem;
				return new Random(DateTime.Now.Millisecond).Next(minValue2, num);
			}
			return num4;
		}
		catch
		{
			return 0;
		}
	}

	public int ThuDuocTien(Players play)
	{
		try
		{
			double num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				_ = Rxjh_Exp;
				num = 0.0;
			}
			else if (Level < 155)
			{
				if (play.Player_Level < 100)
				{
					num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold;
				}
				else
				{
					var num2 = play.Player_Level - Level;
					if (play.Player_Level > Level)
					{
						num /= 0.5 + num2 + 2.0;
					}
					else if (play.Player_Level == Level)
					{
						num /= 2.0;
					}
					else
					{
						var num3 = Level - play.Player_Level;
						if (num3 == 1)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_1_Level_Quai;
						}
						else if (num3 == 2)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_2_Level_Quai;
						}
						else if (num3 == 3)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_3_Level_Quai;
						}
						else if (num3 == 4)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_4_Level_Quai;
						}
						else if (num3 == 5)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai;
						}
						else if (num3 == 6)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_6_Level_Quai;
						}
						else if (num3 == 7)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_7_Level_Quai;
						}
						else if (num3 == 8)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_8_Level_Quai;
						}
						else if (num3 == 9)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_9_Level_Quai;
						}
						else if (num3 >= 10 && num3 <= 14)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_10_Level_Quai;
						}
						else if (num3 >= 15 && num3 <= 19)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_15_Level_Quai;
						}
						else if (num3 == 20)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_20_Level_Quai;
						}
					}
					if (play.Player_Level >= 100 && play.Player_Level < 115)
					{
						num /= World.Giam_Gold_Drop_Level_100_114;
					}
					else if (play.Player_Level >= 115 && play.Player_Level < 120)
					{
						num /= World.Giam_Gold_Drop_Level_115_119;
					}
					else if (play.Player_Level >= 120 && play.Player_Level < 125)
					{
						num /= World.Giam_Gold_Drop_Level_120_124;
					}
					else if (play.Player_Level >= 125 && play.Player_Level < 129)
					{
						num /= World.Giam_Gold_Drop_Level_125_128;
					}
					else if (play.Player_Level >= 129 && play.Player_Level < 131)
					{
						num /= World.Giam_Gold_Drop_Level_129_130;
					}
				}
			}
			else if (Level >= 155 && Level < 170)
			{
				var num4 = play.Player_Level - Level;
				if (play.Player_Level > Level)
				{
					num /= 0.5 + num4 + 3.0;
				}
				else if (play.Player_Level == Level)
				{
					num /= 2.0;
				}
				else
				{
					switch (Level - play.Player_Level)
					{
						case 1:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_1_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1;
							break;
						case 2:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_2_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2;
							break;
						case 3:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_3_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3;
							break;
						case 4:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_4_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4;
							break;
						case 5:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5;
							break;
						case 6:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6;
							break;
						case 7:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7;
							break;
						case 8:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8;
							break;
						case 9:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9;
							break;
						case 10:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10;
							break;
						case 11:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11;
							break;
						case 12:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12;
							break;
						case 13:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13;
							break;
						case 14:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14;
							break;
						case 15:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15;
							break;
						case 16:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16;
							break;
						case 17:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17;
							break;
						case 18:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18;
							break;
						case 19:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19;
							break;
						case 20:
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20;
							break;
						default:
							num = 0.0;
							return 0;
					}
				}
			}
			else if (Level >= 170)
			{
				num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold;
			}
			if (num < 0.0)
			{
				num = 0.0;
				var minValue = (int)num / World.Random_Rate_Gold;
				return new Random(DateTime.Now.Millisecond).Next(minValue, (int)num);
			}
			var num5 = (int)num / World.Random_Rate_Gold;
			var num6 = 0;
			if (num + num5 > 2147483000.0)
			{
				num6 = 2147483000;
			}
			else
			{
				num6 = (int)num + num5;
				if (num6 < 0)
				{
					num6 = 0;
				}
			}
			var num7 = new Random(DateTime.Now.Millisecond).Next((int)num - num5, num6);
			if (num7 < 0)
			{
				num = 0.0;
				var minValue2 = (int)num / World.Random_Rate_Gold;
				return new Random(DateTime.Now.Millisecond).Next(minValue2, (int)num);
			}
			return num7;
		}
		catch
		{
			return 0;
		}
	}

	public int CalculateSkillExp(Players play)
	{
		try
		{
			var num = Rxjh_Exp * World.LichLuyenBoiSo / Level / World.Phan_Tram_Chia_Ky_Nang;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				_ = Rxjh_Exp;
				num = 0;
			}
			else
			{
				num = Rxjh_Exp * World.LichLuyenBoiSo / Level / World.Phan_Tram_Chia_Ky_Nang;
			}
			if (num < 0)
			{
				num = 0;
				var minValue = num / World.Phan_Tram_Chia_Ky_Nang;
				return new Random(DateTime.Now.Millisecond).Next(minValue, num);
			}
			var num2 = num / World.Phan_Tram_Chia_Ky_Nang;
			var num3 = 0;
			if (num + num2 > 2147483000)
			{
				num3 = 2147483000;
			}
			else
			{
				num3 = num + num2;
				if (num3 < 0)
				{
					num3 = 0;
				}
			}
			var num4 = (int)(new Random().Next(num - num2, num3) * (1.0 - World.PhanTram_GiamXuong_TienNhanDuoc));
			if (num4 < 0)
			{
				num = 0;
				var minValue2 = num / World.Phan_Tram_Chia_Ky_Nang;
				return new Random(DateTime.Now.Millisecond).Next(minValue2, num);
			}
			return num4;
		}
		catch
		{
			return 0;
		}
	}

	public int ThuDuocThangThienLichLuyen(Players playe)
	{
		try
		{
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				return 0;
			}
			var num = (int)(Rxjh_Exp * World.ThangThien_LichLuyen_BoiSo / 1000.0 / World.Phan_Tram_Chia_Ky_Nang_TT);
			if (playe.Player_Level < 150)
			{
				num *= World.Random_Rate_KyNangThangThien;
			}
			num = Math.Max(0, num);
			var num2 = num / World.Random_Rate_KyNangThangThien;
			var maxValue = Math.Min(2147483000, num + num2);
			Random random = new(DateTime.Now.Millisecond);
			var val = random.Next(num - num2, maxValue);
			return Math.Max(0, val);
		}
		catch
		{
			return 0;
		}
	}

	private bool ThoiGian_KetThuc_Rate_Exp_Drop_Gold()
	{
		var now = DateTime.Now;
		return now.Hour == World.TheLucChien_MoRa_Gio && FLD_BOSS == 0 && now.Minute < World.Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang;
	}
	private void RespawnEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (IsNpc == 1)
			{
				AutoRespawnEnabled = false;
				return;
			}
			AutomaticMoveEnabled = true;
			if (!NPCDeath)
			{
				//LogHelper.WriteLine(LogLevel.Info, $"[RESPAWN DEBUG] NPC {Name} is not dead (NPCDeath=false), skipping respawn");
				return;
			}
			//LogHelper.WriteLine(LogLevel.Info, $"[RESPAWN DEBUG] NPC {Name} is dead, calling RefreshSpawnData");
			RefreshSpawnData();
			// if (FLD_BOSS == 1)
			// {
			// 	foreach (var value in World.allConnectedChars.Values)
			// 	{
			// 		if (!value.Client.TreoMay && value.IsJoinWorld)
			// 		{
			//             var text = "Ma tôn [" + Name + "] tái xuất giang hồ, hồi sinh đầy uy lực tại [Kênh " + World.ServerID + "], ẩn mình nơi hiểm địa [" + X_Toa_Do_Class.getmapname(Rxjh_Map) + "] ở tọa độ [" + Rxjh_X + "," + Rxjh_Y + "]. Anh hùng thiên hạ, ai dám đến diệt trừ yêu ma?";
			// 			World.conn.Transmit("PK_MESSAGE|" + 22 + "|" + text);
			// 		}
			// 	}
			// 	return;
			// }
			// if (FLD_PID != 16278 || Rxjh_Map != 40101 || World.DCH_Progress == 0)
			// {
			// 	return;
			// }
			// foreach (var value2 in World.allConnectedChars.Values)
			// {
			// 	if (value2.MapID == 40101)
			// 	{
			// 		value2.HeThongNhacNho("Đại ma đầu Diêm La Quân đã tái sinh, quần hùng chuẩn bị nghênh chiến!!!", 7, "Thiên cơ các");
			// 		value2.HeThongNhacNho("Đại ma đầu Diêm La Quân đã tái sinh, giang hồ rung chuyển!!!", 6, "Thiên cơ các");
			// 		value2.HeThongNhacNho("Đại ma đầu Diêm La Quân đã tái sinh, anh hùng tứ phương hội tụ!!!", 10, "Thiên cơ các");
			// 	}
			// }
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"RespawnEvent error for NPC {FLD_PID}: {ex.Message}");
		}
	}

	public void NPC_Attack(Players targetPlayer)
	{
		try
		{
			if (FLD_PID == 16270 || FLD_PID == 16271 || FLD_PID == 15293 || FLD_PID == 15732)
			{
				AutomaticAttackEnabled = false;
				if (FLD_PID == 15732)
				{
					AutomaticMoveEnabled = false;
				}
			}
			else if (IsNpc != 0)
			{
				AutomaticAttackEnabled = false;
			}
			else if (Rxjh_HP < 0)
			{
				AutomaticAttackEnabled = false;
			}
			else
			{
				if (FLD_AT <= 0.0)
				{
					return;
				}

				attackCount++;
				if (FLD_BOSS == 1)
				{
					HandleBossMagicAttack(targetPlayer, 8);
				}
				else
				{
					HandleNpcAttackPlayer(targetPlayer);
				}

			}
		}
		catch (Exception)
		{
			PlayCw = null;
			DiTinh_MissDame = false;
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
		}
	}

	private void HandleNpcAttackPlayer(Players targetPlayer)
	{
		var num = (int)(FLD_AT * 0.8);
		Random random = new(DateTime.Now.Millisecond);
		//AutomaticAttack.Interval = 1500.0;
		var attackDamage = random.Next(num - 8, num + 8);
		var num3 = (targetPlayer.MapID != 40101) ? 250 : 200;

		if (targetPlayer.NhanVat_HP > 0 && !targetPlayer.PlayerTuVong)
		{
			var targetDef = targetPlayer.FLD_NhanVatCoBan_PhongNgu + targetPlayer.FLD_NhanVat_ThemVao_PhongThu_QuaiVat + targetPlayer.ThangThien_5_MaHonChiLuc;
			if (!targetPlayer.KiemTraDocXaXuatDongTrangThai() && targetPlayer.Player_Job == 12)
			{
				targetPlayer.TriggerAttributePromotion = 0;
				if (targetPlayer.KhongGiPhaNoi >= RNG.Next(1, 100))
				{
					targetPlayer.TriggerAttributePromotion = 1;
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 1010);
				}
			}
			if (targetPlayer.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich > 0.0)
			{
				attackDamage = (int)(attackDamage * (1.0 - targetPlayer.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich));
			}
			var attackType = 28;
			if (Level >= 60)
			{
				switch (RNG.Next(0, 10))
				{
					case 1:
						attackType = 28;
						break;
					case 2:
						attackType = 29;
						break;
					case 4:
						attackType = 29;
						break;
				}
				if (attackType == 29)
				{
					attackDamage = (int)(attackDamage * 1.05);
				}
			}
			if (targetPlayer.TrungCapPhuHon_DiTinh != 0 && RNG.Next(1, 200) <= (double)targetPlayer.TrungCapPhuHon_DiTinh)
			{
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 405);
				StatusEffect value2 = new(targetPlayer, 3000, Effects.DI_TINH, 0);
				targetPlayer.AppendStatusList.Add(Effects.DI_TINH, value2);
				targetPlayer.StatusEffect(BitConverter.GetBytes(Effects.DI_TINH), 1, 3000);
				attackDamage = 0;
			}
			if (targetPlayer.Player_Job == 3)
			{
				if (RNG.Next(1, 100) <= targetPlayer.THUONG_ChuyenCongViThu + targetPlayer.THUONG_ThangThien_2_KhiCong_DiThoiViTien)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					targetDef += targetPlayer.FLD_CongKich * 0.2;
				}
			}
			else if (targetPlayer.Player_Job == 10)
			{
				if (RNG.Next(0, 100) <= targetPlayer.QuyenSu_ChuyenCongViThu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					targetDef += targetPlayer.FLD_CongKich;
				}
				if (attackType == 29 && RNG.Next(1, 110) <= targetPlayer.QuyenSu_KimCuongBatHoai)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 554);
					attackDamage = (int)(attackDamage * 0.1);
				}
				if (RNG.Next(1, 100) <= targetPlayer.ThangThien_5_BatTu_ChiKhu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 1021);
					attackDamage = 0;
				}
			}
			else if (targetPlayer.Player_Job == 12 && RNG.Next(0, 100) <= targetPlayer.TuHao_ChuyenCongViThu)
			{
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
				targetDef += targetPlayer.FLD_CongKich / 2.0;
			}
			if (targetPlayer.TrungCapPhuHon_HoThe != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_HoThe)
			{
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 406);
				targetPlayer.NhanVat_HP += attackDamage;
				targetPlayer.CapNhat_HP_MP_SP();
				attackDamage = 0;
			}
			var finalDamage = ((!(attackDamage > targetDef)) ? 1 : (attackDamage - (int)targetDef));
			if (FindPlayers(20, targetPlayer))
			{
				if (targetPlayer.TrungCapPhuHon_HonNguyen != 0 && RNG.Next(1, 150) <= targetPlayer.TrungCapPhuHon_HonNguyen + targetPlayer.TrungCapPhuHon_HonNguyen_Giap)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 407);
					finalDamage = (int)(finalDamage * 0.5);
				}
				if (targetPlayer.FLD_TrangBi_GiamXuong_MucThuongTon > 0.0)
				{
					finalDamage -= (int)targetPlayer.FLD_TrangBi_GiamXuong_MucThuongTon;
				}
				var num14 = 0;
				if (targetPlayer.Player_Job == 1)
				{
					var num15 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num15)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 2)
				{
					double num16 = finalDamage;
					if (RNG.Next(1, 105) <= targetPlayer.KIEM_ThangThien_1_KhiCong_HoThan_CuongKhi)
					{
						targetPlayer.ShowBigPrint(targetPlayer.SessionID, 25);
						finalDamage = (int)(num16 * 0.5);
					}
					var num17 = targetPlayer.KIEM_HoiLieu_ThanPhap + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num17)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 3)
				{
					var num18 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num18)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 4)
				{
					var num19 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num19)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 5)
				{
					var num20 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num20)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 6)
				{
					if (RNG.Next(1, 110) <= targetPlayer.NINJA_ThangThien_1_KhiCong_DaMaTrienThan)
					{
						finalDamage = (int)(finalDamage * 0.7);
						targetPlayer.ShowBigPrint(targetPlayer.SessionID, 370);
					}
					if (RNG.Next(1, 100) <= targetPlayer.NINJA_ThangThien_2_KhiCong_ThuanThuyThoiChu)
					{
						targetPlayer.AddBlood((int)(finalDamage * 0.2));
						targetPlayer.ShowBigPrint(targetPlayer.SessionID, 371);
					}
					var num21 = targetPlayer.NINJA_TamHoaTuDinh + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.02;
					if (RNG.Next(1, 110) <= num21)
					{
						num14 = 1;
						targetPlayer.NINJA_LienTieuDaiDa_SoLuong = finalDamage * targetPlayer.NINJA_LienTieuDaiDa;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 7)
				{
					var num22 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num22)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 8)
				{
					var num23 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num23)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 9)
				{
					double num24 = finalDamage;
					if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_HoThan_CuongKhi)
					{
						targetPlayer.ShowBigPrint(targetPlayer.SessionID, 25);
						finalDamage = (int)(num24 * 0.5);
					}
					var num25 = targetPlayer.DamHoaLien_HoiLieu_ThanPhap + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 120) <= num25)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 10)
				{
					var num26 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num26)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 11)
				{
					var num27 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num27)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 12)
				{
					var num28 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num28)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				else if (targetPlayer.Player_Job == 13)
				{
					var num29 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
					if (RNG.Next(1, 100) <= num29)
					{
						num14 = 1;
						finalDamage = 0;
					}
				}
				if (targetPlayer.NhanVat_BatTu == 1)
				{
					num14 = 1;
					finalDamage = 0;
				}
				// if (finalDamage <= 4 && num14 == 0)
				// {
				//     finalDamage = RNG.Next(5, 10);
				// }
				if (finalDamage < 0)
					finalDamage = 0;
				var shield = 0;
				if (targetPlayer.Player_Job == 11 && targetPlayer.MaiLieuChan_ChuongLucKichHoat > 0.0)
				{
					shield = (int)(finalDamage * (targetPlayer.MaiLieuChan_ChuongLucKichHoat * 2.0 * 0.01));
					if (shield > targetPlayer.NhanVat_AP)
					{
						shield = targetPlayer.NhanVat_AP;
					}
					targetPlayer.NhanVat_AP -= shield;
				}
				var damageDeal = finalDamage - shield;
				if (targetPlayer.TrungCapPhuHon_DiTinh != 0 && targetPlayer.AppendStatusList.ContainsKey(Effects.DI_TINH))
				{
					damageDeal = 0;
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 405);
				}
				GuiDi_CongKichSoLieu(damageDeal, attackType, targetPlayer.SessionID, shield);
				HandlePlayerAfterDamage(targetPlayer, targetDef, finalDamage, damageDeal);
			}
			else if (FindPlayers(100, targetPlayer))
			{
				SendMovingData(targetPlayer.PosX, targetPlayer.PosY, 10, 2);
			}
			else
			{
				PlayCw = null;
				AutomaticAttackEnabled = false;
				AutomaticMoveEnabled = true;
				Rxjh_X = Rxjh_cs_X;
				Rxjh_Y = Rxjh_cs_Y;
				Rxjh_Z = Rxjh_cs_Z;
				SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
			}
		}
		else
		{
			if (targetPlayer.AutomaticRecovery != null)
			{
				targetPlayer.AutomaticRecovery.Enabled = false;
				targetPlayer.AutomaticRecovery.Close();
				targetPlayer.AutomaticRecovery.Dispose();
				targetPlayer.AutomaticRecovery = null;
			}
			if (targetPlayer.InvincibleTimeCounter != null)
			{
				targetPlayer.InvincibleTimeCounter.Enabled = false;
				targetPlayer.InvincibleTimeCounter.Close();
				targetPlayer.InvincibleTimeCounter.Dispose();
			}
			PlayCw = null;
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
		}
	}

	/// <summary>
	/// Phiên bản cải tiến của HandleNpcAttackPlayer với cấu trúc rõ ràng và dễ maintain hơn
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	private void HandleNpcAttackPlayerV2(Players targetPlayer)
	{
		// ===== BƯỚC 1: KIỂM TRA ĐIỀU KIỆN CƠ BẢN =====
		if (targetPlayer.NhanVat_HP <= 0 || targetPlayer.PlayerTuVong)
		{
			HandlePlayerDeath(targetPlayer);
			return;
		}

		// ===== BƯỚC 2: TÍNH TOÁN DAMAGE CƠ BẢN =====
		var baseDamage = CalculateBaseDamage();

		// ===== BƯỚC 3: XỬ LÝ DEFENSE VÀ SPECIAL EFFECTS =====
		var (modifiedDamage, targetDefense) = ProcessDefenseAndSpecialEffects(targetPlayer, baseDamage);

		// ===== BƯỚC 4: XỬ LÝ ATTACK TYPE VÀ BONUS =====
		var (attackDamage, attackType) = ProcessAttackTypeAndBonus(modifiedDamage);

		// ===== BƯỚC 5: XỬ LÝ SPECIAL EFFECTS (DI TINH, HỘ THỂ) =====
		attackDamage = ProcessSpecialEffects(targetPlayer, attackDamage);
		if (attackDamage == 0)
		{
			// Damage đã bị hấp thụ hoàn toàn, gửi data và return
			GuiDi_CongKichSoLieu(0, attackType, targetPlayer.SessionID, 0);
			HandleMovementLogic(targetPlayer);
			return;
		}

		// ===== BƯỚC 6: XỬ LÝ JOB-SPECIFIC DEFENSE =====
		var finalDefense = ProcessJobSpecificDefense(targetPlayer, targetDefense, attackType, attackDamage);

		// ===== BƯỚC 7: XỬ LÝ JOB-SPECIFIC DAMAGE REDUCTION =====
		attackDamage = ProcessJobSpecificDamageReduction(targetPlayer, attackType, attackDamage);
		if (attackDamage == 0)
		{
			GuiDi_CongKichSoLieu(0, attackType, targetPlayer.SessionID, 0);
			HandleMovementLogic(targetPlayer);
			return;
		}

		// ===== BƯỚC 8: TÍNH FINAL DAMAGE =====
		var finalDamage = Math.Max(1, attackDamage - (int)finalDefense);

		// ===== BƯỚC 9: XỬ LÝ PLAYER TRONG RANGE =====
		if (!FindPlayers(30, targetPlayer))
		{
			HandleOutOfRangeLogic(targetPlayer);
			return;
		}

		// ===== BƯỚC 10: XỬ LÝ HỒN NGUYÊN VÀ DAMAGE REDUCTION =====
		finalDamage = ProcessHonNguyenAndDamageReduction(targetPlayer, finalDamage);

		// ===== BƯỚC 11: XỬ LÝ DODGE/EVASION THEO JOB =====
		var (actualDamage, isDodged) = ProcessJobSpecificDodge(targetPlayer, finalDamage);

		// ===== BƯỚC 12: XỬ LÝ BẤT TỬ =====
		if (targetPlayer.NhanVat_BatTu == 1)
		{
			isDodged = true;
			actualDamage = 0;
		}

		// Đảm bảo damage không âm
		if (actualDamage < 0) actualDamage = 0;

		// ===== BƯỚC 13: XỬ LÝ SHIELD (MLC) =====
		var shield = ProcessShieldLogic(targetPlayer, actualDamage);
		var damageToDeal = actualDamage - shield;

		// ===== BƯỚC 14: XỬ LÝ DI TINH STATUS EFFECT =====
		if (targetPlayer.TrungCapPhuHon_DiTinh != 0 && targetPlayer.AppendStatusList.ContainsKey(Effects.DI_TINH))
		{
			damageToDeal = 0;
			targetPlayer.ShowBigPrint(targetPlayer.SessionID, 405);
		}

		// ===== BƯỚC 15: GỬI DAMAGE DATA VÀ XỬ LÝ HẬU QUẢ =====
		GuiDi_CongKichSoLieu(damageToDeal, attackType, targetPlayer.SessionID, shield);
		HandlePlayerAfterDamage(targetPlayer, finalDefense, actualDamage, damageToDeal);
	}

	private int HandlePlayerAfterDamage(Players targetPlayer, double targetDef, int finalDamage, int damageDeal)
	{
		//targetPlayer.NhanVat_HP -= damageDeal;
		PlayerEvents.OnPlayerHpChanged(targetPlayer, damageDeal, null, PlayerEvents.HpChangeType.Damage, this);
		if (targetPlayer.Player_Job != 1 && targetPlayer.Player_Job != 7)
		{
			if (targetPlayer.Player_Job == 2)
			{
				if (RNG.Next(1, 100) <= targetPlayer.KIEM_DiHoa_TiepMoc)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 26);
					targetPlayer.AddBlood((int)(finalDamage * 0.5));
					targetPlayer.CapNhat_HP_MP_SP();
				}
				if (RNG.Next(1, 100) <= targetPlayer.KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu && targetPlayer.NhanVat_HP <= 0)
				{
					targetPlayer.NhanVat_HP = 10;
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 322);
				}
				if (damageDeal <= targetDef)
				{
					if (!targetPlayer.NoKhi)
					{
						targetPlayer.NhanVat_SP++;
					}
				}
				else if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP += 2;
				}
			}
			else if (targetPlayer.Player_Job == 3)
			{
				if (targetPlayer.THUONG_CuongThanHangThe != 0.0)
				{
					if (!targetPlayer.NoKhi)
					{
						var num32 = (int)(targetPlayer.THUONG_CuongThanHangThe * 100.0) * 20;
						targetPlayer.NhanVat_SP += num32 / 2;
					}
				}
				else if (damageDeal <= targetDef)
				{
					if (!targetPlayer.NoKhi)
					{
						targetPlayer.NhanVat_SP++;
					}
				}
				else if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP += 2;
				}
			}
			else if (targetPlayer.Player_Job == 6)
			{
				var nINJA_KinhKhaChiNo = targetPlayer.NINJA_KinhKhaChiNo;
				if (nINJA_KinhKhaChiNo > 0.0)
				{
					targetPlayer.NhanVat_SP += (int)(3.0 + targetPlayer.Player_Level * 0.5 * 0.01 * nINJA_KinhKhaChiNo);
				}
				else if (damageDeal <= targetDef)
				{
					targetPlayer.NhanVat_SP++;
				}
				else
				{
					targetPlayer.NhanVat_SP += 2;
				}
			}
			else if (targetPlayer.Player_Job == 8)
			{
				if (damageDeal <= targetDef)
				{
					if (!targetPlayer.NoKhi)
					{
						targetPlayer.NhanVat_SP++;
					}
				}
				else if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP += 2;
				}
				try
				{
					if (RNG.Next(1, 100) <= targetPlayer.HanBaoQuan_TruyCotHapNguyen)
					{
						var num33 = damageDeal * (targetPlayer.HanBaoQuan_TruyCotHapNguyen * 0.01);
						if (num33 <= 0.0)
						{
							num33 = 1.0;
						}
						targetPlayer.AddBlood((int)num33);
						var num34 = 0;
						if ((int)num33 > Max_Rxjh_HP)
						{
							num34 = Max_Rxjh_HP;
						}
						else
						{
							num34 = (int)num33;
							if (Rxjh_HP > 0 && num34 > Rxjh_HP)
							{
								num34 = Rxjh_HP;
							}
						}
						// Play_Add(targetPlayer, num34);
						// Rxjh_HP -= num34;
						// Phan Damage
						ReceiveDamage(targetPlayer, num34, 0, 0);
						// UpdateHPWithTracking('-', num34, targetPlayer.SessionID, targetPlayer.CharacterName, targetPlayer.Player_Level, targetPlayer.GuildName, targetPlayer.GuildId);
						// if (Rxjh_HP <= 0)
						// {
						// 	double tienBac = (uint)ThuDuocTien(targetPlayer);
						// 	double num35 = ThuDuocKinhNghiem();
						// 	double lichLuyen = ThuDuocLichLuyen(targetPlayer);
						// 	if (targetPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_KyDuyen)
						// 	{
						// 		num35 *= 2.0;
						// 		targetPlayer.ShowBigPrint(targetPlayer.SessionID, 403);
						// 	}
						// 	targetPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(targetPlayer, this, num35, lichLuyen, tienBac, 0.0);
						// 	GuiDiTuVongSoLieuWrapper(NPC_SessionID);
						// }
					}
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Event tấn công tự động HanBaoQuan [TruyCotHapNguyen] Chống thương tích error：" + ex);
				}
			}
			else if (targetPlayer.Player_Job == 9)
			{
				if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_DiHoa_TiepMoc)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 26);
					targetPlayer.AddBlood(finalDamage * 2);
					targetPlayer.CapNhat_HP_MP_SP();
				}
				if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu && targetPlayer.NhanVat_HP <= 0)
				{
					targetPlayer.NhanVat_HP = 10;
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 322);
				}
				if (damageDeal <= targetDef)
				{
					if (!targetPlayer.NoKhi)
					{
						targetPlayer.NhanVat_SP++;
					}
				}
				else if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP += 2;
				}
			}
			else if (targetPlayer.Player_Job == 10)
			{
				if (targetPlayer.QuyenSu_CuongThanHangThe != 0.0)
				{
					if (!targetPlayer.NoKhi)
					{
						var num36 = (int)(targetPlayer.QuyenSu_CuongThanHangThe * 100.0) * 10;
						targetPlayer.NhanVat_SP += num36 / 2;
					}
				}
				else if (damageDeal <= targetDef)
				{
					if (!targetPlayer.NoKhi)
					{
						targetPlayer.NhanVat_SP++;
					}
				}
				else if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP += 2;
				}
			}
			else if (targetPlayer.Player_Job == 11)
			{
				if (damageDeal <= targetDef)
				{
					if (!targetPlayer.NoKhi)
					{
						targetPlayer.NhanVat_SP++;
					}
				}
				else if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP += 2;
				}
				var num37 = targetPlayer.CharacterMax_AP / 2;
				if (targetPlayer.MaiLieuChan_ChuongLucKhoiPhuc > 0.0 && targetPlayer.NhanVat_AP < num37 && RNG.Next(1, 120) <= targetPlayer.MaiLieuChan_ChuongLucKhoiPhuc)
				{
					targetPlayer.NhanVat_AP = num37;
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 801);
				}
				if (targetPlayer.MaiLieuChan_PhanNoBaoPhat > 0.0 && RNG.Next(1, 100) <= 40 && targetPlayer.NoKhi_Point < 3 && targetPlayer.NhanVat_BatTu == 0)
				{
					targetPlayer.NoKhi_Point++;
				}
			}
			else if (damageDeal <= targetDef)
			{
				if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP++;
				}
			}
			else if (!targetPlayer.NoKhi)
			{
				targetPlayer.NhanVat_SP += 2;
			}
		}
		else
		{
			if (damageDeal <= targetDef)
			{
				if (!targetPlayer.NoKhi)
				{
					targetPlayer.NhanVat_SP++;
				}
			}
			else if (!targetPlayer.NoKhi)
			{
				targetPlayer.NhanVat_SP += 2;
			}
			try
			{
				if (damageDeal <= 0)
				{
					damageDeal = 1;
				}
				if (RNG.Next(1, 120) <= ((targetPlayer.Player_Job != 1) ? (targetPlayer.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet + 10.0) : (targetPlayer.QuaiVat_PhanSatThuong_TiLe + 10.0)) && damageDeal > 0)
				{
					var num38 = 0;
					if (targetPlayer.Player_Job == 7)
					{
						targetPlayer.ShowBigPrint(targetPlayer.SessionID, 391);
						GuiDi_PhanSatThuong_CongKichSoLieu(damageDeal, targetPlayer.SessionID);
						num38 = damageDeal;
					}
					if ((targetPlayer.Player_Job == 1 || targetPlayer.Player_Job == 8) && targetPlayer.QuaiVat_PhanSatThuong_TiLe != 0.0 && RNG.Next(1, 100) <= targetPlayer.QuaiVat_PhanSatThuong_TiLe + targetPlayer.DAO_ThangThien_2_KhiCong_CungDoMatLo)
					{
						if ((targetPlayer.Player_Job == 1 || targetPlayer.Player_Job == 8) && targetPlayer.DAO_ThangThien_2_KhiCong_CungDoMatLo != 0.0 && RNG.Next(1, 100) <= targetPlayer.DAO_ThangThien_2_KhiCong_CungDoMatLo)
						{
							targetPlayer.ShowBigPrint(targetPlayer.SessionID, 19);
							GuiDi_PhanSatThuong_CongKichSoLieu(damageDeal * 2, targetPlayer.SessionID);
							num38 = damageDeal * 2;
						}
						else
						{
							targetPlayer.ShowBigPrint(targetPlayer.SessionID, 15);
							GuiDi_PhanSatThuong_CongKichSoLieu(damageDeal, targetPlayer.SessionID);
							num38 = damageDeal;
						}
					}
					var num39 = 0;
					if (num38 > Max_Rxjh_HP)
					{
						num39 = Max_Rxjh_HP;
					}
					else
					{
						num39 = num38;
						if (Rxjh_HP > 0 && num39 > Rxjh_HP)
						{
							num39 = Rxjh_HP;
						}
					}
					// Sử dụng UpdateHPWithTracking để tích hợp damage tracking
					ReceiveDamage(targetPlayer, num39, 0, 0);
					// UpdateHPWithTracking('-', num39, targetPlayer.SessionID, targetPlayer.CharacterName,
					//     targetPlayer.Player_Level, targetPlayer.GuildName, targetPlayer.GuildId, false, targetPlayer);
					// if ((!NPCDeath && Rxjh_HP <= 0) || (FLD_BOSS == 1 && (Rxjh_HP <= 0 || NPCDeath)))
					// {
					//     double tienBac2 = (uint)ThuDuocTien(targetPlayer);
					//     double num40 = ThuDuocKinhNghiem();
					//     double lichLuyen2 = ThuDuocLichLuyen(targetPlayer);
					//     if (targetPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_KyDuyen)
					//     {
					//         num40 *= 2.0;
					//         targetPlayer.ShowBigPrint(targetPlayer.SessionID, 403);
					//     }
					//     var array4 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
					//     if (World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
					//     {

					//     }
					//     else
					//     {
					//         targetPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(targetPlayer, this, num40, lichLuyen2, tienBac2, 0.0);
					//     }
					//     GuiDiTuVongSoLieuWrapper(NPC_SessionID);
					// }
				}
			}
			catch (Exception ex2)
			{
				LogHelper.WriteLine(LogLevel.Error, "Automatic Attack Event Nhạc công /Dao Khách phản tổn thương error：" + ex2);
			}
		}
		if (targetPlayer.FLD_TrangBi_ThemVao_Phan_NoKhi > 0 && !targetPlayer.NoKhi)
		{
			targetPlayer.NhanVat_SP += targetPlayer.FLD_TrangBi_ThemVao_Phan_NoKhi;
		}
		if (targetPlayer.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram > 0.0 && RNG.Next(1, 100) <= targetPlayer.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram && !ContainsKeyInAbnormalState(3))
		{
			TrangThai_BatThuong.Add(3, new(this, PlayerWid, 60000, 3, 0.0));
		}
		if (targetPlayer.NhanVat_HP <= 0)
		{
			targetPlayer.HeThongNhacNho("Đại hiệp bị yêu quái cấp [" + Level + "] hạ sát, thật đáng tiếc!", 10, "Thiên cơ các");
			if (targetPlayer.TeamID != 0 && World.WToDoi.TryGetValue(targetPlayer.TeamID, out var value4))
			{
				foreach (var value7 in value4.PartyPlayers.Values)
				{
					if (value7.SessionID != targetPlayer.SessionID)
					{
						value7.HeThongNhacNho("Bị yêu quái đánh trọng thương, phải nhập y quán dưỡng thương!!", 2, targetPlayer.CharacterName ?? "");
					}
				}
			}
			if (World.ChetCoMatKinhNghiemKhong == 1 && targetPlayer.Player_Level > 10)
			{
				var num41 = ((long)World.lever[targetPlayer.Player_Level + 1] - (long)World.lever[targetPlayer.Player_Level]) / 1000;
				if (targetPlayer.PublicDrugs != null)
				{
					if (targetPlayer.KiemTra_Phu() && targetPlayer.KiemTra_Phu2())
					{
					}
				}
				else
				{
					var num42 = RNG.Next(1, 100);
					num41 = ((num42 >= 1 && num42 <= 10) ? num41 : ((num42 >= 11 && num42 <= 20) ? (num41 * 2) : ((num42 >= 21 && num42 <= 30) ? (num41 * 3) : ((num42 >= 31 && num42 <= 40) ? (num41 * 4) : ((num42 >= 41 && num42 <= 50) ? (num41 * 5) : ((num42 >= 51 && num42 <= 60) ? (num41 * 6) : ((num42 >= 61 && num42 <= 70) ? (num41 * 7) : ((num42 >= 71 && num42 <= 80) ? (num41 * 8) : ((num42 < 81 || num42 > 90) ? (num41 * 10) : (num41 * 9))))))))));
				}
				if (targetPlayer.FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot > 0.0)
				{
					num41 = (long)(num41 * (1.0 - targetPlayer.FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot));
					if (num41 < 0)
					{
						num41 = 0L;
					}
				}
				for (var l = 0; l < 15; l++)
				{
					if (BitConverter.ToInt32(targetPlayer.Item_Wear[l].VatPham_ID, 0) == 700004)
					{
						num41 = 0L;
						targetPlayer.Item_Wear[l].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						targetPlayer.LoadCharacterWearItem();
						break;
					}
				}
				for (var m = 0; m < targetPlayer.Item_In_Bag.Length; m++)
				{
					var num43 = 0;
					if (BitConverter.ToInt32(targetPlayer.Item_In_Bag[m].VatPham_ID, 0) == 1008000142)
					{
						targetPlayer.VatPham_GiamDi_SoLuong_DoBen(m, num43);
						if (num43 > 0)
						{
							num43 = 0;
						}
						num41 = 0L;
						targetPlayer.HeThongNhacNho("Đại hiệp vừa thi triển Sinh Tử Phù, thoát khỏi lằn ranh sinh tử!");
						targetPlayer.Item_In_Bag[m].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						targetPlayer.Init_Item_In_Bag();
						break;
					}
				}
				if (targetPlayer.GetAddState(1008000160) || targetPlayer.GetAddState(1008000159))
				{
					num41 = 0L;
				}
				targetPlayer.CharacterExperience -= num41;
				targetPlayer.TinhToan_NhanVatCoBan_DuLieu3();
				targetPlayer.UpdateKinhNghiemVaTraiNghiem();
			}
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
			targetPlayer.NhanVat_HP = 0;
			targetPlayer.Death();
			PlayCw = null;
		}
		targetPlayer.CapNhat_HP_MP_SP();
		return damageDeal;
	}

	#region HandleNpcAttackPlayerV2 Helper Methods

	/// <summary>
	/// Tính toán damage cơ bản của NPC
	/// </summary>
	/// <returns>Damage cơ bản với random variation</returns>
	private int CalculateBaseDamage()
	{
		var baseDamage = (int)(FLD_AT * 0.8);
		Random random = new(DateTime.Now.Millisecond);
		return random.Next(baseDamage - 8, baseDamage + 8);
	}

	/// <summary>
	/// Xử lý defense và các special effects của player
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="attackDamage">Damage gốc</param>
	/// <returns>Tuple chứa (modifiedDamage, targetDefense)</returns>
	private (int modifiedDamage, double targetDefense) ProcessDefenseAndSpecialEffects(Players targetPlayer, int attackDamage)
	{
		// Tính defense cơ bản
		var targetDef = targetPlayer.FLD_NhanVatCoBan_PhongNgu +
					   targetPlayer.FLD_NhanVat_ThemVao_PhongThu_QuaiVat +
					   targetPlayer.ThangThien_5_MaHonChiLuc;

		var modifiedDamage = attackDamage;

		// Xử lý special effect cho job 12 (Tự Hào)
		if (!targetPlayer.KiemTraDocXaXuatDongTrangThai() && targetPlayer.Player_Job == 12)
		{
			targetPlayer.TriggerAttributePromotion = 0;
			if (targetPlayer.KhongGiPhaNoi >= RNG.Next(1, 100))
			{
				targetPlayer.TriggerAttributePromotion = 1;
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 1010);
			}
		}

		// Giảm damage theo trang bị
		if (targetPlayer.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich > 0.0)
		{
			modifiedDamage = (int)(modifiedDamage * (1.0 - targetPlayer.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich));
		}

		return (modifiedDamage, targetDef);
	}

	/// <summary>
	/// Xử lý attack type và damage bonus cho NPC level cao
	/// </summary>
	/// <param name="attackDamage">Damage hiện tại</param>
	/// <returns>Tuple chứa (finalDamage, attackType)</returns>
	private (int finalDamage, int attackType) ProcessAttackTypeAndBonus(int attackDamage)
	{
		var attackType = 28; // Default attack type
		var finalDamage = attackDamage;

		// NPC level >= 60 có thể dùng attack type đặc biệt
		if (Level >= 60)
		{
			switch (RNG.Next(0, 10))
			{
				case 1:
				case 2:
				case 4:
					attackType = (RNG.Next(0, 10) == 1) ? 28 : 29;
					break;
			}

			// Attack type 29 có damage bonus
			if (attackType == 29)
			{
				finalDamage = (int)(finalDamage * 1.05);
			}
		}

		return (finalDamage, attackType);
	}

	/// <summary>
	/// Xử lý các special effects đặc biệt (Di Tinh, Hộ Thể, etc.)
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="attackDamage">Damage hiện tại</param>
	/// <returns>Damage sau khi xử lý special effects</returns>
	private int ProcessSpecialEffects(Players targetPlayer, int attackDamage)
	{
		var finalDamage = attackDamage;

		// Xử lý Di Tinh (Evasion with status effect)
		if (targetPlayer.TrungCapPhuHon_DiTinh != 0 && RNG.Next(1, 200) <= (double)targetPlayer.TrungCapPhuHon_DiTinh)
		{
			targetPlayer.ShowBigPrint(targetPlayer.SessionID, 405);
			StatusEffect statusEffect = new(targetPlayer, 3000, Effects.DI_TINH, 0);
			targetPlayer.AppendStatusList.Add(Effects.DI_TINH, statusEffect);
			targetPlayer.StatusEffect(BitConverter.GetBytes(Effects.DI_TINH), 1, 3000);
			return 0; // Hoàn toàn tránh damage
		}

		// Xử lý Hộ Thể (Life steal)
		if (targetPlayer.TrungCapPhuHon_HoThe != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_HoThe)
		{
			targetPlayer.ShowBigPrint(targetPlayer.SessionID, 406);
			targetPlayer.NhanVat_HP += finalDamage;
			targetPlayer.CapNhat_HP_MP_SP();
			return 0; // Damage bị hấp thụ
		}

		return finalDamage;
	}

	/// <summary>
	/// Xử lý defense đặc biệt theo job của player
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="targetDef">Defense hiện tại</param>
	/// <param name="attackType">Loại tấn công</param>
	/// <param name="attackDamage">Damage hiện tại</param>
	/// <returns>Defense sau khi xử lý job-specific bonuses</returns>
	private double ProcessJobSpecificDefense(Players targetPlayer, double targetDef, int attackType, int attackDamage)
	{
		var modifiedDef = targetDef;

		switch (targetPlayer.Player_Job)
		{
			case 3: // Thương (Spear)
				if (RNG.Next(1, 100) <= targetPlayer.THUONG_ChuyenCongViThu + targetPlayer.THUONG_ThangThien_2_KhiCong_DiThoiViTien)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					modifiedDef += targetPlayer.FLD_CongKich * 0.2;
				}
				break;

			case 10: // Quyền Sư (Fist)
				if (RNG.Next(0, 100) <= targetPlayer.QuyenSu_ChuyenCongViThu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					modifiedDef += targetPlayer.FLD_CongKich;
				}
				break;

			case 12: // Tự Hào (Pride)
				if (RNG.Next(0, 100) <= targetPlayer.TuHao_ChuyenCongViThu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					modifiedDef += targetPlayer.FLD_CongKich / 2.0;
				}
				break;
		}

		return modifiedDef;
	}

	/// <summary>
	/// Xử lý damage reduction đặc biệt theo job
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="attackType">Loại tấn công</param>
	/// <param name="attackDamage">Damage hiện tại</param>
	/// <returns>Damage sau khi xử lý job-specific reductions</returns>
	private int ProcessJobSpecificDamageReduction(Players targetPlayer, int attackType, int attackDamage)
	{
		var modifiedDamage = attackDamage;

		switch (targetPlayer.Player_Job)
		{
			case 10: // Quyền Sư (Fist)
					 // Kim Cương Bất Hoại - giảm damage từ attack type 29
				if (attackType == 29 && RNG.Next(1, 110) <= targetPlayer.QuyenSu_KimCuongBatHoai)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 554);
					modifiedDamage = (int)(modifiedDamage * 0.1);
				}
				// Bất Tử Chi Khư - hoàn toàn tránh damage
				if (RNG.Next(1, 100) <= targetPlayer.ThangThien_5_BatTu_ChiKhu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 1021);
					modifiedDamage = 0;
				}
				break;
		}

		return modifiedDamage;
	}

	/// <summary>
	/// Xử lý dodge/evasion theo job của player
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="finalDamage">Damage cuối cùng</param>
	/// <returns>Tuple chứa (finalDamage, isDodged)</returns>
	private (int finalDamage, bool isDodged) ProcessJobSpecificDodge(Players targetPlayer, int finalDamage)
	{
		var modifiedDamage = finalDamage;
		var isDodged = false;

		switch (targetPlayer.Player_Job)
		{
			case 1: // Đao (Sword)
				var dodgeChance1 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
				if (RNG.Next(1, 100) <= dodgeChance1)
				{
					isDodged = true;
					modifiedDamage = 0;
				}
				break;

			case 2: // Kiếm (Blade)
					// Hộ Thần Cường Khí - giảm 50% damage
				if (RNG.Next(1, 105) <= targetPlayer.KIEM_ThangThien_1_KhiCong_HoThan_CuongKhi)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 25);
					modifiedDamage = (int)(modifiedDamage * 0.5);
				}
				// Dodge
				var dodgeChance2 = targetPlayer.KIEM_HoiLieu_ThanPhap + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
				if (RNG.Next(1, 100) <= dodgeChance2)
				{
					isDodged = true;
					modifiedDamage = 0;
				}
				break;

			case 6: // Ninja
					// Đá Ma Triển Thần - giảm 30% damage
				if (RNG.Next(1, 110) <= targetPlayer.NINJA_ThangThien_1_KhiCong_DaMaTrienThan)
				{
					modifiedDamage = (int)(modifiedDamage * 0.7);
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 370);
				}
				// Thuận Thủy Thôi Chu - hồi máu
				if (RNG.Next(1, 100) <= targetPlayer.NINJA_ThangThien_2_KhiCong_ThuanThuyThoiChu)
				{
					targetPlayer.AddBlood((int)(modifiedDamage * 0.2));
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 371);
				}
				// Tam Hoa Tụ Đỉnh - dodge với counter
				var dodgeChance6 = targetPlayer.NINJA_TamHoaTuDinh + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.02;
				if (RNG.Next(1, 110) <= dodgeChance6)
				{
					isDodged = true;
					targetPlayer.NINJA_LienTieuDaiDa_SoLuong = modifiedDamage * targetPlayer.NINJA_LienTieuDaiDa;
					modifiedDamage = 0;
				}
				break;

			case 9: // Đàm Hoa Liên (Flower)
					// Hộ Thần Cường Khí - giảm 50% damage
				if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_HoThan_CuongKhi)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 25);
					modifiedDamage = (int)(modifiedDamage * 0.5);
				}
				// Dodge
				var dodgeChance9 = targetPlayer.DamHoaLien_HoiLieu_ThanPhap + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
				if (RNG.Next(1, 120) <= dodgeChance9)
				{
					isDodged = true;
					modifiedDamage = 0;
				}
				break;

			default: // Các job khác (3,4,5,7,8,10,11,12,13)
				var dodgeChanceDefault = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
				if (RNG.Next(1, 100) <= dodgeChanceDefault)
				{
					isDodged = true;
					modifiedDamage = 0;
				}
				break;
		}

		return (modifiedDamage, isDodged);
	}

	/// <summary>
	/// Xử lý logic khi player chết hoặc không hợp lệ
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	private void HandlePlayerDeath(Players targetPlayer)
	{
		if (targetPlayer.AutomaticRecovery != null)
		{
			targetPlayer.AutomaticRecovery.Enabled = false;
			targetPlayer.AutomaticRecovery.Close();
			targetPlayer.AutomaticRecovery.Dispose();
			targetPlayer.AutomaticRecovery = null;
		}
		if (targetPlayer.InvincibleTimeCounter != null)
		{
			targetPlayer.InvincibleTimeCounter.Enabled = false;
			targetPlayer.InvincibleTimeCounter.Close();
			targetPlayer.InvincibleTimeCounter.Dispose();
		}
		PlayCw = null;
		AutomaticAttackEnabled = false;
		AutomaticMoveEnabled = true;
		Rxjh_X = Rxjh_cs_X;
		Rxjh_Y = Rxjh_cs_Y;
		Rxjh_Z = Rxjh_cs_Z;
		SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
	}

	/// <summary>
	/// Xử lý logic movement khi damage = 0
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	private void HandleMovementLogic(Players targetPlayer)
	{
		if (FindPlayers(100, targetPlayer))
		{
			SendMovingData(targetPlayer.PosX, targetPlayer.PosY, 10, 2);
		}
		else
		{
			PlayCw = null;
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
		}
	}

	/// <summary>
	/// Xử lý logic khi player ngoài range
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	private void HandleOutOfRangeLogic(Players targetPlayer)
	{
		if (FindPlayers(100, targetPlayer))
		{
			SendMovingData(targetPlayer.PosX, targetPlayer.PosY, 10, 2);
		}
		else
		{
			PlayCw = null;
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
		}
	}

	/// <summary>
	/// Xử lý Hồn Nguyên và các damage reduction khác
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="finalDamage">Damage hiện tại</param>
	/// <returns>Damage sau khi xử lý</returns>
	private int ProcessHonNguyenAndDamageReduction(Players targetPlayer, int finalDamage)
	{
		var modifiedDamage = finalDamage;

		// Xử lý Hồn Nguyên - giảm 50% damage
		if (targetPlayer.TrungCapPhuHon_HonNguyen != 0 &&
			RNG.Next(1, 150) <= targetPlayer.TrungCapPhuHon_HonNguyen + targetPlayer.TrungCapPhuHon_HonNguyen_Giap)
		{
			targetPlayer.ShowBigPrint(targetPlayer.SessionID, 407);
			modifiedDamage = (int)(modifiedDamage * 0.5);
		}

		// Xử lý giảm damage từ trang bị
		if (targetPlayer.FLD_TrangBi_GiamXuong_MucThuongTon > 0.0)
		{
			modifiedDamage -= (int)targetPlayer.FLD_TrangBi_GiamXuong_MucThuongTon;
		}

		return Math.Max(0, modifiedDamage);
	}

	/// <summary>
	/// Xử lý shield logic cho Mài Liệu Chân (job 11)
	/// </summary>
	/// <param name="targetPlayer">Player mục tiêu</param>
	/// <param name="finalDamage">Damage cuối cùng</param>
	/// <returns>Lượng shield hấp thụ</returns>
	private int ProcessShieldLogic(Players targetPlayer, int finalDamage)
	{
		var shield = 0;
		if (targetPlayer.Player_Job == 11 && targetPlayer.MaiLieuChan_ChuongLucKichHoat > 0.0)
		{
			shield = (int)(finalDamage * (targetPlayer.MaiLieuChan_ChuongLucKichHoat * 2.0 * 0.01));
			if (shield > targetPlayer.NhanVat_AP)
			{
				shield = targetPlayer.NhanVat_AP;
			}
			targetPlayer.NhanVat_AP -= shield;
		}
		return shield;
	}

	#endregion

	public int testAnimation = 0;

	private void HandleBossMagicAttack(Players targetPlayer, int countToSkill)
	{
		if (isDoingMech)
			return;

		int aoe = 100;
		int base_attack = (int)(FLD_AT * 1);
		int monsterSkillAnimation = 1;
		double currentHPPercent = (double)Rxjh_HP * 100 / Max_Rxjh_HP;
		int timeAnimation = 2; // process bar time animation


		switch (FLD_PID)
		{
			case 15423:
				if (IsWorldBoss)
				{

					HandleBossStages15423(targetPlayer, currentHPPercent, countToSkill);
				}
				else
				{
					NormalBossBehavior(targetPlayer, countToSkill, base_attack, monsterSkillAnimation, timeAnimation, aoe);
				}
				break;
			case 15419:
			case 15420:
			case 15421:
			case 15422:
			case 15424:
			case 16556:
			case 16555:
			case 16786:
			case 16787:
			case 16788:
				LogHelper.WriteLine(LogLevel.Info, "Test animation: " + 2);

				BossMagicAttack(targetPlayer, base_attack, 3, timeAnimation, 0, aoe);
				break;
				// targetPlayer.HeadGlow(1, "yellow");

				//NormalBossBehavior(targetPlayer, countToSkill, base_attack, monsterSkillAnimation, timeAnimation, aoe);
				// break;
			default:
				BossMagicAttack(targetPlayer, base_attack, monsterSkillAnimation, timeAnimation, countToSkill, aoe);
				isCastingMagic = false;
				attackCount = 0;
				break;
		}
		return;
	}

	private void HandleBossStages15423(Players targetPlayer, double currentHPPercent, int countToSkill)
	{
		int base_attack = (int)(FLD_AT * 1.5);
		int monsterSkillAnimation = 1;
		int timeAnimation = 2;
		int aoe = 100;
		int[] summonId = { 15231, 15232, 15233, 15234, 15235, 15237, 15238, 15239 }; // Quái nam lâm

		LogHelper.WriteLine(LogLevel.Error, $"Stage {StageManager.CurrentBossStage} Type {StageManager.CurrentStage} Processing {StageManager.IsProcessingStage} HP {currentHPPercent}");

		// Kiểm tra các stage cần trigger
		if (StageManager.ShouldTriggerStage(currentHPPercent, 1))
		{
			// Stage 1: Summon at 75% HP
			StageManager.StartStage(NpcStageType.Summon, 1);
			StageManager.SetNextStage(NpcStageType.ApplyEffects);
			SummonNpc15423Simple(targetPlayer, summonId, 75, 50);
			NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(10000)); // 10s cho summon
			return;
		}
		else if (StageManager.ShouldTriggerStage(currentHPPercent, 2))
		{
			// Stage 2: Summon at 25% HP
			StageManager.StartStage(NpcStageType.Summon, 2);
			StageManager.SetNextStage(NpcStageType.ApplyEffects);
			SummonNpc15423Simple(targetPlayer, summonId, 25, 75);
			NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(10000)); // 10s cho summon
			return;
		}

		// Xử lý stage hiện tại
		switch (StageManager.CurrentStage)
		{
			case NpcStageType.Summon:
				// Kết thúc summon, chuyển sang apply effects
				StageManager.CompleteCurrentStage();
				ApplyStageEffects(targetPlayer, 150);
				NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(2000)); // 2s buffer
				return;

			case NpcStageType.ApplyEffects:
				// Kết thúc effects, quay về attack bình thường
				StageManager.CompleteCurrentStage();
				NormalBossBehavior(targetPlayer, countToSkill, base_attack, monsterSkillAnimation, timeAnimation, aoe);
				NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(1500));
				return;

			case NpcStageType.Idle:
			default:
				// Attack bình thường
				NormalBossBehavior(targetPlayer, countToSkill, base_attack, monsterSkillAnimation, timeAnimation, aoe);
				NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(1500));
				return;
		}
	}

	public int BossStage { get; set; }
	public NpcStageManager StageManager { get; private set; } = new NpcStageManager();
	public int Rxjh_Gold { get; internal set; }
	public int FLD_QItemDrop { get; internal set; }
	public int FLD_QDropPP { get; internal set; }

	private void NormalBossBehavior(Players targetPlayer, int countToSkill, int base_attack, int monsterSkillAnimation, int timeAnimation, int aoe)
	{
		if (attackCount >= countToSkill)
		{
			BossMagicAttack(targetPlayer, base_attack, monsterSkillAnimation, timeAnimation, countToSkill, aoe);
		}
		else
		{
			HandleNpcAttackPlayer(targetPlayer);
		}
	}

	private void SummonNpc15423Simple(Players targetPlayer, int[] summonId, int hpFactor, int positionFactor)
	{
		try
		{
			LogHelper.WriteLine(LogLevel.Info, $"Boss 15423 Summon - HP Factor: {hpFactor}%");

			// Gửi animation summon
			Send8705(targetPlayer, 3, 10, 1);

			// Summon NPCs
			SummonNpcs(summonId, positionFactor, 10000);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "SummonNpc15423Simple Error: " + ex.Message);
		}
	}

	private void SummonNpcs(int[] summonId, int positionFactor, int timetoDie)
	{
		try
		{
			int normalSum = RNG.Next(0, summonId.Length - 1);
			int specialSum;
			do
			{
				specialSum = RNG.Next(0, summonId.Length - 1);
			}
			while (specialSum == normalSum);

			for (int i = 0; i < 10; i++)
			{
				var npc = World.AddNpcNeo(summonId[normalSum], Rxjh_cs_X + RNG.Next(-positionFactor, positionFactor), Rxjh_cs_Y + RNG.Next(-positionFactor, positionFactor), Rxjh_Map, string.Empty, 0, 0, 0, worldBoss: false, marked: 0, timetoDie);
			}
			var npcx = World.AddNpcNeo(summonId[specialSum], Rxjh_cs_X + RNG.Next(-positionFactor, positionFactor), Rxjh_cs_Y + RNG.Next(-positionFactor, positionFactor), Rxjh_Map, string.Empty, 0, 0, 0, worldBoss: false, marked: 1, timetoDie);
			LogHelper.WriteLine(LogLevel.Error, "marked Type " + npcx.NPC_SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Info, "SummonNpcs Error " + ex.Message);
		}

	}
	private void TaoCo()
	{
		if (MarkedType == 1)
		{
			if (Rxjh_HP <= 0)
			{
				LogHelper.WriteLine(LogLevel.Error, "--Quai bi giet");
			}
			LogHelper.WriteLine(LogLevel.Error, "--MarkType = 1");
			World.AddNpcNeo(15732, Rxjh_cs_X, Rxjh_cs_Y, Rxjh_Map, string.Empty, 0, 0, 0, false, 0, 10000);
		}
	}

	private void ApplyStageEffects(Players targetPlayer, int range)
	{
		LogHelper.WriteLine(LogLevel.Error, "Stage Effects");
		var players = GetNearbyPlayers(range);
		foreach (var item in players)
		{
			if (!item.LookInNpc(15732, 65))
			{
				//item.HeThongNhacNho("Khong dung gan 15732");
				int rate = 10;
				if (BossStage == 1)
				{
					rate = 20;
				}
				ApplyStatusEffects(item, rate);
			}
		}
		Send8710(targetPlayer, 100, 0);
	}

	private void ApplyStatusEffects(Players player, int rate = 20)
	{
		X_Di_Thuong_Trang_Thai_Loai rootTarget = new(player, 10000, 4, 0.0);
		player.TrangThai_BatThuong.Add(4, rootTarget);

		X_Di_Thuong_Trang_Thai_Loai bleed = new(player, 10000, 10, player.CharacterMax_HP / rate);
		player.TrangThai_BatThuong.Add(10, bleed);
		bleed.TrangThai_BatThuong_LoaiChayMau(player.CharacterMax_HP / rate);

		StatusEffect value14 = new(player, 10000, Effects.BOSS_BLEEDING, 0);
		player.AppendStatusList.Add(Effects.BOSS_BLEEDING, value14);
		player.StatusEffect(BitConverter.GetBytes(Effects.BOSS_BLEEDING), 1, 10000);
		player.ShowBigPrint(player.SessionID, 1014);
	}

	private void BossMagicHeal(Players targetPlayer, int healingValue, int countToSkill, int aoe, int processBar = 1)
	{
		if (attackCount >= countToSkill && isCastingMagic)
		{
			//AutomaticAttack.Interval = 2500.0;
			//Rxjh_HP += healingValue;
			UpdateHP('+', healingValue);
			Send4238(targetPlayer, healingValue);
			isCastingMagic = false;
			attackCount = 0;
		}
	}
	public void UpdateHP(char operation, int value)
	{
		switch (operation)
		{
			case '+':
				Rxjh_HP += value;
				break;
			case '-':
				Rxjh_HP -= value;
				if (Rxjh_HP < 0)
					Rxjh_HP = 0; // Giả sử HP không thể âm
				break;
			case '*':
				Rxjh_HP *= value;
				break;
			case '/':
				if (value != 0)
					Rxjh_HP /= value;
				else
					throw new ArgumentException("Không thể chia cho 0.");
				break;
			default:
				throw new ArgumentException("Phép toán không hợp lệ.");
		}

		if (Rxjh_HP > Max_Rxjh_HP)
			Rxjh_HP = Max_Rxjh_HP;
	}

	// Đối tượng khóa để đồng bộ hóa việc cập nhật HP
	private readonly object _hpLock = new();

	/// <summary>
	/// Cập nhật HP với tích hợp damage tracking
	/// </summary>
	/// <param name="operation">Phép toán (+, -, *, /)</param>
	/// <param name="value">Giá trị</param>
	/// <param name="playerSession">Session ID của player</param>
	/// <param name="playerName">Tên player</param>
	/// <param name="playerLevel">Level player</param>
	/// <param name="guildName">Tên guild</param>
	/// <param name="guildId">ID guild</param>
	/// <param name="skipbs">Skip broadcast</param>
	/// <param name="attacker">Player object để tracking damage</param>
	public void UpdateHPWithTracking(char operation, int value, int playerSession, string playerName, int playerLevel,
		string guildName, int guildId, string accountID, bool skipbs = false, Players attacker = null)
	{
		// Sử dụng khóa để tránh race condition
		lock (_hpLock)
		{
			// Xử lý UpdateHP bình thường nếu không phải cross-server
			switch (operation)
			{
				case '+':
					_Rxjh_HP += value;
					break;
				case '-':
					var damage = value;
					_Rxjh_HP -= value;

					if (_Rxjh_HP < 0)
					{
						damage += _Rxjh_HP;
						_Rxjh_HP = 0;
					}

					// **TÍCH HỢP DAMAGE TRACKING** - Thêm damage vào tracking nếu có attacker
					if (attacker != null && damage >= 0 && IsNpc == 0)
					{
						try
						{
							LogHelper.WriteLine(LogLevel.Debug, $"UpdateHPWithTracking: Adding {damage} damage from player {attacker.SessionID} to NPC {NPC_SessionID}");

							// Sử dụng optimized target list để track damage
							_optimizedTargetList.AddDamage(attacker.SessionID, damage, attacker.TeamID);

							// Xử lý World Boss damage tracking và notifications
							if (World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
							{
								HandleWorldBossDamageTracking(attacker, damage);
							}
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, $"UpdateHP damage tracking error: {ex.Message}");
						}
					}

					if (IsWorldBoss && World.List_WorldBossContribute.TryGetValue(ID, out var contributes))
					{
						// Xử lý contribute liên server
						contributes.UpdateContribute(World.ServerID, playerSession, playerName, damage, 1, accountID);

						// Nếu là cross-server boss, broadcast HP update
						if (contributes.IsCrossServer)
						{
							var hpInfo = new HeroBoss.BossHPUpdateInfo
							{
								BossId = ID,
								CurrentHP = _Rxjh_HP,
								MaxHP = _Max_Rxjh_HP,
								DamageDealt = damage,
								AttackerServerId = World.ServerID,
								AttackerSessionId = playerSession,
								AttackerName = playerName
							};

							var message = HeroBoss.CrossServerBossProtocol.CreateBossHPUpdateMessage(hpInfo);
							World.conn?.Transmit(message);

							// Cập nhật state trong CrossServerBossManager
							HeroBoss.CrossServerBossManager.Instance.UpdateBossState(ID, _Rxjh_HP, _Max_Rxjh_HP,
								_Rxjh_HP <= 0 ? HeroBoss.CrossServerBossState.Dead : HeroBoss.CrossServerBossState.Active);
						}
					}
					break;
				case '*':
					_Rxjh_HP *= value;
					break;
				case '/':
					if (value != 0)
						_Rxjh_HP /= value;
					else
						throw new ArgumentException("Không thể chia cho 0.");
					break;
				default:
					throw new ArgumentException("Phép toán không hợp lệ.");
			}

			if (_Rxjh_HP > _Max_Rxjh_HP)
				_Rxjh_HP = _Max_Rxjh_HP;
		}
	}

	private void BossMagicAttack(Players targetPlayer, int atkDmgBoss, int monsterSkillAnimation, int timeAnimation, int countToSkill, int aoe, int processBar = 1)
	{
		if (attackCount >= countToSkill && !isCastingMagic)
		{
			// AutomaticAttack.Interval = 2000.0;
			Send8705(targetPlayer, monsterSkillAnimation, timeAnimation, processBar);
			NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(2000));
			isCastingMagic = true;
			return;
		}
		if (attackCount >= countToSkill && isCastingMagic)
		{
			// AutomaticAttack.Interval = 2500.0;
			Send8710(targetPlayer, aoe, atkDmgBoss);
			NpcManager.NpcManager.SetNextActionTime(this, DateTime.Now.AddMilliseconds(2500));
			isCastingMagic = false;
			attackCount = 0;
		}
	}

	private void Send4238(Players targetPlayer, int attackDmg)
	{
		var res = Converter.HexStringToByte("AA5514004C7B8E100C004C7B0000010100003075000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, res, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, res, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, res, 14, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, res, 15, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(attackDmg), 0, res, 0x12, 4);
		SendBroadCastData(res);
	}

	private void Send8710(Players targetPlayer, int aoe, int attackDmg)
	{
		var type = 1;
		var type2 = 1;

		byte[] res = Converter.HexStringToByte("AA556401855206225C010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400D030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, res, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, res, 14, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type2), 0, res, 16, 2);

		System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.PosX), 0, res, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.PosY), 0, res, 30, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, res, 10, 4);
		var players = GetNearbyPlayers(200);
		System.Buffer.BlockCopy(BitConverter.GetBytes(players.Count), 0, res, 18, 4);
		int i = 0;
		foreach (Players player in players)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, res, 34 + i * 4, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(attackDmg), 0, res, 114 + i * 4, 4);
			i++;
			//player.NhanVat_HP -= attackDmg;
			//PlayerEvents.OnPlayerHpChanged(player, attackDmg,null,PlayerEvents.HpChangeType.Damage,this);
			HandlePlayerAfterDamage(player, 0, attackDmg, attackDmg);
		}
		SendBroadCastData(res);
	}

	private void SendMagicAnimation()
	{
		var array = Converter.HexStringToByte("aa551000c8781022080001000000c878000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 10, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 14, 4);
		SendBroadCastData(array);
	}

	private void Send8705(Players targetPlayer, int ani, int time, int processbar)
	{
		int unknow = 11; // 10 11 66
		int monsterSkillAnimation = ani; // 1 2
		int timeAnimation = time; //  process bar time animation
		int headIcon = 0; // 0 1
		int processBar = processbar; // process bar on off
		int unknow6 = 2;
		//targetPlayer.HeThongLogMessage($"{FLD_PID} Send 8705 u1 {unknow} ani {monsterSkillAnimation} head {headIcon} time {timeAnimation}",7,"");
		byte[] array3 = Converter.HexStringToByte("aa553c004e7b01223400da01000066000200020000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array3, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, array3, 10, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(unknow), 0, array3, 14, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(monsterSkillAnimation), 0, array3, 16, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(timeAnimation), 0, array3, 18, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(headIcon), 0, array3, 20, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(processBar), 0, array3, 22, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(unknow6), 0, array3, 24, 2);
		SendBroadCastData(array3);
	}
	public List<NpcClass> DanhNhieuMucTieu_TraTimPhamVi_Npc2(Players targetPlayer, int SoLuong, float range = 50)
	{
		try
		{
			if (targetPlayer.NearbyNpcs == null)
				return [];

			// Lấy danh sách NPC hợp lệ và sắp xếp theo khoảng cách
			var npcs = targetPlayer.NearbyNpcs.Values
				.Where(npc => !npc.NPCDeath &&
							npc.IsNpc == 0 &&
							npc != this)
				.Select(npc => new
				{
					Npc = npc,
					Distance = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, this.Rxjh_X, this.Rxjh_Y)
				})
				.Where(x => x.Distance <= range)
				.OrderBy(x => x.Distance)
				.Take(SoLuong)
				.Select(x => x.Npc)
				.ToList();
			// Log all the distance
			foreach (var npc in npcs)
			{
				targetPlayer.HeThongNhacNho($"NPC {npc.FLD_PID} distance: {CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, this.Rxjh_X, this.Rxjh_Y)} - {CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY)}");
			}
			return npcs;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đánh nhiều mục tiêu Nhìn vào Npc 11 error：" + ex);
			return null;
		}
	}


	public static void UpdateNPC_Spawn(Dictionary<int, NpcClass> NpcList, Players Playe)
	{
		try
		{
			if (NpcList == null || NpcList.Count <= 0)
			{
				return;
			}
			using SendingClass SendingClass = new();
			SendingClass.Write4(NpcList.Count);
			foreach (var value in NpcList.Values)
			{
				SendingClass.Write4(value.NPC_SessionID);
				SendingClass.Write4(value.NPC_SessionID);
				SendingClass.Write2(value.FLD_PID);
				SendingClass.Write2(1);
				SendingClass.Write4(value.Rxjh_HP);
				SendingClass.Write4(value.Max_Rxjh_HP);
				SendingClass.Write(value.Rxjh_X);
				SendingClass.Write(value.Rxjh_Z);
				SendingClass.Write(value.Rxjh_Y);
				SendingClass.Write4(1082130432);
				SendingClass.Write(value.FLD_FACE1);
				SendingClass.Write(value.FLD_FACE2);
				SendingClass.Write(value.Rxjh_X);
				SendingClass.Write(value.Rxjh_Z);
				SendingClass.Write(value.Rxjh_Y);
				SendingClass.Write4(0);
				SendingClass.Write4(value.FLD_BOSS >= 1 ? 1 : 0);
				SendingClass.Write4(12);
				SendingClass.Write4(0);
				SendingClass.Write4(0);
				SendingClass.Write4(uint.MaxValue);
				SendingClass.Write4(0);
				if (value.NPCDeath)
				{
					value.UpdateNPC_DeXoaSoLieu(Playe);
					value.LamMoi_NPCDeathSoLieu(Playe);
				}
			}
			Playe.Client?.SendPak(SendingClass, 26368, Playe.SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại đây update NPC SoLieu 1212 !!");
		}
	}

	public static NpcClass GetNpcBySessionAndWorldId(int sessionId, string worldId)
	{
		// Nếu worldId trùng với server hiện tại, tìm NPC trong server hiện tại
		if (worldId == World.ServerID.ToString())
		{
			foreach (var map in World.MapList.Values)
			{
				var npc = map.GetNpcBySessionId(sessionId);
				if (npc != null)
				{
					return npc;
				}
			}
		}
		return null;
	}

	public static void UpdateNPC_Despawn(Dictionary<int, NpcClass> NpcList, Players Playe)
	{
		try
		{
			if (NpcList == null || NpcList.Count <= 0)
			{
				return;
			}
			using SendingClass w = new();
			w.Write4(NpcList.Count);
			foreach (var value in NpcList.Values)
			{
				w.Write4(value.NPC_SessionID);
				w.Write4(value.NPC_SessionID);
				w.Write2(value.FLD_PID);
				w.Write4(1);
				w.Write4((uint)value.Rxjh_HP);
				w.Write4((uint)value.Max_Rxjh_HP);
				w.Write(value.Rxjh_X);
				w.Write(value.Rxjh_Z);
				w.Write(value.Rxjh_Y);
				w.Write4(1082130432);
				w.Write(value.FLD_FACE1);
				w.Write(value.FLD_FACE2);
				w.Write(value.Rxjh_X);
				w.Write(value.Rxjh_Z);
				w.Write(value.Rxjh_Y);
				w.Write4(0);
				w.Write4(0);
				w.Write4(12);
				w.Write4(0);
				w.Write4(2359296);
				w.Write4(uint.MaxValue);
				w.Write4(0);
			}
			Playe.Client?.SendPak(w, 26624, Playe.SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Update NPC DeXoa SoLieu 12345 !! ");
		}
	}

	public void UpdateNPC_DeXoaSoLieu(Players Playe)
	{
		try
		{
			using SendingClass w = new();
			w.Write4(1);
			w.Write4(NPC_SessionID);
			w.Write4(NPC_SessionID);
			w.Write2(FLD_PID);
			w.Write2(1);
			w.Write4(Rxjh_HP);
			w.Write4(Max_Rxjh_HP);
			w.Write(Rxjh_X);
			w.Write(Rxjh_Z);
			w.Write(Rxjh_Y);
			w.Write4(1082130432);
			w.Write(FLD_FACE1);
			w.Write(FLD_FACE2);
			w.Write(Rxjh_X);
			w.Write(Rxjh_Z);
			w.Write(Rxjh_Y);
			w.Write4(0);
			w.Write4(0);
			w.Write4(12);
			w.Write4(0);
			w.Write4(0);
			w.Write4(uint.MaxValue);
			w.Write4(0);
			Playe.Client?.SendPak(w, 26624, Playe.SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Update NPC 2222 !! ");
		}
	}

	public void RefreshSpawnData()
	{
		var num = 0;
		try
		{
			//LogHelper.WriteLine(LogLevel.Info, $"[RESPAWN DEBUG] Starting RefreshSpawnData for NPC {Name} (ID: {FLD_PID})");
			num = 1;
			NPCDeath = false;
			Rxjh_HP = Max_Rxjh_HP;
			// random tọa độ boss
			if (_FLD_PID != 15349 && _FLD_PID != 15350 && _FLD_PID != 15121 && _FLD_PID != 15122 && _FLD_PID != 16278 && _FLD_PID != 15293)
			{
				Random random = new((int)DateTime.Now.Ticks);
				var num2 = new Random(World.GetRandomSeed()).Next(0, 4);
				var num3 = random.NextDouble() * 50.0;
				var num4 = random.NextDouble() * 50.0;
				switch (num2)
				{
					case 0:
						Rxjh_X = Rxjh_cs_X + (float)num3;
						Rxjh_Y = Rxjh_cs_Y + (float)num4;
						break;
					case 1:
						Rxjh_X = Rxjh_cs_X - (float)num3;
						Rxjh_Y = Rxjh_cs_Y - (float)num4;
						break;
					case 2:
						Rxjh_X = Rxjh_cs_X + (float)num3;
						Rxjh_Y = Rxjh_cs_Y - (float)num4;
						break;
					default:
						Rxjh_X = Rxjh_cs_X - (float)num3;
						Rxjh_Y = Rxjh_cs_Y + (float)num4;
						break;
				}
			}
			else if (Rxjh_Map == 40101 && _FLD_PID == 16278)
			{
				Rxjh_X = RNG.Next(-250, 200);
				Rxjh_Y = 0f;
			}
			else
			{
				Rxjh_X = Rxjh_cs_X;
				Rxjh_Y = Rxjh_cs_Y;
			}
			Rxjh_Z = Rxjh_cs_Z;
			if (FLD_BOSS == 1)
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (!value.Client.TreoMay && value.IsJoinWorld)
					{
						value.HeThongNhacNho("Đại ma đầu vừa tái sinh tại bản đồ " + X_Toa_Do_Class.GetName_TiengViet(Rxjh_Map) + " - Tọa độ: [" + (int)Rxjh_X + "," + (int)Rxjh_Y + "]", 8, "Truyền Âm Các");
					}
				}
			}
			// LogHelper.WriteLine(LogLevel.Info, $"[RESPAWN DEBUG] Sending respawn packet for NPC {Name} (ID: {FLD_PID}) at ({Rxjh_X}, {Rxjh_Y})");
			// LogHelper.WriteLine(LogLevel.Info, $"[RESPAWN DEBUG] PlayList count: {PlayList?.Count ?? 0}");

			using (SendingClass w = new())
			{
				w.Write4(1);
				w.Write4(NPC_SessionID);
				w.Write4(NPC_SessionID);
				w.Write2(FLD_PID);
				w.Write2(1);
				w.Write4(Rxjh_HP);
				w.Write4(Max_Rxjh_HP);
				w.Write(Rxjh_X);
				w.Write(Rxjh_Z);
				w.Write(Rxjh_Y);
				w.Write4(0);
				w.Write(FLD_FACE1);
				w.Write(FLD_FACE2);
				w.Write(Rxjh_X);
				w.Write(Rxjh_Z);
				w.Write(Rxjh_Y);
				w.Write4(0);
				w.Write4(0);
				w.Write4(12);
				w.Write4(0);
				w.Write4(0);
				w.Write4(uint.MaxValue);
				w.Write4(0);
				SendCurrentRangeBroadcastData(w, 31488, NPC_SessionID);
			}
			// Don't disable respawn here - let NpcManager handle it
			// LogHelper.WriteLine(LogLevel.Info, $"[RESPAWN DEBUG] NPC {Name} (ID: {FLD_PID}) respawned at position ({Rxjh_X}, {Rxjh_Y}) - COMPLETED");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC hồi sinh - Lỗi num: [" + num + "] -ID:[" + FLD_PID + "]" + ex.Message);
		}
	}

	public static void Update_NPC_HoiSinh_SoLieu_FireDragon(Dictionary<int, NpcClass> NpcList, Players Playe)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		foreach (var value in NpcList.Values)
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write4(value.NPC_SessionID);
			sendingClass.Write4(value.NPC_SessionID);
			sendingClass.Write2(value.FLD_PID);
			sendingClass.Write2(1);
			sendingClass.Write4(value.Rxjh_HP);
			sendingClass.Write4(value.Max_Rxjh_HP);
			sendingClass.Write(value.Rxjh_X);
			sendingClass.Write(value.Rxjh_Z);
			sendingClass.Write(value.Rxjh_Y);
			sendingClass.Write(4f);
			sendingClass.Write(value.FLD_FACE1);
			sendingClass.Write(value.FLD_FACE2);
			sendingClass.Write(value.Rxjh_X);
			sendingClass.Write(value.Rxjh_Z);
			sendingClass.Write(value.Rxjh_Y);
			sendingClass.Write4(0);
			sendingClass.Write4(128369664);
			sendingClass.Write4(0);
			sendingClass.Write4(215040);
			sendingClass.Write4(0);
			sendingClass.Write4(786432);
			sendingClass.Write4(uint.MaxValue);
			sendingClass.Write4(0);
			Playe.Client?.SendPak(sendingClass, 31488, value.NPC_SessionID);
		}
	}

	private void LamMoi_NPCDeathSoLieu(Players Playe)
	{
		using SendingClass pak = new();
		Playe.Client?.SendPak(pak, 34816, NPC_SessionID);
	}

	public void BroadCastNpcDead()
	{
		//LogHelper.WriteLine(LogLevel.Debug, "QuangBa_NPCDeathSoLieu");
		using SendingClass pak = new();
		SendCurrentRangeBroadcastData(pak, 34816, NPC_SessionID);
	}



	public void SendBroadCastData(byte[] Packet_DuLieu)
	{
		try
		{
			// CRITICAL FIX: Use AOI radius instead of 700 for consistent broadcasting
			var broadcastRange = AOISystem.AOI_RADIUS;
			int playersReached = 0;

			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == Rxjh_Map && FindPlayers(broadcastRange, value) && value.Client != null && !value.Client.TreoMay && value.IsJoinWorld)
				{
					value.Client.Send_Map_Data(Packet_DuLieu, Packet_DuLieu.Length);
					playersReached++;
				}
			}

			// if (AOI.// AOIConfiguration.Instance. EnableDebugLogging)
			// {

			// 	LogHelper.WriteLine(LogLevel.Debug, $"NPC {NPC_SessionID} SendBroadCastData reached {playersReached} players within range {broadcastRange}");
			// }
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Gửi đi trước mắt phạm vi qua\u0309ng bá số liệu 3 Phạm sai lầm:" + ex.StackTrace);
		}
	}

	public void SendMovingData(float x, float y, int moveDistance, int Type_Move, float oldx = -99999f, float oldy = -99999f)
	{
		try
		{
			// // Update NPC position in AOI system if enabled, not send packet
			// try
			// {
			// 	if (// AOIConfiguration.Instance. ShouldUseAOI(Rxjh_Map))
			// 	{
			// 		// Update NPC position in AOI
			// 		AOISystem.Instance.UpdateNPCPosition(this, x, y);
			// 		//LogHelper.WriteLine(LogLevel.Debug, $"NPC {Name} position updated in AOI system: ({x}, {y})");
			// 	}
			// }
			// catch (Exception ex)
			// {
			// 	LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC position in AOI: {ex.Message}");
			// }
			// Send Packet

			SendingClass sendingClass = new();
			if (FLD_PID != 15293 && FLD_PID != 16270 && FLD_PID != 16271 && FLD_PID != 16430 && FLD_PID != 16430 && FLD_PID != 16435)
			{
				var posX = Rxjh_X;
				var posY = Rxjh_Y;
				if (oldx != -99999f && oldy != -99999f)
				{
					posX = oldx;
					posY = oldy;
				}
				double num3 = (int)Math.Sqrt((Rxjh_cs_X - (double)posX) * (Rxjh_cs_X - (double)posX) + (Rxjh_cs_Y - (double)posY) * (Rxjh_cs_Y - (double)posY));
				Random random = new(World.GetRandomSeed());
				var num4 = random.Next(0, 4);
				var num5 = random.NextDouble() * moveDistance;
				var num6 = random.NextDouble() * moveDistance;
				switch (num4)
				{
					case 0:
						Rxjh_X = x + (float)num5;
						Rxjh_Y = y + (float)num6;
						break;
					case 1:
						Rxjh_X = x - (float)num5;
						Rxjh_Y = y + (float)num6;
						break;
					case 2:
						Rxjh_X = x + (float)num5;
						Rxjh_Y = y - (float)num6;
						break;
					default:
						Rxjh_X = x - (float)num5;
						Rxjh_Y = y - (float)num6;
						break;
				}
				if (num3 > 100.0)
				{
					Rxjh_X = Rxjh_cs_X;
					Rxjh_Y = Rxjh_cs_Y;
				}
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write4(-1);
				sendingClass.Write4(Type_Move);
				sendingClass.Write((float)num5);
				sendingClass.Write4(Rxjh_HP);
				sendingClass.Write(posX);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write(posY);
				SendCurrentRangeBroadcastData(sendingClass, 29696, NPC_SessionID, false);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NpcClass GuiDi DiDong SoLieu New Error" + FLD_PID + "|" + Name + " " + ex.Message);
		}
	}

	public void GuiDi_CongKichSoLieu(int CongKichLuc, int CongKichLoaiHinh, int CharacterFullServerID, int KhoiPhuc_LaChan)
	{
		try
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(CharacterFullServerID);
			sendingClass.Write2(1);
			sendingClass.Write2(0);
			sendingClass.Write4(CongKichLuc);
			sendingClass.Write4(19356438);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(KhoiPhuc_LaChan);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(CongKichLoaiHinh);
			sendingClass.Write(Rxjh_X);
			sendingClass.Write(15f);
			sendingClass.Write(Rxjh_Y);
			sendingClass.Write(0);
			sendingClass.Write(1);
			sendingClass.Write2(0);

			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(-1);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			SendCurrentRangeBroadcastData(sendingClass, 3072, NPC_SessionID);
		}
		catch (Exception ex)
		{
			var array = new string[6]
			{
				"Send CongKichSoLieu   error",
				FLD_PID.ToString(),
				"|",
				Name,
				"   ",
				null
			};
			array[5] = ex.ToString();
			LogHelper.WriteLine(LogLevel.Error, string.Concat(array));
		}
	}
	private readonly object _lock = new();

	public void SendCurrentRangeBroadcastData(SendingClass pak, int id, int sessionId, bool skipBs = false)
	{
		try
		{
			// ENHANCED: Use AOI radius for consistent broadcasting
			var broadcastRange = AOISystem.AOI_RADIUS;
			int playersReached = 0;

			lock (_lock)
			{
				// if (// AOIConfiguration.Instance. ShouldUseAOI(Rxjh_Map))
				// {
				// 	var aoiGrids = AOISystem.Instance.GetNearbyGrids(Rxjh_X, Rxjh_Y, Rxjh_Map);

				// 	foreach (var grid in aoiGrids)
				// 	{
				// 		// Use optimized ForEach method from our improved AOIGrid
				// 		grid.ForEachPlayer(player =>
				// 		{
				// 			if (DistanceHelper.IsWithinRangeSquared(Rxjh_X, Rxjh_Y, player.PosX, player.PosY, broadcastRange))
				// 			{
				// 				player.Client.SendPak(pak, id, wordid);
				// 				playersReached++;

				// 				if (AOI.// AOIConfiguration.Instance. EnableDebugLogging)
				// 				{
				// 					var distance = Math.Sqrt(Math.Pow(player.PosX - Rxjh_X, 2) + Math.Pow(player.PosY - Rxjh_Y, 2));
				// 					//LogHelper.WriteLine(LogLevel.Debug, $"NPC {NPC_SessionID} broadcast packet {id} to player {player.CharacterName} at distance {distance:F1}");
				// 				}
				// 			}
				// 		});
				// 	}
				// }
				// else
				// {
				// Fallback to old system
				foreach (var value in World.allConnectedChars.Values)
				{
					if (value.MapID == Rxjh_Map && FindPlayers(broadcastRange, value) && value.Client != null && !value.Client.TreoMay && value.IsJoinWorld)
					{
						// LogHelper.WriteLine(LogLevel.Debug, $"NPC {NPC_SessionID} broadcast packet {id} to player {value.CharacterName}");
						value.Client.SendPak(pak, id, sessionId);
						playersReached++;
					}
				}
				// }

				// if (AOI.// AOIConfiguration.Instance. EnableDebugLogging)
				// {
				// 	LogHelper.WriteLine(LogLevel.Debug, $"NPC {NPC_SessionID} broadcast packet {id} reached {playersReached} players within range {broadcastRange}");
				// }
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC Qua\u0309ng bá số liệu | Phạm sai lầm 3：" + ex.Message);
		}
	}

	public void LamMoiTuVongSoLieu()
	{
		try
		{
			if (NPCDeath)
			{
				return;
			}
			NPCDeath = true;
			if (_QuaiXuatHien_DuyNhatMotLan)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				ClearTargetList();
				BroadCastNpcDead();
				Dispose();
				return;
			}
			// Timer management moved to NpcManager
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = false;
			AutoRespawnEnabled = true; // This will set the interval automatically
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			ClearTargetList();
			BroadCastNpcDead();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "LamMoi TuVong SoLieu error [" + FLD_PID + "]-Name:[" + Name + "] - " + ex.Message);
		}
	}

	public void GuiDiHaiThuocSoLieu()
	{
		if (IsNpc == 2)
		{
			AutoRespawnEnabled = true; // This will set the interval automatically
			PlayCw = null;
			ClearTargetList();
			BroadCastNpcDead();
		}
	}

	public void GuiDuLieu_TuVong_MotLanCuaQuaiVat()
	{
		try
		{
			AbnormalStatusList();
			EndAbnormalBloodDropStatusList();
			if (IsNpc != 1 && !NPCDeath)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				ClearTargetList();
				BroadCastNpcDead();
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GuiDi TuVong SoLieu lỗi 222 | " + FLD_PID + "|" + Name + "   " + ex.Message);
		}
	}
	public void GuiDiTuVongSoLieu()
	{
		try
		{

			AbnormalStatusList();
			EndAbnormalBloodDropStatusList();

			if (IsNpc == 1)
				return;
			BroadCastNpcDead();
			Task.Delay(2000).ContinueWith(_ =>
			{
				// *** KHÔNG XÓA PlayerTargetList ở đây nữa - để OnNpcDeath xử lý trước ***
				// PlayerTargetList?.Clear(); // REMOVED - gây race condition

				NPCDeath = true;

				if (QuaiXuatHien_DuyNhatMotLan)
				{
					if (PlayCw != null)
						PlayCw = null;
					timeNpcRevival = DateTime.MinValue;
					Dispose();
					return;
				}
				else
				{
					// Schedule respawn using NpcManager
					var respawnTime = FLD_NEWTIME > 0 ? FLD_NEWTIME * 1000 : 10000;
					timeNpcRevival = DateTime.Now.AddSeconds(FLD_NEWTIME);
					NpcManager.NpcManager.EnableRespawn(NPC_SessionID, respawnTime);
				}

				// Timer management moved to NPCTimerManager
				AutomaticAttackEnabled = false;
				AutomaticMoveEnabled = true;

				AutoRespawnEnabled = true; // This will set the interval automatically

				if (PlayCw != null)
					PlayCw = null;
			});
		}
		catch (Exception ex2)
		{
			BroadCastNpcDead();
			if (QuaiXuatHien_DuyNhatMotLan)
			{
				timeNpcRevival = DateTime.MinValue;
			}
			else
			{
				// Schedule respawn using NpcManager
				var respawnTime = FLD_NEWTIME > 0 ? FLD_NEWTIME * 1000 : 10000;
				timeNpcRevival = DateTime.Now.AddSeconds(FLD_NEWTIME);
				NpcManager.NpcManager.EnableRespawn(NPC_SessionID, respawnTime);
			}
			LogHelper.WriteLine(LogLevel.Error, "GuiDiTuVongSoLieu   error" + FLD_PID + "|" + Name + "   " + ex2.Message);
		}
	}

	/// <summary>
	/// Xử lý World Boss damage tracking và notifications
	/// </summary>
	private void HandleWorldBossDamageTracking(Players attacker, int damage)
	{
		try
		{
			// Logic từ Play_Add cho World Boss
			var topPlayer = _optimizedTargetList.GetTopPlayer();
			var topTeam = _optimizedTargetList.GetTopTeam();

			if (topPlayer == null || topPlayer.PlayID == 0)
			{
				return;
			}

			var characterData = attacker.GetCharacterData(topPlayer.PlayID);
			var text = "Unknown";
			var text2 = Rxjh_HP.ToString("N0");

			if (characterData != null)
			{
				text = characterData.CharacterName;
			}

			var text3 = topPlayer.DamageDealt.ToString("N0");

			// Get current player's damage from optimized list
			var currentPlayerTargets = _optimizedTargetList.GetAllTargets();
			var currentPlayerTarget = currentPlayerTargets.FirstOrDefault(p => p.PlayID == attacker.SessionID);
			var text4 = currentPlayerTarget?.DamageDealt.ToString("N0") ?? "0";

			var text5 = Math.Round((double)(topPlayer.DamageDealt * 100 / Max_Rxjh_HP), 2) + "%";

			if (topTeam.TeamID > 0)
			{
				var text7 = "";
				if (World.WToDoi.TryGetValue(topTeam.TeamID, out var value))
				{
					text7 = value.CaptainName;
				}
				var text8 = topTeam.Gjxl_team.ToString("N0");
				var text9 = Math.Round(topTeam.Gjxl_team * 100.0 / Max_Rxjh_HP, 2) + "%";
				// Boss damage notifications có thể được thêm vào đây nếu cần
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"HandleWorldBossDamageTracking error: {ex.Message}");
		}
	}

	/// <summary>
	/// Thêm player vào target list để targeting (không gây damage)
	/// </summary>
	/// <param name="player">Player cần thêm vào target list</param>
	public void AddPlayerTarget(Players player)
	{
		try
		{
			if (player == null || IsNpc == 1)
				return;

			// Thêm player vào target list với damage = 0 để targeting
			_optimizedTargetList.AddDamage(player.SessionID, 0, player.TeamID);

			//LogHelper.WriteLine(LogLevel.Debug, $"Added player {player.SessionID} to target list for NPC {NPC_SessionID}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.AddPlayerTarget error: {ex.Message}");
		}
	}

	/// <summary>
	/// Nhận damage từ player và xử lý logic tấn công
	/// </summary>
	/// <param name="attacker">Player tấn công</param>
	/// <param name="damage">Lượng damage</param>
	/// <param name="attackType">Loại tấn công</param>
	/// <param name="voCongId">ID võ công (0 nếu tấn công vật lý)</param>
	/// <returns>True nếu NPC chết, False nếu còn sống</returns>
	public bool ReceiveDamage(Players attacker, int damage, int attackType, int voCongId = 0)
	{
		try
		{
			LogHelper.WriteLine(LogLevel.Debug, $"ReceiveDamage: NPC {NPC_SessionID} receiving {damage} damage from player {attacker.SessionID}");

			if (damage <= 0 || IsNpc == 1 || NPCDeath)
			{
				LogHelper.WriteLine(LogLevel.Debug, $"ReceiveDamage: Skipped - damage={damage}, IsNpc={IsNpc}, NPCDeath={NPCDeath}");
				return false;
			}

			// Tính toán damage thực tế
			var actualDamage = CalculateActualDamage(damage);

			// Cập nhật HP với tích hợp damage tracking
			UpdateHPWithTracking('-', damage, attacker.SessionID, attacker.CharacterName,
				attacker.Player_Level, attacker.GuildName, attacker.GuildId, attacker.AccountID, false, attacker);

			// Kiểm tra nếu NPC chết
			// if ((NPCDeath && Rxjh_HP <= 0) || (FLD_BOSS == 1 && (NPCDeath || Rxjh_HP <= 0)))
			// Nếu chưa chết và HP <= 0 thì mới xử lý chết
			if (!NPCDeath && Rxjh_HP <= 0)
			{
				OnDeath(attacker, voCongId);
				return true;
			}
			else
			{
				// NPC còn sống, kích hoạt tấn công lại
				StartCounterAttack(attacker, attackType);
				return false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.ReceiveDamage error: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Tính toán damage thực tế dựa trên HP tối đa của NPC
	/// </summary>
	private int CalculateActualDamage(int damage)
	{
		if (damage > Max_Rxjh_HP)
		{
			return Max_Rxjh_HP;
		}

		if (Rxjh_HP > 0 && damage > Rxjh_HP)
		{
			return Rxjh_HP;
		}

		return damage;
	}

	/// <summary>
	/// Xử lý khi NPC chết
	/// </summary>
	private void OnDeath(Players killer, int voCongId)
	{
		try
		{
			LogHelper.WriteLine(LogLevel.Debug, "OnDeath called");
			// Tính toán rewards
			var rewards = CalculateRewards(killer);

			LogHelper.WriteLine(LogLevel.Debug, "Rewards calculated");
			LogHelper.WriteLine(LogLevel.Debug,
			$"Rewards Info : Experience: {rewards.experience}, Money: {rewards.money}, LichLuyen: {rewards.lichLuyen}, ThangThienLichLuyen: {rewards.thangThienLichLuyen}"
			);

			// Xử lý soul sucking
			killer.SoulSucking(NPC_SessionID);

			// Áp dụng bonus kỳ duyên nếu có
			ApplyKyDuyenBonus(killer, ref rewards.experience);

			// Phân phối rewards
			if (IsWorldBoss)
			{
				HandleWorldBossDeath(rewards, killer);
			}
			else
			{
				DistributeRewards(rewards, killer);
			}

			// Xử lý death data
			HandleDeathData(killer);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.OnDeath error: {ex.Message}");
		}
	}

	/// <summary>
	/// Cấu trúc chứa thông tin rewards
	/// </summary>
	public struct NpcRewards
	{
		public double money;
		public double experience;
		public double lichLuyen;
		public double thangThienLichLuyen;
	}

	/// <summary>
	/// Tính toán rewards khi NPC chết
	/// </summary>
	private NpcRewards CalculateRewards(Players killer)
	{
		return new NpcRewards
		{
			money = ThuDuocTien(killer),
			experience = ThuDuocKinhNghiem(),
			lichLuyen = CalculateSkillExp(killer),
			thangThienLichLuyen = ThuDuocThangThienLichLuyen(killer)
		};
	}

	/// <summary>
	/// Áp dụng bonus kỳ duyên
	/// </summary>
	private void ApplyKyDuyenBonus(Players player, ref double experience)
	{
		if (player.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= player.TrungCapPhuHon_KyDuyen)
		{
			experience *= 2.0;
			player.ShowBigPrint(player.SessionID, 403);
		}
	}

	/// <summary>
	/// Kích hoạt tấn công lại khi NPC còn sống
	/// </summary>
	private void StartCounterAttack(Players attacker, int attackType)
	{
		try
		{
			AutomaticAttackEnabled = true;
			AutomaticMoveEnabled = false;

			if (attackType < 150)
			{
				attacker.TanCongVatLy_NhanVat_ID = NPC_SessionID;
			}
			else
			{
				attacker.AttackList.Clear();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.StartCounterAttack error: {ex.Message}");
		}
	}

	/// <summary>
	/// Phân phối rewards cho players
	/// </summary>
	private void DistributeRewards(NpcRewards rewards, Players killer)
	{
		try
		{
			// Sử dụng logic phân phối tối ưu mới
			LogHelper.WriteLine(LogLevel.Debug, "DistributeRewards called");
			DistributeRewardsOptimized(rewards, killer);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.DistributeRewards error: {ex.Message}");
			// Fallback to old logic if needed
			killer.PhanPhoiKinhNghiemLichLuyenTienTai(killer, this,
				rewards.experience, rewards.lichLuyen, rewards.money, rewards.thangThienLichLuyen);
		}
	}

	/// <summary>
	/// Xử lý World Boss death với logic đặc biệt
	/// </summary>
	private void HandleWorldBossDeath(NpcRewards rewards, Players killer)
	{
		try
		{
			// Xử lý World Boss theo logic hiện có
			var playGjClass = FindMaxDame(PlayerTargetList);
			var check_team4 = FindMaxDameTeam(PlayerTargetList);
			// Raise boss killed event
			GroupQuestEvent.Instance.RaiseBossKilled(this, killer);

			if (playGjClass.DamageDealt > check_team4.Gjxl_team)
			{
				// Individual player has highest damage
				var attacker = killer.GetCharacterData(playGjClass.PlayID);
				if (attacker != null)
				{
					killer.PhanPhoiKinhNghiemLichLuyenTienTai(attacker, this,
						rewards.experience, rewards.lichLuyen, rewards.money, rewards.thangThienLichLuyen);
				}
			}
			else if (check_team4.TeamID > 0)
			{
				// Team has highest damage
				var teamMembers = PlayerTargetList.Where(k => k.TeamID == check_team4.TeamID);
				var attacker = killer.GetCharacterData(teamMembers.FirstOrDefault()?.PlayID ?? 0);
				if (attacker != null)
				{
					killer.PhanPhoiKinhNghiemLichLuyenTienTai(attacker, this,
						rewards.experience, rewards.lichLuyen, rewards.money, rewards.thangThienLichLuyen);
				}
			}
			else
			{
				// Fallback to killer
				killer.PhanPhoiKinhNghiemLichLuyenTienTai(killer, this,
					rewards.experience, rewards.lichLuyen, rewards.money, rewards.thangThienLichLuyen);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.HandleWorldBossDeath error: {ex.Message}");
			// Fallback to normal distribution
			DistributeRewards(rewards, killer);
		}
	}

	/// <summary>
	/// Xử lý gửi death data và tìm top damage dealer
	/// </summary>
	private void HandleDeathData(Players killer)
	{
		try
		{
			if (DamageContributeList != null && DamageContributeList.Count >= 2)
			{
				// Tìm player có damage cao nhất
				var topDamagePlayer = FindTopDamagePlayer();
				var topPlayerData = killer.GetCharacterData(topDamagePlayer.SessionId);

				if (topPlayerData != null && topPlayerData.LookInNpc(100, this))
				{
					// Kiểm tra level gap
					if (killer.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
					{
						GuiDiTuVongSoLieuWrapper(topDamagePlayer.SessionId);
					}
					else
					{
						GuiDiTuVongSoLieuWrapper(killer.SessionID);
					}
				}
				else
				{
					GuiDiTuVongSoLieuWrapper(killer.SessionID);
				}
			}
			else
			{
				GuiDiTuVongSoLieuWrapper(killer.SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.HandleDeathData error: {ex.Message}");
			// Fallback
			GuiDiTuVongSoLieuWrapper(killer.SessionID);
		}
	}

	/// <summary>
	/// Tìm player có damage cao nhất
	/// </summary>
	private (int SessionId, double Damage) FindTopDamagePlayer()
	{
		try
		{
			var topPlayer = _optimizedTargetList.GetTopPlayer();
			if (topPlayer != null && topPlayer.PlayID > 0)
			{
				return (topPlayer.PlayID, topPlayer.DamageDealt);
			}

			return (0, 0);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.FindTopDamagePlayer error: {ex.Message}");
			return (0, 0);
		}
	}

	public void GuiDiTuVongSoLieu(int SessionId)
	{
		var num = 0;
		if (!World.allConnectedChars.TryGetValue(SessionId, out var attacker) || !IsPlayerInRange(attacker)) return;
		var num2 = 0;
		try
		{
			foreach (var value9 in attacker.QuestList.Values)
			{
				var dictionary = MapClass.GetnpcTemplate(SessionId);
				foreach (var item in value9.NhiemVu_GiaiDoan)
				{
					if ((item.GiaiDoan_TrangThai != 0 && item.GiaiDoan_TrangThai != 1) || item.GiaiDoanCanVatPham_.Count <= 0)
					{
						continue;
					}
					var num3 = 0;
					var rW = new X_Nhiem_Vu_Loai().GetRW(value9.RwID);
					foreach (var item2 in rW.NhiemVu_GiaiDoan)
					{
						if (item2.GiaiDoanID == item.GiaiDoanID)
						{
							num3 = item2.MucDo_KhoKhan;
							item.GiaiDoanCanVatPham_ = item2.GiaiDoanCanVatPham_;
							break;
						}
					}
					num = value9.RwID;
					if (num == 45 && 15062 == FLD_PID)
					{
						attacker.SetUpQuestItems(900000099, 1);
					}
					if (num == 46 && 15072 == FLD_PID)
					{
						attacker.SetUpQuestItems(900000101, 1);
					}
					var key = RNG.Next(0, item.GiaiDoanCanVatPham_.Count - 1);
					var x_Giai_Doan_Can_Vat_Pham_Loai = item.GiaiDoanCanVatPham_[key];
					var num4 = 0;
					if (value9.NhiemVuID == 1201)
					{
						num4 = RNG.Next(1, World.Rate_Rot_Quest);
					}
					else if (value9.NhiemVuID == 1202)
					{
						num4 = RNG.Next(1, 200);
					}
					else if (value9.NhiemVuID == 1203)
					{
						num4 = RNG.Next(1, 70);
					}
					else if (value9.NhiemVuID == 1204)
					{
						num4 = RNG.Next(1, 100);
					}
					if (num4 > num3)
					{
						continue;
					}
					if (attacker.TeamID != 0 && World.WToDoi.TryGetValue(attacker.TeamID, out var value2) && value9.NhiemVuID != 1203)
					{
						foreach (var value10 in value2.PartyPlayers.Values)
						{
							if ((value10.CompletedQuestList.TryGetValue(1201, out var value3) && value3.nhiemvudate.Date == DateTime.Now.Date) || !FindPlayers(800, value10) || value10.NhanVat_HP <= 0 || value10.PlayerTuVong || attacker.SessionID == value10.SessionID)
							{
								continue;
							}
							foreach (var value11 in value10.QuestList.Values)
							{
								if (value11.NhiemVuID != value9.NhiemVuID)
								{
									continue;
								}
								foreach (var item3 in value11.NhiemVu_GiaiDoan)
								{
									if (item3.GiaiDoanCanVatPham_.Count <= 0)
									{
										continue;
									}
									foreach (var item4 in rW.NhiemVu_GiaiDoan)
									{
										if (item4.GiaiDoanID == item3.GiaiDoanID)
										{
											item3.GiaiDoanCanVatPham_ = item4.GiaiDoanCanVatPham_;
											break;
										}
									}
									var x_Giai_Doan_Can_Vat_Pham_Loai2 = item3.GiaiDoanCanVatPham_[key];
									if (x_Giai_Doan_Can_Vat_Pham_Loai2.QuaiVat_ID == FLD_PID && !value10.CheckItem(x_Giai_Doan_Can_Vat_Pham_Loai2.VatPham_ID, x_Giai_Doan_Can_Vat_Pham_Loai2.TongSoVatPham))
									{
										if (value9.NhiemVuID == 1201 && value11.NhiemVuID == value9.NhiemVuID)
										{
											value10.SetUpQuestItems(World.Item_Quest, 1);
										}
										else
										{
											value10.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai2.VatPham_ID, 1);
										}
									}
								}
							}
						}
						if (x_Giai_Doan_Can_Vat_Pham_Loai.QuaiVat_ID != FLD_PID || attacker.CheckItem(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, x_Giai_Doan_Can_Vat_Pham_Loai.TongSoVatPham))
						{
							continue;
						}
						if (attacker.CompletedQuestList.TryGetValue(1201, out var value4))
						{
							if (value4.nhiemvudate.Date != DateTime.Now.Date)
							{
								if (value9.NhiemVuID == 1201)
								{
									attacker.SetUpQuestItems(World.Item_Quest, 1);
								}
								else
								{
									attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
								}
							}
						}
						else if (value9.NhiemVuID == 1201)
						{
							attacker.SetUpQuestItems(World.Item_Quest, 1);
						}
						else
						{
							attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
						}
					}
					else
					{
						if (x_Giai_Doan_Can_Vat_Pham_Loai.QuaiVat_ID != FLD_PID || attacker.CheckItem(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, x_Giai_Doan_Can_Vat_Pham_Loai.TongSoVatPham))
						{
							continue;
						}
						if (value9.NhiemVuID == 1201)
						{
							attacker.SetUpQuestItems(World.Item_Quest, 1);
						}
						else if (value9.NhiemVuID == 1203 && World.Event_Noel_Progress != 0)
						{
							attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
						}
						else if (x_Giai_Doan_Can_Vat_Pham_Loai.QuaiVat_ID == 15900)
						{
							if (World.Event_Noel_Progress != 0)
							{
								attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
							}
						}
						else
						{
							attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
						}
					}
				}
			}
			EndAbnormalBloodDropStatusList();
			if (IsNpc == 1 || NPCDeath)
			{
				return;
			}
			if (World.NpcEvent_DCH.TryGetValue(NPC_SessionID, out var value5))
			{
				if (World.allConnectedChars.TryGetValue(SessionId, out attacker) && attacker.MapID == 40101)
				{
					Boss_BaoSuat_VatPham_DCH(attacker);
					if (value5.FLD_PID >= 16272 && value5.FLD_PID <= 16273)
					{
						attacker.DCH_StackA_SoLuong += 4;
						attacker.DCH_Stack_Add(attacker, 1008001797, attacker.DCH_StackA_SoLuong, 1);
						attacker.DCH_StackA = 100;
					}
					else if (value5.FLD_PID >= 16274 && value5.FLD_PID <= 16277)
					{
						attacker.DCH_StackA_SoLuong += 4;
						attacker.DCH_Stack_Add(attacker, 1008001797, attacker.DCH_StackA_SoLuong, 1);
						attacker.DCH_StackB_SoLuong += 2;
						attacker.DCH_StackA = 100;
						attacker.DCH_Stack_Add(attacker, 1008001798, attacker.DCH_StackB_SoLuong, 1);
						attacker.DCH_StackB = 400;
					}
					else if (value5.FLD_PID == 16278)
					{
						attacker.DCH_StackC_SoLuong += 10;
						attacker.DCH_Stack_Add(attacker, 1008001799, attacker.DCH_StackC_SoLuong, 1);
						attacker.DCH_StackC = 4000;
						attacker.UpdateCharacterData(attacker);
						attacker.UpdateBroadcastCharacterData();
						foreach (var value12 in World.allConnectedChars.Values)
						{
							value12.HeThongNhacNho("[" + attacker.CharacterName + "] hạ sát đại ma đầu Diêm La Quân, chiến công lừng lẫy!", 21, "Thiên cơ các");
							value12.HeThongNhacNho("[" + attacker.CharacterName + "] hạ sát đại ma đầu Diêm La Quân, danh vang tứ hải!", 22, "Thiên cơ các");
							value12.HeThongNhacNho("[" + attacker.CharacterName + "] hạ sát đại ma đầu Diêm La Quân, anh hùng vô song!", 23, "Thiên cơ các");
						}
					}
				}
			}
			else if (World.allConnectedChars.TryGetValue(SessionId, out attacker))
			{
				if (attacker.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
				{
					BaoSuat_VatPham(attacker);
				}
				if (World.Event_Noel_Progress != 0)
				{
					if (FLD_PID == World.ID_Monster_Drop_Event_GiangSinh)
					{
						var num5 = RNG.Next(1, 100);
						if (num5 >= 90)
						{
							attacker.DROP_ITEM_ADD(World.PhanThuong_Drop_Event_GiangSinh, 0, 0, 0, 0, 0, "", attacker);
							foreach (var value13 in World.allConnectedChars.Values)
							{
								if (attacker.SessionID != value13.SessionID)
								{
									value13.HeThongNhacNho("Đánh bại Tuần Lộc, bảo vật hộp quà rơi ra khắp nơi!", RNG.Next(21, 23), "[" + attacker.CharacterName + "]");
								}
								else
								{
									attacker.HeThongNhacNho("Đại hiệp đánh bại Tuần Lộc, hộp quà rơi đầy đất!", 7, "Giáng Sinh");
								}
							}
						}
					}
				}
			}
			else
			{
				num2 = 6;
				Math.Abs(PlayCw.Playe.Player_Level - Level);
				if (SessionId > 40000 && PlayCw != null && PlayCw.Playe != null && PlayCw.Playe.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
				{
					BaoSuat_VatPham(PlayCw.Playe);
				}
				else
				{
					BaoSuat_VatPham(null);
				}
			}

			NPCDeath = true;
			if (_QuaiXuatHien_DuyNhatMotLan)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				timeNpc_HoiSinh = DateTime.MinValue;
				BroadCastNpcDead();
				Dispose();
				return;
			}
			else
			{
				// Schedule respawn using NpcManager
				var respawnTime = FLD_NEWTIME > 0 ? FLD_NEWTIME * 1000 : 10000;
				timeNpc_HoiSinh = DateTime.Now.AddSeconds(FLD_NEWTIME);
				NpcManager.NpcManager.EnableRespawn(NPC_SessionID, respawnTime);
			}
			// Timer management moved to NpcManager
			AutomaticAttackEnabled = false;
			AutomaticMoveEnabled = true;
			if ((World.eve != null || World.tmc_flag) && attacker.MapID == 801 && attacker.Player_Zx == 1)
			{
				attacker.HeThongNhacNho("Hiệp khách [" + attacker.CharacterName + "] đã hạ sát [" + Name + "] tại bản đồ [" + X_Toa_Do_Class.getmapname(attacker.MapID) + "]", 6, "Thiên cơ các");
				attacker.GuiDi_TheLucChien_KetThuc_TinTuc(1);
			}
			if ((World.eve != null || World.tmc_flag) && attacker.MapID == 801 && attacker.Player_Zx == 2)
			{
				attacker.HeThongNhacNho("Hiệp khách [" + attacker.CharacterName + "] đã hạ sát [" + Name + "] tại bản đồ [" + X_Toa_Do_Class.getmapname(attacker.MapID) + "]", 6, "Thiên cơ các");
				attacker.GuiDi_TheLucChien_KetThuc_TinTuc(2);
			}
			if (World.TheLucChien_Progress == 3 && (_FLD_PID == 16320 || _FLD_PID == 16321) && Rxjh_Map == 801)
			{
				if (_FLD_PID == 16320)
				{
					World.TheLucChien_TaPhai_DiemSo += 25;
				}
				else if (_FLD_PID == 16321)
				{
					World.TheLucChien_ChinhPhai_DiemSo += 25;
				}
			}
			AutoRespawnEnabled = true; // This will set the interval automatically
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			BroadCastNpcDead();
		}
		catch (Exception ex)
		{
			BroadCastNpcDead();
			if (QuaiXuatHien_DuyNhatMotLan)
			{
				timeNpc_HoiSinh = DateTime.MinValue;
			}
			else
			{
				// Schedule respawn using NpcManager
				var respawnTime = FLD_NEWTIME > 0 ? FLD_NEWTIME * 1000 : 10000;
				timeNpc_HoiSinh = DateTime.Now.AddSeconds(FLD_NEWTIME);
				NpcManager.NpcManager.EnableRespawn(NPC_SessionID, respawnTime);
			}
			LogHelper.WriteLine(LogLevel.Error, "Send TuVong SoLieu lỗi tại num: [" + num2 + "]-[" + FLD_PID + "]-[" + Name + "] - " + ex.Message);
		}
	}

	public void GuiDi_PhanSatThuong_CongKichSoLieu(int CongKichLuc, int NhanVat_ID)
	{
		var array = Converter.HexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NhanVat_ID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(CongKichLuc), 0, array, 18, 2);
		QuangBaSoLieu(array, array.Length);
	}

	public byte[] RoiRaVatPham(DropClass drop, Players yxqname)
	{
		try
		{
			var dBItmeId = RxjhClass.CreateItemSeries();
			var array = new byte[World.Item_Db_Byte_Length];
			var bytes = BitConverter.GetBytes(dBItmeId);
			var array2 = new byte[56];
			var array3 = Converter.HexStringToByte("AA557200940223006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			if (!World.ItemList.TryGetValue(drop.FLD_PID, out var value))
			{
				return null;
			}
			if (value.FLD_QUESTITEM != 1)
			{
				try
				{
					if (World.Droplog)
					{
						LogHelper.WriteLine(LogLevel.Debug, "VatPham Làm rơi VatPhamTen [" + drop.FLD_NAME + "] ThuocTinh1 [" + drop.FLD_MAGIC0 + "] ThuocTinh2 [" + drop.FLD_MAGIC1 + "] ThuocTinh3 [" + drop.FLD_MAGIC2 + "] ThuocTinh4 [" + drop.FLD_MAGIC3 + "] ThuocTinh5 [" + drop.FLD_MAGIC4 + "]");
					}
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC0), 0, array2, 0, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC1), 0, array2, 4, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC2), 0, array2, 8, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC3), 0, array2, 12, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC4), 0, array2, 16, 4);
					System.Buffer.BlockCopy(bytes, 0, array, 0, 4);
					System.Buffer.BlockCopy(array2, 0, array, 16, 20);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_PID), 0, array, 8, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 12, 4);
					if (drop.FLD_DAYS > 0 && drop.FLD_LEVEL1 <= Level && drop.FLD_LEVEL2 >= Level && drop.FLD_PP != 0 && drop.FLD_PP >= World.Random_So_Drop)
					{
						DateTime value2 = new(1970, 1, 1, 8, 0, 0);
						System.Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.Subtract(value2).TotalSeconds), 0, array, 52, 4);
						System.Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddDays(drop.FLD_DAYS).Subtract(value2).TotalSeconds), 0, array, 56, 4);
					}
					if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1000), 0, array, 60, 2);
					}
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Drop item 1 error: NPC: " + FLD_PID + "|" + Name + "   " + ex.Message);
					return null;
				}
				GroundItem x_Mat_Dat_Vat_Pham_Loai;
				GroundItem value3;
				try
				{
					if (FLD_BOSS == 0)
					{
						x_Mat_Dat_Vat_Pham_Loai = new(array, Rxjh_X, Rxjh_Y, Rxjh_Z, Rxjh_Map, yxqname, 0);
					}
					else
					{
						var num = RNG.Next(0, 2);
						var num2 = 0.0;
						var num3 = 0.0;
						if (FLD_PID == 16278)
						{
							num2 = RNG.Next(-50, 50);
							num3 = RNG.Next(-50, 50);
						}
						else
						{
							num2 = RNG.Next(-25, 25);
							num3 = RNG.Next(-25, 25);
						}
						var x = Rxjh_X + (float)((num != 0) ? (0.0 - num2) : num2);
						var y = Rxjh_Y + (float)((num != 0) ? (0.0 - num3) : num3);
						x_Mat_Dat_Vat_Pham_Loai = new(array, x, y, Rxjh_Z, Rxjh_Map, yxqname, 0);
					}
					if (x_Mat_Dat_Vat_Pham_Loai == null)
					{
						return null;
					}
					if (!World.GroundItemList.TryGetValue(dBItmeId, out value3))
					{
						World.GroundItemList.Add(dBItmeId, x_Mat_Dat_Vat_Pham_Loai);
						x_Mat_Dat_Vat_Pham_Loai.AddToAOI();
					}
					x_Mat_Dat_Vat_Pham_Loai.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "Drop item 3 error: " + FLD_PID + "|" + Name + "   " + ex2.Message);
					return null;
				}
				try
				{
					if (World.GroundItemList.TryGetValue(dBItmeId, out value3))
					{
						x_Mat_Dat_Vat_Pham_Loai.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
					}
					return array;
				}
				catch (Exception ex3)
				{
					LogHelper.WriteLine(LogLevel.Error, "Drop item 4 error: " + FLD_PID + "|" + Name + " " + ex3.Message);
					return null;
				}
			}
			if (yxqname != null)
			{
				var parcelVacancy = yxqname.GetParcelVacancy(yxqname);
				if (parcelVacancy != -1)
				{
					yxqname.AddItems(bytes, BitConverter.GetBytes(drop.FLD_PID), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
				}
			}
			return null;
		}
		catch (Exception ex4)
		{
			LogHelper.WriteLine(LogLevel.Error, "Rơi ra vật phẩm bị lỗi !! " + FLD_PID + "|" + Name + " " + ex4.Message);
			return null;
		}
		finally
		{
			drop.FLD_PID = drop.FLD_PIDNew;
			drop.FLD_MAGIC0 = drop.FLD_MAGICNew0;
			drop.FLD_MAGIC1 = drop.FLD_MAGICNew1;
			drop.FLD_MAGIC2 = drop.FLD_MAGICNew2;
			drop.FLD_MAGIC3 = drop.FLD_MAGICNew3;
			drop.FLD_MAGIC4 = drop.FLD_MAGICNew4;
		}
	}

	public void BaoSuat_VatPham(Players yxqname)
	{
		try
		{
			if (Rxjh_Map == 801)
			{
				yxqname = null;
			}
			switch (FLD_BOSS)
			{
				case 0:
					BaoSuat_VatPham2(yxqname);
					break;
				case 1:
					Boss_BaoSuat_VatPham(yxqname);
					break;
				case 2:
					GSBaoSuat_VatPham(1, 9, yxqname);
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public void GSBaoSuat_VatPham(int sl, int maxsl, Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			var gSDrop = DropClass.GetGSDrop(Level, sl, maxsl);
			if (gSDrop == null)
			{
				return;
			}
			foreach (var item in gSDrop)
			{
				if (item == null)
				{
					continue;
				}
				switch (item.FLD_PID)
				{
					case ItemDef.Item.HanNgocThach:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC11 = 0;
							var num11 = RNG.Next(1, 100);
							foreach (var item2 in item.DropShuX)
							{
								if (num11 <= item2.Max)
								{
									fLD_MAGIC11 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC11;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.KimCuongThach:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC7 = 0;
							var num7 = RNG.Next(1, 100);
							foreach (var item3 in item.DropShuX)
							{
								if (num7 <= item3.Max)
								{
									fLD_MAGIC7 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC7;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.KimCuongThachCaoCap:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC9 = 0;
							var num9 = RNG.Next(1, 100);
							foreach (var item4 in item.DropShuX)
							{
								if (num9 <= item4.Max)
								{
									fLD_MAGIC9 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC9;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.HanNgocThachCaoCap:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC6 = 0;
							var num6 = RNG.Next(1, 100);
							foreach (var item5 in item.DropShuX)
							{
								if (num6 <= item5.Max)
								{
									fLD_MAGIC6 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC6;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.KimCuongThach_DaKich:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC12 = 0;
							var num12 = RNG.Next(1, 100);
							foreach (var item6 in item.DropShuX)
							{
								if (num12 <= item6.Max)
								{
									fLD_MAGIC12 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC12;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.KimCuongThach_VoCong:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC3 = 0;
							var num3 = RNG.Next(1, 100);
							foreach (var item7 in item.DropShuX)
							{
								if (num3 <= item7.Max)
								{
									fLD_MAGIC3 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC3;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.ThuocTinhThach:
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						break;
					case 800000030:
						item.FLD_MAGIC0 = World.GetValue(800000030, 5);
						break;
					case 800000031:
						item.FLD_MAGIC0 = World.GetValue(800000031, 5);
						break;
					case 800000032:
						item.FLD_MAGIC0 = World.GetValue(800000032, 5);
						break;
					case 800000033:
						item.FLD_MAGIC0 = World.GetValue(800000033, 5);
						break;
					case 800000034:
						item.FLD_MAGIC0 = World.GetValue(800000034, 5);
						break;
					case ItemDef.Item.NhietHuyetThach:
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						break;
					case ItemDef.Item.TrungCapKyNgocThach:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC8 = 0;
							var num8 = RNG.Next(1, 100);
							foreach (var item8 in item.DropShuX)
							{
								if (num8 <= item8.Max)
								{
									fLD_MAGIC8 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC8;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = RNG.Next(23, 51);
						}
						break;
					case ItemDef.Item.SoCapKyNgocThach:
						if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = RNG.Next(1, 22);
						}
						break;
					case ItemDef.TapHonThach.HaCapTapHonChau:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(10, 50);
						break;
					case ItemDef.TapHonThach.TrungCapTapHonCha:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(100, 150);
						break;
					case ItemDef.TapHonThach.ThuongCapTapHonChau:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(400, 699);
						break;
					default:
						{
							if (item.FLD_MAGIC0 != 10)
							{
								break;
							}
							var fLD_MAGIC2 = 0;
							var num2 = RNG.Next(1, 100);
							foreach (var item9 in item.DropShuX)
							{
								if (num2 <= item9.Max)
								{
									fLD_MAGIC2 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC2;
							break;
						}
					case ItemDef.TapHonThach.TuLinhTapHonChau:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(2000, 2499);
						break;
					case ItemDef.Item.HanNgocThachSieuCap:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC13 = 0;
							var num13 = RNG.Next(1, 100);
							foreach (var item10 in item.DropShuX)
							{
								if (num13 <= item10.Max)
								{
									fLD_MAGIC13 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC13;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.HanNgocThachHonNguyen:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC10 = 0;
							var num10 = RNG.Next(1, 100);
							foreach (var item11 in item.DropShuX)
							{
								if (num10 <= item11.Max)
								{
									fLD_MAGIC10 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC10;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.KimCuongThachSieuCap:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC5 = 0;
							var num5 = RNG.Next(1, 100);
							foreach (var item12 in item.DropShuX)
							{
								if (num5 <= item12.Max)
								{
									fLD_MAGIC5 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC5;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case ItemDef.Item.KimCuongThachHonNguyen:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC4 = 0;
							var num4 = RNG.Next(1, 100);
							foreach (var item13 in item.DropShuX)
							{
								if (num4 <= item13.Max)
								{
									fLD_MAGIC4 = RNG.Next(item13.ShuXMin, item13.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC4;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
					case 1000001620:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC = 0;
							var num = RNG.Next(1, 100);
							foreach (var item14 in item.DropShuX)
							{
								if (num <= item14.Max)
								{
									fLD_MAGIC = RNG.Next(item14.ShuXMin, item14.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
						}
						break;
				}
				RoiRaVatPham(item, yxqname);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham 1 error：" + ex);
		}
	}

	public void Boss_BaoSuat_VatPham_DCH(Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			var bossDrop_DCH = DropClass.GetBossDrop_DCH(Level);
			if (bossDrop_DCH == null)
			{
				return;
			}
			foreach (var item in bossDrop_DCH)
			{
				if (item != null)
				{
					RoiRaVatPham(item, yxqname);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public void Boss_BaoSuat_VatPham(Players playe)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi [Rxjh_Exp = 0] nên Drop sẽ không rơi gì !!!, tăng [Rxjh_Exp] lên >= [1] trong [TBL_XWWL_MONSTER]");
				return;
			}
			if (FLD_BOSS == 1)
			{
				if (FLD_PID == 15100)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID == 15236)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID >= 15355 && FLD_PID <= 15356)
				{
					World.SoLuong_Item_DropBoss = 5;
				}
				else if (FLD_PID == 15403)
				{
					World.SoLuong_Item_DropBoss = 4;
				}
				else if (FLD_PID >= 15897 && FLD_PID <= 15899)
				{
					World.SoLuong_Item_DropBoss = 5;
				}
				else if (FLD_PID == 15900)
				{
					World.SoLuong_Item_DropBoss = RNG.Next(5, 10);
				}
				else if (FLD_PID == 15983)
				{
					World.SoLuong_Item_DropBoss = 100;
				}
				else if (Rxjh_Map == 1001 && FLD_PID == 16278)
				{
					World.SoLuong_Item_DropBoss = 50;
				}
				else if (Rxjh_Map == 101 && FLD_PID == 16278)
				{
					World.SoLuong_Item_DropBoss = 100;
				}
				else if (FLD_PID == 16320)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID == 16321)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID >= 16550 && FLD_PID <= 16554)
				{
					World.SoLuong_Item_DropBoss = 15;
				}
				else
				{
					World.SoLuong_Item_DropBoss = 15;
				}
			}
			var rate_Drop_Server = World.Rate_Drop_Server;
			if (playe != null)
			{
				if (playe.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram > 0.0)
				{
					rate_Drop_Server += (int)(rate_Drop_Server * playe.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
				}
				if (playe.QueryThienQuanDiaDoMap(playe.MapID))
				{
					playe.GetTianguanBenefitBonus(1, playe.MapID);
				}
			}
			var bossDrop = DropClass.GetBossDrop(Level);
			if (bossDrop == null)
			{
				LogHelper.WriteLine(LogLevel.Error, "Boss BaoSuat VatPham error：bossdrop == null [Vô giá trị] == :[" + playe.CharacterName + "]");
				return;
			}
			foreach (var item in bossDrop)
			{
				if (item != null)
				{
					switch (item.FLD_PID)
					{
						case ItemDef.Item.HanNgocThach:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC11 = 0;
								var num11 = RNG.Next(1, 100);
								foreach (var item2 in item.DropShuX)
								{
									if (num11 <= item2.Max)
									{
										fLD_MAGIC11 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC11;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.KimCuongThach:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC7 = 0;
								var num7 = RNG.Next(1, 100);
								foreach (var item3 in item.DropShuX)
								{
									if (num7 <= item3.Max)
									{
										fLD_MAGIC7 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC7;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.KimCuongThachCaoCap:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC9 = 0;
								var num9 = RNG.Next(1, 100);
								foreach (var item4 in item.DropShuX)
								{
									if (num9 <= item4.Max)
									{
										fLD_MAGIC9 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC9;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.HanNgocThachCaoCap:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC6 = 0;
								var num6 = RNG.Next(1, 100);
								foreach (var item5 in item.DropShuX)
								{
									if (num6 <= item5.Max)
									{
										fLD_MAGIC6 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC6;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.KimCuongThach_DaKich:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC12 = 0;
								var num12 = RNG.Next(1, 100);
								foreach (var item6 in item.DropShuX)
								{
									if (num12 <= item6.Max)
									{
										fLD_MAGIC12 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC12;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.KimCuongThach_VoCong:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC3 = 0;
								var num3 = RNG.Next(1, 100);
								foreach (var item7 in item.DropShuX)
								{
									if (num3 <= item7.Max)
									{
										fLD_MAGIC3 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC3;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.ThuocTinhThach:
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							break;
						case 800000030:
							item.FLD_MAGIC0 = World.GetValue(800000030, 4);
							break;
						case 800000031:
							item.FLD_MAGIC0 = World.GetValue(800000031, 4);
							break;
						case 800000032:
							item.FLD_MAGIC0 = World.GetValue(800000032, 4);
							break;
						case 800000033:
							item.FLD_MAGIC0 = World.GetValue(800000033, 4);
							break;
						case 800000034:
							item.FLD_MAGIC0 = World.GetValue(800000034, 4);
							break;
						case ItemDef.Item.NhietHuyetThach:
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							break;
						case ItemDef.Item.TrungCapKyNgocThach:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC8 = 0;
								var num8 = RNG.Next(1, 100);
								foreach (var item8 in item.DropShuX)
								{
									if (num8 <= item8.Max)
									{
										fLD_MAGIC8 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC8;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = RNG.Next(23, 51);
							}
							break;
						case ItemDef.Item.SoCapKyNgocThach:
							if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = RNG.Next(1, 22);
							}
							break;
						case ItemDef.TapHonThach.HaCapTapHonChau:
							item.FLD_MAGIC0 = RNG.Next(1001, 2999);
							item.FLD_MAGIC1 = RNG.Next(10, 50);
							break;
						case ItemDef.TapHonThach.TrungCapTapHonCha:
							item.FLD_MAGIC0 = RNG.Next(1001, 2999);
							item.FLD_MAGIC1 = RNG.Next(100, 150);
							break;
						case ItemDef.TapHonThach.ThuongCapTapHonChau:
							item.FLD_MAGIC0 = RNG.Next(1001, 2999);
							item.FLD_MAGIC1 = RNG.Next(400, 699);
							break;
						default:
							{
								if (item.FLD_MAGIC0 != 10)
								{
									break;
								}
								var fLD_MAGIC2 = 0;
								var num2 = RNG.Next(1, 100);
								foreach (var item9 in item.DropShuX)
								{
									if (num2 <= item9.Max)
									{
										fLD_MAGIC2 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC2;
								break;
							}
						case ItemDef.TapHonThach.TuLinhTapHonChau:
							item.FLD_MAGIC0 = RNG.Next(1001, 2999);
							item.FLD_MAGIC1 = RNG.Next(2000, 2499);
							break;
						case ItemDef.Item.HanNgocThachSieuCap:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC13 = 0;
								var num13 = RNG.Next(1, 100);
								foreach (var item10 in item.DropShuX)
								{
									if (num13 <= item10.Max)
									{
										fLD_MAGIC13 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC13;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.HanNgocThachHonNguyen:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC10 = 0;
								var num10 = RNG.Next(1, 100);
								foreach (var item11 in item.DropShuX)
								{
									if (num10 <= item11.Max)
									{
										fLD_MAGIC10 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC10;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.KimCuongThachSieuCap:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC5 = 0;
								var num5 = RNG.Next(1, 100);
								foreach (var item12 in item.DropShuX)
								{
									if (num5 <= item12.Max)
									{
										fLD_MAGIC5 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC5;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case ItemDef.Item.KimCuongThachHonNguyen:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC4 = 0;
								var num4 = RNG.Next(1, 100);
								foreach (var item13 in item.DropShuX)
								{
									if (num4 <= item13.Max)
									{
										fLD_MAGIC4 = RNG.Next(item13.ShuXMin, item13.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC4;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
						case 1000001620:
							if (item.FLD_MAGIC0 == 10)
							{
								var fLD_MAGIC = 0;
								var num = RNG.Next(1, 100);
								foreach (var item14 in item.DropShuX)
								{
									if (num <= item14.Max)
									{
										fLD_MAGIC = RNG.Next(item14.ShuXMin, item14.ShuXMax - 1);
										break;
									}
								}
								item.FLD_MAGIC0 = fLD_MAGIC;
							}
							else if (item.FLD_MAGIC0 == 0)
							{
								item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
							}
							break;
					}
					if (FLD_PID == 16278 && Rxjh_Map == World.ThanVoMon)
					{
						if (playe != null)
						{
							var parcelVacancy = playe.GetParcelVacancy(playe);
							if (parcelVacancy != -1)
							{
								playe.AddItem_ThuocTinh_int(item.FLD_PID, parcelVacancy, 1, item.FLD_MAGIC0, item.FLD_MAGIC1, item.FLD_MAGIC2, item.FLD_MAGIC3, item.FLD_MAGIC4, item.FLD_SoCapPhuHon, item.FLD_TrungCapPhuHon, item.FLD_TienHoa, item.FLD_KhoaLai, 0);
								World.ToanCucNhacNho("Thiên cơ các", 7, "Chúc mừng Đại hiệp [" + playe.CharacterName + "] tiêu diệt Ma Đầu nhận vật phẩm: [" + item.FLD_NAME + "].");
							}
							else
							{
								playe.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
							}
						}
					}
					else if (World.BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu == 1)
					{
						RoiRaVatPham(item, playe);
					}
					else
					{
						RoiRaVatPham(item, playe);
					}
				}
				else
				{
					LogHelper.WriteLine(LogLevel.Error, "BossDropClass item vô giá trị == NULL !!!!!!!!!");
					var string_ = "Lỗi 123 [" + playe.AccountID + "][" + playe.CharacterName + "] 1:[" + FLD_PID + "] 2:[" + NPC_SessionID + "] 3:[" + Rxjh_Map + "] 4:[" + Level + "] 5:[" + Rxjh_Exp + "] 6:[" + bossDrop + "] 7:[" + item + "] 8:[" + playe.TeamID + "]";
					// logo.Log_Drop_BOSS_loi(string_, playe.UserName);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public static void GuiDi_TruocMat_BieuHien_ThaoPhat_PhoBan_QuaiVat(Dictionary<int, NpcClass> NpcList, Players player)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		foreach (var value in NpcList.Values)
		{
			if (value.FLD_PID == 16555 || value.FLD_PID == 16556)
			{
				continue;
			}
			if (value.NPCDeath)
			{
				var array = Converter.HexStringToByte("AA5536008E47012230008E4700000000000000000000000001000000A0400000000000004442000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array, 10, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_X), 0, array, 26, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_Y), 0, array, 34, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.怪物数字), 0, array, 46, 4);
				player.Client?.Send(array, array.Length);
			}
			else
			{
				var array2 = Converter.HexStringToByte("AA5526008E47052220008E47010000000000000034C20000000000006DC300000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array2, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array2, 10, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_X), 0, array2, 18, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_Y), 0, array2, 26, 4);
				player.Client?.Send(array2, array2.Length);
			}
		}
	}

	public void PhoBan_Event_FireDragon_StatusEffect()
	{
		Random random = new(DateTime.Now.Millisecond);
		var num = random.Next(1, 400);
		Rxjh_X = num - 200;
		num = random.Next(1, 400);
		Rxjh_Y = num - 400;
		var array = Converter.HexStringToByte("AA5536008E47012230008E4700000000000000000000000001000000A0400000000000004442000000000000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_X), 0, array, 26, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_Y), 0, array, 34, 4);
		if (FLD_PID == 16607 || FLD_PID == 16557)
		{
			num = random.Next(2, 5);
			System.Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 46, 4);
			怪物数字 = num;
		}
		QuangBaSoLieu(array, array.Length);
		NPCDeath = true;
		// Timer management moved to NPCTimerManager
		AutomaticAttackEnabled = false;
		AutomaticMoveEnabled = true;
		AutoRespawnEnabled = true; // This will set the interval automatically
	}

	public void PhoBan_Event_FireDragon_StatusEffect_KetThuc(bool 是否玩家击杀)
	{
		NPCDeath = false;
		var array = Converter.HexStringToByte("AA5526008E47052220008E47010000000000000034C20000000000006DC300000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_X), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_Y), 0, array, 26, 4);
		QuangBaSoLieu(array, array.Length);
		AutoRespawnEnabled = false;
		if (!是否玩家击杀)
		{
			ThaoPhat_PhoBan_QuaiVat_Time();
		}
	}

	public void ThaoPhat_PhoBan_QuaiVat_Time()
	{
		switch (FLD_PID)
		{
			case 16557:
				{
					List<Players> list = new();
					foreach (var value in World.allConnectedChars.Values)
					{
						if (value.MapID == 43001 && World.是否讨伐副本危险区域(value) && !value.PlayerTuVong && value.副本复活剩余次数 > 0)
						{
							list.Add(value);
						}
					}
					if (list.Count <= 0)
					{
						break;
					}
					for (var i = 0; i < 怪物数字; i++)
					{
						Random random = new(DateTime.Now.Millisecond);
						var index = random.Next(0, list.Count - 1);
						var players = list[index];
						players.NhanVat_HP = 0;
						players.Death();
						PlayCw = null;
						ClearTargetList();
						players.CapNhat_HP_MP_SP();
						list.Remove(players);
						if (list.Count == 0)
						{
							break;
						}
					}
					break;
				}
			case 16600:
				{
					foreach (var value2 in World.allConnectedChars.Values)
					{
						if (!FindPlayers(100, value2) && World.是否讨伐副本危险区域(value2) && value2.副本复活剩余次数 > 0)
						{
							SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
							value2.NhanVat_HP = 0;
							value2.Death();
							PlayCw = null;
							ClearTargetList();
							value2.CapNhat_HP_MP_SP();
							World.讨伐副本添加怪物();
						}
					}
					break;
				}
			case 16602:
				{
					foreach (var value3 in World.allConnectedChars.Values)
					{
						if (FindPlayers(100, value3) && value3.副本复活剩余次数 > 0)
						{
							SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
							value3.NhanVat_HP = 0;
							value3.Death();
							PlayCw = null;
							ClearTargetList();
							value3.CapNhat_HP_MP_SP();
						}
					}
					break;
				}
			case 16604:
				{
					foreach (var value4 in World.allConnectedChars.Values)
					{
						if (FindPlayers(80, value4) && value4.副本复活剩余次数 > 0)
						{
							SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
							value4.NhanVat_HP = 0;
							value4.Death();
							PlayCw = null;
							ClearTargetList();
							value4.CapNhat_HP_MP_SP();
						}
					}
					break;
				}
			case 16607:
				if (NPCDeath)
				{
					break;
				}
				{
					foreach (var value5 in World.allConnectedChars.Values)
					{
						if (World.是否讨伐副本危险区域(value5) && !value5.PlayerTuVong && value5.副本复活剩余次数 > 0)
						{
							SendMovingData(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
							value5.NhanVat_HP = 0;
							value5.Death();
							PlayCw = null;
							ClearTargetList();
							value5.CapNhat_HP_MP_SP();
						}
					}
					break;
				}
		}
	}

	public void BaoSuat_VatPham2(Players play)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			var num = RNG.Next(1, 8000);
			var rate_Drop_Server = World.Rate_Drop_Server;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				rate_Drop_Server = 0;
				return;
			}
			if (play.Player_Level < 159)
			{
				if (play.Player_Level < 159)
				{
					rate_Drop_Server = World.Rate_Drop_Server;
				}
				else
				{
					var num2 = play.Player_Level - Level;
					if (play.Player_Level > Level)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / (0.5 + num2) + 2.0);
					}
					else if (play.Player_Level == Level)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / 2.0);
					}
					else
					{
						var num3 = Level - play.Player_Level;
						rate_Drop_Server = ((num3 == 1) ? ((int)(rate_Drop_Server / World.Drop_Cach_1_Level_Quai)) : ((num3 == 2) ? ((int)(rate_Drop_Server / World.Drop_Cach_2_Level_Quai)) : ((num3 == 3) ? ((int)(rate_Drop_Server / World.Drop_Cach_3_Level_Quai)) : ((num3 == 4) ? ((int)(rate_Drop_Server / World.Drop_Cach_4_Level_Quai)) : ((num3 == 5) ? ((int)(rate_Drop_Server / World.Drop_Cach_5_Level_Quai)) : ((num3 == 6) ? ((int)(rate_Drop_Server / World.Drop_Cach_6_Level_Quai)) : ((num3 == 7) ? ((int)(rate_Drop_Server / World.Drop_Cach_7_Level_Quai)) : ((num3 == 8) ? ((int)(rate_Drop_Server / World.Drop_Cach_8_Level_Quai)) : ((num3 == 9) ? ((int)(rate_Drop_Server / World.Drop_Cach_9_Level_Quai)) : ((num3 >= 10 && num3 <= 14) ? ((int)(rate_Drop_Server / World.Drop_Cach_10_Level_Quai)) : ((num3 >= 15 && num3 <= 19) ? ((int)(rate_Drop_Server / World.Drop_Cach_15_Level_Quai)) : ((num3 == 20) ? ((int)(rate_Drop_Server / World.Drop_Cach_20_Level_Quai)) : 0))))))))))));
					}
					if (play.Player_Level >= 100 && play.Player_Level < 115)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_100_114);
					}
					else if (play.Player_Level >= 115 && play.Player_Level < 120)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_115_119);
					}
					else if (play.Player_Level >= 120 && play.Player_Level < 125)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_120_124);
					}
					else if (play.Player_Level >= 125 && play.Player_Level < 129)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_125_128);
					}
					else if (play.Player_Level >= 129 && play.Player_Level < 131)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_129_130);
					}
				}
			}
			else
			{
				var num4 = play.Player_Level - Level;
				if (play.Player_Level > Level)
				{
					rate_Drop_Server = (int)(rate_Drop_Server / (0.5 + num4) + 3.0);
				}
				else if (play.Player_Level == Level)
				{
					rate_Drop_Server = (int)(rate_Drop_Server / 2.0);
				}
				else
				{
					switch (Level - play.Player_Level)
					{
						case 1:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1);
							break;
						case 2:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2);
							break;
						case 3:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3);
							break;
						case 4:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4);
							break;
						case 5:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5);
							break;
						case 6:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6);
							break;
						case 7:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7);
							break;
						case 8:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8);
							break;
						case 9:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9);
							break;
						case 10:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10);
							break;
						case 11:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11);
							break;
						case 12:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12);
							break;
						case 13:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13);
							break;
						case 14:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14);
							break;
						case 15:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15);
							break;
						case 16:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16);
							break;
						case 17:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17);
							break;
						case 18:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18);
							break;
						case 19:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19);
							break;
						case 20:
							rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20);
							break;
						default:
							rate_Drop_Server = 0;
							return;
					}
				}
			}
			if (play.TitleDrug.ContainsKey(1008001511) || play.TitleDrug.ContainsKey(1008002101))
			{
				rate_Drop_Server = (int)(rate_Drop_Server * World.Bonus_Drop_BanDo_TDKH);
			}
			if (World.KhuLuyenTap_PK_Event != null && play.MapID >= World.KhuLuyenTap1 && play.MapID <= World.KhuLuyenTap9)
			{
				rate_Drop_Server += 1000;
			}
			if (play != null)
			{
				if (play.Player_Level <= World.X2BaoSuat_CapDo_GioiHanCaoNhat)
				{
					rate_Drop_Server *= (int)World.X2CapDo_GioiHanCaoNhat_BoiSo;
				}
				if (play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram > 0.0)
				{
					rate_Drop_Server += (int)(rate_Drop_Server * play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
				}
				if (play.QueryThienQuanDiaDoMap(play.MapID))
				{
					rate_Drop_Server += (int)play.GetTianguanBenefitBonus(1, play.MapID);
				}
			}
			if (num > rate_Drop_Server)
			{
				return;
			}
			DropClass drop;
			if (World.allConnectedChars.TryGetValue(play.SessionID, out var value))
			{
				if (value.FLD_VIP != 0)
				{
					return;
				}
				drop = DropClass.GetDrop(Level);
				if (drop == null)
				{
					return;
				}
			}
			else
			{
				drop = DropClass.GetDrop(Level);
				if (drop == null)
				{
					return;
				}
			}
			switch (drop.FLD_PID)
			{
				case ItemDef.Item.HanNgocThach:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC11 = 0;
						var num15 = RNG.Next(1, 100);
						foreach (var item in drop.DropShuX)
						{
							if (num15 <= item.Max)
							{
								fLD_MAGIC11 = RNG.Next(item.ShuXMin, item.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC11;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.KimCuongThach:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC7 = 0;
						var num11 = RNG.Next(1, 100);
						foreach (var item2 in drop.DropShuX)
						{
							if (num11 <= item2.Max)
							{
								fLD_MAGIC7 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC7;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.KimCuongThachCaoCap:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC9 = 0;
						var num13 = RNG.Next(1, 100);
						foreach (var item3 in drop.DropShuX)
						{
							if (num13 <= item3.Max)
							{
								fLD_MAGIC9 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC9;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.HanNgocThachCaoCap:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC6 = 0;
						var num10 = RNG.Next(1, 100);
						foreach (var item4 in drop.DropShuX)
						{
							if (num10 <= item4.Max)
							{
								fLD_MAGIC6 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC6;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.KimCuongThach_DaKich:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC12 = 0;
						var num16 = RNG.Next(1, 100);
						foreach (var item5 in drop.DropShuX)
						{
							if (num16 <= item5.Max)
							{
								fLD_MAGIC12 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC12;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.KimCuongThach_VoCong:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC3 = 0;
						var num7 = RNG.Next(1, 100);
						foreach (var item6 in drop.DropShuX)
						{
							if (num7 <= item6.Max)
							{
								fLD_MAGIC3 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC3;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.ThuocTinhThach:
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					break;
				case 800000030:
					drop.FLD_MAGIC0 = World.GetValue(800000030, 3);
					break;
				case 800000031:
					drop.FLD_MAGIC0 = World.GetValue(800000031, 3);
					break;
				case 800000032:
					drop.FLD_MAGIC0 = World.GetValue(800000032, 3);
					break;
				case 800000033:
					drop.FLD_MAGIC0 = World.GetValue(800000033, 3);
					break;
				case 800000034:
					drop.FLD_MAGIC0 = World.GetValue(800000034, 3);
					break;
				case ItemDef.Item.NhietHuyetThach:
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					break;
				case ItemDef.Item.TrungCapKyNgocThach:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC8 = 0;
						var num12 = RNG.Next(1, 100);
						foreach (var item7 in drop.DropShuX)
						{
							if (num12 <= item7.Max)
							{
								fLD_MAGIC8 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC8;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = RNG.Next(23, 51);
					}
					break;
				case ItemDef.Item.SoCapKyNgocThach:
					if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = RNG.Next(1, 22);
					}
					break;
				case ItemDef.TapHonThach.HaCapTapHonChau:
					drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
					drop.FLD_MAGIC1 = RNG.Next(10, 50);
					break;
				case ItemDef.TapHonThach.TrungCapTapHonCha:
					drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
					drop.FLD_MAGIC1 = RNG.Next(100, 150);
					break;
				case ItemDef.TapHonThach.ThuongCapTapHonChau:
					drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
					drop.FLD_MAGIC1 = RNG.Next(400, 699);
					break;
				default:
					{
						if (drop.FLD_MAGIC0 != 10)
						{
							break;
						}
						var fLD_MAGIC2 = 0;
						var num6 = RNG.Next(1, 100);
						foreach (var item8 in drop.DropShuX)
						{
							if (num6 <= item8.Max)
							{
								fLD_MAGIC2 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC2;
						break;
					}
				case ItemDef.TapHonThach.TuLinhTapHonChau:
					drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
					drop.FLD_MAGIC1 = RNG.Next(2000, 2499);
					break;
				case ItemDef.Item.HanNgocThachSieuCap:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC13 = 0;
						var num17 = RNG.Next(1, 100);
						foreach (var item9 in drop.DropShuX)
						{
							if (num17 <= item9.Max)
							{
								fLD_MAGIC13 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC13;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.HanNgocThachHonNguyen:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC10 = 0;
						var num14 = RNG.Next(1, 100);
						foreach (var item10 in drop.DropShuX)
						{
							if (num14 <= item10.Max)
							{
								fLD_MAGIC10 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC10;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.KimCuongThachSieuCap:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC5 = 0;
						var num9 = RNG.Next(1, 100);
						foreach (var item11 in drop.DropShuX)
						{
							if (num9 <= item11.Max)
							{
								fLD_MAGIC5 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC5;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case ItemDef.Item.KimCuongThachHonNguyen:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC4 = 0;
						var num8 = RNG.Next(1, 100);
						foreach (var item12 in drop.DropShuX)
						{
							if (num8 <= item12.Max)
							{
								fLD_MAGIC4 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC4;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
				case 1000001620:
					if (drop.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC = 0;
						var num5 = RNG.Next(1, 100);
						foreach (var item13 in drop.DropShuX)
						{
							if (num5 <= item13.Max)
							{
								fLD_MAGIC = RNG.Next(item13.ShuXMin, item13.ShuXMax - 1);
								break;
							}
						}
						drop.FLD_MAGIC0 = fLD_MAGIC;
					}
					else if (drop.FLD_MAGIC0 == 0)
					{
						drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
					}
					break;
			}
			RoiRaVatPham(drop, play);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public void AbnormalStatusList()
	{
		if (TrangThai_BatThuong == null || TrangThai_BatThuong.Count == 0)
		{
			return;
		}
		try
		{
			var queue = Queue.Synchronized(new());
			foreach (var value in TrangThai_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				var x_Di_Thuong_Trang_Thai_Loai = (X_Di_Thuong_Trang_Thai_Loai)queue.Dequeue();
				x_Di_Thuong_Trang_Thai_Loai.ThoiGianKetThucSuKien();
				TrangThai_BatThuong?.Remove(x_Di_Thuong_Trang_Thai_Loai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "MPC TrangThai_BatThuong Danh sách error: [" + NPC_SessionID + "]-[" + Name + "]" + ex.Message);
		}
	}

	public void EndAbnormalBloodDropStatusList()
	{
		if (TrangThai_MatMau_BatThuong == null)
		{
			return;
		}
		var queue = Queue.Synchronized(new());
		try
		{
			foreach (var value in TrangThai_MatMau_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				if (World.jlMsg == 1)
				{
					LogHelper.WriteLine(0, "TrangThai_MatMau_BatThuong Danh sách");
				}
				var x_Di_Thuong_Mat_Mau_Trang_Thai_Loai = (X_Di_Thuong_Mat_Mau_Trang_Thai_Loai)queue.Dequeue();
				x_Di_Thuong_Mat_Mau_Trang_Thai_Loai.ThoiGianKetThucSuKien();
				TrangThai_MatMau_BatThuong?.Remove(x_Di_Thuong_Mat_Mau_Trang_Thai_Loai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "MPC TrangThai_MatMau_BatThuong Liệt kê danh sách error: [" + NPC_SessionID + "]-[" + Name + "]" + ex.Message);
		}
		finally
		{
			queue = null;
		}
	}

	public void GuiDi_QuaiVat_TrenDau_DoTieu(int 是否消失)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "NpcClass_ Gửi đi quái vật trên đầu đồ tiêu");
		}
		var string_ = "AA551200E9478B100C0046050000E94700000100000055AA";
		var array = Converter.HexStringToByte(string_);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(是否消失), 0, array, 18, 4);
		QuangBaSoLieu(array, array.Length);
	}

	public void GuiDi_DameLenNguoi(int CongKichLuc, int NhanVat_ID, int Quai_ID)
	{
		var array = Converter.HexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(Quai_ID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NhanVat_ID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(CongKichLuc), 0, array, 18, 2);
		QuangBaSoLieu(array, array.Length);
	}
	public bool FindPlayerByRange(int far_, Players Playe)
	{
		if (Playe.MapID != Rxjh_Map)
		{
			return false;
		}
		if (Playe.MapID == 7101)
		{
			far_ = 1000;
		}
		var distance = CalculateDistance(Rxjh_X, Rxjh_Y, Playe.PosX, Playe.PosY);
		return distance <= far_;
	}

	public bool FindPet(int far_, X_Linh_Thu_Loai pet)
	{
		if (pet.NhanVatToaDo_MAP != Rxjh_Map)
		{
			return false;
		}
		var distance = CalculateDistance(Rxjh_X, Rxjh_Y, pet.NhanVatToaDo_X, pet.NhanVatToaDo_Y);
		return distance <= far_;
	}
	public bool FindPlayers(int far, Players player)
	{
		try
		{
			// Kiểm tra cơ bản về bản đồ
			if (player.MapID != this.Rxjh_Map)
			{
				return false;
			}

			// Sử dụng optimized distance calculation
			var distance = CalculateDistance(this.Rxjh_X, this.Rxjh_Y, player.PosX, player.PosY);
			return distance <= far;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NpcClass.FindPlayersZone error: {ex.Message}");
			return false;
		}
	}


	public void QuangBaSoLieu(byte[] data, int length)
	{
		try
		{
			ForEachNearbyPlayer(player =>
			{
				if (player.Client != null && player.Client.Running)
				{
					player.Client.Send_Map_Data(data, length);
				}
			});
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC QuangBa SoLieu 333 error2：" + ex);
		}
	}

	#region NpcManager Integration Methods

	/// <summary>
	/// Calculate distance between two points
	/// </summary>
	private double CalculateDistance(float x1, float y1, float x2, float y2)
	{
		var dx = x1 - x2;
		var dy = y1 - y2;
		return Math.Sqrt(dx * dx + dy * dy);
	}


	/// <summary>
	/// Process automatic respawn (called by NpcManager)
	/// </summary>
	public void ProcessAutomaticRespawn()
	{
		try
		{
			// Call the existing respawn logic
			RespawnEvent(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"ProcessAutomaticRespawn error for NPC {NPC_SessionID}: {ex.Message}");
		}
	}

	/// <summary>
	/// Optimized player scanning using AOI system
	/// </summary>
	public void OptimizedScanNearbyPlayers()
	{
		try
		{
			// Always use AOI system for better performance
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);

			foreach (var grid in aoiGrids)
			{
				// Use optimized ForEach method from our improved AOIGrid
				grid.ForEachPlayer(player =>
				{
					var distance = CalculateDistance(Rxjh_X, Rxjh_Y, player.PosX, player.PosY);
					if (distance <= 400)
					{
						player.GetReviewScopeNpc();
					}
				});
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "OptimizedScanNearbyPlayers Error: " + ex);

			// Fallback to old system if AOI fails
			try
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (FindPlayers(400, value))
					{
						value.GetReviewScopeNpc();
					}
				}
			}
			catch (Exception fallbackEx)
			{
				LogHelper.WriteLine(LogLevel.Error, "Fallback player scan also failed: " + fallbackEx);
			}
		}
	}



	#endregion
	#region AOI Grid Helper Methods (Replacing PlayList functionality)

	/// <summary>
	/// Get all players within NPC's awareness range using AOI Grid system
	/// Replaces PlayList.Values enumeration
	/// </summary>
	/// <param name="range">Detection range (default: chase range)</param>
	/// <returns>List of players within range</returns>
	public List<Players> GetNearbyPlayers(double range = 0)
	{
		// CRITICAL FIX: Use AOI radius as default to match visibility system
		if (range <= 0) range = AOISystem.AOI_RADIUS; // Use AOI radius (512) instead of 300
		var nearbyPlayers = new List<Players>();

		try
		{
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);

			foreach (var grid in aoiGrids)
			{
				foreach (var player in grid.GetPlayers())
				{
					if (player != null && player.Client?.Running == true &&
						player.NhanVat_HP > 0 && !player.PlayerTuVong &&
						player.MapID == Rxjh_Map && player.GMMode != 8)
					{
						// ENHANCED: Use AOI-aware distance calculation for consistency
						var distance = Math.Sqrt(Math.Pow(player.PosX - Rxjh_X, 2) + Math.Pow(player.PosY - Rxjh_Y, 2));
						if (distance <= range)
						{
							nearbyPlayers.Add(player);

							// if (AOI.// AOIConfiguration.Instance. EnableDebugLogging)
							// {
							//     LogHelper.WriteLine(LogLevel.Debug, $"NPC {NPC_SessionID} found nearby player {player.CharacterName} at distance {distance:F1}");
							// }
						}
					}
				}
			}

			// if (AOI.// AOIConfiguration.Instance. EnableDebugLogging && nearbyPlayers.Count>0)
			// {
			//     LogHelper.WriteLine(LogLevel.Debug, $"NPC {NPC_SessionID} GetNearbyPlayers found {nearbyPlayers.Count} players within range {range}");
			// }
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Error in GetNearbyPlayers for NPC {NPC_SessionID}: {ex.Message}");
		}

		return nearbyPlayers;
	}

	/// <summary>
	/// Check if a specific player is within NPC's awareness range
	/// Replaces PlayList.ContainsKey functionality
	/// </summary>
	/// <param name="player">Player to check</param>
	/// <param name="range">Detection range (default: chase range)</param>
	/// <returns>True if player is within range</returns>
	public bool IsPlayerInRange(Players player, double range = 0)
	{
		if (player == null || player.MapID != Rxjh_Map ||
			player.Client?.Running != true || player.NhanVat_HP <= 0 ||
			player.PlayerTuVong || player.GMMode == 8)
			return false;

		// CRITICAL FIX: Use AOI radius as default to match visibility system
		if (range <= 0) range = AOISystem.AOI_RADIUS; // Use AOI radius (512) instead of 300

		var distance = Math.Sqrt(Math.Pow(player.PosX - Rxjh_X, 2) + Math.Pow(player.PosY - Rxjh_Y, 2));
		return distance <= range;
	}

	/// <summary>
	/// Get count of players within NPC's awareness range
	/// Replaces PlayList.Count functionality
	/// </summary>
	/// <param name="range">Detection range (default: chase range)</param>
	/// <returns>Number of players within range</returns>
	public int GetNearbyPlayersCount(double range = 0)
	{
		return GetNearbyPlayers(range).Count;
	}

	/// <summary>
	/// Find the closest player within range
	/// Enhanced version of PlayList target finding
	/// </summary>
	/// <param name="range">Detection range (default: chase range)</param>
	/// <returns>Closest player or null if none found</returns>
	public Players GetClosestPlayer(double range = 0)
	{
		if (range <= 0) range = 300; // Default chase range

		Players closestPlayer = null;
		double closestDistance = double.MaxValue;

		foreach (var player in GetNearbyPlayers(range))
		{
			var distance = Math.Sqrt(Math.Pow(player.PosX - Rxjh_X, 2) + Math.Pow(player.PosY - Rxjh_Y, 2));
			if (distance < closestDistance)
			{
				closestDistance = distance;
				closestPlayer = player;
			}
		}

		return closestPlayer;
	}

	/// <summary>
	/// Execute action for each player within range
	/// Replaces foreach (var player in PlayList.Values) patterns
	/// </summary>
	/// <param name="action">Action to execute for each player</param>
	/// <param name="range">Detection range (default: chase range)</param>
	public void ForEachNearbyPlayer(Action<Players> action, double range = 0)
	{
		if (action == null) return;

		foreach (var player in GetNearbyPlayers(range))
		{
			try
			{
				action(player);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error executing action for player {player.SessionID} in NPC {NPC_SessionID}: {ex.Message}");
			}
		}
	}

	/// <summary>
	/// Comprehensive NPC respawn notification system
	/// Ensures all nearby players are properly notified about NPC respawn
	/// </summary>
	private void NotifyNearbyPlayersOfRespawn()
	{
		try
		{
			var notifiedPlayers = 0;
			var spawnDict = new Dictionary<int, NpcClass> { { NPC_SessionID, this } };

			// if (AOI.// AOIConfiguration.Instance. ShouldUseAOI(Rxjh_Map))
			// {
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);

			foreach (var grid in aoiGrids)
			{
				grid.ForEachPlayer(player =>
				{
					if (ShouldNotifyPlayer(player))
					{
						try
						{
							// Method 1: Direct spawn packet
							NpcClass.UpdateNPC_Spawn(spawnDict, player);

							// Method 2: Add to player's NpcList and refresh
							if (!player.NearbyNpcs.ContainsKey(NPC_SessionID))
							{
								player.NearbyNpcs.TryAdd(NPC_SessionID, this);
							}

							// Method 3: Force AOI refresh for this player
							player.GetReviewScopeNpc();

							notifiedPlayers++;
							LogHelper.WriteLine(LogLevel.Info, $"Notified player {player.CharacterName} about NPC {Name} respawn (AOI)");
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, $"Error notifying player {player.SessionID} about NPC respawn: {ex.Message}");
						}
					}
				});
			}
			// }
			// else
			// {
			//     // Fallback for non-AOI maps
			//     foreach (var player in World.allConnectedChars.Values)
			//     {
			//         if (ShouldNotifyPlayer(player))
			//         {
			//             var distance = Math.Sqrt(Math.Pow(player.PosX - Rxjh_X, 2) + Math.Pow(player.PosY - Rxjh_Y, 2));
			//             if (distance <= 400) // Visibility range
			//             {
			//                 try
			//                 {
			//                     NpcClass.UpdateNPC_Spawn(spawnDict, player);

			//                     if (!player.NpcList.ContainsKey(NPC_SessionID))
			//                     {
			//                         player.NpcList.TryAdd(NPC_SessionID, this);
			//                     }

			//                     player.GetReviewScopeNpc();
			//                     notifiedPlayers++;
			//                     LogHelper.WriteLine(LogLevel.Info, $"Notified player {player.CharacterName} about NPC {Name} respawn (fallback)");
			//                 }
			//                 catch (Exception ex)
			//                 {
			//                     LogHelper.WriteLine(LogLevel.Error, $"Error notifying player {player.SessionID} about NPC respawn (fallback): {ex.Message}");
			//                 }
			//             }
			//         }
			//     }
			// }

			LogHelper.WriteLine(LogLevel.Info, $"NPC {Name} respawn notification completed. Notified {notifiedPlayers} players.");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Error in NotifyNearbyPlayersOfRespawn for NPC {NPC_SessionID}: {ex.Message}");
		}
	}

	/// <summary>
	/// Check if a player should be notified about NPC respawn
	/// </summary>
	private bool ShouldNotifyPlayer(Players player)
	{
		return player != null &&
			   player.Client?.Running == true &&
			   player.MapID == Rxjh_Map &&
			   player.NhanVat_HP > 0 &&
			   !player.PlayerTuVong &&
			   player.GMMode != 8; // GM mode players don't need NPC notifications
	}

	/// <summary>
	/// Trigger AOI update for nearby players to fix visibility issues
	/// </summary>
	public void TriggerAOIUpdateForNearbyPlayers()
	{
		try
		{
			// if (AOI.// AOIConfiguration.Instance. ShouldUseAOI(Rxjh_Map))
			// {
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);

			foreach (var grid in aoiGrids)
			{
				grid.ForEachPlayer(player =>
				{
					if (player != null && player.Client?.Running == true &&
						player.MapID == Rxjh_Map)
					{
						try
						{
							// Force AOI update for this player
							player.GetReviewScopeNpc();
							LogHelper.WriteLine(LogLevel.Info, $"Triggered AOI update for player {player.CharacterName} due to NPC {Name} respawn");
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, $"Error triggering AOI update for player {player.SessionID}: {ex.Message}");
						}
					}
				});
			}
			// }
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Error in TriggerAOIUpdateForNearbyPlayers for NPC {NPC_SessionID}: {ex.Message}");
		}
	}

	#endregion
}

public enum NpcStageType
{
	Idle,
	Summon,
	ApplyEffects,
	Attack
}

public class NpcStageManager
{
	public NpcStageType CurrentStage { get; private set; } = NpcStageType.Idle;
	public NpcStageType NextStage { get; private set; } = NpcStageType.Idle;
	public int CurrentBossStage { get; private set; } = 0;
	public bool IsProcessingStage { get; private set; } = false;

	public void StartStage(NpcStageType stageType, int bossStage)
	{
		CurrentStage = stageType;
		CurrentBossStage = bossStage;
		IsProcessingStage = true;
	}

	public void SetNextStage(NpcStageType nextStage)
	{
		NextStage = nextStage;
	}

	public void CompleteCurrentStage()
	{
		IsProcessingStage = false;
		CurrentStage = NextStage;
		NextStage = NpcStageType.Idle;
	}

	public void Reset()
	{
		CurrentStage = NpcStageType.Idle;
		NextStage = NpcStageType.Idle;
		CurrentBossStage = 0;
		IsProcessingStage = false;
	}

	public bool ShouldTriggerStage(double hpPercent, int targetStage)
	{
		return !IsProcessingStage && CurrentBossStage < targetStage && hpPercent <= GetStageHpThreshold(targetStage);
	}

	private double GetStageHpThreshold(int stage)
	{
		return stage switch
		{
			1 => 75.0,
			2 => 25.0,
			_ => 0.0
		};
	}
}
