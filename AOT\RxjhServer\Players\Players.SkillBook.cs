﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    

	public void XoaBo_BaLo_VatPham(byte[] packetData, int packetSize)
	{
		var num = -1;
		try
		{
			num = packetData[11];
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Xóa Cltr-Alt-Click chuột !!!");
		}
		if (num >= 0 && num <= 95)
		{
			var xVatPhamLoai = Item_In_Bag[num];
			if (xVatPhamLoai != null && World.Check_VuKhi_Hero(Item_In_Bag[num].GetVatPham_ID) == 0)
			{
				HeThongNhacNho("Xóa bỏ ô [" + num + "] thành công, giang hồ nhẹ gánh!", 10, "Thiên cơ các");
				SubtractItem(num, xVatPhamLoai.GetVatPhamSoLuong);
				var text = "[" + AccountID + "] [xoa " + num + "] Item:[" + Item_In_Bag[num].GetVatPham_ID + "] CH SL:[" + Item_In_Bag[num].FLD_CuongHoaSoLuong + "] D0:[" + Item_In_Bag[num].FLD_MAGIC0 + "] D1:[" + Item_In_Bag[num].FLD_MAGIC1 + "] D2:[" + Item_In_Bag[num].FLD_MAGIC2 + "] D3:[" + Item_In_Bag[num].FLD_MAGIC3 + "] D4:[" + Item_In_Bag[num].FLD_MAGIC4 + "] TT SL:[" + Item_In_Bag[num].FLDThuocTinhSoLuong + "] TT L:[" + Item_In_Bag[num].FLDThuocTinhLoaiHinh + "] TuLinh:[" + Item_In_Bag[num].FLD_TuLinh + "] SoulHa:[" + Item_In_Bag[num].FLD_FJ_LowSoul + "] SoulTrung:[" + Item_In_Bag[num].FLD_FJ_TrungCapPhuHon + "] TienHoa:[" + Item_In_Bag[num].FLD_FJ_TienHoa + "] Ngay:[" + Item_In_Bag[num].FLD_FJ_NJ + "] Lock:[" + Item_In_Bag[num].VatPham_KhoaLai + "]";
				// logo.Log_Xoa_Ctrl_Alt_ClickChuot(text, UserName);
			}
			else
			{
				HeThongNhacNho("Không thể xóa thần binh phong ấn, bảo vật này quá quý giá!!", 7, "Thiên cơ các");
				HeThongNhacNho("Dùng bí lệnh !xoatatca 0 (đặt vào ô đầu tiên của hành trang)", 10, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Hành trang vượt giới hạn, đại hiệp cần dọn dẹp!!", 10, "Thiên cơ các");
		}
	}

	public void SwitchPkMode(int pk)
	{
		var array = Converter.HexStringToByte("AA550800930416100200020055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(pk), 0, array, 10, 1);
		CharacterPKMode = pk;
		World.NguongSo_NhipTim = 5;
		Client?.Send_Map_Data(array, array.Length);
	}


	public void AbnormalStatusList()
	{
		try
		{
			if (TrangThai_BatThuong == null || TrangThai_BatThuong.Count <= 0)
			{
				return;
			}
			foreach (var value in TrangThai_BatThuong.Values)
			{
				value.ThoiGianKetThucSuKien();
				TrangThai_BatThuong.Remove(value.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TrangThai_BatThuong Liệt kê danh sách error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void ClearTheNewListOfAdditionalStatus()
	{
		if (AppendStatusNewList == null || AppendStatusNewList.Count == 0)
		{
			return;
		}
		try
		{
			foreach (var value in AppendStatusNewList.Values)
			{
				AppendStatusNewList.Remove(value.FLD_PID);
				value.ThoiGianKetThucSuKien();
				value.Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Append Status New List error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void ClearTheAppendStatusList()
	{
		if (AppendStatusList == null || AppendStatusList.Count == 0)
		{
			return;
		}
		var queue = Queue.Synchronized(new Queue());
		try
		{
			foreach (var value in AppendStatusList.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				var xThemVaoTrangThaiLoai = (StatusEffect)queue.Dequeue();
				AppendStatusList.Remove(xThemVaoTrangThaiLoai.FLD_PID);
				xThemVaoTrangThaiLoai.ThoiGianKetThucSuKien();
				xThemVaoTrangThaiLoai.Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Trống Append Status List error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}


	public void Send_Nhieu_Packet_PhamVi_HienTai(byte[] data, int length)
	{
		try
		{
			// Sử dụng Grid-based broadcasting thay vì NearbyPlayers cache
			var grids = AOI.AOISystem.Instance.GetNearbyGrids(MapID, PosX, PosY);
			foreach (var grid in grids)
			{
				grid.ForEachPlayer(nearbyPlayer =>
				{
					if (nearbyPlayer.SessionID != SessionID &&
						nearbyPlayer?.Client?.Running == true &&
						!nearbyPlayer.Client.TreoMay &&
						nearbyPlayer.IsJoinWorld &&
						AOI.AOISystem.Instance.IsWithinAOIRange(PosX, PosY, nearbyPlayer.PosX, nearbyPlayer.PosY))
					{
						nearbyPlayer.Client.SendMultiplePackage(data, length);
					}
				});
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Gửi nhiều gói dữ liệu phát sóng phạm vi hiện tại 222 ERROR: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}


	public void Packet_TuyetRoi()
	{
		try
		{
			if (World.Time_Auto_Mo_Tuyet_Roi == 1)
			{
				if (DateTime.Now.Hour == 0)
				{
					World.WorldTime = 15500;
				}
				if (DateTime.Now.Hour == 1)
				{
					World.WorldTime = 16000;
				}
				if (DateTime.Now.Hour >= 2 && DateTime.Now.Hour <= 4)
				{
					World.WorldTime = 16500;
				}
				else if (DateTime.Now.Hour == 5)
				{
					World.WorldTime = 17000;
				}
				else if (DateTime.Now.Hour == 6)
				{
					World.WorldTime = 18000;
				}
				else if (DateTime.Now.Hour == 7)
				{
					World.WorldTime = 19000;
				}
				else if (DateTime.Now.Hour == 8)
				{
					World.WorldTime = 1000;
				}
				else if (DateTime.Now.Hour == 9)
				{
					World.WorldTime = 3000;
				}
				else if (DateTime.Now.Hour == 10)
				{
					World.WorldTime = 5000;
				}
				else if (DateTime.Now.Hour == 11)
				{
					World.WorldTime = 7000;
				}
				else if (DateTime.Now.Hour == 12)
				{
					World.WorldTime = 8000;
				}
				else if (DateTime.Now.Hour == 13)
				{
					World.WorldTime = 9000;
				}
				else if (DateTime.Now.Hour == 14)
				{
					World.WorldTime = 10000;
				}
				else if (DateTime.Now.Hour == 15)
				{
					World.WorldTime = 11000;
				}
				else if (DateTime.Now.Hour == 16)
				{
					World.WorldTime = 11500;
				}
				else if (DateTime.Now.Hour == 17)
				{
					World.WorldTime = 12000;
				}
				else if (DateTime.Now.Hour == 18)
				{
					World.WorldTime = 12500;
				}
				else if (DateTime.Now.Hour == 19)
				{
					World.WorldTime = 13000;
				}
				else if (DateTime.Now.Hour == 20)
				{
					World.WorldTime = 13500;
				}
				else if (DateTime.Now.Hour == 21)
				{
					World.WorldTime = 14000;
				}
				else if (DateTime.Now.Hour == 22)
				{
					World.WorldTime = 14500;
				}
				else if (DateTime.Now.Hour == 23)
				{
					World.WorldTime = 15000;
				}
				World.WorldTime++;
			}
			var worldTime = World.WorldTime;
			var text = "AA550A00000080000400D007000055AA";
			var text2 = "AA550A00000080000400D107000055AA";
			var array = Converter.HexStringToByte(text);
			var array2 = Converter.HexStringToByte(text2);
			if (World.CoMoRa_TuyetRoiHayKhong == 1)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 13, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array2, 13, 1);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 13, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array2, 13, 1);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(worldTime), 0, array, 10, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(worldTime++), 0, array2, 10, 2);
			Client?.Send_Map_Data(array, array.Length);
			Client?.Send_Map_Data(array2, array2.Length);
		}
		catch
		{
		}
	}


	public void Hop_Auto(byte[] data, int length)
	{
		var flag = data[10];
		if (flag == 1)
		{
			// Kiểm tra thùng đồ xem có auto hay không
			// Xử lý auto
			Tao_DangXaiAuto = true;
		}
		else
		{
			Tao_DangXaiAuto = false;
		}
	}

	public void DragonMove(byte[] packetData, int packetSize)
	{
		try
		{
			if (CharacterBeast == null || CharacterBeast.ThuBay == 0 || PlayerTuVong || TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(27))
			{
				return;
			}
			var array = new byte[4];
			var array2 = new byte[4];
			var dst = new byte[4];
			var array3 = new byte[4];
			var array4 = new byte[4];
			var dst2 = new byte[4];
			var dst3 = new byte[4];
			var dst4 = new byte[4];
			var dst5 = new byte[4];
			var array5 = new byte[4];
			Buffer.BlockCopy(packetData, 4, array5, 0, 2);
			Buffer.BlockCopy(packetData, 10, dst4, 0, 4);
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			Buffer.BlockCopy(packetData, 18, dst, 0, 4);
			Buffer.BlockCopy(packetData, 22, array2, 0, 4);
			Buffer.BlockCopy(packetData, 26, array3, 0, 4);
			Buffer.BlockCopy(packetData, 30, dst2, 0, 4);
			Buffer.BlockCopy(packetData, 34, array4, 0, 4);
			Buffer.BlockCopy(packetData, 42, dst3, 0, 4);
			Buffer.BlockCopy(packetData, 46, dst5, 0, 4);
			// var num = BitConverter.ToInt32(array5, 0);
			// var num2 = BitConverter.ToSingle(array, 0);
			// var num3 = BitConverter.ToSingle(array2, 0);
			// var num4 = BitConverter.ToSingle(array3, 0);
			// var num5 = BitConverter.ToSingle(array4, 0);
			// var num6 = num2 - NhanVatToaDo_X;
			// var num7 = num3 - NhanVatToaDo_Y;
			if (CuaHangCaNhan != null || PlayerTuVong || NhanVat_HP <= 0 || Exiting || GiaoDich.GiaoDichBenTrong ||
				InTheShop) return;
			if (TriggerMapMovementEvent)
			{
				_yxsl = 0;
			}
			var num8 = BitConverter.ToSingle(array3, 0);
			var num9 = BitConverter.ToSingle(array4, 0);
			TocDo_TinhToan(num8, num9);
			_toaDoCuoiCungX = num8;
			_toaDoCuoiCungY = num9;
			PKTuVong = false;
			Player_VoDich = false;
			if (AutomaticRecovery != null)
			{
				AutomaticRecovery.Enabled = false;
				AutomaticRecovery.Close();
				AutomaticRecovery.Dispose();
				AutomaticRecovery = null;
			}
			var array6 = new byte[packetSize];
			Buffer.BlockCopy(packetData, 0, array6, 0, packetSize);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array6, 4, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(30f), 0, array6, 46, 4);
			array6[6] = 101;
			array6[7] = 0;
			SendCurrentRangeBroadcastData(array6, array6.Length);
			AOIExtensions.UpdateAOIPosition(this, num8, num9, MapID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Rồng V20 di chuyển lỗi : [" + AccountID + "]-[" + CharacterName + "]-[" + Converter.ToString(packetData) + "]-[" + ex.Message);
		}
	}

	public void LogoEmoji(byte[] packetData, int packetSize)
	{
		var array = new byte[4];
		Buffer.BlockCopy(packetData, 14, array, 0, 4);
		LogoEmoji(World.FindPlayerBySession(BitConverter.ToInt32(array, 0)), packetData[18]);
	}


	public void LogoEmoji(Players play, int id)
	{
		var array = Converter.HexStringToByte("AA5512008100E0040C0081000000A30400000200000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 18, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(play.SessionID), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void GiftRoses(Players nguoiGui, Players nguoiNhan, int hoaHongSoLuong, string msg)
	{
		try
		{
			CreateBiography(nguoiGui, nguoiNhan.CharacterName, 0, msg, 3, hoaHongSoLuong).GetAwaiter().GetResult();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GiftRoes error : " + ex.Message);
		}
	}

	public void GiftRoses(byte[] packetData, int lenght)
	{
		PacketModification(packetData, lenght);
		if (!FLD_canYouSendFlowers)
		{
			HeThongNhacNho("Mỗi lần tặng hoa phải cách nhau " + World.TreoMayBanThuong_ThoiGianChuKyLapLai + " khắc, đại hiệp hãy kiên nhẫn!");
			return;
		}
		if (FLD_Couple.Length == 0)
		{
			HeThongNhacNho("Hoa hồng chỉ dành cho đôi lứa hữu tình giữa lễ vật giang hồ!");
			return;
		}
		if (GiaiTruQuanHe_Countdown != 0)
		{
			CoupleTips(53, CharacterName, FLD_Couple);
			return;
		}
		var array = new byte[14];
		var array2 = new byte[60];
		Buffer.BlockCopy(packetData, 33, array2, 0, 60);
		Buffer.BlockCopy(packetData, 11, array, 0, 14);
		var @string = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
		var msg = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
		var num = BitConverter.ToInt32(packetData, 244);
		int num2 = packetData[252];
		var characterData = GetCharacterData(@string);
		if (num == 0 || (num != 1008001382 && num != 1008001383 && num != 1008001384 && num != 1008001385) || Item_In_Bag[num2].GetVatPham_ID != num || Item_In_Bag[num2].GetVatPhamSoLuong <= 0)
		{
			return;
		}
		if (characterData != null)
		{
			if (FLD_Couple != characterData.CharacterName)
			{
				HeThongNhacNho("Đại hiệp tặng hoa cho người khác, liệu ý trung nhân có biết chăng?");
				return;
			}
			FLD_canYouSendFlowers = false;
			var num3 = num switch
			{
				1008001382 => 9,
				1008001383 => 99,
				1008001384 => 999,
				1008001385 => 9999,
				_ => 0
			};
			var num4 = RNG.Next(10, 30);
			if (CoupleInTeam)
			{
				num4 *= 2;
			}
			var num5 = num4 * num3;
			ItemUse(1, num2, 1);
			CapNhatXepHang_HoaHong(this, characterData, num3);
			FLD_Couple_Love += num5;
			characterData.FLD_Couple_Love += num5;
			characterData.RoseTitlePoints += num3;
			characterData.ObtainTheAttributeOfTheRoseTitle();
			GiftRoses(this, characterData, num3, msg);
			if (num3 >= 999)
			{
				if ((DateTime.Now.Month == 2 && DateTime.Now.Day == 14) || (DateTime.Now.Month == 7 && DateTime.Now.Day == 7))
				{
					World.GuiThongBao(CharacterName + "  Hướng  " + characterData.CharacterName + " Đưa" + num3 + "Đóa lễ tình nhân hoa hồng, thật hi vọng cùng ngươi mãi mãi cho đến già, để ngươi làm ta hồi tâm bên trong bảo. Lễ tình nhân nhanh cầm sư!");
				}
				else
				{
					World.GuiThongBao(CharacterName + "  Hướng  " + characterData.CharacterName + " Đưa" + num3 + "Đóa hoa hồng, thật hi vọng cùng ngươi mãi mãi cho đến già, để ngươi làm ta hồi tâm bên trong bảo.");
				}
			}
			switch (FLD_Couple_Love)
			{
				case >= 35000:
					FLD_Couple_Love = 35000;
					FLD_loveDegreeLevel = 1;
					break;
				case > 30000:
					{
						if (FLD_loveDegreeLevel == 2)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 1;
						break;
					}
				case > 21000:
					{
						if (FLD_loveDegreeLevel == 3)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 2;
						break;
					}
				case > 14700:
					{
						if (FLD_loveDegreeLevel == 4)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 3;
						break;
					}
				case > 10290:
					{
						if (FLD_loveDegreeLevel == 5)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 4;
						break;
					}
				case > 7203:
					{
						if (FLD_loveDegreeLevel == 6)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 5;
						break;
					}
				case > 5042:
					{
						if (FLD_loveDegreeLevel == 7)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 6;
						break;
					}
				case > 3025:
					{
						if (FLD_loveDegreeLevel == 8)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 7;
						break;
					}
				case > 1513:
					{
						if (FLD_loveDegreeLevel == 9)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 8;
						break;
					}
				case > 605:
					{
						if (FLD_loveDegreeLevel == 10)
						{
							CoupleTips(27, CharacterName, characterData.CharacterName);
						}
						FLD_loveDegreeLevel = 9;
						break;
					}
				default:
					FLD_loveDegreeLevel = 10;
					break;
			}

			switch (characterData.FLD_Couple_Love)
			{
				case >= 35000:
					characterData.FLD_Couple_Love = 35000;
					characterData.FLD_loveDegreeLevel = 1;
					break;
				case > 30000:
					{
						if (characterData.FLD_loveDegreeLevel == 2)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 1;
						break;
					}
				case > 21000:
					{
						if (characterData.FLD_loveDegreeLevel == 3)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 2;
						break;
					}
				case > 14700:
					{
						if (characterData.FLD_loveDegreeLevel == 4)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 3;
						break;
					}
				case > 10290:
					{
						if (characterData.FLD_loveDegreeLevel == 5)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 4;
						break;
					}
				case > 7203:
					{
						if (characterData.FLD_loveDegreeLevel == 6)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 5;
						break;
					}
				case > 5042:
					{
						if (characterData.FLD_loveDegreeLevel == 7)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 6;
						break;
					}
				case > 3025:
					{
						if (characterData.FLD_loveDegreeLevel == 8)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 7;
						break;
					}
				case > 1513:
					{
						if (characterData.FLD_loveDegreeLevel == 9)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 8;
						break;
					}
				case > 605:
					{
						if (characterData.FLD_loveDegreeLevel == 10)
						{
							characterData.CoupleTips(27, characterData.CharacterName, CharacterName);
						}
						characterData.FLD_loveDegreeLevel = 9;
						break;
					}
				default:
					characterData.FLD_loveDegreeLevel = 10;
					break;
			}
			CoupleTips(29, CharacterName, characterData.CharacterName);
			characterData.CoupleTips(29, characterData.CharacterName, CharacterName);
			CoupleTips(7, CharacterName, characterData.CharacterName);
			characterData.CoupleTips(7, CharacterName, characterData.CharacterName);
			if (characterData.AppendStatusList != null)
			{
				if (characterData.GetAddState(1008001382))
				{
					characterData.AppendStatusList[1008001382].ThoiGianKetThucSuKien();
				}
				if (characterData.GetAddState(1008001383))
				{
					characterData.AppendStatusList[1008001383].ThoiGianKetThucSuKien();
				}
				if (characterData.GetAddState(1008001384))
				{
					characterData.AppendStatusList[1008001384].ThoiGianKetThucSuKien();
				}
				if (characterData.GetAddState(1008001385))
				{
					characterData.AppendStatusList[1008001385].ThoiGianKetThucSuKien();
				}
			}
			else
			{
				characterData.AppendStatusList = new ThreadSafeDictionary<int, StatusEffect>();
			}
			var num6 = World.TreoMayBanThuong_ThoiGianChuKyLapLai * 1000 * 60;
			int thoiGian;
			characterData.StatusEffect(BitConverter.GetBytes(num), 1, thoiGian = num6 + 3000);
			StatusEffect xThemVaoTrangThaiLoai = new(characterData, thoiGian, num, 0);
			characterData.AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
			switch (characterData.FLD_loveDegreeLevel)
			{
				case 1:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 15;
					characterData.FLD_NhanVat_ThemVao_PhongNgu += 15;
					characterData.FLD_NhanVat_ThemVao_KhiCong++;
					characterData.FLD_NhanVat_ThemVao_PhanTramKinhNghiem += 0.05;
					characterData.UpdateKhiCong();
					break;
				case 2:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 15;
					characterData.FLD_NhanVat_ThemVao_PhongNgu += 15;
					characterData.FLD_NhanVat_ThemVao_KhiCong++;
					characterData.UpdateKhiCong();
					break;
				case 3:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 15;
					characterData.FLD_NhanVat_ThemVao_PhongNgu += 15;
					break;
				case 4:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 10;
					characterData.FLD_NhanVat_ThemVao_PhongNgu += 10;
					break;
				case 5:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 10;
					characterData.FLD_NhanVat_ThemVao_PhongNgu += 5;
					break;
				case 6:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 5;
					characterData.FLD_NhanVat_ThemVao_PhongNgu += 5;
					break;
				case 7:
					characterData.CharactersToAddMax_HP += 150;
					characterData.FLD_NhanVat_ThemVao_CongKich += 5;
					break;
				case 8:
					characterData.CharactersToAddMax_HP += 150;
					break;
				case 9:
					characterData.CharactersToAddMax_HP += 100;
					break;
				case 10:
					characterData.CharactersToAddMax_HP += 50;
					break;
			}
			UpdateCoupleSystem(2, characterData.CharacterName, NhanCuoiKhacChu, GiaiTruQuanHe_Countdown, DateTime.Now);
			characterData.UpdateCoupleSystem(2, CharacterName, characterData.NhanCuoiKhacChu, characterData.GiaiTruQuanHe_Countdown, DateTime.Now);
			characterData.UpdateMartialArtsAndStatus();
			characterData.CapNhat_HP_MP_SP();
		}
		else
		{
			CoupleTips(18, CharacterName, FLD_Couple);
		}
	}

	public void WarehousePharmacy(byte[] packetData, int length)
	{
		if (NhanVat_HP <= 0 || PlayerTuVong || Exiting || GiaoDich.GiaoDichBenTrong || OpenWarehouse || InTheShop || CuaHangCaNhan != null)
		{
			return;
		}
		PacketModification(packetData, length);
		switch (packetData[11])
		{
			case 1:
				if (Player_Money >= 1000000)
				{
					Player_Money -= 1000000L;
					UpdateMoneyAndWeight();
					OpenStore(1, 1, 0);
					break;
				}
				HeThongNhacNho("Ngân lượng trong tay không đủ, mỗi lần mở kho tùy thân cần 100 vạn phí thủ tục!");
				return;
			case 0:
				OpenStore(3, 1, 0);
				break;
		}
		var array = Converter.HexStringToByte("AA5513002C01BE0005000201000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void CollectMartialArtsProperty(byte[] packetData, int length)
	{
		PacketModification(packetData, length);
		BitConverter.ToInt64(packetData, 14);
	}

	public async Task PhanPhoi_BangPhaiVoHuan(byte[] packetData, int length)
	{
		PacketModification(packetData, length);
		var num = BitConverter.ToInt32(packetData, 14);
		if (num < 0)
		{
			return;
		}
		var num2 = BitConverter.ToInt32(packetData, 18);
		var array = new byte[14];
		Buffer.BlockCopy(packetData, 22, array, 0, 14);
		var characterData = GetCharacterData(Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim());
		if (characterData != null)
		{
			var dataTable = await GameDb.FindGuild(GuildName);
			if (dataTable != null)
			{
				var num3 = (int)dataTable.bangphaivohuan;
				if (num3 < num)
				{
					HeThongNhacNho("Bang phái của đại hiệp không đủ Võ Huân để phân phối!");
					return;
				}
				int num4 = num3 - num;
				// characterData.Player_WuXun += num;
				characterData.UpdateHonorPoint(num);
				characterData.UpdateMartialArtsAndStatus();
				//DBA.ExeSqlCommand(string.Format("UPDATE TBL_XWWL_Guild SET BangPhaiVoHuan={1} WHERE ID={0}", num2, num4)).GetAwaiter().GetResult();
				await GameDb.UpdateGuildHonor(num2, num4);
				await ChaGang();
				HeThongNhacNho("Bang phái Võ Huân đã phân phối thành công, quần hùng hoan hỉ!");
				characterData.HeThongNhacNho("Đạt được Bang chủ [" + CharacterName + "] phân phối bang phái Võ Huân" + num + ".");

			}
		}
		else
		{
			HeThongNhacNho("Đối phương không xuất hiện trong giang hồ, không thể liên lạc!");
		}
	}

	public void MobileTrainingPlace(byte[] data, int length)
	{
		PacketModification(data, length);
		try
		{
			PacketModification(data, length);
			var array = new byte[4];
			int num = data[10];
			Buffer.BlockCopy(data, 14, array, 0, 4);
			var num2 = BitConverter.ToInt32(array, 0);
			switch (num)
			{
				case 8:
					switch (num2)
					{
						case 32002:
							{
								var num6 = -1;
								var num7 = 0;
								if (ActivityMapRemainingTime <= 0)
								{
									ActivityMapRemainingTime = 0;
									for (var j = 0; j < 96; j++)
									{
										var num8 = BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0);
										switch (num8)
										{
											case 1008001328:
												num7 = num8;
												num6 = j;
												break;
											case 1008001182:
												num7 = num8;
												num6 = j;
												break;
											case 1008001183:
												num7 = num8;
												num6 = j;
												break;
											case 1008001184:
												num7 = num8;
												num6 = j;
												break;
											default:
												continue;
										}
										break;
									}
									switch (num7)
									{
										case 1008001328:
											ActivityMapRemainingTime = 2000000;
											break;
										case 1008001182:
											ActivityMapRemainingTime = 240;
											break;
										case 1008001183:
											ActivityMapRemainingTime = 480;
											break;
										case 1008001184:
											ActivityMapRemainingTime += 1440;
											break;
										case 0:
											HeThongNhacNho("Hành trang phải có [Phiếu Thông Hành] mới được bước vào Lãng Quên Thôn Trang!");
											return;
									}
									ItemUse(1, num6, 1);
								}
								int num9 = BitConverter.ToInt16(data, 13);
								var x = 0f;
								var y = 0f;
								switch (num9)
								{
									case 1:
										x = -1520f;
										y = 1248f;
										break;
									case 2:
										x = -1520f;
										y = -1248f;
										break;
									case 3:
										x = 1280f;
										y = 1248f;
										break;
									case 4:
										x = 1280f;
										y = -1260f;
										break;
								}
								Mobile(x, y, 15f, num2, 0);
								FBtime = DateTime.Now;
								var array5 = Converter.HexStringToByte("AA551E0000009100100001000000010000000100000000000000000000000000000055AA");
								OpenWarehouse = false;
								Buffer.BlockCopy(BitConverter.GetBytes(1010), 0, array5, 18, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array5, 10, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array5, 14, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array5, 4, 2);
								Client?.Send_Map_Data(array5, array5.Length);
								HeThongNhacNho("Thời gian bắt đầu tính, sau [" + ActivityMapRemainingTime + "] khắc, đại hiệp sẽ bị truyền tống ra khỏi Lãng Quên Thôn Trang!");
								break;
							}
						case 1301:
							{
								var num10 = -1;
								var num11 = 0;
								if (ActivityMapRemainingTime <= 0)
								{
									ActivityMapRemainingTime = 0;
									for (var k = 0; k < 96; k++)
									{
										var num12 = BitConverter.ToInt32(Item_In_Bag[k].VatPham_ID, 0);
										switch (num12)
										{
											case 1008001328:
												num11 = num12;
												num10 = k;
												break;
											case 1008001182:
												num11 = num12;
												num10 = k;
												break;
											case 1008001183:
												num11 = num12;
												num10 = k;
												break;
											case 1008001184:
												num11 = num12;
												num10 = k;
												break;
											default:
												continue;
										}
										break;
									}
									switch (num11)
									{
										case 1008001328:
											ActivityMapRemainingTime = 2000000;
											break;
										case 1008001182:
											ActivityMapRemainingTime = 240;
											break;
										case 1008001183:
											ActivityMapRemainingTime = 480;
											break;
										case 1008001184:
											ActivityMapRemainingTime += 1440;
											break;
										case 0:
											HeThongNhacNho("Hành trang phải có [Phiếu Thông Hành] mới được bước vào Lãng Quên Thôn Trang!");
											return;
									}
									ItemUse(1, num10, 1);
								}
								int num13 = BitConverter.ToInt16(data, 13);
								var x2 = 0f;
								var y2 = 0f;
								switch (num13)
								{
									case 1:
										x2 = -1520f;
										y2 = 1248f;
										break;
									case 2:
										x2 = -1520f;
										y2 = -1248f;
										break;
									case 3:
										x2 = 1280f;
										y2 = 1248f;
										break;
									case 4:
										x2 = 1280f;
										y2 = -1260f;
										break;
								}
								Mobile(x2, y2, 15f, num2, 0);
								FBtime = DateTime.Now;
								var array6 = Converter.HexStringToByte("AA551E0000009100100001000000010000000100000000000000000000000000000055AA");
								OpenWarehouse = false;
								Buffer.BlockCopy(BitConverter.GetBytes(1010), 0, array6, 18, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array6, 10, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array6, 14, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array6, 4, 2);
								Client?.Send_Map_Data(array6, array6.Length);
								HeThongNhacNho("Thời gian bắt đầu tính, sau [" + ActivityMapRemainingTime + "] khắc, đại hiệp sẽ bị truyền tống ra khỏi Lãng Quên Thôn Trang!");
								break;
							}
						default:
							if (World.ChoTuLuyenMoRa_ID == 0)
							{
								var num3 = -1;
								var num4 = 0;
								if (RemainingTimeOfTrainingMap <= 0)
								{
									RemainingTimeOfTrainingMap = 0;
									for (var i = 0; i < 96; i++)
									{
										var num5 = BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0);
										switch (num5)
										{
											case 1008001502:
												num4 = num5;
												num3 = i;
												break;
											case 1008001503:
												num4 = num5;
												num3 = i;
												break;
											case 1008001504:
												num4 = num5;
												num3 = i;
												break;
											case 1008001327:
												num4 = num5;
												num3 = i;
												break;
											case 1008001190:
												num4 = num5;
												num3 = i;
												break;
											default:
												continue;
										}
										break;
									}
									switch (num4)
									{
										case 1008001190:
											RemainingTimeOfTrainingMap = 120;
											break;
										case 0:
											HeThongNhacNho("Hành trang phải mang [Phiếu Thông Hành Tu Luyện] mới được vào nơi tu luyện!");
											return;
										case 1008001502:
											RemainingTimeOfTrainingMap = 240;
											break;
										case 1008001503:
											RemainingTimeOfTrainingMap = 480;
											break;
										case 1008001504:
											RemainingTimeOfTrainingMap = 1440;
											break;
										case 1008001327:
											RemainingTimeOfTrainingMap = 120;
											break;
									}
									ItemUse(1, num3, 1);
								}
								Mobile(0f, 0f, 15f, num2, 0);
								FBtime = DateTime.Now;
								var array3 = Converter.HexStringToByte("AA551E0000009100100001000000010000000100000000000000000000000000000055AA");
								OpenWarehouse = false;
								Buffer.BlockCopy(BitConverter.GetBytes(1010), 0, array3, 18, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array3, 10, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array3, 14, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
								Client?.Send_Map_Data(array3, array3.Length);
								HeThongNhacNho("Tu luyện bắt đầu tính giờ, sau [" + RemainingTimeOfTrainingMap + "] khắc, đại hiệp sẽ bị truyền tống khỏi nơi tu luyện!");
							}
							else
							{
								if (RemainingTimeOfTrainingMap < 0)
								{
									RemainingTimeOfTrainingMap = 0;
								}
								RemainingTimeOfTrainingMap += 120;
								Mobile(0f, 0f, 15f, num2, 0);
								FBtime = DateTime.Now;
								var array4 = Converter.HexStringToByte("AA551E0000009100100001000000010000000100000000000000000000000000000055AA");
								OpenWarehouse = false;
								Buffer.BlockCopy(BitConverter.GetBytes(1010), 0, array4, 18, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array4, 10, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array4, 14, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
								Client?.Send_Map_Data(array4, array4.Length);
								HeThongNhacNho("Tu luyện bắt đầu tính giờ, sau [" + RemainingTimeOfTrainingMap + "] khắc, đại hiệp sẽ bị truyền tống khỏi nơi tu luyện!");
							}
							break;
					}
					GetTheReviewRangePlayers();
					GetReviewScopeNpc();
					ScanGroundItems();
					break;
				case 3:
					{
						if (AppendStatusList.ContainsKey(1008002169))
						{
							Mobile(-422f, -960f, 15f, 42001, 0);
						}
						else
						{
							HeThongNhacNho("Chỉ khi chiến thắng Công Thành Chiến, đại hiệp mới nhận được sự hỗ trợ này!");
						}
						var array2 = Converter.HexStringToByte("AA551E0000009100100001000000010000000100000000000000000000000000000055AA");
						OpenWarehouse = false;
						Buffer.BlockCopy(BitConverter.GetBytes(1010), 0, array2, 18, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array2, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array2, 14, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
						Client?.Send_Map_Data(array2, array2.Length);
						break;
					}
			}
		}
		catch (Exception)
		{
		}
	}

	public void ParticipateInTheBattleOfKings(byte[] data, int length)
	{
		PacketModification(data, length);
		BitConverter.ToInt32(data, 11);
	}

	public void ThayDoi_ThietBi_HoTro()
	{
		try
		{
			var num = 100;
			if (Exiting)
			{
				LogHelper.WriteLine(LogLevel.Debug, "Rời khỏi phục chế BUG: [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]      ");
			}
			else
			{
				if (OpenWarehouse || (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa))
				{
					return;
				}
				for (var i = 0; i < 14; i++)
				{
					if (BitConverter.ToInt32(Sub_Wear[i].VatPham_ID, 0) == 0)
					{
						continue;
					}
					var vatPhamByte = new byte[World.Item_Db_Byte_Length];
					var xVatPhamLoai = Sub_Wear[i];
					if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) == 0 || xVatPhamLoai.Lock_Move)
					{
						continue;
					}
					var itmeClass = World.ItemList[BitConverter.ToInt32(Sub_Wear[i].VatPham_ID, 0)];
					if (itmeClass.FLD_LEVEL <= Player_Level && (itmeClass.FLD_ZX == 0 || itmeClass.FLD_ZX == Player_Zx) && (itmeClass.FLD_RESIDE1 == 0 || itmeClass.FLD_RESIDE1 == Player_Job) && (itmeClass.FLD_JOB_LEVEL == 0 || itmeClass.FLD_JOB_LEVEL <= Player_Job_level) && (itmeClass.FLD_SEX == 0 || itmeClass.FLD_SEX == Player_Sex) && (itmeClass.FLD_XWJD < 1 || itmeClass.FLD_XWJD <= VoHuanGiaiDoan) && itmeClass.FLD_RESIDE2 >= 1 && itmeClass.FLD_RESIDE2 <= 15 && (i != 13 || KiemTraMonGiap_DieuKien(itmeClass.FLD_PID)))
					{
						if (BitConverter.ToInt32(Item_Wear[i].VatPham_ID, 0) == 0)
						{
							Item_Wear[i].VatPham_byte = Sub_Wear[i].VatPham_byte;
							Sub_Wear[i].VatPham_byte = vatPhamByte;
							ChangeEquipmentLocation(123, i, 0, i, Item_Wear[i].VatPham_byte, BitConverter.ToInt32(Item_Wear[i].VatPhamSoLuong, 0));
						}
						else
						{
							var vatPhamByte2 = Item_Wear[i].VatPham_byte;
							Item_Wear[i].VatPham_byte = Sub_Wear[i].VatPham_byte;
							Sub_Wear[i].VatPham_byte = vatPhamByte2;
							ChangeEquipmentLocation(123, i, 0, i, Item_Wear[i].VatPham_byte, BitConverter.ToInt32(Item_Wear[i].VatPhamSoLuong, 0));
						}
						num += 100;
						Thread.Sleep(num);
					}
				}
				UpdateCharacterData(this);
				UpdateEquipmentEffects();
				CalculateCharacterEquipmentData();
				UpdateMartialArtsAndStatus();
				CapNhat_HP_MP_SP();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thay đổi thiết bị error 11 : [" + AccountID + "][" + CharacterName + "] " + ex.Message);
		}
	}

	public void ChuyenDoiThietBiPhuTro(byte[] data, int length)
	{
		PacketModification(data, length);
		Thread thread = new(ThayDoi_ThietBi_HoTro);
		thread.Name = "Timer      Thread";
		thread.Start();
	}

	public void OpenTheHallOfHonor(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		int num = packetData[11];
		using SendingClass sendingClass = new();
		switch (packetData[14])
		{
			case 2:
			case 3:
				switch (num)
				{
					default:
						if (World.BangPhaiXepHangSoLieu.Count > 0)
						{
							sendingClass.Write4(World.BangPhaiXepHangSoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							for (var m = 0; m < World.BangPhaiXepHangSoLieu.Count; m++)
							{
								if (World.BangPhaiXepHangSoLieu[m] != null)
								{
									sendingClass.WriteName(World.BangPhaiXepHangSoLieu[m].BangPhaiTenNhanVat);
									sendingClass.WriteName(World.BangPhaiXepHangSoLieu[m].BangPhaiBangPhaiTen);
									sendingClass.Write2(World.BangPhaiXepHangSoLieu[m].BangPhaiNgheNghiep);
									sendingClass.Write2(0);
									sendingClass.Write2(World.BangPhaiXepHangSoLieu[m].BangPhaiChinhTa);
									sendingClass.Write2(World.BangPhaiXepHangSoLieu[m].BangPhaiNhanVat_DangCap);
									sendingClass.Write2(0);
									sendingClass.Write8(World.BangPhaiXepHangSoLieu[m].BangPhaiVinhDu_DiemSo);
									sendingClass.Write8(m + 1);
								}
							}

							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						else
						{
							sendingClass.Write4(World.BangPhaiXepHangSoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							sendingClass.WriteName("");
							sendingClass.WriteName("");
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						break;
					case 2:
						if (World.VoLamHuyetChien_XepHang_SoLieu.Count != 0)
						{
							sendingClass.Write4(World.VoLamHuyetChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							for (var n = 0; n < World.VoLamHuyetChien_XepHang_SoLieu.Count; n++)
							{
								if (World.VoLamHuyetChien_XepHang_SoLieu[n] != null)
								{
									sendingClass.WriteName(World.VoLamHuyetChien_XepHang_SoLieu[n].PlayerName);
									sendingClass.WriteName(World.VoLamHuyetChien_XepHang_SoLieu[n].GuildName);
									sendingClass.Write2(World.VoLamHuyetChien_XepHang_SoLieu[n].PlayerJob);
									sendingClass.Write2(0);
									sendingClass.Write2(World.VoLamHuyetChien_XepHang_SoLieu[n].Faction);
									sendingClass.Write2(World.VoLamHuyetChien_XepHang_SoLieu[n].PlayerLevel);
									sendingClass.Write2(0);
									sendingClass.Write8(World.VoLamHuyetChien_XepHang_SoLieu[n].Point);
									sendingClass.Write8(n + 1);
								}
							}

							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						else
						{
							sendingClass.Write4(World.VoLamHuyetChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							sendingClass.WriteName("");
							sendingClass.WriteName("");
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						break;
					case 0:
					case 1:
						if (World.TheLucChien_XepHang_SoLieu.Count != 0)
						{
							sendingClass.Write4(World.TheLucChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							for (var l = 0; l < World.TheLucChien_XepHang_SoLieu.Count; l++)
							{
								if (World.TheLucChien_XepHang_SoLieu[l] != null)
								{
									sendingClass.WriteName(World.TheLucChien_XepHang_SoLieu[l].PlayerName);
									sendingClass.WriteName(World.TheLucChien_XepHang_SoLieu[l].GuildName);
									sendingClass.Write2(World.TheLucChien_XepHang_SoLieu[l].PlayerJob);
									sendingClass.Write2(0);
									sendingClass.Write2(World.TheLucChien_XepHang_SoLieu[l].Faction);
									sendingClass.Write2(World.TheLucChien_XepHang_SoLieu[l].PlayerLevel);
									sendingClass.Write2(0);
									sendingClass.Write8(World.TheLucChien_XepHang_SoLieu[l].Point);
									sendingClass.Write8(l + 1);
								}
							}

							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						else
						{
							sendingClass.Write4(World.TheLucChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(num);
							sendingClass.WriteName("");
							sendingClass.WriteName("");
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						break;
				}
				break;
			case 1:
				switch (num)
				{
					default:
						if (World.BangPhaiXepHangSoLieu.Count != 0)
						{
							sendingClass.Write4(World.BangPhaiXepHangSoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							for (var j = 0; j < World.BangPhaiXepHangSoLieu.Count; j++)
							{
								if (World.BangPhaiXepHangSoLieu[j] != null)
								{
									sendingClass.WriteName(World.BangPhaiXepHangSoLieu[j].BangPhaiTenNhanVat);
									sendingClass.WriteName(World.BangPhaiXepHangSoLieu[j].BangPhaiBangPhaiTen);
									sendingClass.Write2(World.BangPhaiXepHangSoLieu[j].BangPhaiNgheNghiep);
									sendingClass.Write2(0);
									sendingClass.Write2(World.BangPhaiXepHangSoLieu[j].BangPhaiChinhTa);
									sendingClass.Write2(World.BangPhaiXepHangSoLieu[j].BangPhaiNhanVat_DangCap);
									sendingClass.Write2(0);
									sendingClass.Write8(World.BangPhaiXepHangSoLieu[j].BangPhaiVinhDu_DiemSo);
									sendingClass.Write8(j + 1);
								}
							}

							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						else
						{
							sendingClass.Write4(World.BangPhaiXepHangSoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							sendingClass.WriteName("");
							sendingClass.WriteName("");
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						break;
					case 2:
						if (World.VoLamHuyetChien_XepHang_SoLieu.Count != 0)
						{
							sendingClass.Write4(World.VoLamHuyetChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							for (var k = 0; k < World.VoLamHuyetChien_XepHang_SoLieu.Count; k++)
							{
								if (World.VoLamHuyetChien_XepHang_SoLieu[k] != null)
								{
									sendingClass.WriteName(World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName);
									sendingClass.WriteName(World.VoLamHuyetChien_XepHang_SoLieu[k].GuildName);
									sendingClass.Write2(World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerJob);
									sendingClass.Write2(0);
									sendingClass.Write2(World.VoLamHuyetChien_XepHang_SoLieu[k].Faction);
									sendingClass.Write2(World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerLevel);
									sendingClass.Write2(0);
									sendingClass.Write8(World.VoLamHuyetChien_XepHang_SoLieu[k].Point);
									sendingClass.Write8(k + 1);
								}
							}

							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						else
						{
							sendingClass.Write4(World.VoLamHuyetChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							sendingClass.WriteName("");
							sendingClass.WriteName("");
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						break;
					case 0:
					case 1:
						if (World.TheLucChien_XepHang_SoLieu.Count != 0)
						{
							sendingClass.Write4(World.TheLucChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							for (var i = 0; i < World.TheLucChien_XepHang_SoLieu.Count; i++)
							{
								if (World.TheLucChien_XepHang_SoLieu[i] != null)
								{
									sendingClass.WriteName(World.TheLucChien_XepHang_SoLieu[i].PlayerName);
									sendingClass.WriteName(World.TheLucChien_XepHang_SoLieu[i].GuildName);
									sendingClass.Write2(World.TheLucChien_XepHang_SoLieu[i].PlayerJob);
									sendingClass.Write2(0);
									sendingClass.Write2(World.TheLucChien_XepHang_SoLieu[i].Faction);
									sendingClass.Write2(World.TheLucChien_XepHang_SoLieu[i].PlayerLevel);
									sendingClass.Write2(0);
									sendingClass.Write8(World.TheLucChien_XepHang_SoLieu[i].Point);
									sendingClass.Write8(i + 1);
								}
							}

							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						else
						{
							sendingClass.Write4(World.TheLucChien_XepHang_SoLieu.Count);
							sendingClass.Write(0);
							sendingClass.Write(num);
							sendingClass.Write2(0);
							sendingClass.Write4(1);
							sendingClass.WriteName("");
							sendingClass.WriteName("");
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write2(0);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							Client?.SendPak(sendingClass, 23041, SessionID);
						}
						break;
				}
				break;
		}
	}

	public void TopThreeHonors(byte[] packetData, int packetSize)
	{
		int num = packetData[11];
		SendingClass sendingClass = new();
		switch (num)
		{
			case 1:
				{
					for (var j = 0; j < World.TheLucChien_XepHang_SoLieu.Count && j <= 3; j++)
					{
						switch (j)
						{
							case 2:
								if (World.TheLucChien_XepHang_SoLieu[j].PlayerName != null)
								{
									var array5 = GetUpdatedCharacterRankingData(World.TheLucChien_XepHang_SoLieu[j].PlayerName, World.TheLucChien_XepHang_SoLieu[j].GuildName, World.TheLucChien_XepHang_SoLieu[j].Faction, World.TheLucChien_XepHang_SoLieu[j].PlayerLevel, World.TheLucChien_XepHang_SoLieu[j].PlayerJob, 0, 3).ToArray3();
									sendingClass.Write(array5, 0, array5.Length);
								}
								break;
							case 1:
								if (World.TheLucChien_XepHang_SoLieu[j].PlayerName != null)
								{
									var array6 = GetUpdatedCharacterRankingData(World.TheLucChien_XepHang_SoLieu[j].PlayerName, World.TheLucChien_XepHang_SoLieu[j].GuildName, World.TheLucChien_XepHang_SoLieu[j].Faction, World.TheLucChien_XepHang_SoLieu[j].PlayerLevel, World.TheLucChien_XepHang_SoLieu[j].PlayerJob, 0, 2).ToArray3();
									sendingClass.Write(array6, 0, array6.Length);
								}
								break;
							case 0:
								if (World.TheLucChien_XepHang_SoLieu[j].PlayerName != null)
								{
									var array4 = GetUpdatedCharacterRankingData(World.TheLucChien_XepHang_SoLieu[j].PlayerName, World.TheLucChien_XepHang_SoLieu[j].GuildName, World.TheLucChien_XepHang_SoLieu[j].Faction, World.TheLucChien_XepHang_SoLieu[j].PlayerLevel, World.TheLucChien_XepHang_SoLieu[j].PlayerJob, 0, 1).ToArray3();
									sendingClass.Write(array4, 0, array4.Length);
								}
								break;
						}
					}
					break;
				}
			case 2:
				{
					for (var k = 0; k < World.VoLamHuyetChien_XepHang_SoLieu.Count && k <= 3; k++)
					{
						switch (k)
						{
							case 2:
								if (World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName != null)
								{
									var array8 = GetUpdatedCharacterRankingData(World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName, World.VoLamHuyetChien_XepHang_SoLieu[k].GuildName, World.VoLamHuyetChien_XepHang_SoLieu[k].Faction, World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerLevel, World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerJob, 0, 3).ToArray3();
									sendingClass.Write(array8, 0, array8.Length);
								}
								break;
							case 1:
								if (World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName != null)
								{
									var array9 = GetUpdatedCharacterRankingData(World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName, World.VoLamHuyetChien_XepHang_SoLieu[k].GuildName, World.VoLamHuyetChien_XepHang_SoLieu[k].Faction, World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerLevel, World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerJob, 0, 2).ToArray3();
									sendingClass.Write(array9, 0, array9.Length);
								}
								break;
							case 0:
								if (World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName != null)
								{
									var array7 = GetUpdatedCharacterRankingData(World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName, World.VoLamHuyetChien_XepHang_SoLieu[k].GuildName, World.VoLamHuyetChien_XepHang_SoLieu[k].Faction, World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerLevel, World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerJob, 0, 1).ToArray3();
									sendingClass.Write(array7, 0, array7.Length);
								}
								break;
						}
					}
					break;
				}
			case 3:
				{
					for (var i = 0; i < World.BangPhaiXepHangSoLieu.Count && i <= 3; i++)
					{
						switch (i)
						{
							case 2:
								if (World.BangPhaiXepHangSoLieu[i].BangPhaiTenNhanVat != null)
								{
									var array2 = GetUpdatedCharacterRankingData(World.BangPhaiXepHangSoLieu[i].BangPhaiTenNhanVat, World.BangPhaiXepHangSoLieu[i].BangPhaiBangPhaiTen, World.BangPhaiXepHangSoLieu[i].BangPhaiChinhTa, World.BangPhaiXepHangSoLieu[i].BangPhaiNhanVat_DangCap, World.BangPhaiXepHangSoLieu[i].BangPhaiNgheNghiep, World.BangPhaiXepHangSoLieu[i].BangPhaiChuyenChuc, 3).ToArray3();
									sendingClass.Write(array2, 0, array2.Length);
								}
								break;
							case 1:
								if (World.BangPhaiXepHangSoLieu[i].BangPhaiTenNhanVat != null)
								{
									var array3 = GetUpdatedCharacterRankingData(World.BangPhaiXepHangSoLieu[i].BangPhaiTenNhanVat, World.BangPhaiXepHangSoLieu[i].BangPhaiBangPhaiTen, World.BangPhaiXepHangSoLieu[i].BangPhaiChinhTa, World.BangPhaiXepHangSoLieu[i].BangPhaiNhanVat_DangCap, World.BangPhaiXepHangSoLieu[i].BangPhaiNgheNghiep, World.BangPhaiXepHangSoLieu[i].BangPhaiChuyenChuc, 2).ToArray3();
									sendingClass.Write(array3, 0, array3.Length);
								}
								break;
							case 0:
								if (World.BangPhaiXepHangSoLieu[i].BangPhaiTenNhanVat != null)
								{
									var array = GetUpdatedCharacterRankingData(World.BangPhaiXepHangSoLieu[i].BangPhaiTenNhanVat, World.BangPhaiXepHangSoLieu[i].BangPhaiBangPhaiTen, World.BangPhaiXepHangSoLieu[i].BangPhaiChinhTa, World.BangPhaiXepHangSoLieu[i].BangPhaiNhanVat_DangCap, World.BangPhaiXepHangSoLieu[i].BangPhaiNgheNghiep, World.BangPhaiXepHangSoLieu[i].BangPhaiChuyenChuc, 1).ToArray3();
									sendingClass.Write(array, 0, array.Length);
								}
								break;
						}
					}
					break;
				}
		}
		if (sendingClass != null && Client != null)
		{
			Client.SendPak(sendingClass, 5635, SessionID);
		}
	}

	public void SendThe6TurnSkillBook(int vacancy)
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var num = 0;
		switch (Player_Job)
		{
			case 1:
				num = ((Player_Zx != 1) ? 1000000342 : 1000000336);
				break;
			case 2:
				num = ((Player_Zx != 1) ? 1000000343 : 1000000337);
				break;
			case 3:
				num = ((Player_Zx != 1) ? 1000000344 : 1000000338);
				break;
			case 4:
				num = ((Player_Zx != 1) ? 1000000345 : 1000000339);
				break;
			case 5:
				num = ((Player_Zx != 1) ? 1000000346 : 1000000340);
				break;
			case 6:
				num = ((Player_Zx != 1) ? 1000000347 : 1000000341);
				break;
			case 7:
				num = ((Player_Zx != 1) ? 1000000497 : 1000000494);
				break;
			case 8:
				num = 1000000567;
				break;
			case 9:
				{
					var num2 = GetParcelVacancy(this);
					if (num2 == -1)
					{
						num2 = 35;
					}
					IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001010), num2, BitConverter.GetBytes(1), new byte[56]);
					return;
				}
			case 10:
				num = ((Player_Zx != 1) ? 1000001107 : 1000001106);
				break;
			case 11:
				num = 1000001532;
				break;
			case 12:
				num = 1000001164;
				break;
			case 13:
				num = 1000001284;
				break;
		}
		IncreaseItem2(bytes, BitConverter.GetBytes(num), vacancy, BitConverter.GetBytes(1), new byte[56]);
	}

	public void SendTheSevenTurnSkillBook(int vacancy)
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var num = 0;
		switch (Player_Job)
		{
			case 1:
				num = ((Player_Zx != 1) ? 1000000394 : 1000000388);
				break;
			case 2:
				num = ((Player_Zx != 1) ? 1000000395 : 1000000389);
				break;
			case 3:
				num = ((Player_Zx != 1) ? 1000000396 : 1000000390);
				break;
			case 4:
				num = ((Player_Zx != 1) ? 1000000397 : 1000000391);
				break;
			case 5:
				num = ((Player_Zx != 1) ? 1000000398 : 1000000392);
				break;
			case 6:
				num = ((Player_Zx != 1) ? 1000000399 : 1000000393);
				break;
			case 7:
				num = ((Player_Zx != 1) ? 1000000498 : 1000000495);
				break;
			case 8:
				num = 1000000568;
				break;
			case 10:
				num = ((Player_Zx != 1) ? 1000001109 : 1000001108);
				break;
			case 11:
				num = 1000001533;
				break;
			case 12:
				num = 1000001165;
				break;
			case 13:
				num = 1000001286;
				break;
		}
		IncreaseItem2(bytes, BitConverter.GetBytes(num), vacancy, BitConverter.GetBytes(1), new byte[56]);
	}

	public void SendThe8TurnSkillBook(int vacancy)
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var num = 0;
		switch (Player_Job)
		{
			case 1:
				num = ((Player_Zx != 1) ? 1000000470 : 1000000464);
				break;
			case 2:
				num = ((Player_Zx != 1) ? 1000000471 : 1000000465);
				break;
			case 3:
				num = ((Player_Zx != 1) ? 1000000472 : 1000000466);
				break;
			case 4:
				num = ((Player_Zx != 1) ? 1000000473 : 1000000467);
				break;
			case 5:
				num = ((Player_Zx != 1) ? 1000000474 : 1000000468);
				break;
			case 6:
				num = ((Player_Zx != 1) ? 1000000475 : 1000000469);
				break;
			case 7:
				num = ((Player_Zx != 1) ? 1000000499 : 1000000496);
				break;
			case 8:
				num = 1000000569;
				break;
			case 9:
				num = 1000001013;
				break;
			case 10:
				num = ((Player_Zx != 1) ? 1000001111 : 1000001110);
				break;
			case 11:
				num = 1000001534;
				break;
			case 12:
				num = 1000001166;
				break;
			case 13:
				num = 1000001287;
				break;
		}
		IncreaseItem2(bytes, BitConverter.GetBytes(num), vacancy, BitConverter.GetBytes(1), new byte[56]);
	}

	public void SendNineTurnSkillBook(int vacancy)
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var num = 0;
		switch (Player_Job)
		{
			case 1:
				num = ((Player_Zx != 1) ? 1000001039 : 1000001032);
				break;
			case 2:
				num = ((Player_Zx != 1) ? 1000001040 : 1000001033);
				break;
			case 3:
				num = ((Player_Zx != 1) ? 1000001041 : 1000001034);
				break;
			case 4:
				num = ((Player_Zx != 1) ? 1000001042 : 1000001035);
				break;
			case 5:
				num = ((Player_Zx != 1) ? 1000001043 : 1000001036);
				break;
			case 6:
				num = ((Player_Zx != 1) ? 1000001044 : 1000001037);
				break;
			case 7:
				num = ((Player_Zx != 1) ? 1000001045 : 1000001038);
				break;
			case 8:
				num = 1000001046;
				break;
			case 9:
				num = 1000001047;
				break;
			case 10:
				num = ((Player_Zx != 1) ? 1000001113 : 1000001112);
				break;
			case 11:
				num = 1000001535;
				break;
			case 12:
				num = 1000001167;
				break;
			case 13:
				num = 1000001288;
				break;
		}
		IncreaseItem2(bytes, BitConverter.GetBytes(num), vacancy, BitConverter.GetBytes(1), new byte[56]);
	}

	public void SendThe10TurnSkillBook()
	{
		try
		{
			var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
			var num = 0;
			switch (Player_Job)
			{
				case 1:
					num = ((Player_Zx != 1) ? 1000001192 : 1000001191);
					break;
				case 2:
					num = ((Player_Zx != 1) ? 1000001194 : 1000001193);
					break;
				case 3:
					num = ((Player_Zx != 1) ? 1000001196 : 1000001195);
					break;
				case 4:
					num = ((Player_Zx != 1) ? 1000001198 : 1000001197);
					break;
				case 5:
					num = ((Player_Zx != 1) ? 1000001200 : 1000001199);
					break;
				case 6:
					num = ((Player_Zx != 1) ? 1000001202 : 1000001201);
					break;
				case 7:
					num = ((Player_Zx != 1) ? 1000001204 : 1000001203);
					break;
				case 8:
					num = 1000001205;
					IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001223), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
					break;
				case 9:
					num = 1000001206;
					break;
				case 10:
					num = ((Player_Zx != 1) ? 1000001208 : 1000001207);
					break;
				case 11:
					num = 1000001209;
					break;
				case 12:
					num = 1000001210;
					break;
				case 13:
					num = 1000001289;
					break;
			}
			IncreaseItem2(bytes, BitConverter.GetBytes(num), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
		}
		catch
		{
		}
	}

	public void SendThe11TurnSkillBook()
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var num = 0;
		switch (Player_Job)
		{
			case 1:
				num = ((Player_Zx != 2) ? 1000001320 : 1000001321);
				break;
			case 2:
				num = ((Player_Zx != 2) ? 1000001322 : 1000001323);
				break;
			case 3:
				num = ((Player_Zx != 2) ? 1000001324 : 1000001325);
				break;
			case 4:
				num = ((Player_Zx != 2) ? 1000001326 : 1000001327);
				break;
			case 5:
				num = ((Player_Zx != 2) ? 1000001328 : 1000001329);
				break;
			case 6:
				num = ((Player_Zx != 2) ? 1000001330 : 1000001331);
				break;
			case 7:
				num = ((Player_Zx != 2) ? 1000001332 : 1000001333);
				break;
			case 8:
				num = 1000001334;
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001335), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
				break;
			case 9:
				num = 1000001336;
				break;
			case 10:
				num = ((Player_Zx != 2) ? 1000001337 : 1000001338);
				break;
			case 11:
				num = 1000001339;
				break;
			case 12:
				num = 1000001340;
				break;
			case 13:
				num = 1000001341;
				break;
		}
		IncreaseItem2(bytes, BitConverter.GetBytes(num), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
	}

	public void SendThe12TurnSkillBook()
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var num = 0;
		switch (Player_Job)
		{
			case 1:
				num = ((Player_Zx == 1) ? 1000002020 : 1000002021);
				break;
			case 2:
				num = ((Player_Zx == 1) ? 1000002022 : 1000002023);
				break;
			case 3:
				num = ((Player_Zx == 1) ? 1000002024 : 1000002025);
				break;
			case 4:
				num = ((Player_Zx == 1) ? 1000002026 : 1000002027);
				break;
			case 5:
				num = ((Player_Zx == 1) ? 1000002028 : 1000002029);
				break;
			case 6:
				num = ((Player_Zx == 1) ? 1000002030 : 1000002031);
				break;
			case 7:
				num = ((Player_Zx == 1) ? 1000002032 : 1000002033);
				break;
			case 8:
				num = 1000002034;
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000002035), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
				break;
			case 9:
				num = 1000002036;
				break;
			case 10:
				num = ((Player_Zx == 1) ? 1000002037 : 1000002038);
				break;
			case 11:
				num = 1000002039;
				break;
			case 12:
				num = 1000002040;
				break;
			case 13:
				num = 1000002041;
				break;
		}
		IncreaseItem2(bytes, BitConverter.GetBytes(num), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
	}

	public void NewLearningQigong(int khiCongPosition, int type)
	{
		if (type == 0)
		{
			if (BitConverter.ToInt16(KhiCong[khiCongPosition].KhiCong_byte, 0) == 255)
			{
				KhiCong[khiCongPosition] = new X_Khi_Cong_Loai(new byte[2]);
				KhiCong[khiCongPosition].KhiCongID = GetKhiCong_ID(khiCongPosition, Player_Job);
				UpdateMartialArtsAndStatus();
			}
		}
		else
		{
			KhiCong[khiCongPosition] = new X_Khi_Cong_Loai(new byte[2]);
			KhiCong[khiCongPosition].KhiCongID = GetKhiCong_ID(khiCongPosition, Player_Job);
			UpdateMartialArtsAndStatus();
		}
	}

}
