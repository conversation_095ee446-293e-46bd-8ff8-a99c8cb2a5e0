﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    	public bool SuccessfulTransaction(Players playe, bool icomplete)
	{
		try
		{
			if (!playe.Exiting)
			{
				if (playe.GiaoDich.NguoiGiaoDich.Exiting)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Thoát bản sao BUG 12 [" + playe.GiaoDich.NguoiGiaoDich.AccountID + "][" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "][" + playe.GiaoDich.NguoiGiaoDich.Client.ToString() + "]      ");
				}
				else if (playe.CuaHangCaNhan != null)
				{
					LogHelper.WriteLine(LogLevel.Debug, "<PERSON><PERSON><PERSON> ph<PERSON>ơng đang mở cửa hàng cá nhân, <PERSON><PERSON><PERSON> lỗi !!! [" + playe.AccountID + "]-[" + playe.CharacterName + "]开店GiaoDich");
				}
				else if (playe.GiaoDich.NguoiGiaoDich.CuaHangCaNhan != null)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp GiaoDich 11 [" + playe.GiaoDich.NguoiGiaoDich.AccountID + "]-[" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "]开店GiaoDich");
				}
				else if (!playe.OpenWarehouse)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Đối phương đang mở Kho cá nhân, Giao Dịch lỗi !!! [" + playe.AccountID + "]-[" + playe.CharacterName + "]");
				}
				else if (!playe.GiaoDich.NguoiGiaoDich.OpenWarehouse)
				{
					LogHelper.WriteLine(LogLevel.Debug, "GiaoDich sự thành công BUG 11 ! [" + playe.GiaoDich.NguoiGiaoDich.AccountID + "]-[" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "]");
				}
				else if (playe.GiaoDich.GiaoDichVatPham1.Count > playe.GiaoDich.NguoiGiaoDich.GetParcelVacancyNumber())
				{
					LogHelper.WriteLine(LogLevel.Debug, "GiaoDich sự thành công BUG 15 [" + playe.GiaoDich.NguoiGiaoDich.AccountID + "]-[" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "]");
				}
				else
				{
					if (playe.GiaoDich.GiaoDichTien > 0)
					{
						if (playe.Player_Money < playe.GiaoDich.GiaoDichTien)
						{
							goto IL_102a;
						}
						if (playe.GiaoDich.NguoiGiaoDich.Player_Money + playe.GiaoDich.GiaoDichTien > World.Money_Max)
						{
							playe.GiaoDich.GiaoDichTien = World.Money_Max - playe.GiaoDich.NguoiGiaoDich.Player_Money;
						}
						var playerMoney = playe.Player_Money;
						var playerMoney2 = playe.GiaoDich.NguoiGiaoDich.Player_Money;
						playe.check_bug_gold_tang_bat_thuong = true;
						check_bug_gold_tang_bat_thuong = true;
						playe.GiaoDich.NguoiGiaoDich.check_bug_gold_tang_bat_thuong = true;
						GiaoDich.NguoiGiaoDich.check_bug_gold_tang_bat_thuong = true;
						playe.GiaoDich.NguoiGiaoDich.Player_Money += playe.GiaoDich.GiaoDichTien;
						playe.GiaoDich.NguoiGiaoDich.UpdateMoneyAndWeight();
						playe.Player_Money -= playe.GiaoDich.GiaoDichTien;
						var playerMoney3 = playe.Player_Money;
						var playerMoney4 = playe.GiaoDich.NguoiGiaoDich.Player_Money;
						if (playerMoney2 < playerMoney4)
						{
							var text = "[" + playe.AccountID + "][" + playe.CharacterName + "] chuyen cho:[" + playe.GiaoDich.NguoiGiaoDich.AccountID + "][" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "] nhan:[" + playe.GiaoDich.GiaoDichTien + "] so du:[" + playe.GiaoDich.NguoiGiaoDich.Player_Money + "]";
							// logo.LOG_GD_nhan_GOLD(text, playe.GiaoDich.NguoiGiaoDich.UserName);
						}
						if (playerMoney > playerMoney3)
						{
							var text2 = "[" + playe.AccountID + "][" + playe.CharacterName + "] chuyen cho [" + playe.GiaoDich.NguoiGiaoDich.AccountID + "][" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "][" + playe.GiaoDich.GiaoDichTien + "] so du [" + playerMoney3 + "]";
							// logo.LOG_GD_chuyen_GOLD(text2, playe.UserName);
						}
					}
					foreach (var value2 in playe.GiaoDich.GiaoDichVatPham1.Values)
					{
						var vatPham = value2.VatPham;
						var getVatPhamId = playe.Item_In_Bag[vatPham.VatPhamViTri].GetVatPham_ID;
						var getItemGlobalId = playe.Item_In_Bag[vatPham.VatPhamViTri].GetItemGlobal_ID;
						if (getVatPhamId != vatPham.GetVatPham_ID || getItemGlobalId != vatPham.GetItemGlobal_ID)
						{
							HeThongNhacNho("Người chơi [" + playe.CharacterName + "] có hành vi duple Vật phẩm. Vui lòng báo với Admin !!", 6, "Cảnh báo");
							playe.HeThongNhacNho("Người chơi [" + playe.CharacterName + "] có hành vi duple Vật phẩm. Vui lòng báo với Admin !!", 6, "Cảnh báo");
							BanAccount(77, CharacterName, "Bug Item GiaoDich");
							playe.BanAccount(77, playe.CharacterName, "Bug Item GiaoDich");
							Client.Dispose();
							playe.Client.Dispose();
							var txt = "ID 1[" + playe.AccountID + "][" + playe.CharacterName + "]-ID 2[" + AccountID + "][" + CharacterName + "]-[" + getVatPhamId + "][" + getItemGlobalId + "]";
							// logo.Log_Bug_Item_Giao_Dich(txt);
							return false;
						}
						var parcelVacancy = GetParcelVacancy(playe.GiaoDich.NguoiGiaoDich);
						if (parcelVacancy == -1)
						{
							break;
						}
						var characterItemsGlobalId = GetCharacterItemsGlobal_ID(playe, vatPham.GetItemGlobal_ID);
						if (characterItemsGlobalId == null)
						{
							var array = new string[13]
							{
								"非法复制_GiaoDich3[",
								playe.AccountID,
								"]-[",
								playe.CharacterName,
								"]VatPhamTen称[",
								characterItemsGlobalId.GetItemName(),
								"]      VatPhamTen称2[",
								vatPham.GetItemName(),
								"]      VatPhamSoLuong[",
								null,
								null,
								null,
								null
							};
							array[9] = BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0).ToString();
							array[10] = "]      SoLuong[";
							array[11] = BitConverter.ToInt32(vatPham.VatPhamSoLuong, 0).ToString();
							array[12] = "]";
							LogHelper.WriteLine(LogLevel.Debug, string.Concat(array));
							break;
						}
						if (!World.ItemList.TryGetValue(BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0), out var value))
						{
							break;
						}
						var fLdSide = value.FLD_SIDE;
						if (value2.VatPhamSoLuong < 1)
						{
							var array2 = new string[13]
							{
								"非法复制_GiaoDich22[",
								playe.AccountID,
								"]-[",
								playe.CharacterName,
								"]VatPhamTen称[",
								characterItemsGlobalId.GetItemName(),
								"]      VatPhamTen称2[",
								vatPham.GetItemName(),
								"]      VatPhamSoLuong[",
								null,
								null,
								null,
								null
							};
							array2[9] = BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0).ToString();
							array2[10] = "]      SoLuong[";
							array2[11] = value2.VatPhamSoLuong.ToString();
							array2[12] = "]";
							LogHelper.WriteLine(LogLevel.Debug, string.Concat(array2));
							break;
						}
						if (value2.VatPhamSoLuong > BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0))
						{
							var array3 = new string[13]
							{
								"非法复制_GiaoDich2[",
								playe.AccountID,
								"]-[",
								playe.CharacterName,
								"]VatPhamTen称[",
								characterItemsGlobalId.GetItemName(),
								"]      VatPhamTen称2[",
								vatPham.GetItemName(),
								"]      VatPhamSoLuong[",
								null,
								null,
								null,
								null
							};
							array3[9] = BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0).ToString();
							array3[10] = "]      SoLuong[";
							array3[11] = value2.VatPhamSoLuong.ToString();
							array3[12] = "]";
							LogHelper.WriteLine(LogLevel.Debug, string.Concat(array3));
							break;
						}
						if (characterItemsGlobalId.DatDuocVatPhamViTriLoaiHinh() != 1 && characterItemsGlobalId.DatDuocVatPhamViTriLoaiHinh() != 2 && characterItemsGlobalId.DatDuocVatPhamViTriLoaiHinh() != 5 && characterItemsGlobalId.DatDuocVatPhamViTriLoaiHinh() != 6)
						{
							if (characterItemsGlobalId.DatDuocVatPhamViTriLoaiHinh() == 4 && characterItemsGlobalId.FLD_MAGIC1 >= ********)
							{
								LogHelper.WriteLine(LogLevel.Debug, "Chỉ số vượt cho phép 111 [" + playe.AccountID + "][" + playe.CharacterName + "] GiaoDichVatPham :[" + playe.GiaoDich.NguoiGiaoDich.AccountID + "][" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "] VatPham:" + characterItemsGlobalId.GetItemName() + " số seri:" + BitConverter.ToInt32(characterItemsGlobalId.DatDuocGlobal_ID(), 0) + " SoLuong[" + value2.VatPhamSoLuong + "] ThuocTinh:[" + characterItemsGlobalId.FLD_MAGIC0 + "," + characterItemsGlobalId.FLD_MAGIC1 + "," + characterItemsGlobalId.FLD_MAGIC2 + "," + characterItemsGlobalId.FLD_MAGIC3 + "," + characterItemsGlobalId.FLD_MAGIC4 + "]");
								HeThongNhacNho("Bảo vật vượt giới hạn CLVC - LỖI - 111!", 20, "Thiên cơ các");
								Client.Dispose();
							}
						}
						else if (characterItemsGlobalId.FLD_MAGIC1 > *********)
						{
							LogHelper.WriteLine(LogLevel.Debug, "Chỉ số vượt cho phép 222 [" + playe.AccountID + "][" + playe.CharacterName + "] GiaoDichVatPham :[" + playe.GiaoDich.NguoiGiaoDich.AccountID + "][" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "] VatPham:" + characterItemsGlobalId.GetItemName() + " số seri:" + BitConverter.ToInt32(characterItemsGlobalId.DatDuocGlobal_ID(), 0) + " SoLuong[" + value2.VatPhamSoLuong + "] ThuocTinh:[" + characterItemsGlobalId.FLD_MAGIC0 + "," + characterItemsGlobalId.FLD_MAGIC1 + "," + characterItemsGlobalId.FLD_MAGIC2 + "," + characterItemsGlobalId.FLD_MAGIC3 + "," + characterItemsGlobalId.FLD_MAGIC4 + "]");
							HeThongNhacNho("Bảo vật vượt giới hạn ULPT - LỖI - 222!", 20, "Thiên cơ các");
							Client.Dispose();
						}
						if (fLdSide == 0 && value2.VatPhamSoLuong > 1)
						{
							var array4 = new string[13]
							{
								"非法复制_GiaoDich1[",
								playe.AccountID,
								"]-[",
								playe.CharacterName,
								"]VatPhamTen称[",
								characterItemsGlobalId.GetItemName(),
								"]      VatPhamTen称2[",
								vatPham.GetItemName(),
								"]      VatPhamSoLuong[",
								null,
								null,
								null,
								null
							};
							array4[9] = BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0).ToString();
							array4[10] = "]      SoLuong[";
							array4[11] = value2.VatPhamSoLuong.ToString();
							array4[12] = "]";
							LogHelper.WriteLine(LogLevel.Debug, string.Concat(array4));
							break;
						}
						var userid = playe.AccountID;
						var userName = playe.CharacterName;
						var userid2 = playe.GiaoDich.NguoiGiaoDich.AccountID;
						var userName2 = playe.GiaoDich.NguoiGiaoDich.CharacterName;
						double num = BitConverter.ToInt64(characterItemsGlobalId.ItemGlobal_ID, 0);
						var num2 = BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0);
						var text3 = characterItemsGlobalId.GetItemName();
						var vatPhamSoLuong = value2.VatPhamSoLuong;
						var text4 = characterItemsGlobalId.FLD_MAGIC0 + "-" + characterItemsGlobalId.FLD_MAGIC1 + "-" + characterItemsGlobalId.FLD_MAGIC2 + "-" + characterItemsGlobalId.FLD_MAGIC3 + "-" + characterItemsGlobalId.FLD_MAGIC4 + "初" + characterItemsGlobalId.FLD_FJ_LowSoul + "中" + characterItemsGlobalId.FLD_FJ_TrungCapPhuHon + "进" + characterItemsGlobalId.FLD_FJ_TienHoa;
						RxjhClass.ItemRecord(userid, userName, userid2, userName2, num, num2, text3, vatPhamSoLuong, text4, 0, "GiaoDich");
						characterItemsGlobalId.FLD_FJ_NJ = 0;
						if (Is_ItASpiritBeast(BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0)))
						{
							GameDb.UpdatePetName(BitConverter.ToInt32(characterItemsGlobalId.ItemGlobal_ID, 0), playe.GiaoDich.NguoiGiaoDich.CharacterName, 0).GetAwaiter().GetResult();
							//DBA.ExeSqlCommand(string.Format("UPDATE TBL_XWWL_Cw SET ZrName='{1}',FLD_ZCD={2} WHERE ItmeId={0}", BitConverter.ToInt64(characterItemsGlobalId.ItemGlobal_ID, 0), playe.GiaoDich.NguoiGiaoDich.CharacterName, 0)).GetAwaiter().GetResult();
						}
						playe.GiaoDich.NguoiGiaoDich.AddItems(characterItemsGlobalId.ItemGlobal_ID, characterItemsGlobalId.VatPham_ID, parcelVacancy, BitConverter.GetBytes(value2.VatPhamSoLuong), characterItemsGlobalId.VatPham_ThuocTinh);
						playe.SubtractItem(characterItemsGlobalId.VatPhamViTri, value2.VatPhamSoLuong);
					}
				}
			}
			goto IL_102a;
		IL_102a:
			return true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GiaoDich thành công error [" + playe.AccountID + "][" + playe.CharacterName + "][" + playe.Client.ToString() + "]和[" + playe.GiaoDich.NguoiGiaoDich.AccountID + "][" + playe.GiaoDich.NguoiGiaoDich.CharacterName + "][" + playe.GiaoDich.NguoiGiaoDich.Client.ToString() + "] " + ex.ToString());
			return false;
		}
	}

	public void GiaoDich_Bo_VatPham(byte[] packetData, int length)
	{
		if (!OpenWarehouse)
		{
			LogHelper.WriteLine(LogLevel.Debug, "GiaoDich Đặt đồ vật BUG![" + AccountID + "]-[" + CharacterName + "]");
			return;
		}
		try
		{
			PacketModification(packetData, length);
			var dst = new byte[World.Item_Db_Byte_Length];
			var array = new byte[8];
			var array2 = new byte[4];
			var array3 = new byte[8];
			Buffer.BlockCopy(packetData, 22, dst, 0, 16);
			Buffer.BlockCopy(packetData, 42, dst, 16, World.VatPham_ThuocTinh_KichThuoc);
			Buffer.BlockCopy(packetData, 22, array, 0, 8);
			Buffer.BlockCopy(packetData, 34, array2, 0, 4);
			Buffer.BlockCopy(packetData, 42, array3, 0, 8);
			if (BitConverter.ToInt64(array3, 0) < 1 || !World.ItemList.TryGetValue(BitConverter.ToInt32(array2, 0), out var value) || value.FLD_QUESTITEM == 1)
			{
				return;
			}
			if (value.FLD_LOCK == 1)
			{
				HeThongNhacNho("Bảo vật bị cấm trao đổi!", 10, "Thiên cơ các");
			}
			else if (BitConverter.ToInt32(array2, 0) == **********)
			{
				var num = BitConverter.ToInt64(array3, 0);
				if (num <= 0 || num > World.Money_Max || num > Player_Money || num + GiaoDich.GiaoDichTien > Player_Money)
				{
					return;
				}
				var parcelVacancy = GetParcelVacancy(this);
				if (parcelVacancy != -1)
				{
					if (GiaoDich.NguoiGiaoDich.Player_Money + num > World.Money_Max)
					{
						HeThongNhacNho("Đối phương mang ngân lượng vượt quá [" + World.Money_Max / ********* + "] lượng!", 10, "Thiên cơ các");
						return;
					}
					GiaoDich.GiaoDichTien += num;
					var array4 = Converter.HexStringToByte("AA55760098009A006800010000000*********00000000000000000000000000000000943577000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000CD055AA");
					Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array4, 42, 8);
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
					Client?.SendMultiplePackage(array4, array4.Length);

					GiaoDich.NguoiGiaoDich.Client?.SendMultiplePackage(array4, array4.Length);
				}
				else
				{
					HeThongNhacNho("Hành trang của đại hiệp đã đầy, không thể tiến hành giao dịch!!", 10, "Thiên cơ các");
				}
			}
			else
			{
				if (BitConverter.ToInt64(array, 0) == 0)
				{
					return;
				}
				var characterItemsGlobalId = GetCharacterItemsGlobal_ID(this, BitConverter.ToInt64(array, 0));
				if (characterItemsGlobalId == null || characterItemsGlobalId.VatPham_KhoaLai || GiaoDich.GiaoDichVatPham1.ContainsKey(BitConverter.ToInt64(array, 0)))
				{
					return;
				}
				X_Vat_Pham_Giao_Dich_Loai xVatPhamGiaoDichLoai = new();
				xVatPhamGiaoDichLoai.VatPham = characterItemsGlobalId;
				xVatPhamGiaoDichLoai.VatPhamSoLuong = BitConverter.ToInt32(array3, 0);
				if (xVatPhamGiaoDichLoai.VatPhamSoLuong >= 1)
				{
					GiaoDich.GiaoDichVatPham1.Add(BitConverter.ToInt64(array, 0), xVatPhamGiaoDichLoai);
					var array5 = Converter.HexStringToByte("AA55760094029A006800010000000*********0000008716E56781832006000000000208AF2F000000000*********000000470D03000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000CD055AA");
					Buffer.BlockCopy(characterItemsGlobalId.VatPham_byte, 0, array5, 22, 8);
					Buffer.BlockCopy(characterItemsGlobalId.VatPham_byte, 8, array5, 34, 4);
					Buffer.BlockCopy(array3, 0, array5, 42, 8);
					Buffer.BlockCopy(characterItemsGlobalId.VatPham_byte, 16, array5, 50, World.VatPham_ThuocTinh_KichThuoc);
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array5, 4, 2);
					Client?.Send_Map_Data(array5, array5.Length);

					GiaoDich.NguoiGiaoDich.Client?.SendMultiplePackage(array5, array5.Length);
					if (Is_ItASpiritBeast(BitConverter.ToInt32(array2, 0)))
					{
						SendSpiritBeastData((int)BitConverter.ToInt64(array, 0));
						GiaoDich.NguoiGiaoDich.SendSpiritBeastData((int)BitConverter.ToInt64(array, 0));
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GiaoDich Đặt đồ vật error 123 [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.ToString());
		}
	}

	public void GiaoDich_LoaiLon(byte[] packetData, int length)
	{
		try
		{
			PacketModification(packetData, length);
			if (Player_Level < World.CapDoChoPhepGiaoDich)
			{
				HeThongNhacNho("Đạt cấp [" + World.CapDoChoPhepGiaoDich + "] mới có thể giao dịch!", 10, "Thiên cơ các");
				return;
			}
			if (World.WhetherTheCurrentLineIsSilver == 1)
			{
				HeThongNhacNho("Đang ở khu chợ, không thể tiến hành giao dịch!", 10, "Thiên cơ các");
				return;
			}
			if (MapID == 801 && World.TheLucChien_Progress == 3)
			{
				HeThongNhacNho("Thế Lực Chiến Chính Tà đang diễn ra, không thể giao dịch!", 10, "Thiên cơ các");
				return;
			}
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 18, array, 0, 4);
			Buffer.BlockCopy(packetData, 10, array2, 0, 4);
			var nhanVatId = BitConverter.ToInt32(array, 0);
			var num = BitConverter.ToInt32(array2, 0);
			switch (num)
			{
				case 1:
					SendTransactionRequest(nhanVatId, num);
					break;
				case 2:
					AcceptTransactionRequest(nhanVatId, num);
					break;
				case 3:
					TheOtherPartyCancelsTheTransactionRequest(nhanVatId, num);
					break;
				case 4:
					CancelTheTransactionRequest(nhanVatId, num);
					break;
				case 5:
					AgreeToAccept(nhanVatId, num);
					break;
				case 6:
					CloseTransaction(nhanVatId, num);
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GiaoDich Danh mục lớn error [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.ToString());
		}
	}

	public void AgreeToAccept(int nhanVatId, int thaoTacD)
	{
		try
		{
			Gdtime = DateTime.Now;
			HeThongNhacNho("Bắt đầu chấp nhận giao dịch. Thời gian: " + Gdtime.ToString("dd/MM/yyyy hh:mm:ss"));
			if (Exiting)
			{
				return;
			}
			var array = Converter.HexStringToByte("AA554200000098003400010000000*********000000000000000000000000000000000000000000000000000000000000000000000000000000000*********00000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 10, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 14, 4);
			var bytes = Encoding.Default.GetBytes(CharacterName);
			Buffer.BlockCopy(bytes, 0, array, 22, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 18, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.SendMultiplePackage(array, array.Length);

			GiaoDich.NguoiGiaoDich.Client?.SendMultiplePackage(array, array.Length);
			GiaoDich.GiaoDich_TiepNhan = true;
			if (GiaoDich.NguoiGiaoDich.GiaoDich.GiaoDich_TiepNhan)
			{
				if (GiaoDich.NguoiGiaoDich.Client.Running)
				{
					var flag = SuccessfulTransaction(this, icomplete: true);
					GiaoDich.GiaoDichVatPham1.Clear();
					UpdateMoneyAndWeight();
					var flag2 = SuccessfulTransaction(GiaoDich.NguoiGiaoDich, icomplete: true);
					SaveCharacterDataAsync().GetAwaiter().GetResult();
					GiaoDich.NguoiGiaoDich.SaveCharacterDataAsync().GetAwaiter().GetResult();
					GiaoDich.NguoiGiaoDich.GiaoDich.GiaoDichVatPham1.Clear();
					GiaoDich.NguoiGiaoDich.UpdateMoneyAndWeight();
					OpenWarehouse = false;
					GiaoDich.NguoiGiaoDich.OpenWarehouse = false;
					GiaoDich.CloseTransaction();
					Gdtime = DateTime.Now;
					if (!flag2)
					{
						HeThongNhacNho("Giao dịch thất bại. Đối phương có hành vi duple vật phẩm. Thời gian: " + Gdtime.ToString("dd/MM/yyyy hh:mm:ss"));
					}
					else
					{
						HeThongNhacNho("Giao dịch thành công. Thời gian: " + Gdtime.ToString("dd/MM/yyyy hh:mm:ss"));
					}
				}
			}
			else
			{
				Gdtime = DateTime.Now;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đồng ý chấp nhận error [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.ToString());
		}
	}

	public void CloseTransaction(int nhanVatId, int thaoTacD)
	{
		try
		{
			var array = Converter.HexStringToByte("AA554200000098003400010000000*********000000000000000000000000000000000000000000000000000000000000000000000000000000000*********00000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 10, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 14, 4);
			var bytes = Encoding.Default.GetBytes(CharacterName);
			Buffer.BlockCopy(bytes, 0, array, 22, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 18, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);

			GiaoDich.NguoiGiaoDich.Client?.Send_Map_Data(array, array.Length);
			GiaoDich.CloseTransaction();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Khép kín GiaoDich error  [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.ToString());
		}
	}

	public void AcceptTransactionRequest(int nhanVatId, int ThaoTacD)
	{
		try
		{
			// Form1.WriteLine(88, $"Request AccepTransactionRequest");
			var response = Converter.HexStringToByte("aa553500f60998002d00020000000**********0000000000000000000000000000000000000000000000000070000000000000000000055aa");
			System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, response, 10, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, response, 14, 4);
			var userName1 = Encoding.GetEncoding(World.Language_Charset).GetBytes(GiaoDich.NguoiGiaoDich.CharacterName);
			System.Buffer.BlockCopy(userName1, 0, response, 22, userName1.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, response, 4, 2);
			base.Client?.Send_Map_Data(response, response.Length);
			if (GiaoDich.NguoiGiaoDich.Client == null) return;
			response = Converter.HexStringToByte("aa553500f60998002d00020000000**********0000000000000000000000000000000000000000000000000070000000000000000000055aa");
			GiaoDich.GiaoDichBenTrong = true;
			GiaoDich.NguoiGiaoDich.GiaoDich.GiaoDichBenTrong = true;
			OpenWarehouse = true;
			GiaoDich.NguoiGiaoDich.OpenWarehouse = true;
			OpenWarehouse = true;
			GiaoDich.NguoiGiaoDich.OpenWarehouse = true;
			System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, response, 10, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, response, 14, 4);
			var userName2 = Encoding.GetEncoding(World.Language_Charset).GetBytes(base.CharacterName);
			System.Buffer.BlockCopy(userName2, 0, response, 22, userName2.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(GiaoDich.NguoiGiaoDich.SessionID), 0, response, 4, 2);
			if (GiaoDich.NguoiGiaoDich.Client == null) return;
			GiaoDich.NguoiGiaoDich.Client.Send_Map_Data(response, response.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "AcceptTransactionRequest error [" + base.AccountID + "][" + base.CharacterName + "][" + base.Client.ToString() + "]  " + ex.ToString());
		}
	}

	public void Offline_GiaoDich(Players player, int nhanVatId, int thaoTacD)
	{
		try
		{
			if (player.GiaoDich != null && player.GiaoDich.NguoiGiaoDich != null)
			{
				var array = Converter.HexStringToByte("AA554200000098003400010000000*********000000000000000000000000000000000000000000000000000000000000000000000000000000000*********00000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 10, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 14, 4);
				var bytes = Encoding.Default.GetBytes(player.CharacterName);
				Buffer.BlockCopy(bytes, 0, array, 22, bytes.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 18, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				player.Client?.Send_Map_Data(array, array.Length);
				player.GiaoDich.NguoiGiaoDich.Client?.SendMultiplePackage(array, array.Length);
				player.GiaoDich.CloseTransaction();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Bên kia đã hủy GiaoDich hỏi 111 error  [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.Message);
		}
	}

	public void TheOtherPartyCancelsTheTransactionRequest(int nhanVatId, int thaoTacD)
	{
		try
		{
			var array = Converter.HexStringToByte("AA554200000098003400010000000*********000000000000000000000000000000000000000000000000000000000000000000000000000000000*********00000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 10, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 14, 4);
			var bytes = Encoding.Default.GetBytes(CharacterName);
			Buffer.BlockCopy(bytes, 0, array, 22, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 18, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);

			GiaoDich.NguoiGiaoDich.Client?.SendMultiplePackage(array, array.Length);
			GiaoDich.CloseTransaction();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Bên kia đã hủy GiaoDich hỏi 222 error  [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.Message);
		}
	}

	public void CancelTheTransactionRequest(int nhanVatId, int thaoTacD)
	{
		try
		{
			var array = Converter.HexStringToByte("AA554200000098003400010000000*********000000000000000000000000000000000000000000000000000000000000000000000000000000000*********00000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(6), 0, array, 10, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(6), 0, array, 14, 4);
			var bytes = Encoding.Default.GetBytes(CharacterName);
			Buffer.BlockCopy(bytes, 0, array, 23, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 18, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);

			GiaoDich.NguoiGiaoDich.Client?.SendMultiplePackage(array, array.Length);
			GiaoDich.CloseTransaction();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tôi hủy bỏ GiaoDich hỏi error [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.Message);
		}
	}

	public void SendTransactionRequest(int nhanVatId, int thaoTacD)
	{
		try
		{
			if (Exiting || InTheShop || OpenWarehouse || nhanVatId == SessionID || PlayerTuVong || NhanVat_HP <= 0 || (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa) || (GiaoDich != null && GiaoDich.NguoiGiaoDich != null))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA554200000098003400010000000*********000000000000000000000000000000000000000000000000000000000000000000000000000000000*********00000000000055AA");
			var characterData = GetCharacterData(nhanVatId);
			if (characterData == null || characterData.Exiting || characterData.OpenWarehouse || characterData.InTheShop || characterData.PlayerTuVong || characterData.NhanVat_HP <= 0 || (characterData.CuaHangCaNhan != null && characterData.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa) || (characterData.GiaoDich != null && characterData.GiaoDich.NguoiGiaoDich != null))
			{
				return;
			}
			if (characterData.Config.GiaoDich == 0)
			{
				HeThongNhacNho("Cài đặt của đối phương không cho phép giao dịch!", 10, "Thiên cơ các");
			}
			else if (characterData.Player_Level >= World.CapDoChoPhepGiaoDich)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 10, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array, 18, 4);
				var bytes = Encoding.Default.GetBytes(CharacterName);
				Buffer.BlockCopy(bytes, 0, array, 22, bytes.Length);
				GiaoDich = new X_Giao_Dich_Loai(characterData);
				characterData.GiaoDich = new X_Giao_Dich_Loai(this);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				characterData.Client?.SendMultiplePackage(array, array.Length);
				Client?.SendMultiplePackage(array, array.Length);
			}
			else
			{
				HeThongNhacNho("Nhân vật [" + characterData.CharacterName + "] dưới cấp [" + World.CapDoChoPhepGiaoDich + "], không thể giao dịch!", 10, "Thiên cơ các");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "hỏi GiaoDicherror [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  " + ex.Message);
		}
	}

}
