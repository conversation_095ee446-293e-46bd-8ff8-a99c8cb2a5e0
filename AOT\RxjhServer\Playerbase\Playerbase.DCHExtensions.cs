﻿using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace RxjhServer;

public partial class PlayersBes : <PERSON>_<PERSON><PERSON>_Cong_Thuoc_Tinh
{
    
	public void DCH_DOIXANH_ONETOONE(Players player, Players player2, int type)
	{
		var text = "AA552B00E50108512500010000002C010000010000000000000000000000320000001014000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 29, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 14, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
		if (player2.Client != null)
		{
			player2.Client.Send_Map_Data(array, array.Length);
		}
	}

	public void DCH_DOIXANH(Players player, int type)
	{
		var text = "AA552B00E50108512500010000002C010000010000000000000000000000320000001014000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 29, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 14, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
		player.SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void DCH_DOIXANH_EFFECT_FIRSTGAME(Players player, Players toPlayer, int type)
	{
		var text = "AA552B00E50108512500010000002C010000010000000000000000000000320000001014000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 29, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(toPlayer.SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 14, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
		if (toPlayer.Client != null)
		{
			toPlayer.Client.Send_Map_Data(array, array.Length);
		}
	}

	public void DCH_DOIXANH_EFFECT(Players player, Players toPlayer, int type, List<Players> CungNhau)
	{
		if (CungNhau != null && CungNhau.Count > 0)
		{
			if (CungNhau.Count == 1)
			{
				var text = "AA5514007B0330510C005E2D000000000000EF6F000055AA";
				var array = Converter.HexStringToByte(text);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array, array.Length);
				}
				player.SendCurrentRangeBroadcastData(array, array.Length);
				player.DCH_HP_BONUS = 4000;
				player.DCH_ATTACK_BONUS = 200;
				player.DCH_DEF_BONUS = 200;
				player.DCH_CLVC_BONUS = 0.2;
				player.DCH_ULPT_BONUS = 0.2;
			}
			else if (CungNhau.Count == 2)
			{
				var text2 = "AA551400900330510C004A2A0000851000001768000055AA";
				var array2 = Converter.HexStringToByte(text2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array2, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array2, array2.Length);
				}
				player.SendCurrentRangeBroadcastData(array2, array2.Length);
				player.DCH_HP_BONUS = 6000;
				player.DCH_ATTACK_BONUS = 400;
				player.DCH_DEF_BONUS = 400;
				player.DCH_CLVC_BONUS = 0.4;
				player.DCH_ULPT_BONUS = 0.4;
			}
			else if (CungNhau.Count == 3)
			{
				var text3 = "AA551400A00230510C002146000085100000D670000055AA";
				var array3 = Converter.HexStringToByte(text3);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array3, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array3, array3.Length);
				}
				player.SendCurrentRangeBroadcastData(array3, array3.Length);
				player.DCH_HP_BONUS = 8000;
				player.DCH_ATTACK_BONUS = 600;
				player.DCH_DEF_BONUS = 600;
				player.DCH_CLVC_BONUS = 0.6;
				player.DCH_ULPT_BONUS = 0.6;
			}
			else if (CungNhau.Count >= 4)
			{
				var text4 = "AA5514002D0130510C00AD210000294F0000D670000055AA";
				var array4 = Converter.HexStringToByte(text4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array4, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array4, array4.Length);
				}
				player.SendCurrentRangeBroadcastData(array4, array4.Length);
				player.DCH_HP_BONUS = 10000;
				player.DCH_ATTACK_BONUS = 800;
				player.DCH_DEF_BONUS = 800;
				player.DCH_CLVC_BONUS = 0.8;
				player.DCH_ULPT_BONUS = 0.8;
			}
		}
		else
		{
			var text5 = "AA551200260430510C0085100000000000000000000055AA";
			var array5 = Converter.HexStringToByte(text5);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array5, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array5, array5.Length);
			}
			player.SendCurrentRangeBroadcastData(array5, array5.Length);
			player.DCH_HP_BONUS = 0;
			player.DCH_ATTACK_BONUS = 0;
			player.DCH_DEF_BONUS = 0;
			player.DCH_CLVC_BONUS = 0.0;
			player.DCH_ULPT_BONUS = 0.0;
		}
		player.CapNhat_HP_MP_SP();
		player.UpdateMartialArtsAndStatus();
		player.UpdateBroadcastCharacterData();
		player.UpdateCharacterData(player);
	}

	public void DCH_DOIDO_EFFECT_FIRSTGAME(Players player, Players toPlayer, int type)
	{
		var text = "AA552B009C0308512500010000002C01000002003D000000003200000000320000001014000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 29, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(toPlayer.SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 14, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
		if (toPlayer.Client != null)
		{
			toPlayer.Client.Send_Map_Data(array, array.Length);
		}
	}

	public void DCH_DOIDO_EFFECT(Players player, Players toPlayer, int type, List<Players> CungNhau)
	{
		if (CungNhau != null && CungNhau.Count > 0)
		{
			if (CungNhau.Count == 1)
			{
				var text = "AA5514007B0330510C005E2D000000000000EF6F000055AA";
				var array = Converter.HexStringToByte(text);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array, array.Length);
				}
				player.SendCurrentRangeBroadcastData(array, array.Length);
				player.DCH_HP_BONUS = 4000;
				player.DCH_ATTACK_BONUS = 200;
				player.DCH_DEF_BONUS = 200;
				player.DCH_CLVC_BONUS = 0.2;
				player.DCH_ULPT_BONUS = 0.2;
			}
			else if (CungNhau.Count == 2)
			{
				var text2 = "AA551400900330510C004A2A0000851000001768000055AA";
				var array2 = Converter.HexStringToByte(text2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array2, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array2, array2.Length);
				}
				player.SendCurrentRangeBroadcastData(array2, array2.Length);
				player.DCH_HP_BONUS = 6000;
				player.DCH_ATTACK_BONUS = 400;
				player.DCH_DEF_BONUS = 400;
				player.DCH_CLVC_BONUS = 0.4;
				player.DCH_ULPT_BONUS = 0.4;
			}
			else if (CungNhau.Count == 3)
			{
				var text3 = "AA551400A00230510C002146000085100000D670000055AA";
				var array3 = Converter.HexStringToByte(text3);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array3, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array3, array3.Length);
				}
				player.SendCurrentRangeBroadcastData(array3, array3.Length);
				player.DCH_HP_BONUS = 8000;
				player.DCH_ATTACK_BONUS = 600;
				player.DCH_DEF_BONUS = 600;
				player.DCH_CLVC_BONUS = 0.6;
				player.DCH_ULPT_BONUS = 0.6;
			}
			else if (CungNhau.Count >= 4)
			{
				var text4 = "AA5514002D0130510C00AD210000294F0000D670000055AA";
				var array4 = Converter.HexStringToByte(text4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array4, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array4, array4.Length);
				}
				player.SendCurrentRangeBroadcastData(array4, array4.Length);
				player.DCH_HP_BONUS = 10000;
				player.DCH_ATTACK_BONUS = 800;
				player.DCH_DEF_BONUS = 800;
				player.DCH_CLVC_BONUS = 0.8;
				player.DCH_ULPT_BONUS = 0.8;
			}
		}
		else
		{
			var text5 = "AA551200260430510C0085100000000000000000000055AA";
			var array5 = Converter.HexStringToByte(text5);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array5, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array5, array5.Length);
			}
			player.SendCurrentRangeBroadcastData(array5, array5.Length);
			player.DCH_HP_BONUS = 0;
			player.DCH_ATTACK_BONUS = 0;
			player.DCH_DEF_BONUS = 0;
			player.DCH_CLVC_BONUS = 0.0;
			player.DCH_ULPT_BONUS = 0.0;
		}
		player.CapNhat_HP_MP_SP();
		player.UpdateMartialArtsAndStatus();
		player.UpdateBroadcastCharacterData();
		player.UpdateCharacterData(player);
	}

	public void DCH_DOIDO(Players player, int type)
	{
		var text = "AA552B009C0308512500010000002C01000002003D000000003200000000320000001014000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 29, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 14, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
		player.SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void DCH_DOIDO_ONETOONE(Players player, Players player2, int type)
	{
		var text = "AA552B009C0308512500010000002C01000002003D000000003200000000320000001014000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 29, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 14, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
		if (player2.Client != null)
		{
			player2.Client.Send_Map_Data(array, array.Length);
		}
	}


}
