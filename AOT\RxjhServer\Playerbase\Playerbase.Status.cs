﻿using HeroYulgang.Constants;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class PlayersBes : X_Khi_Cong_Thuoc_Tinh
{

    
	public void HeadGlow(int enable, int hexColor)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551E00DC0102311000DC0100000000000001000000FFEF0100000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 10, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(enable), 0, array, 0x12, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(hexColor), 0, array, 0x16, 1);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
			SendCurrentRangeBroadcastData(array, array.Length);
		}
		catch (Exception)
		{
		}
	}

	public void HeadGlow(Players players_0)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551E00DC0102311000DC0100000000000001000000FFEF0100000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(players_0.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(players_0.SessionID), 0, array, 10, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
			SendCurrentRangeBroadcastData(array, array.Length);
		}
		catch (Exception)
		{
		}
	}

	public void Event_x2_Sunday(Players Play)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(World.IdItemX2))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, World.IdItemX2, 0.0);
				Play.AppendStatusList.Add(World.IdItemX2, value);
				Play.StatusEffect(BitConverter.GetBytes(World.IdItemX2), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan = 1.0;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
				Play.HeThongNhacNho(" [+" + FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan * 100.0 + "%] Kinh nghiệm [Chủ Nhật] (Bắt đầu)!", 10, "Truyền Âm Các");
			}
		}
		catch
		{
		}
	}

	public void Event_x3_Saturday(Players Play)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(World.IdItemX3))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, World.IdItemX3, 0.0);
				Play.AppendStatusList.Add(World.IdItemX3, value);
				Play.StatusEffect(BitConverter.GetBytes(World.IdItemX3), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan = 1.0;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
				Play.HeThongNhacNho(" [+" + FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan * 100.0 + "%] Kinh nghiệm [Thứ Bảy] (Bắt đầu)!", 10, "Truyền Âm Các");
			}
		}
		catch
		{
		}
	}

	public void Event_x4(Players Play)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(World.IdItem_Bonus))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, World.IdItem_Bonus, 0.0);
				Play.AppendStatusList.Add(World.IdItem_Bonus, value);
				Play.StatusEffect(BitConverter.GetBytes(World.IdItem_Bonus), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_Bonus = World.Event_Bonus_Rate;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
				Play.HeThongNhacNho(" [+" + FLD_NhanVat_ThemVao_KinhNghiem_Bonus * 100.0 + "%] Kinh nghiệm [Sự Kiện Đặc Biệt] (Bắt đầu)!", 10, "Truyền Âm Các");
			}
		}
		catch
		{
		}
	}

	public void Buff_Party_Tu_2_Den_7_Nguoi(Players Play)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(World.IdItem_Party))
			{
				var num = 300000;
				StatusEffect value = new(Play, num, World.IdItem_Party, 0);
				Play.AppendStatusList.Add(World.IdItem_Party, value);
				Play.StatusEffect(BitConverter.GetBytes(World.IdItem_Party), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_Party = 0.1;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
			}
		}
		catch
		{
		}
	}

	public void Buff_Party_8_Nguoi(Players Play)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(World.IdItem_Party))
			{
				var num = 300000;
				StatusEffect value = new(Play, num, World.IdItem_Party, 0);
				Play.AppendStatusList.Add(World.IdItem_Party, value);
				Play.StatusEffect(BitConverter.GetBytes(World.IdItem_Party), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_Party = 0.3;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
			}
		}
		catch
		{
		}
	}

	public void Event_xExp_Player_Job_leve_8(Players Play, int VatPhamID)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(VatPhamID))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, VatPhamID, 0.0);
				Play.AppendStatusList.Add(VatPhamID, value);
				Play.StatusEffect(BitConverter.GetBytes(VatPhamID), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_TanThu = 1.0;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
			}
		}
		catch
		{
		}
	}

	public void Event_xExp_Player_Job_leve_9(Players Play, int VatPhamID)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(VatPhamID))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, VatPhamID, 0.0);
				Play.AppendStatusList.Add(VatPhamID, value);
				Play.StatusEffect(BitConverter.GetBytes(VatPhamID), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_TanThu = 0.5;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
			}
		}
		catch
		{
		}
	}

	public void Event_xExp_Player_Job_leve_10(Players Play, int VatPhamID)
	{
		try
		{
			if (Play.AppendStatusList != null && !Play.AppendStatusList.ContainsKey(VatPhamID))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, VatPhamID, 0.0);
				Play.AppendStatusList.Add(VatPhamID, value);
				Play.StatusEffect(BitConverter.GetBytes(VatPhamID), 1, num);
				Play.FLD_NhanVat_ThemVao_KinhNghiem_TanThu = 0.25;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
			}
		}
		catch
		{
		}
	}

	public void Event_xExp_CTP(Players Play)
	{
		try
		{
			if (!Play.AppendStatusList.ContainsKey(1000000920))
			{
				var num = ((23 - DateTime.Now.Hour) * 3600 + (59 - DateTime.Now.Minute) * 60 + (59 - DateTime.Now.Second + 5)) * 1000;
				StatusEffect value = new(Play, num, 1000000920, 0);
				Play.AppendStatusList.Add(1000000920, value);
				Play.StatusEffect(BitConverter.GetBytes(1000000920), 1, num);
				Play.FLD_NhanVat_ThemVao_Exp_CTP = 0.1;
				Play.UpdateMartialArtsAndStatus();
				Play.CapNhat_HP_MP_SP();
			}
		}
		catch
		{
		}
	}

	public void Buff_HP_TLC_TayDai(Players Play)
	{
		var num = 1800000;
		StatusEffect value = new(Play, num, 1008001140, 1);
		Play.AppendStatusList.Add(1008001140, value);
		Play.StatusEffect(BitConverter.GetBytes(1008001140), 1, num);
		Play.CharactersToAddMax_HP += 5000;
		Play.UpdateMartialArtsAndStatus();
		Play.UpdateCharacterData(Play);
		Play.CapNhat_HP_MP_SP();
	}

	public void Buff_HP_TLC_TayNgan(Players Play)
	{
		var num = 1800000;
		StatusEffect value = new(Play, num, 1008001139, 1);
		Play.AppendStatusList.Add(1008001139, value);
		Play.StatusEffect(BitConverter.GetBytes(1008001139), 1, num);
		Play.CharactersToAddMax_HP += 10000;
		Play.UpdateMartialArtsAndStatus();
		Play.UpdateCharacterData(Play);
		Play.CapNhat_HP_MP_SP();
	}


	public bool KiemTraHongNguyetCuongPhongTrangThai()
	{
		return GetAddState(1008001172);
	}

	public bool KiemTraHongNguyetCuongPhongTrangThai_New()
	{
		return GetAddState(1008001694);
	}

	public bool KiemTraAiHongBienDaTrangThai()
	{
		return GetAddState(1008001176);
	}

	public bool KiemTraManNguyetCuongPhongTrangThai()
	{
		if (!GetAddState(1008001171))
		{
			return NoKhi;
		}
		return true;
	}

	public bool KiemTraDocXaXuatDongTrangThai()
	{
		return GetAddState(1008001170);
	}

	public bool KiemTraTriTanTrangThai()
	{
		return GetAddState(1008002012);
	}

	public bool KiemTraLietNhatViemViemTrangThai()
	{
		return GetAddState(1008001169);
	}

	public bool KiemTraTruongHongQuanThienTrangThai()
	{
		return GetAddState(1008001173);
	}

	public bool KiemTraHopAmTrangThai()
	{
		if (!AppendStatusList.ContainsKey(900401) && !AppendStatusList.ContainsKey(900402))
		{
			return AppendStatusList.ContainsKey(900403);
		}
		return true;
	}
	
	public void ShowBigPrint(int int_109, int int_110)
	{
		try
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(int_109);
			sendingClass.Write4(int_110);
			sendingClass.Write4(0);
			if (Client != null)
			{
				Client.SendPak(sendingClass, 35072, SessionID);
				SendCurrentRangeBroadcastData(sendingClass, 35072, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "hiển thị hiệu ứng khí công LỖI !!! " + ex.Message);
		}
	}

	public void ItemUse(int int_109, int int_110, int int_111)
	{
		BitConverter.GetBytes(int_110);
		BitConverter.GetBytes(int_111);
		var num = BitConverter.ToInt32((int_109 != 1) ? CharacterBeast.ThuCung_Thanh_TrangBi[int_110].VatPham_ID : Item_In_Bag[int_110].VatPham_ID, 0);
		if (int_109 == 1)
		{
			if (BitConverter.ToInt32(Item_In_Bag[int_110].VatPhamSoLuong, 0) <= int_111)
			{
				Item_In_Bag[int_110].VatPham_byte = new byte[76];
				BitConverter.GetBytes(0);
			}
			else
			{
				var bytes = BitConverter.GetBytes(BitConverter.ToInt32(Item_In_Bag[int_110].VatPhamSoLuong, 0) - int_111);
				Item_In_Bag[int_110].VatPhamSoLuong = bytes;
			}
		}
		else if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[int_110].VatPhamSoLuong, 0) <= int_111)
		{
			CharacterBeast.ThuCung_Thanh_TrangBi[int_110].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			BitConverter.GetBytes(0);
		}
		else
		{
			var bytes2 = BitConverter.GetBytes(BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[int_110].VatPhamSoLuong, 0) - int_111);
			CharacterBeast.ThuCung_Thanh_TrangBi[int_110].VatPhamSoLuong = bytes2;
		}
		SendingClass sendingClass = new();
		sendingClass.Write1(int_109);
		sendingClass.Write1(int_110);
		sendingClass.Write2(0);
		sendingClass.Write8(Item_In_Bag[int_110].GetVatPham_ID);
		if (int_111 == 0)
		{
			if (int_109 == 1)
			{
				sendingClass.Write4(Item_In_Bag[int_110].GetVatPhamSoLuong);
			}
			else
			{
				sendingClass.Write4(CharacterBeast.ThuCung_Thanh_TrangBi[int_110].GetVatPhamSoLuong);
			}
		}
		else
		{
			sendingClass.Write4(int_111);
		}
		if (int_109 == 1)
		{
			sendingClass.Write4(Item_In_Bag[int_110].GetVatPhamSoLuong);
		}
		else
		{
			sendingClass.Write4(CharacterBeast.ThuCung_Thanh_TrangBi[int_110].GetVatPhamSoLuong);
		}
		if (int_109 == 1 && num == ItemConstants.HOP_AUTO)
		{
			sendingClass.Write4(10111);
		}
		else
		{
			sendingClass.Write4(0);
		}
		sendingClass.Write4(0);
		if (Client != null)
		{
			Client.SendPak(sendingClass, 15104, SessionID);
		}
	}

	public void ItemUseRing(int int_109, int int_110, int int_111)
	{
		var array = Converter.HexStringToByte("AA55220000003B001400010A000065CA9A3B010000006100000000000000000000000000000055AAA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 11, 2);
		System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_ID, 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_111), 0, array, 18, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void ItemUse_SamTuyetNganNam(int int_109, int int_110)
	{
		try
		{
			if (Poisoning)
			{
				int_110 *= 2;
			}
			var array = Converter.HexStringToByte("AA552B002C013B001C000105000065CA9A3B000000000100000009000000000000000000000000000000000000000055AA");
			var array2 = new byte[4];
			System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_byte, 20, array2, 0, 4);
			var num = BitConverter.ToInt32(array2, 0) - 2010000000 - int_110;
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 11, 2);
			System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_ID, 0, array, 14, 4);
			if (num <= 0)
			{
				if (BitConverter.ToInt32(Item_In_Bag[int_109].VatPhamSoLuong, 0) <= 1)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(2010000000), 0, array, 30, 4);
					Item_In_Bag[int_109].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					var value = BitConverter.ToInt32(Item_In_Bag[int_109].VatPhamSoLuong, 0) - 1;
					Item_In_Bag[int_109].VatPhamSoLuong = BitConverter.GetBytes(value);
					if (Item_In_Bag[int_109].GetVatPham_ID != 1008000044 && Item_In_Bag[int_109].GetVatPham_ID != 1008000045)
					{
						if (Item_In_Bag[int_109].GetVatPham_ID != 1008000003 && Item_In_Bag[int_109].GetVatPham_ID != 1008000006)
						{
							if (Item_In_Bag[int_109].GetVatPham_ID != 1008000007 && Item_In_Bag[int_109].GetVatPham_ID != 1008000008)
							{
								if (Item_In_Bag[int_109].GetVatPham_ID != 1008000068 && Item_In_Bag[int_109].GetVatPham_ID != 1008000069)
								{
									System.Buffer.BlockCopy(BitConverter.GetBytes(2010600000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
								}
								else
								{
									System.Buffer.BlockCopy(BitConverter.GetBytes(2011000000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
								}
							}
							else
							{
								System.Buffer.BlockCopy(BitConverter.GetBytes(2010400000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
							}
						}
						else
						{
							System.Buffer.BlockCopy(BitConverter.GetBytes(2011000000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
						}
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(2019999999), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
					}
					System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPhamSoLuong, 0, array, 26, 4);
					if (Item_In_Bag[int_109].GetVatPham_ID != 1008000044 && Item_In_Bag[int_109].GetVatPham_ID != 1008000045)
					{
						if (Item_In_Bag[int_109].GetVatPham_ID != 1008000003 && Item_In_Bag[int_109].GetVatPham_ID != 1008000006)
						{
							if (Item_In_Bag[int_109].GetVatPham_ID != 1008000007 && Item_In_Bag[int_109].GetVatPham_ID != 1008000008)
							{
								if (Item_In_Bag[int_109].GetVatPham_ID != 1008000068 && Item_In_Bag[int_109].GetVatPham_ID != 1008000069)
								{
									System.Buffer.BlockCopy(BitConverter.GetBytes(2010600000), 0, array, 30, 4);
								}
								else
								{
									System.Buffer.BlockCopy(BitConverter.GetBytes(2011000000), 0, array, 30, 4);
								}
							}
							else
							{
								System.Buffer.BlockCopy(BitConverter.GetBytes(2010400000), 0, array, 30, 4);
							}
						}
						else
						{
							System.Buffer.BlockCopy(BitConverter.GetBytes(2011000000), 0, array, 30, 4);
						}
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(2019999999), 0, array, 30, 4);
					}
				}
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(num + 2010000000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
				System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPhamSoLuong, 0, array, 26, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(num + 2010000000), 0, array, 30, 4);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void ItemUse_VoSongCuuChuyenDan(int int_109, int int_110)
	{
		try
		{
			if (Poisoning)
			{
				int_110 *= 2;
			}
			var array = Converter.HexStringToByte("AA552200FF033B001C00010000004DDC143C000000000100000001000000923192410000000055AA");
			var array2 = new byte[4];
			System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_byte, 20, array2, 0, 4);
			var num = (int)(((Item_In_Bag[int_109].GetVatPham_ID != 1008000077 || Item_In_Bag[int_109].GetVatPham_ID != 1008000079) ? (((BitConverter.ToInt32(array2, 0) - 1100000000) / 100 * CharacterMax_HP - (double)int_110) / (1000 * CharacterMax_HP)) : (((BitConverter.ToInt32(array2, 0) - 1100000000) / 100 * CharacterMax_HP - (double)int_110) / (1000 * CharacterMax_HP))) * 100000.0);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 11, 2);
			System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_ID, 0, array, 14, 4);
			if (num <= 0)
			{
				if (BitConverter.ToInt32(Item_In_Bag[int_109].VatPhamSoLuong, 0) <= 1)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1100000000), 0, array, 30, 4);
					Item_In_Bag[int_109].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					var value = BitConverter.ToInt32(Item_In_Bag[int_109].VatPhamSoLuong, 0) - 1;
					Item_In_Bag[int_109].VatPhamSoLuong = BitConverter.GetBytes(value);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1100100000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
					System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPhamSoLuong, 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1100100000), 0, array, 30, 4);
				}
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(num + 1100000000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
				System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPhamSoLuong, 0, array, 26, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(num + 1100000000), 0, array, 30, 4);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void ItemUse_VoSong_SamTuyetNganNam(int int_109, int int_110)
	{
		try
		{
			if (Poisoning)
			{
				int_110 *= 2;
			}
			var array = Converter.HexStringToByte("AA552200FF033B001C00010000004DDC143C000000000100000001000000923192410000000055AA");
			var array2 = new byte[4];
			System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_byte, 20, array2, 0, 4);
			var num = (int)(((Item_In_Bag[int_109].GetVatPham_ID != 1008000078 || Item_In_Bag[int_109].GetVatPham_ID != 1008000080) ? (((BitConverter.ToInt32(array2, 0) - 1110000000) / 100 * CharacterMax_MP - (double)int_110) / (1000 * CharacterMax_MP)) : (((BitConverter.ToInt32(array2, 0) - 1110000000) / 100 * CharacterMax_MP - (double)int_110) / (1000 * CharacterMax_MP))) * 100000.0);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 11, 2);
			System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPham_ID, 0, array, 14, 4);
			if (num <= 0)
			{
				if (BitConverter.ToInt32(Item_In_Bag[int_109].VatPhamSoLuong, 0) <= 1)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1110000000), 0, array, 30, 4);
					Item_In_Bag[int_109].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					var value = BitConverter.ToInt32(Item_In_Bag[int_109].VatPhamSoLuong, 0) - 1;
					Item_In_Bag[int_109].VatPhamSoLuong = BitConverter.GetBytes(value);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1110100000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
					System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPhamSoLuong, 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1110100000), 0, array, 30, 4);
				}
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(num + 1110000000), 0, Item_In_Bag[int_109].VatPham_byte, 20, 4);
				System.Buffer.BlockCopy(Item_In_Bag[int_109].VatPhamSoLuong, 0, array, 26, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(num + 1110000000), 0, array, 30, 4);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void Update_CongKichTocDo()
	{
		var array = Converter.HexStringToByte("AA551A00B20206200C00060000008801580064000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_CongKichTocDo), 0, array, 18, 4);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void SendActivationSkillData(int skillID, int int_110)
	{
		var array = Converter.HexStringToByte("AA552E00A2013D00200026B706000100000000000000000000000000000000000000E803000001000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(skillID), 0, array, 10, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 14, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (CurrentlyActiveSkill_ID == 0)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 38, 2);
		}
		else
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 38, 2);
		}
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void Uncall(Players players_0, Players players_1)
	{
		var array = Converter.HexStringToByte("AA551600549C6300080001000000549C0001000000000000A37B55AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(players_1.CharacterBeast.FullServiceID), 0, array, 14, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(players_0.SessionID), 0, array, 4, 2);
		players_0.Client?.SendMultiplePackage(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void NotifyPlayerExit(Players players_0, Players players_1)
	{
		try
		{
			var array = Converter.HexStringToByte("AA5513000300630008000100000003000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(players_1.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(players_0.SessionID), 0, array, 4, 2);
			players_0.Client?.SendMultiplePackage(array, array.Length);
			if (players_1.CharacterBeast != null)
			{
				var array2 = Converter.HexStringToByte("AA551600549C6300080001000000549C0001000000000000A37B55AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(players_1.CharacterBeast.FullServiceID), 0, array2, 14, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(players_0.SessionID), 0, array2, 4, 2);
				players_0.Client?.SendMultiplePackage(array2, array2.Length);
			}
		}
		catch
		{
		}
	}

	public bool PrivateState(int Key)
	{
		if (AppendStatusList == null)
		{
			AppendStatusList = new();
			return false;
		}
		if (AppendStatusList.Count <= 0)
		{
			return false;
		}
		StatusEffect value;
		return AppendStatusList.TryGetValue(Key, out value);
	}


	public void SendASecurityCodeMessage(int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552B002C0138151C000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
			AnToan_MatMa_ThaoTacID = int_109;
		}
		catch
		{
		}
	}

	public void SendGangMessage(string string_11, byte[] byte_0, int int_109)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.GuildName == string_11 && value.Client != null)
				{
					value.Client.Send_Map_Data(byte_0, int_109);
				}
			}
		}
		catch
		{
		}
	}


	public void AddBlood(int int_109)
	{
		if (Poisoning)
		{
			int_109 -= (int)(int_109 * 0.2);
		}
		if (Player_Job == 3)
		{
			int_109 = (int)(int_109 * (1.0 + THUONG_VanKhi_LieuThuong));
		}
		if (Player_Job != 3 && Player_Job_level >= 6)
		{
			int_109 += (int)ThangThien_1_KhiCong_VanKhi_LieuThuong;
		}
		if (NhanVat_HP + int_109 < CharacterMax_HP)
		{
			NhanVat_HP += int_109;
		}
		else
		{
			NhanVat_HP = CharacterMax_HP;
		}
	}

	public void MagicPlus(int int_109)
	{
		if (Poisoning)
		{
			int_109 -= (int)(int_109 * 0.2);
		}
		if (Player_Job == 5)
		{
			int_109 = (int)(int_109 * (1.0 + DAIPHU_VanKhiLieuTam));
		}
		if (Player_Job == 13)
		{
			int_109 = (int)(int_109 * (1.0 + ThanNu_VanKhiHanhTam));
		}
		if (Player_Job != 5 && Player_Job_level >= 6)
		{
			int_109 = (int)(int_109 * (1.0 + ThangThien_1_KhiCong_VanKhiHanhTam));
		}
		if (NhanVat_MP + int_109 < CharacterMax_MP)
		{
			NhanVat_MP += int_109;
		}
		else
		{
			NhanVat_MP = CharacterMax_MP;
		}
	}

	public void AddBloodBest(int int_109)
	{
		if (CharacterBeast.FLD_HP + int_109 < CharacterBeast.Beast_BasicallyTheLargest_HP)
		{
			CharacterBeast.FLD_HP += int_109;
		}
		else
		{
			CharacterBeast.FLD_HP = CharacterBeast.Beast_BasicallyTheLargest_HP;
		}
	}

	public void ThemMaPhap_LinhThu(int int_109)
	{
		if (CharacterBeast.FLD_MP + int_109 < CharacterBeast.Beast_BasicallyTheLargest_MP)
		{
			CharacterBeast.FLD_MP += int_109;
		}
		else
		{
			CharacterBeast.FLD_MP = CharacterBeast.Beast_BasicallyTheLargest_MP;
		}
	}


	public void ThereIsANewBookReminder_2()
	{
		try
		{
			foreach (var value in DanhSach_TruyenThu.Values)
			{
				if (value.DaXemHayChua == 0)
				{
					var text = "AA550F000000B200010002000000000000F1A755AA";
					var array = Converter.HexStringToByte(text);
					Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "lỗi ở ThereIsANewBookReminder src_TK !!");
		}
	}

	public void GetAllMails()
	{
		try
		{
			if (DanhSach_TruyenThu == null)
			{
				return;
			}
			var array = Converter.HexStringToByte("AA55D1010000B20023020000");
			var array2 = Converter.HexStringToByte("00000000000000000055AA");
			var array3 = new byte[array.Length + array2.Length + DanhSach_TruyenThu.Count * 36];
			System.Buffer.BlockCopy(array, 0, array3, 0, array.Length);
			System.Buffer.BlockCopy(array2, 0, array3, array3.Length - array2.Length, array2.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(DanhSach_TruyenThu.Count * 36 + 17), 0, array3, 2, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(DanhSach_TruyenThu.Count * 36 + 9), 0, array3, 8, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(DanhSach_TruyenThu.Count), 0, array3, 11, 2);
			var num = 0;
			foreach (var value in DanhSach_TruyenThu.Values)
			{
				var array4 = new byte[32];
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuID), 0, array4, 0, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.CoPhaiLaNPC), 0, array4, 4, 1);
				var bytes = Encoding.Default.GetBytes(value.TruyenThuNguoiGui);
				System.Buffer.BlockCopy(bytes, 0, array4, 5, bytes.Length);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Year - 2000), 0, array4, 25, 1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Month), 0, array4, 26, 1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Day), 0, array4, 27, 1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Hour), 0, array4, 28, 1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Minute), 0, array4, 29, 1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.DaXemHayChua), 0, array4, 30, 2);
				System.Buffer.BlockCopy(array4, 0, array3, num * 36 + 13, array4.Length);
				num++;
			}
			Client.Send_Map_Data(array3, array3.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kiểm tra TruyenThu error() " + SessionID + "|  " + ex.Message);
		}
	}

	public void NPCGuiDiTruyenThu(string string_11, string string_12, int int_109)
	{
		X_Nguoi_Truyen_Thu_Loai x_Nguoi_Truyen_Thu_Loai = new();
		x_Nguoi_Truyen_Thu_Loai.TruyenThuID = (int)RxjhClass.CreateItemSeries();
		x_Nguoi_Truyen_Thu_Loai.TruyenThuNguoiGui = string_11;
		x_Nguoi_Truyen_Thu_Loai.TruyenThuNoiDung = string_12;
		x_Nguoi_Truyen_Thu_Loai.TruyenThuThoiGian = DateTime.Now;
		x_Nguoi_Truyen_Thu_Loai.CoPhaiLaNPC = 0;
		x_Nguoi_Truyen_Thu_Loai.DaXemHayChua = 0;
		DanhSach_TruyenThu.Add(x_Nguoi_Truyen_Thu_Loai.TruyenThuID, x_Nguoi_Truyen_Thu_Loai);
		GetAllMails();
		NewMailNotification(2, 0);
		if (int_109 == 1)
		{
			GameDb.CreateBirdMail(string_11, CharacterName, 0, string_12, 2);
			//RxjhClass.CreateBiography(string_11, CharacterName, 0, string_12, 2);
		}
	}

	public bool KiemTraNguHanhAoChoang_ThuocTinhPhu(Item VatPhamCLass_0)
	{
		return true;
	}

	public bool KiemTraMonGiap_DieuKien(int int_109)
	{
		if (int_109 == 900102 && GangLevel < 4)
		{
			HeThongNhacNho("Áo Choàng Môn Phái chỉ hữu hiệu khi bang phái của đại hiệp đạt cấp [4]!!", 20, "Thiên cơ các");
			return false;
		}
		if (int_109 == 900103 && GangLevel < 5)
		{
			HeThongNhacNho("Áo Choàng Môn Phái chỉ hữu hiệu khi bang phái của đại hiệp đạt cấp [5]!!", 20, "Thiên cơ các");
			return false;
		}
		if (int_109 == 900104 && GangLevel < 6)
		{
			HeThongNhacNho("Áo Choàng Môn Phái chỉ hữu hiệu khi bang phái của đại hiệp đạt cấp [6]!!", 20, "Thiên cơ các");
			return false;
		}
		if (int_109 >= 900105 && int_109 <= 900108)
		{
			if (GangLevel < 7)
			{
				HeThongNhacNho("Áo Choàng Môn Phái chỉ hữu hiệu khi bang phái của đại hiệp đạt cấp [7]!!", 20, "Thiên cơ các");
				return false;
			}
			if (GangCharacterLevel < 2)
			{
				HeThongNhacNho("Chỉ có Bang Chủ mới có thể sử dụng!!", 20, "Thiên cơ các");
				return false;
			}
		}
		if (int_109 >= 900109 && int_109 <= 900112 && GangLevel < 7)
		{
			HeThongNhacNho("Áo Choàng Môn Phái chỉ hữu hiệu khi bang phái của đại hiệp đạt cấp [7]!!", 20, "Thiên cơ các");
			return false;
		}
		return true;
	}

	public bool QueryThienQuanDiaDoMap(int int_109)
	{
		if (int_109 != 3101 && int_109 != 3001 && int_109 != 7001 && int_109 != 2311 && int_109 != 25210 && int_109 != 25209 && int_109 != 25208)
		{
			return int_109 == 32002;
		}
		return true;
	}

	public bool QueryThienQuanDiaDoConditions(int int_109)
	{
		try
		{
			if (AppendStatusList != null)
			{
				switch (int_109)
				{
					case 3001:
						if (GetAddState(*********))
						{
							return true;
						}
						break;
					case 2311:
						if (GetAddState(*********))
						{
							return true;
						}
						break;
					case 25208:
						if (GetAddState(*********))
						{
							return true;
						}
						break;
					case 25209:
						if (GetAddState(*********))
						{
							return true;
						}
						break;
					case 25210:
						if (GetAddState(*********))
						{
							return true;
						}
						break;
					case 7001:
						if (GetAddState(999000165))
						{
							return true;
						}
						break;
					case 3101:
						if (GetAddState(999000163))
						{
							return true;
						}
						break;
				}
			}
		}
		catch
		{
			return false;
		}
		return false;
	}

	public double GetTianguanBenefitBonus(int int_109, int int_110)
	{
		try
		{
			switch (int_110)
			{
				case 3101:
					return (int_109 == 0) ? World.ThienQuan_TyLePhanTramKinhNghiep_CoSo : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo;
				case 3001:
					return (int_109 == 0) ? (World.ThienQuan_TyLePhanTramKinhNghiep_CoSo + World.ThienQuan_TyLePhanTramKinhNghiep_TangLen) : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo + World.TangTiLe_RotVatPham_TrongDiaDo;
				case 2311:
					return (int_109 == 0) ? (World.ThienQuan_TyLePhanTramKinhNghiep_CoSo + World.ThienQuan_TyLePhanTramKinhNghiep_TangLen * 3.0) : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo + World.TangTiLe_RotVatPham_TrongDiaDo * 3;
				case 32002:
					return (int_109 == 0) ? 0.2 : (World.Rate_Drop_Server * 1.2);
				case 25208:
					return (int_109 == 0) ? (World.ThienQuan_TyLePhanTramKinhNghiep_CoSo + World.ThienQuan_TyLePhanTramKinhNghiep_TangLen * 6.0) : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo + World.TangTiLe_RotVatPham_TrongDiaDo * 6;
				case 25209:
					return (int_109 == 0) ? (World.ThienQuan_TyLePhanTramKinhNghiep_CoSo + World.ThienQuan_TyLePhanTramKinhNghiep_TangLen * 5.0) : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo + World.TangTiLe_RotVatPham_TrongDiaDo * 5;
				case 25210:
					return (int_109 == 0) ? (World.ThienQuan_TyLePhanTramKinhNghiep_CoSo + World.ThienQuan_TyLePhanTramKinhNghiep_TangLen * 4.0) : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo + World.TangTiLe_RotVatPham_TrongDiaDo * 4;
				case 7001:
					return (int_109 == 0) ? (World.ThienQuan_TyLePhanTramKinhNghiep_CoSo + World.ThienQuan_TyLePhanTramKinhNghiep_TangLen * 2.0) : World.ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo + World.TangTiLe_RotVatPham_TrongDiaDo * 2;
			}
		}
		catch
		{
			return 0.0;
		}
		return 0.0;
	}

	public bool KiemTra_Phu()
	{
		if (PublicDrugs == null)
		{
			return false;
		}
		if (!PublicDrugs.ContainsKey(1008000027) && !PublicDrugs.ContainsKey(1008000058) && !PublicDrugs.ContainsKey(1008000061) && !PublicDrugs.ContainsKey(1008000028) && !PublicDrugs.ContainsKey(1008000059) && !PublicDrugs.ContainsKey(1008000062) && !PublicDrugs.ContainsKey(1008000029) && !PublicDrugs.ContainsKey(1008000060))
		{
			return PublicDrugs.ContainsKey(1008000063);
		}
		return true;
	}

	public bool KiemTra_Phu2()
	{
		if (PublicDrugs == null)
		{
			return false;
		}
		if (!PublicDrugs.ContainsKey(1008000124) && !PublicDrugs.ContainsKey(1008000141) && !PublicDrugs.ContainsKey(1008000140) && !PublicDrugs.ContainsKey(1008000311) && !PublicDrugs.ContainsKey(1008000877) && !PublicDrugs.ContainsKey(1008000312) && !PublicDrugs.ContainsKey(1008000320))
		{
			return PublicDrugs.ContainsKey(1008000318);
		}
		return true;
	}

	public int NangCap_DoTrangSuc_NhanDoTrangSuc(int int_109, int int_110, int int_111)
	{
		HeThongNhacNho("Thiên cơ các đã phong ấn, xin liên hệ Quản Mệnh Quan!!", 20, "Thiên cơ các");
		return 0;
	}

	public bool Is_ItASpiritBeast(int int_109)
	{
		if (int_109 != 1000000065 && int_109 != 1000000066 && int_109 != 1000000067 && int_109 != 1000000068 && int_109 != 1000000083 && int_109 != 1000000084 && int_109 != 1000000085 && int_109 != 1000000086 && int_109 != 1000001011 && int_109 != 1000001377 && int_109 != 1000001378)
		{
			return int_109 == 1000001379;
		}
		return true;
	}
	

	public bool GetSTQG(int int_109)
	{
		var count = ThangThienKhiCong.Count;
		X_Thang_Thien_Khi_Cong_Loai value;
		if (ThangThienKhiCong != null && count != 0)
		{
			return ThangThienKhiCong.TryGetValue(int_109, out value);
		}
		return false;
	}

	public bool GetAddState(int int_109)
	{
		StatusEffect value;
		if (AppendStatusList != null && AppendStatusList.Count != 0)
		{
			return AppendStatusList.TryGetValue(int_109, out value);
		}
		return false;
	}

	public bool GetAddStateNew(int int_109)
	{
		X_Them_Vao_Trang_Thai_New_Loai value;
		if (AppendStatusNewList != null && AppendStatusNewList.Count != 0)
		{
			return AppendStatusNewList.TryGetValue(int_109, out value);
		}
		return false;
	}

	public bool GetAbnormalState(int int_109)
	{
		X_Di_Thuong_Trang_Thai_Loai value;
		if (TrangThai_BatThuong != null && TrangThai_BatThuong.Count != 0)
		{
			return TrangThai_BatThuong.TryGetValue(int_109, out value);
		}
		return false;
	}

	public void KhiCongClear()
	{
		for (var i = 0; i < 12; i++)
		{
			KhiCong[i] = new(new byte[4]);
		}
	}

	public void NhiemVuClear()
	{
		QuestList = new();
	}

	public void addFLD_ThemVaoTiLePhanTram_Attack(double double_264)
	{
		using (new Lock(thisLock, "addFLD_ThemVaoTiLePhanTram_CongKich"))
		{
			FLD_ThemVaoTiLePhanTram_CongKich += double_264;
		}
	}

	public void delFLD_ThemVaoTiLePhanTram_Attack(double double_264)
	{
		using (new Lock(thisLock, "dllFLD_ThemVaoTiLePhanTram_CongKich"))
		{
			FLD_ThemVaoTiLePhanTram_CongKich -= double_264;
			if (!(FLD_ThemVaoTiLePhanTram_CongKich >= 0.0))
			{
				FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
			}
		}
	}

	public void addFLD_ThemVaoTiLePhanTram_PhongNgu(double double_264)
	{
		using (new Lock(thisLock, "addFLD_ThemVaoTiLePhanTram_PhongNgu"))
		{
			FLD_ThemVaoTiLePhanTram_PhongNgu += double_264;
		}
	}

	public void delFLD_ThemVaoTiLePhanTram_PhongNgu(double double_264)
	{
		using (new Lock(thisLock, "dllFLD_ThemVaoTiLePhanTram_PhongNgu"))
		{
			FLD_ThemVaoTiLePhanTram_PhongNgu -= double_264;
			if (FLD_ThemVaoTiLePhanTram_PhongNgu < 0.0)
			{
				FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
			}
		}
	}

	public void addFLD_TrangBi_ThemVao_VuKhi_CuongHoa(int int_109)
	{
		using (new Lock(thisLock, "addFLD_TrangBi_ThemVao_VuKhi_CuongHoa"))
		{
			FLD_TrangBi_ThemVao_VuKhi_CuongHoa += int_109;
		}
	}

	public void delFLD_TrangBi_ThemVao_VuKhi_CuongHoa(int int_109)
	{
		using (new Lock(thisLock, "dllFLD_TrangBi_ThemVao_VuKhi_CuongHoa"))
		{
			FLD_TrangBi_ThemVao_VuKhi_CuongHoa -= int_109;
			if (FLD_TrangBi_ThemVao_VuKhi_CuongHoa < 0)
			{
				FLD_TrangBi_ThemVao_VuKhi_CuongHoa = 0;
			}
		}
	}

	public void addFLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa(int int_109)
	{
		using (new Lock(thisLock, "addFLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa"))
		{
			FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa += int_109;
		}
	}

	public void delFLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa(int int_109)
	{
		using (new Lock(thisLock, "dllFLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa"))
		{
			FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa -= int_109;
			if (FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa < 0)
			{
				FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa = 0;
			}
		}
	}
}
