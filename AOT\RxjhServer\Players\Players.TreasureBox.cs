﻿using HeroYulgang.Constants;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public void Goi_Ho_Tro_Level_1(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 300;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = 1000000030;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((Player_Sex != 1) ? 26900005 : 16900001);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 400006 : 400106);
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ItemConstants.HOP_AUTO;
						}
						break;
					case 9:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((job != 11) ? 8 : 108);
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 8 : 108);
						}
						break;
					case 12:
						if (type == num2)
						{
							num = ((job != 11) ? 9 : 109);
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((job != 11) ? 9 : 109);
						}
						break;
					case 14:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008000035;
						}
						break;
					case 16:
						if (type == num2)
						{
							num = GetSetItemId(job, 10, 1, sex, zx);
						}
						break;
					case 17:
						if (type == num2)
						{
							num = ((job != 11) ? 500002 : 500102);
						}
						break;
					case 18:
						switch (job)
						{
							case 1:
								num = 100200001;
								break;
							case 2:
								num = 200200001;
								break;
							case 3:
								num = 300200001;
								break;
							case 4:
								num = 400200001;
								break;
							case 5:
								num = 500200001;
								break;
							case 6:
								num = 700200001;
								break;
							case 7:
								num = 800200001;
								break;
							case 8:
								num = 100204011;
								break;
							case 9:
								num = 200204011;
								break;
							case 10:
								num = 900200001;
								break;
							case 11:
								num = 400204011;
								break;
							case 12:
								num = 300204011;
								break;
							case 13:
								num = 500204011;
								break;
						}
						break;
					case 19:
						if (type == num2)
						{
							num = ((job != 11) ? 800003 : 800103);
						}
						break;
					case 20:
						if (type == num2)
						{
							num = ((job != 11) ? 400001 : 400101);
						}
						break;
					case 21:
						if (type == num2)
						{
							num = 1008000451;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Ho_Tro_Level_60(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 100;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = ((Player_Sex != 1) ? ******** : ********);
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = 1008000035;
						}
						break;
					case 6:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202024 : 100201023);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202024 : 200201023);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202024 : 300201023);
								break;
							case 4:
								num = ((Player_Zx != 1) ? 400202024 : 400201023);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202024 : 500201023);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202024 : 700201023);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202024 : 800201023);
								break;
							case 8:
								num = 100204014;
								break;
							case 9:
								num = 200204014;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202024 : 900201023);
								break;
							case 11:
								num = 400204014;
								break;
							case 12:
								num = 300204014;
								break;
							case 13:
								num = 500204014;
								break;
						}
						break;
					case 7:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 120302006 : 110302006) : ((Player_Sex != 1) ? 120301006 : 110301006));
								break;
							case 2:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 220302006 : 210302006) : ((Player_Sex != 1) ? 220301006 : 210301006));
								break;
							case 3:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 320302006 : 310302006) : ((Player_Sex != 1) ? 320301006 : 310301006));
								break;
							case 4:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 420302006 : 410302006) : ((Player_Sex != 1) ? 420301006 : 410301006));
								break;
							case 5:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 520302006 : 510302006) : ((Player_Sex != 1) ? 520301006 : 510301006));
								break;
							case 6:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 720302006 : 710302006) : ((Player_Sex != 1) ? 720301006 : 710301006));
								break;
							case 7:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 820302006 : 810302006) : ((Player_Sex != 1) ? 820301006 : 810301006));
								break;
							case 8:
								num = ((Player_Sex != 1) ? 120304006 : 110304006);
								break;
							case 9:
								num = ((Player_Sex != 1) ? 220304006 : 210304006);
								break;
							case 10:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 920302006 : 910302006) : ((Player_Sex != 1) ? 920301006 : 910301006));
								break;
							case 11:
								num = ((Player_Sex != 1) ? 420304006 : 411304006);
								break;
							case 12:
								num = 310304006;
								break;
							case 13:
								num = 520304006;
								break;
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802008 : 802108) : ((job != 11) ? 801008 : 800108));
						}
						break;
					case 9:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502007 : 502107) : ((job != 11) ? 501007 : 500107));
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((job != 11) ? 400005 : 400105);
						}
						break;
					case 11:
						if (type == num2)
						{
							num = 1008001100;
						}
						break;
					case 12:
						if (type == num2)
						{
							num = 1008000306;
						}
						break;
					case 13:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 14:
						if (type == num2)
						{
							num = 1008000245;
						}
						break;
					case 15:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 16:
						if (type == num2)
						{
							num = 1008000193;
						}
						break;
					case 17:
						if (type == num2)
						{
							num = 1000001182;
						}
						break;
					case 18:
						if (type == num2)
						{
							num = 1008000446;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Ho_Tro_TrungSinh_14x(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1100;
				switch (item.Reside)
				{
					case 1:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202264 : 500201254);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202264 : 700201254);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202264 : 800201254);
								break;
							case 8:
								num = 100204025;
								break;
							case 9:
								num = 200204025;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202264 : 900201254);
								break;
							case 11:
								num = 400204025;
								break;
							case 12:
								num = 300204022;
								break;
							case 13:
								num = 500204025;
								break;
						}
						break;
					case 2:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 120302020 : 110302020) : ((Player_Sex != 1) ? 120301020 : 110301020));
								break;
							case 2:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 220302020 : 210302020) : ((Player_Sex != 1) ? 220301020 : 210301020));
								break;
							case 3:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 320302020 : 310302020) : ((Player_Sex != 1) ? 320301020 : 310301020));
								break;
							case 4:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 420302020 : 410302020) : ((Player_Sex != 1) ? 420301020 : 410301020));
								break;
							case 5:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 520302020 : 510302020) : ((Player_Sex != 1) ? 520301020 : 510301020));
								break;
							case 6:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 720302020 : 710302020) : ((Player_Sex != 1) ? 720301020 : 710301020));
								break;
							case 7:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 820302020 : 810302020) : ((Player_Sex != 1) ? 820301020 : 810301020));
								break;
							case 8:
								num = ((Player_Sex != 1) ? 120304020 : 110304020);
								break;
							case 9:
								num = ((Player_Sex != 1) ? 220304020 : 210304020);
								break;
							case 10:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 920302020 : 910302020) : ((Player_Sex != 1) ? 920301020 : 910301020));
								break;
							case 11:
								num = ((Player_Sex != 1) ? 420304020 : 410304020);
								break;
							case 12:
								num = 310304020;
								break;
							case 13:
								num = 520304020;
								break;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502018 : 502118) : ((job != 11) ? 501018 : 500118));
						}
						break;
					case 4:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502018 : 502118) : ((job != 11) ? 501018 : 500118));
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802019 : 802119) : ((job != 11) ? 801019 : 800119));
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((job != 11) ? 400016 : 400116);
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Ho_Tro_Offline(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 500;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TrungSinh_Lan_1(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1000;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Donate_200k(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2100;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = 111;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Donate_400k(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2200;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = 111;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Donate_500k_New(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 600;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = 111;
						}
						break;
					case 6:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 7:
						if (type == num2)
						{
							num = 1000000400;
						}
						break;
					case 8:
						if (type == num2)
						{
							num = 1000000534;
						}
						break;
					case 9:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ItemDef.Item.NhietHuyetThach;
						}
						break;
					case 11:
						if (type == num2)
						{
							num = 1008000072;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Donate_500k_Old(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 700;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = 1111;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = 1008000071;
						}
						break;
					case 6:
						if (type == num2)
						{
							num = 1000000400;
						}
						break;
					case 7:
						if (type == num2)
						{
							num = 1000000534;
						}
						break;
					case 8:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 9:
						if (type == num2)
						{
							num = 1008000811;
						}
						break;
					case 10:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 11:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_10(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1200;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 6:
						if (type == num2)
						{
							num = 1008001100;
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((Player_Sex != 1) ? RNG.Next(26900004, 26900017) : RNG.Next(16900027, 16900037));
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ItemConstants.HOP_AUTO;
						}
						break;
					case 9:
						if (type == num2)
						{
							num = ((job != 11) ? 3 : 103);
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((job != 11) ? 3 : 103);
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 700003 : 700103);
						}
						break;
					case 12:
						if (type == num2)
						{
							num = ((job != 11) ? 700003 : 700103);
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((job != 11) ? 100003 : 100103);
						}
						break;
					case 14:
						if (type == num2)
						{
							num = 1008000003;
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008000006;
						}
						break;
					case 16:
						if (type == num2)
						{
							num = GetSetItemId(job, 10, 1, sex, zx);
						}
						break;
					case 17:
						if (type == num2)
						{
							num = ((job != 11) ? 500002 : 500102);
						}
						break;
					case 18:
						if (type == num2)
						{
							num = ((job != 11) ? 500002 : 500102);
						}
						break;
					case 19:
						switch (job)
						{
							case 1:
								num = 100200001;
								break;
							case 2:
								num = 200200001;
								break;
							case 3:
								num = 300200001;
								break;
							case 4:
								num = 400200001;
								break;
							case 5:
								num = 500200001;
								break;
							case 6:
								num = 700200001;
								break;
							case 7:
								num = 800200001;
								break;
							case 8:
								num = 100204011;
								break;
							case 9:
								num = 200204011;
								break;
							case 10:
								num = 900200001;
								break;
							case 11:
								num = 400204011;
								break;
							case 12:
								num = 300204011;
								break;
							case 13:
								num = 500204011;
								break;
						}
						break;
					case 20:
						if (type == num2)
						{
							num = ((job != 11) ? 800003 : 800103);
						}
						break;
					case 21:
						if (type == num2)
						{
							num = ((job != 11) ? 400001 : 400101);
						}
						break;
					case 22:
						if (type == num2)
						{
							num = 1008001651;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_30(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1300;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						{
							var num3 = RNG.Next(1, 3);
							var num4 = 0;
							num4 = num3 switch
							{
								1 => **********,
								2 => **********,
								_ => **********,
							};
							if (type == num2)
							{
								num = num4;
							}
							break;
						}
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 6 : 104);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 6 : 104);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((job != 11) ? 700008 : 700108);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 700008 : 700108);
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((job != 11) ? 100007 : 100107);
						}
						break;
					case 9:
						if (type == num2)
						{
							num = GetSetItemId(job, 30, 1, sex, zx);
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((job != 11) ? 500004 : 500104);
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 500004 : 500104);
						}
						break;
					case 12:
						switch (job)
						{
							case 1:
								num = 100200010;
								break;
							case 2:
								num = 200200010;
								break;
							case 3:
								num = 300200010;
								break;
							case 4:
								num = 400200010;
								break;
							case 5:
								num = 500200010;
								break;
							case 6:
								num = 700200010;
								break;
							case 7:
								num = 800200010;
								break;
							case 8:
								num = 100204012;
								break;
							case 9:
								num = 200204012;
								break;
							case 10:
								num = 900200010;
								break;
							case 11:
								num = 400204012;
								break;
							case 12:
								num = 300204012;
								break;
							case 13:
								num = 500204012;
								break;
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((job != 11) ? 800005 : 800105);
						}
						break;
					case 14:
						if (type == num2)
						{
							num = ((job != 11) ? 400003 : 400103);
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008001652;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_60(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1400;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						{
							var num3 = RNG.Next(1, 3);
							var num4 = 0;
							num4 = num3 switch
							{
								1 => **********,
								2 => **********,
								_ => **********,
							};
							if (type == num2)
							{
								num = num4;
							}
							break;
						}
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 8 : 108);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 8 : 108);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((job != 11) ? 700011 : 700111);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 700011 : 700111);
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((job != 11) ? 100010 : 100110);
						}
						break;
					case 9:
						if (type == num2)
						{
							num = GetSetItemId(job, 60, 1, sex, zx);
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502007 : 502107) : ((job != 11) ? 501007 : 500107));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502007 : 502107) : ((job != 11) ? 501007 : 500107));
						}
						break;
					case 12:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202024 : 100201023);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202024 : 200201023);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202024 : 300201023);
								break;
							case 4:
								num = ((Player_Zx != 1) ? 400202024 : 400201023);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202024 : 500201023);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202024 : 700201023);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202024 : 800201023);
								break;
							case 8:
								num = 100204014;
								break;
							case 9:
								num = 200204014;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202024 : 900201023);
								break;
							case 11:
								num = 400204014;
								break;
							case 12:
								num = 300204014;
								break;
							case 13:
								num = 500204014;
								break;
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802008 : 802108) : ((job != 11) ? 801008 : 800108));
						}
						break;
					case 14:
						if (type == num2)
						{
							num = ((job != 11) ? 400006 : 400106);
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008001653;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_80(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1500;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						{
							var num3 = RNG.Next(1, 3);
							var num4 = 0;
							num4 = num3 switch
							{
								1 => **********,
								2 => **********,
								_ => **********,
							};
							if (type == num2)
							{
								num = num4;
							}
							break;
						}
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 9 : 109);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 9 : 109);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((job != 11) ? 700014 : 700114);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 700014 : 700114);
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((job != 11) ? 100013 : 100113);
						}
						break;
					case 9:
						if (type == num2)
						{
							num = GetSetItemId(job, 80, 1, sex, zx);
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502009 : 502109) : ((job != 11) ? 501009 : 500109));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502009 : 502109) : ((job != 11) ? 501009 : 500109));
						}
						break;
					case 12:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202030 : 100201029);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202030 : 200201029);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202030 : 300201029);
								break;
							case 4:
								num = ((Player_Zx != 1) ? 400202030 : 400201029);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202030 : 500201029);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202030 : 700201029);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202030 : 800201029);
								break;
							case 8:
								num = 100204015;
								break;
							case 9:
								num = 200204015;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202030 : 900201029);
								break;
							case 11:
								num = 400204015;
								break;
							case 12:
								num = 300204015;
								break;
							case 13:
								num = 500204015;
								break;
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802010 : 802110) : ((job != 11) ? 801010 : 800110));
						}
						break;
					case 14:
						if (type == num2)
						{
							num = ((job != 11) ? 400008 : 400108);
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008001654;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_100(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1600;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = 1900015;
						}
						break;
					case 3:
						{
							var num3 = RNG.Next(1, 3);
							var num4 = 0;
							num4 = num3 switch
							{
								1 => **********,
								2 => **********,
								_ => **********,
							};
							if (type == num2)
							{
								num = num4;
							}
							break;
						}
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 10 : 110);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 10 : 110);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((job != 11) ? 700016 : 700116);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 700016 : 700116);
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((job != 11) ? 100014 : 100114);
						}
						break;
					case 9:
						if (type == num2)
						{
							num = GetSetItemId(job, 100, 1, sex, zx);
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502011 : 502111) : ((job != 11) ? 501011 : 500111));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502011 : 502111) : ((job != 11) ? 501011 : 500111));
						}
						break;
					case 12:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202036 : 100201035);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202036 : 200201035);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202036 : 300201035);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202036 : 800201035);
								break;
							case 8:
								num = 100204016;
								break;
							case 9:
								num = 200204016;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202036 : 900201035);
								break;
							case 11:
								num = 400204016;
								break;
							case 12:
								num = 300204016;
								break;
							case 13:
								num = 500204016;
								break;
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802012 : 802112) : ((job != 11) ? 801012 : 800112));
						}
						break;
					case 14:
						if (type == num2)
						{
							num = ((job != 11) ? 400010 : 400110);
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008001655;
						}
						break;
					case 16:
						switch (job)
						{
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 8:
								num = 100204016;
								break;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_120(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1650;
				switch (item.Reside)
				{
					case 1:
						switch (job)
						{
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 8:
								num = *********;
								break;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = 1008000163;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 14 : 114);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 14 : 114);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((job != 11) ? 700020 : 700120);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 700020 : 700120);
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((job != 11) ? 100018 : 100117);
						}
						break;
					case 9:
						if (type == num2)
						{
							num = GetSetItemId(job, 120, 1, sex, zx);
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502014 : 502114) : ((job != 11) ? 501014 : 500114));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502014 : 502114) : ((job != 11) ? 501014 : 500114));
						}
						break;
					case 12:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202040 : 100201039);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202040 : 200201039);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202040 : 300201039);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202040 : 800201039);
								break;
							case 8:
								num = *********;
								break;
							case 9:
								num = 200204018;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202040 : 900201041);
								break;
							case 11:
								num = 400204018;
								break;
							case 12:
								num = 300204018;
								break;
							case 13:
								num = 500204018;
								break;
						}
						break;
					case 13:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802015 : 802115) : ((job != 11) ? 801015 : 800115));
						}
						break;
					case 14:
						if (type == num2)
						{
							num = ((job != 11) ? 400014 : 400114);
						}
						break;
					case 15:
						if (type == num2)
						{
							num = 1008000522;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_130(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 200;
				switch (item.Reside)
				{
					case 5:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202261 : 500201251);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202261 : 700201251);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202261 : 800201251);
								break;
							case 8:
								num = 100204022;
								break;
							case 9:
								num = 200204022;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202261 : 900201251);
								break;
							case 11:
								num = 400204022;
								break;
							case 12:
								num = 300204022;
								break;
							case 13:
								num = 500204022;
								break;
						}
						break;
					case 6:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202261 : 500201251);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202261 : 700201251);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202261 : 800201251);
								break;
							case 8:
								num = 100204022;
								break;
							case 9:
								num = 200204022;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202261 : 900201251);
								break;
							case 11:
								num = 400204022;
								break;
							case 12:
								num = 300204022;
								break;
							case 13:
								num = 500204022;
								break;
						}
						break;
					case 7:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 120302015 : 110302015) : ((Player_Sex != 1) ? 120301015 : 110301015));
								break;
							case 2:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 220302015 : 210302015) : ((Player_Sex != 1) ? 220301015 : 210301015));
								break;
							case 3:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 320302015 : 310302015) : ((Player_Sex != 1) ? 320301015 : 310301015));
								break;
							case 4:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 420302015 : 410302015) : ((Player_Sex != 1) ? 420301015 : 410301015));
								break;
							case 5:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 520302015 : 510302015) : ((Player_Sex != 1) ? 520301015 : 510301015));
								break;
							case 6:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 720302015 : 710302015) : ((Player_Sex != 1) ? 720301015 : 710301015));
								break;
							case 7:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 820302015 : 810302015) : ((Player_Sex != 1) ? 820301015 : 810301015));
								break;
							case 8:
								num = ((Player_Sex != 1) ? 120304015 : 110304015);
								break;
							case 9:
								num = ((Player_Sex != 1) ? 220304015 : 210304015);
								break;
							case 10:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 920302015 : 910302015) : ((Player_Sex != 1) ? 920301015 : 910301015));
								break;
							case 11:
								num = ((Player_Sex != 1) ? 420304015 : 410304015);
								break;
							case 12:
								num = 310304015;
								break;
							case 13:
								num = 520304015;
								break;
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502016 : 502116) : ((job != 11) ? 501016 : 500116));
						}
						break;
					case 9:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502016 : 502116) : ((job != 11) ? 501016 : 500116));
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802017 : 802117) : ((job != 11) ? 801017 : 800117));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 400015 : 400115);
						}
						break;
					case 19:
						if (type == num2)
						{
							num = 1008000523;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_140(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 800;
				switch (item.Reside)
				{
					case 5:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202264 : 500201254);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202264 : 700201254);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202264 : 800201254);
								break;
							case 8:
								num = 100204025;
								break;
							case 9:
								num = 200204025;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202264 : 900201254);
								break;
							case 11:
								num = 400204025;
								break;
							case 12:
								num = 300204025;
								break;
							case 13:
								num = 500204025;
								break;
						}
						break;
					case 6:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202264 : 500201254);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202264 : 700201254);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202264 : 800201254);
								break;
							case 8:
								num = 100204025;
								break;
							case 9:
								num = 200204025;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202264 : 900201254);
								break;
							case 11:
								num = 400204025;
								break;
							case 12:
								num = 300204025;
								break;
							case 13:
								num = 500204025;
								break;
						}
						break;
					case 7:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 120302020 : 110302020) : ((Player_Sex != 1) ? 120301020 : 110301020));
								break;
							case 2:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 220302020 : 210302020) : ((Player_Sex != 1) ? 220301020 : 210301020));
								break;
							case 3:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 320302020 : 310302020) : ((Player_Sex != 1) ? 320301020 : 310301020));
								break;
							case 4:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 420302020 : 410302020) : ((Player_Sex != 1) ? 420301020 : 410301020));
								break;
							case 5:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 520302020 : 510302020) : ((Player_Sex != 1) ? 520301020 : 510301020));
								break;
							case 6:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 720302020 : 710302020) : ((Player_Sex != 1) ? 720301020 : 710301020));
								break;
							case 7:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 820302020 : 810302020) : ((Player_Sex != 1) ? 820301020 : 810301020));
								break;
							case 8:
								num = ((Player_Sex != 1) ? 120304020 : 110304020);
								break;
							case 9:
								num = ((Player_Sex != 1) ? 220304020 : 210304020);
								break;
							case 10:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 920302020 : 910302020) : ((Player_Sex != 1) ? 920301020 : 910301020));
								break;
							case 11:
								num = ((Player_Sex != 1) ? 420304020 : 410304020);
								break;
							case 12:
								num = 310304020;
								break;
							case 13:
								num = 520304020;
								break;
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502018 : 502118) : ((job != 11) ? 501018 : 500118));
						}
						break;
					case 9:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502018 : 502118) : ((job != 11) ? 501018 : 500118));
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802019 : 802119) : ((job != 11) ? 801019 : 800119));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 400016 : 400116);
						}
						break;
					case 20:
						if (type == num2)
						{
							num = 1008000529;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_150(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 900;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = ((Player_Sex != 1) ? RNG.Next(********, ********) : RNG.Next(********, ********));
						}
						break;
					case 2:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202271 : 300201261);
								break;
							case 4:
								num = ((Player_Zx != 1) ? 400202271 : 400201261);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202271 : 500201261);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202271 : 700201261);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202271 : 800201261);
								break;
							case 8:
								num = 100204033;
								break;
							case 9:
								num = 200204033;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202271 : 900201261);
								break;
							case 11:
								num = 400204033;
								break;
							case 12:
								num = 300204033;
								break;
							case 13:
								num = 500204033;
								break;
						}
						break;
					case 3:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 120302022 : 110302022) : ((Player_Sex != 1) ? 120301022 : 110301022));
								break;
							case 2:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 220302022 : 210302022) : ((Player_Sex != 1) ? 220301022 : 210301022));
								break;
							case 3:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 320302022 : 310302022) : ((Player_Sex != 1) ? 320301022 : 310301022));
								break;
							case 4:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 420302022 : 410302022) : ((Player_Sex != 1) ? 420301022 : 410301022));
								break;
							case 5:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 520302022 : 510302022) : ((Player_Sex != 1) ? 520301022 : 510301022));
								break;
							case 6:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 720302022 : 710302022) : ((Player_Sex != 1) ? 720301022 : 710301022));
								break;
							case 7:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 820302022 : 810302022) : ((Player_Sex != 1) ? 820301022 : 810301022));
								break;
							case 8:
								num = ((Player_Sex != 1) ? 120304022 : 110304022);
								break;
							case 9:
								num = ((Player_Sex != 1) ? 220304022 : 210304022);
								break;
							case 10:
								num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 920302022 : 910302022) : ((Player_Sex != 1) ? 920301022 : 910301022));
								break;
							case 11:
								num = ((Player_Sex != 1) ? 420304022 : 410304022);
								break;
							case 12:
								num = 310304022;
								break;
							case 13:
								num = 520304022;
								break;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502020 : 502120) : ((job != 11) ? 501020 : 500120));
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502020 : 502120) : ((job != 11) ? 501020 : 500120));
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802021 : 802121) : ((job != 11) ? 801020 : 800120));
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 400017 : 400117);
						}
						break;
					case 8:
						num = ((Player_Zx != 1) ? ((Player_Sex != 1) ? 900112 : 900111) : ((Player_Sex != 1) ? 900110 : 900109));
						break;
					case 9:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 2:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202271 : 300201261);
								break;
							case 4:
								num = ((Player_Zx != 1) ? 400202271 : 400201261);
								break;
							case 5:
								num = ((Player_Zx != 1) ? 500202271 : 500201261);
								break;
							case 6:
								num = ((Player_Zx != 1) ? 700202271 : 700201261);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202271 : 800201261);
								break;
							case 8:
								num = 100204033;
								break;
							case 9:
								num = 200204033;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202271 : 900201261);
								break;
							case 11:
								num = 400204033;
								break;
							case 12:
								num = 300204033;
								break;
							case 13:
								num = 500204033;
								break;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_TraiNghiem_16x(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2650;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = ((zx != 1) ? ((sex == 1) ? ******** : ********) : ((sex == 1) ? ******** : ********));
						}
						break;
					case 2:
						if (job != 4 && job != 6)
						{
							var num4 = ((zx == 1) ? ********* : *********);
							switch (job)
							{
								case 1:
									num = num4;
									break;
								case 2:
									num = num4 + 100000000;
									break;
								case 3:
									num = num4 + 200000000;
									break;
								case 4:
									num = num4 + 300000000;
									break;
								case 5:
									num = num4 + 400000000;
									break;
								case 6:
									num = num4 + 600000000;
									break;
								case 7:
									num = num4 + 700000000;
									break;
								case 8:
									num = 100204037;
									break;
								case 9:
									num = 200204037;
									break;
								case 10:
									num = ((zx == 1) ? 900201263 : 900202273);
									break;
								case 11:
									num = 400204037;
									break;
								case 12:
									num = 300204037;
									break;
								case 13:
									num = 500204037;
									break;
							}
						}
						break;
					case 3:
						if (type == num2)
						{
							num = GetSetItemId(job, 160, 1, sex, zx);
						}
						break;
					case 4:
					case 5:
						if (type == num2)
						{
							num = ((zx != 1) ? ((job != 11) ? 502021 : 502121) : ((job != 11) ? 501021 : 500121));
						}
						break;
					case 6:
						if (type == num2)
						{
							num = ((zx != 1) ? ((job != 11) ? 802022 : 802122) : ((job != 11) ? 801022 : 800122));
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((job != 11) ? 400019 : 400119);
						}
						break;
					case 8:
						num = ((zx != 1) ? ((sex == 1) ? 900111 : 900112) : ((sex == 1) ? 900109 : 900110));
						break;
					case 9:
						if (job == 4 || job == 6)
						{
							var num3 = ((zx == 1) ? ********* : *********);
							switch (job)
							{
								case 1:
									num = num3;
									break;
								case 2:
									num = num3 + 100000000;
									break;
								case 3:
									num = num3 + 200000000;
									break;
								case 4:
									num = num3 + 300000000;
									break;
								case 5:
									num = num3 + 400000000;
									break;
								case 6:
									num = num3 + 600000000;
									break;
								case 7:
									num = num3 + 700000000;
									break;
								case 8:
									num = 100204037;
									break;
								case 9:
									num = 200204037;
									break;
								case 10:
									num = ((zx == 1) ? 900201263 : 900202273);
									break;
								case 11:
									num = 400204037;
									break;
								case 12:
									num = 300204037;
									break;
								case 13:
									num = 500204037;
									break;
							}
						}
						break;
					case 10:
					case 11:
						if (type == num2 && job != 4 && job != 6)
						{
							num = ((job != 11) ? 16 : 116);
						}
						break;
					case 12:
					case 13:
						if (type == num2 && job != 4 && job != 6)
						{
							num = ((job != 11) ? 700022 : 700122);
						}
						break;
					case 14:
						if (type == num2)
						{
							num = ((job != 11) ? 100021 : 100119);
						}
						break;
					case 15:
						if (type == num2)
						{
							num = RNG.Next(1000001172, 1000001175);
						}
						break;
					case 16:
						num = ((type == num2) ? 1000002006 : num);
						break;
					case 17:
						num = ((type == num2) ? 1000001410 : num);
						break;
					case 18:
						num = ((type == num2) ? 1000001414 : num);
						break;
					case 19:
						num = ((type == num2) ? 1000001415 : num);
						break;
					case 20:
					case 21:
						num = ((type == num2) ? 1000001416 : num);
						break;
					case 22:
						num = ((type == num2) ? 1000001417 : num);
						break;
					case 23:
						num = ((type == num2) ? 1900004 : num);
						break;
					case 24:
						num = ((type == num2) ? 1000000664 : num);
						break;
					case 25:
					case 26:
						if (type == num2 && (job == 4 || job == 6))
						{
							num = ((job != 11) ? 21 : 121);
						}
						break;
					case 27:
					case 28:
						if (type == num2 && (job == 4 || job == 6))
						{
							num = ((job != 11) ? 700026 : 700126);
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_int_Goi_Trai_Nghiem_16x(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Add Item int Goi Trai Nghiem 16x error " + ex.Message);
		}
	}

	public void Goi_TanThu_Server_New_Level_100_Bonus_Char_Cung_DP_Ninja_HBQ(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2000;
				var reside = item.Reside;
				var num3 = reside;
				if (num3 == 1 && type == num2)
				{
					switch (job)
					{
						case 4:
							num = ((Player_Zx != 1) ? ********* : *********);
							break;
						case 5:
							num = ((Player_Zx != 1) ? ********* : *********);
							break;
						case 6:
							num = ((Player_Zx != 1) ? ********* : *********);
							break;
						case 8:
							num = 100204016;
							break;
					}
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_HoTro_Level_100_TheoKip_12x_BanShop(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1800;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = ((job != 11) ? 10 : 110);
						}
						break;
					case 2:
						if (type == num2)
						{
							num = ((job != 11) ? 10 : 110);
						}
						break;
					case 3:
						if (type == num2)
						{
							num = ((job != 11) ? 700017 : 700117);
						}
						break;
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 700017 : 700117);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 100014 : 100114);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = GetSetItemId(job, 100, 1, sex, zx);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502011 : 502111) : ((job != 11) ? 501011 : 500111));
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502011 : 502111) : ((job != 11) ? 501011 : 500111));
						}
						break;
					case 9:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202036 : 100201035);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202036 : 200201035);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202036 : 300201035);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202036 : 800201035);
								break;
							case 8:
								num = 100204016;
								break;
							case 9:
								num = 200204016;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202036 : 900201035);
								break;
							case 11:
								num = 400204016;
								break;
							case 12:
								num = 300204016;
								break;
							case 13:
								num = 500204016;
								break;
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802012 : 802112) : ((job != 11) ? 801012 : 800112));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 400010 : 400110);
						}
						break;
					case 12:
						switch (job)
						{
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 8:
								num = 100204016;
								break;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_HoTro_Level_100_New_Gift_Code(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1900;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = ((job != 11) ? 10 : 110);
						}
						break;
					case 2:
						if (type == num2)
						{
							num = ((job != 11) ? 10 : 110);
						}
						break;
					case 3:
						if (type == num2)
						{
							num = ((job != 11) ? 700017 : 700117);
						}
						break;
					case 4:
						if (type == num2)
						{
							num = ((job != 11) ? 700017 : 700117);
						}
						break;
					case 5:
						if (type == num2)
						{
							num = ((job != 11) ? 100014 : 100114);
						}
						break;
					case 6:
						if (type == num2)
						{
							num = GetSetItemId(job, 100, 1, sex, zx);
						}
						break;
					case 7:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502011 : 502111) : ((job != 11) ? 501011 : 500111));
						}
						break;
					case 8:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 502011 : 502111) : ((job != 11) ? 501011 : 500111));
						}
						break;
					case 9:
						switch (job)
						{
							case 1:
								num = ((Player_Zx != 1) ? 100202036 : 100201035);
								break;
							case 2:
								num = ((Player_Zx != 1) ? 200202036 : 200201035);
								break;
							case 3:
								num = ((Player_Zx != 1) ? 300202036 : 300201035);
								break;
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 7:
								num = ((Player_Zx != 1) ? 800202036 : 800201035);
								break;
							case 8:
								num = 100204016;
								break;
							case 9:
								num = 200204016;
								break;
							case 10:
								num = ((Player_Zx != 1) ? 900202036 : 900201035);
								break;
							case 11:
								num = 400204016;
								break;
							case 12:
								num = 300204016;
								break;
							case 13:
								num = 500204016;
								break;
						}
						break;
					case 10:
						if (type == num2)
						{
							num = ((Player_Zx != 1) ? ((job != 11) ? 802012 : 802112) : ((job != 11) ? 801012 : 800112));
						}
						break;
					case 11:
						if (type == num2)
						{
							num = ((job != 11) ? 400010 : 400110);
						}
						break;
					case 12:
						switch (job)
						{
							case 4:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 5:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 6:
								num = ((Player_Zx != 1) ? ********* : *********);
								break;
							case 8:
								num = 100204016;
								break;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Open_Like_Share(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 1700;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 6:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 7:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 8:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 3; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Goi_Oldbie_Comeback(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2500;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 2:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 5:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 6:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Oldbie comeback error " + ex.Message);
		}
	}

	public void Goi_AoChoang_Tet(int type, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				var randomTypeBoxItem = World.Random_Type_Box_Item;
				if (item.Type != randomTypeBoxItem)
				{
					continue;
				}
				var num = 0;
				var reside = item.Reside;
				var num2 = reside;
				if (num2 == 1 && type == randomTypeBoxItem)
				{
					num = ((Player_Sex != 1) ? ******** : ********);
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Donate_200k_Train_TetGiapThin(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2400;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = ((Player_Sex != 1) ? ********** : **********);
						}
						break;
					case 2:
						if (type == num2)
						{
							num = 111;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Donate_400k_Tet_TetGiapThin(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2450;
				switch (item.Reside)
				{
					case 1:
						if (type == num2)
						{
							num = ((Player_Sex != 1) ? ********** : **********);
						}
						break;
					case 2:
						if (type == num2)
						{
							num = 111;
						}
						break;
					case 3:
						if (type == num2)
						{
							num = **********;
						}
						break;
					case 4:
						if (type == num2)
						{
							num = **********;
						}
						break;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Donate_100k_2_KTTX(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2550;
				var reside = item.Reside;
				var num3 = reside;
				if ((uint)(num3 - 1) <= 1u && type == num2)
				{
					num = **********;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void Donate_200k_4_KTTX(int job, int sex, int type, int zx, double itemGlobalId, int vatPhamId, string vatPhamTen, int packageId, int packagePosition)
	{
		try
		{
			ItemUse(packageId, packagePosition, 1);
			foreach (var item in World.Set_SoLieu)
			{
				if (item.Type != type)
				{
					continue;
				}
				var num = 0;
				var num2 = 2600;
				var reside = item.Reside;
				var num3 = reside;
				if ((uint)(num3 - 1) <= 3u && type == num2)
				{
					num = **********;
				}
				var parcelVacancyPosition = GetParcelVacancyPosition();
				if (parcelVacancyPosition != -1 && num > 0 && num != **********)
				{
					AddItem_ThuocTinh_int(num, parcelVacancyPosition, item.NJ, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
				}
				if (num == **********)
				{
					for (var i = 0; i < 10; i++)
					{
						AddItem_ThuocTinh_int(num, GetParcelVacancyPosition(), 1, item.Magic0, item.Magic2, item.Magic3, item.Magic4, item.Magic5, item.ThucTinh, item.TrungCapPhuHon, item.TienHoa, item.BD, item.DAYS);
					}
				}
				Init_Item_In_Bag();
			}
			RxjhClass.ItemRecord(AccountID, CharacterName, AccountID, CharacterName, itemGlobalId, vatPhamId, vatPhamTen, 1, "0", type, "打开Set");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Set error " + ex.Message);
		}
	}

	public void WithMixedStones()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var num5 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		var position5 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == **********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == **********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == **********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == **********)
			{
				num4 = 1;
				position4 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000199)
			{
				num5 = 1;
				position5 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				SubtractItem(position5, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000365), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Cả năm loại ngọc này đều có thể đổi lấy đá hỗn hợp!", 10, "Thiên cơ các");
		}
	}

	public void Event_TetDoanNgo_Diet_Sau_Bo()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000700)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000258), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 7);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ bảo vật cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_QuocTe_ThieuNhi_1_6()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ bảo vật cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_Cung_CoHon_Thang_7()
	{
		var num = 5;
		var num2 = 5;
		var num3 = 5;
		var num4 = 0;
		var num5 = 0;
		var num6 = 0;
		var num7 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000608)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000609)
			{
				num5 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000610)
			{
				num6 = Item_In_Bag[i].GetVatPhamSoLuong;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000949)
			{
				num7 = 1;
				position4 = i;
			}
			if (num4 != 0 && num5 != 0 && num6 != 0 && num7 != 0)
			{
				break;
			}
		}
		if (num4 >= num && num5 >= num2 && num6 >= num3 && num7 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				SubtractItem(position3, num3);
				SubtractItem(position4, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000692), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Hành trang không đủ chỗ trống!!", 7, "Thiên cơ các");
			}
			return;
		}
		HeThongNhacNho("Yêu cầu: Kẹo Đỏ: [" + num + "], Kẹo Xanh: [" + num2 + "], Kẹo Vàng: [" + num3 + "] để kết hợp!", 7, "Thiên cơ các");
		HeThongNhacNho("Đỏ thiếu: [" + (num - num4) + "], Xanh thiếu: [" + (num2 - num5) + "], Vàng thiếu: [" + (num3 - num6) + "]!", 7, "Thiên cơ các");
	}

	public void Event_Cung_CoHon_Thang_7_Loai_2()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000608)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000609)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000610)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000692), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ bảo vật cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_QuocTe_ThieuNhi_1_6_Loai2()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ bảo vật cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_QuocTe_ThieuNhi_1_6_Loai3()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ bảo vật cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void HiepKhachGiangHo()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000212), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void GiaiPhong_30_4_1_5()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000387), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void GiaiPhong_30_4_1_5_Ruong_Go()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008000387)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000051), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_Hop_KhongBiet_16x()
	{
		var num = 50;
		var num2 = 50;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			if (num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num3 >= num && num4 >= num2)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008001743), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 7);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] và Bảo vật 2: [" + num2 + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num3) + "], Bảo vật 2 còn thiếu: [" + (num2 - num4) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_2_9_2023()
	{
		var num = 10;
		var num2 = 2;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			if (num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num3 >= num && num4 >= num2)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000811), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 30);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] và Bảo vật 2: [" + num2 + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num3) + "], Bảo vật 2 còn thiếu: [" + (num2 - num4) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Hop_LapPhuongHuyenAo_VuKhi()
	{
		var num = 30;
		var num2 = 3;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000001600)
			{
				num3 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008002160)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			if (num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num3 >= num && num4 >= num2)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000655), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 21);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] và Bảo vật 2: [" + num2 + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num3) + "], Bảo vật 2 còn thiếu: [" + (num2 - num4) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Hop_LapPhuongHuyenAo_TrangBi()
	{
		var num = 30;
		var num2 = 1;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000001601)
			{
				num3 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008002152)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			if (num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num3 >= num && num4 >= num2)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000657), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 21);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] và Bảo vật 2: [" + num2 + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num3) + "], Bảo vật 2 còn thiếu: [" + (num2 - num4) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void HaiThuocNamLam_1()
	{
		var num = 10;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008000368)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(ItemConstants.VO_HOANG_DON_NHO_300), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 0);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần: Bảo vật 1: [" + num + "], Bảo vật 1 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void HaiThuocNamLam_2()
	{
		var num = 20;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008000369)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(ItemConstants.VO_HOANG_DON_VUA_500), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 0);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần: Bảo vật 2: [" + num + "], Bảo vật 2 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void HaiThuocNamLam_3()
	{
		var num = 30;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008000370)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			SubtractItem(position, num);
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(ItemConstants.VO_HOANG_DON_YEU_CAU), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 21);
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần: Bảo vật 3: [" + num + "], Bảo vật 3 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void HaiThuocNamLam_4()
	{
		var num = 40;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008000371)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			SubtractItem(position, num);
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(ItemConstants.VO_HOANG_DON_DAC_BIET), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 21);
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần: Bảo vật 4: [" + num + "], Bảo vật 4 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_CheBienMoiNgon()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_BanhSinhNhat()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000906), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 21);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_LuatSu()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 21);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Event_LuatSu_VeKien()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000042)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 21);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa thu thập đủ nguyên liệu cần thiết!!", 10, "Thiên cơ các");
		}
	}

	public void Doi_AC_Nam_Thuong()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000036)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008003445)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008003446), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void Doi_AC_Nu_Thuong()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000037)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008003446)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008003445), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void Doi_AC_Nam_Dep()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000038)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008003447)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008003448), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void Doi_AC_Nu_Dep()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000039)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008003448)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008003447), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void Doi_AC_Nam_Hiem()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000040)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008003449)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008003450), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void Doi_AC_Nu_Hiem()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000041)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008003450)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008003449), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void EventTrungThu_2()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var num5 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		var position5 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num5 = 1;
				position5 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				SubtractItem(position5, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 5 loại bong bóng với 5 màu sắc khác nhau!!", 10, "Thiên cơ các");
		}
	}

	public void EventBanhSinhNhat()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000274), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 8 loại nguyên liệu: [Bột], [Sữa Tươi], [Đường], [Trứng Gà], [Socola], [Vani], [Dâu Tây] và [Nước]!!", 10, "Thiên cơ các");
		}
	}

	public void EventTimKhoBau()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var num5 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		var position5 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 999000753)
			{
				num5 = 1;
				position5 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				SubtractItem(position5, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 5 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void KhoBau_1()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001064), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void KhoBau_2()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001065), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void KhoBau_3()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001066), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void KhoBau_4()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001067), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void KhoBau_5()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001068), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần thu thập đủ 2 loại nguyên liệu!", 10, "Thiên cơ các");
		}
	}

	public void Event_Tet_QuyMao()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_Tet_QuyMao_2()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000534), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void ThuThap_Event_TetGiapThin()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_Tet_GiapThin_KetHop()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000095)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000009), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp còn thiếu [Bánh Tét] để đổi lấy bảo rương Tết Giáp Thìn!", 10, "Thiên cơ các");
		}
	}

	public void Event_Noel_X_MAS()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var num5 = 0;
		var num6 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		var position5 = 0;
		var position6 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000402)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000403)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000404)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000405)
			{
				num4 = 1;
				position4 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000406)
			{
				num5 = 1;
				position5 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000407)
			{
				num6 = 1;
				position6 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0 && num6 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0 && num6 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				SubtractItem(position5, 1);
				SubtractItem(position6, 1);
				if (Player_Sex == 1)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008001808), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 1);
				}
				else
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008001809), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 1);
				}
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp còn thiếu một số loại Vớ khác!", 10, "Thiên cơ các");
		}
	}

	public void Event_Noel_1()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000677)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000251), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp còn thiếu các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_Noel_2()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var num5 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		var position5 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num5 = 1;
				position5 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0 && num5 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				SubtractItem(position5, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp còn thiếu các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_Valentine()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001084), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_Tuan_Cho()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = 1;
				position4 = i;
			}
			if (num < 10 && num2 < 10 && num3 < 10 && num4 < 10)
			{
				break;
			}
		}
		if (num >= 10 && num2 >= 10 && num3 >= 10 && num4 >= 10)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 10);
				SubtractItem(position2, 10);
				SubtractItem(position3, 10);
				SubtractItem(position4, 10);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa đủ số lượng bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Hop_Event_NgayNhaGiao_20_11()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 7);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa đủ số lượng bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Hop_Event_Trung_Thu_1()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000887)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000953)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008002665), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 7);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa đủ số lượng bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Hop_Event_Trung_Thu_2()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000001648)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000887)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000953)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008002665)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008001736), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 7);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa đủ số lượng bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Hop_Event_V23()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000013)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000001680)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000010), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Chưa đủ số lượng bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_QuocTePhuNu_20_10()
	{
		var num = 5;
		var num2 = 0;
		var num3 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000973)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000001681)
			{
				num3 = 1;
				position2 = i;
			}
			if (num2 != 0 && num3 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num3);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008002698), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần [" + num + "] bảo vật để kết hợp, còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void ManhGhep_TrangBi_VoHuan_12x()
	{
		var num = 2;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				if (Player_Job == 1)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001051), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 2)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001052), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 3)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001053), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 4)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001054), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 5)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001055), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 6)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001056), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 7)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001057), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 8)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001058), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 9)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001059), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 10)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001060), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 11)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001061), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 12)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001062), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
				else if (Player_Job == 13)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001063), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 3);
				}
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần [" + num + "] bảo vật để kết hợp, còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void ManhGhep_TrangBi_VoHuan_13xC()
	{
		var num = 2;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				if (Player_Job == 1)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000501), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 2)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000502), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 3)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000503), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 4)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000504), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 5)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000505), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 6)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000506), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 7)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000507), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 8)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000508), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 9)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000509), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 10)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000510), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 11)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000511), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 12)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000512), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
				else if (Player_Job == 13)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000513), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 3);
				}
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần [" + num + "] bảo vật để kết hợp, còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void ThuThap_VatPham_VoHuan_16x()
	{
		var num = 20;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 30);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần [" + num + "] bảo vật để kết hợp, còn thiếu: [" + (num - num2) + "]!", 20, "Thiên cơ các");
		}
	}

	public void VatPham_16x_VuKhi()
	{
		var num = 20;
		var num2 = 1;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000001508)
			{
				num3 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008002062)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			if (num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num3 >= num && num4 >= num2)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000657), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 21);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] và Bảo vật 2: [" + num2 + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num3) + "], Bảo vật 2 còn thiếu: [" + (num2 - num4) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Item_Hop_16x_Fake()
	{
		var num = 10;
		var num2 = 1;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000531)
			{
				num3 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num4 = Item_In_Bag[i].GetVatPhamSoLuong;
				position2 = i;
			}
			if (num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num3 >= num && num4 >= num2)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				SubtractItem(position2, num2);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000532), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 30);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] để kết hợp, còn thiếu: [" + (num - num3) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Hop_Do_VoHuan_15x()
	{
		var array = new int[66];
		var array2 = new int[66];
		for (var i = 0; i < 96; i++)
		{
			var value = BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0);
			var array3 = new int[66]
			{
				*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
				*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
				*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
				*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
				*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
				*********, *********, *********, *********, 700503036, 700803036, 800200326, 800303036, 800403037, 800503036,
				800803036, 900200326, 900303036, 900403037, 900503036, 900803036
			};
			var num = Array.IndexOf(array3, value);
			if (num != -1)
			{
				array[num] = 1;
				array2[num] = i;
			}
			if (array[0] != 0 && array.Any((int n) => n != 0))
			{
				break;
			}
		}
		if (array[0] != 0 && array.Any((int n) => n != 0))
		{
			SubtractItem(array2[0], 1);
			IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001068), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
		}
	}

	public void Item_Doi_Hop_16xC()
	{
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) != 900000841)
			{
				continue;
			}
			var source = new int[66]
			{
				100201264, 100202274, 110301025, 110302025, 110304025, 120301025, 120302025, 120304025, 200201264, 200202274,
				210301025, 210302025, 210304025, 220301025, 220302025, 220304025, 300201264, 300202274, 310301025, 310302025,
				310304025, 320301025, 320302025, 400201264, 400202274, 410301025, 410302025, 410304025, 420301025, 420302025,
				420304025, 500201264, 500202274, 510301025, 510302025, 520301025, 520302025, 520304025, 700201264, 700202274,
				710301025, 710302025, 720301025, 720302025, 800201264, 800202274, 810301025, 810302025, 820301025, 820302025,
				900201264, 900202274, 910301025, 910302025, 920301025, 920302025, 400019, 400119, 500122, 501022,
				502022, 502122, 800123, 801023, 802023, 802123
			};
			var value = BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0);
			if (source.Contains(value))
			{
				if (Item_In_Bag[i].GetVatPhamSoLuong != 0 && Item_In_Bag[0].GetVatPhamSoLuong != 0 && !Item_In_Bag[0].VatPham_KhoaLai)
				{
					if (GetParcelVacancyNumber() >= 1)
					{
						SubtractItem(i, 1);
						SubtractItem(0, 1);
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000532), GetParcelVacancy(this), BitConverter.GetBytes(1), new byte[56]);
					}
					else
					{
						HeThongNhacNho("Hành trang không còn chỗ trống!!", 20, "Thiên cơ các");
					}
				}
				else
				{
					HeThongNhacNho("Đại hiệp chưa thu thập đủ nguyên liệu!!", 20, "Thiên cơ các");
				}
				continue;
			}
			HeThongNhacNho("Đại hiệp cần tìm bảo vật thích hợp đặt vào ô đầu tiên của hành trang!", 20, "Thiên cơ các");
			break;
		}
	}

	public void Event_2_9_2024_VeBac()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000524)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_2_9_2024_VeVang()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var position = 0;
		var position2 = 0;
		var position3 = 0;
		var position4 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = 1;
				position2 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num3 = 1;
				position3 = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000525)
			{
				num4 = 1;
				position4 = i;
			}
			if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0 && num3 != 0 && num4 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				SubtractItem(position3, 1);
				SubtractItem(position4, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000645), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_Halloween_0()
	{
		var num = 5;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_Halloween()
	{
		var num = 1;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_Halloween_1()
	{
		var num = 1;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000177), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_Halloween_2()
	{
		var num = 1;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1008000177)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_Halloween_3()
	{
		var num = 1;
		var num2 = 0;
		var position = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num2 = Item_In_Bag[i].GetVatPhamSoLuong;
				position = i;
			}
			if (num2 != 0)
			{
				break;
			}
		}
		if (num2 >= num)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, num);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 10);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp cần Bảo vật 1: [" + num + "] để kết hợp, Bảo vật 1 còn thiếu: [" + (num - num2) + "]!!", 20, "Thiên cơ các");
		}
	}

	public void Event_8_3_2024()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == *********)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 100017)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_ThichHutCan()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000011)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000012)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000127), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 5);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

	public void Event_ThichHutCan_2_Item_Boss()
	{
		var num = 0;
		var num2 = 0;
		var position = 0;
		var position2 = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000127)
			{
				num = 1;
				position = i;
			}
			else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000974)
			{
				num2 = 1;
				position2 = i;
			}
			if (num != 0 && num2 != 0)
			{
				break;
			}
		}
		if (num != 0 && num2 != 0)
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				SubtractItem(position, 1);
				SubtractItem(position2, 1);
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 7);
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống hành trang!!", 20, "Thiên cơ các");
			}
		}
		else
		{
			HeThongNhacNho("Đại hiệp vẫn chưa thu thập đủ các bảo vật cần thiết!", 10, "Thiên cơ các");
		}
	}

}
