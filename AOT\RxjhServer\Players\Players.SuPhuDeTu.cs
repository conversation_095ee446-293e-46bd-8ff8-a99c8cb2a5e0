﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    

	public void MasterAndApprenticeTips(int id, string doiPhuongTen)
	{
		var array = Converter.HexStringToByte("AA552000B9004110120029000F0000000000000000000000000000000000000000001C8455AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		var bytes = Encoding.Default.GetBytes(doiPhuongTen);
		Buffer.BlockCopy(bytes, 0, array, 14, bytes.Length);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void MentoringSystem(byte[] packetData, int packetSize)
	{
		try
		{
			var array = new byte[15];
			for (var i = 0; i < 15 && packetData[12 + i] != 0; i++)
			{
				array[i] = packetData[12 + i];
			}
			var text = Encoding.Default.GetString(array).Replace("\0", "").Trim();
			switch (packetData[10])
			{
				case 2:
					{
						if (MasterData.TID != -1)
						{
							MasterAndApprenticeTips(22, text);
							break;
						}
						if (Player_Job_level < 1)
						{
							MasterAndApprenticeTips(12, text);
							break;
						}
						var players2 = World.KiemTra_Ten_NguoiChoi(text);
						if (players2 != null)
						{
							if (players2.MasterData.TID != -1)
							{
								MasterAndApprenticeTips(21, text);
								break;
							}
							if (Player_Job == 8)
							{
								MasterAndApprenticeTips(42, text);
								break;
							}
							if (Player_Job == 9)
							{
								HeThongNhacNho("Đàm Hoa Liên không thể thu làm đồ đệ!", 10, "Thiên cơ các");
								break;
							}
							if (Player_Job == 10)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ!", 10, "Thiên cơ các");
								break;
							}
							if (Player_Job == 11)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ 11!", 10, "Thiên cơ các");
								break;
							}
							if (Player_Job == 12)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ 22!", 10, "Thiên cơ các");
								break;
							}
							if (Player_Job == 13)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ 33!", 10, "Thiên cơ các");
								break;
							}
							var flag2 = false;
							for (var k = 0; k < 3; k++)
							{
								if (players2.ApprenticeData[k].TID == -1)
								{
									flag2 = true;
									break;
								}
							}
							if (!flag2)
							{
								MasterAndApprenticeTips(21, text);
								break;
							}
							if (players2.Player_Job_level < 2)
							{
								MasterAndApprenticeTips(11, text);
								break;
							}
							if (Player_Zx != 0 && Player_Zx != players2.Player_Zx)
							{
								MasterAndApprenticeTips(14, text);
								break;
							}
							if (!FindPlayers(40, players2))
							{
								MasterAndApprenticeTips(15, text);
								break;
							}
							FLD_TemporaryMasters = text;
							players2.FLD_TemporaryMasters = CharacterName;
							MasterAndApprenticeTips(513, text);
							players2.MasterAndApprenticeTips(513, CharacterName);
						}
						else
						{
							MasterAndApprenticeTips(41, text);
						}
						break;
					}
				case 1:
					{
						if (MasterData.TID != -1)
						{
							MasterAndApprenticeTips(21, text);
						}
						var flag = false;
						for (var j = 0; j < 3; j++)
						{
							if (ApprenticeData[j].TID == -1)
							{
								flag = true;
								break;
							}
						}
						if (!flag)
						{
							MasterAndApprenticeTips(21, text);
							break;
						}
						if (Player_Job_level < 2)
						{
							MasterAndApprenticeTips(11, text);
							break;
						}
						var players = World.KiemTra_Ten_NguoiChoi(text);
						if (players != null)
						{
							if (MasterData.TID != -1)
							{
								MasterAndApprenticeTips(22, text);
								break;
							}
							if (players.Player_Job == 8)
							{
								MasterAndApprenticeTips(42, text);
								break;
							}
							if (players.Player_Job == 9)
							{
								HeThongNhacNho("Đàm Hoa Liên không thể thu làm đồ đệ!", 10, "Thiên cơ các");
								break;
							}
							if (players.Player_Job == 10)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ!", 10, "Thiên cơ các");
								break;
							}
							if (players.Player_Job == 11)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ 11!", 10, "Thiên cơ các");
								break;
							}
							if (players.Player_Job == 12)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ 22!", 10, "Thiên cơ các");
								break;
							}
							if (players.Player_Job == 13)
							{
								HeThongNhacNho("Cách Đấu Thức không thể thu làm đồ đệ 33!", 10, "Thiên cơ các");
								break;
							}
							if (players.Player_Job_level < 1)
							{
								MasterAndApprenticeTips(12, text);
								break;
							}
							if (players.Player_Zx != 0 && Player_Zx != players.Player_Zx)
							{
								MasterAndApprenticeTips(14, text);
								break;
							}
							if (!FindPlayers(40, players))
							{
								MasterAndApprenticeTips(15, text);
								break;
							}
							FLD_TemporaryMasters = text;
							players.FLD_TemporaryMasters = CharacterName;
							MasterAndApprenticeTips(257, text);
							players.MasterAndApprenticeTips(257, CharacterName);
						}
						else
						{
							MasterAndApprenticeTips(41, text);
						}
						break;
					}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thạc sĩ và học việc Thông báo error" + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}
	
	public void MasterApprenticeSystemRequest(byte[] packetData, int packetSize)
	{
		try
		{
			int num = packetData[10];
			int num2 = packetData[11];
			var players = World.KiemTra_Ten_NguoiChoi(FLD_TemporaryMasters);
			if (players == null)
			{
				return;
			}
			switch (num)
			{
				case 2:
					switch (num2)
					{
						case 2:
							FLD_TemporaryMasters = "";
							players.FLD_TemporaryMasters = "";
							MasterAndApprenticeRequestAcceptance(514, "");
							players.MasterAndApprenticeRequestAcceptance(514, CharacterName);
							break;
						case 1:
							{
								var num4 = -1;
								for (var j = 0; j < 3; j++)
								{
									if (players.ApprenticeData[j].TID == -1)
									{
										num4 = j;
										break;
									}
								}
								//if (num4 != -1 && RxjhClass.TaoMoiQuanHeSuDo(players.FLD_TemporaryMasters, FLD_TemporaryMasters, Player_Level, num4) == 1)
								if (num4 != -1 && GameDb.AddMasterApprenticeRelation(players.FLD_TemporaryMasters, FLD_TemporaryMasters, Player_Level).GetAwaiter().GetResult())
								{
									MasterData.STNAME = FLD_TemporaryMasters;
									MasterData.STLEVEL = 5;
									MasterData.TID = num4;
									MasterData.STYHD = 0;
									MasterData.TLEVEL = Player_Level;
									MasterData.STWG1 = 0;
									MasterData.STWG2 = 0;
									MasterData.STWG3 = 0;
									players.ApprenticeData[num4].TID = num4;
									players.ApprenticeData[num4].STNAME = players.FLD_TemporaryMasters;
									players.ApprenticeData[num4].STLEVEL = 5;
									players.ApprenticeData[num4].STYHD = 0;
									players.ApprenticeData[num4].TLEVEL = Player_Level;
									players.ApprenticeData[num4].STWG1 = 0;
									players.ApprenticeData[num4].STWG2 = 0;
									players.ApprenticeData[num4].STWG3 = 0;
									HeThongNhacNho("Bái sư thành công, ngài sư phó là [" + FLD_TemporaryMasters + "]. CTRL+B Mở ra sư đồ giao diện.");
									players.MasterAndApprenticeRequestAcceptance(258, players.FLD_TemporaryMasters);
									ApprenticeUpdateMentoringSystem();
									players.MasterUpdatesTheMentoringSystem(num4);
									FLD_TemporaryMasters = "";
									players.FLD_TemporaryMasters = "";
								}
								break;
							}
					}
					break;
				case 1:
					switch (num2)
					{
						case 2:
							FLD_TemporaryMasters = "";
							players.FLD_TemporaryMasters = "";
							MasterAndApprenticeRequestAcceptance(513, "");
							players.MasterAndApprenticeRequestAcceptance(513, CharacterName);
							break;
						case 1:
							{
								var num3 = -1;
								for (var i = 0; i < 3; i++)
								{
									if (ApprenticeData[i].TID == -1)
									{
										num3 = i;
										break;
									}
								}
								if (num3 != -1 && GameDb.AddMasterApprenticeRelation(FLD_TemporaryMasters, players.FLD_TemporaryMasters, players.Player_Level).GetAwaiter().GetResult())
								//if (num3 != -1 && RxjhClass.TaoMoiQuanHeSuDo(FLD_TemporaryMasters, players.FLD_TemporaryMasters, players.Player_Level, num3) == 1)
								{
									players.MasterData.STNAME = players.FLD_TemporaryMasters;
									players.MasterData.STLEVEL = 5;
									players.MasterData.TID = num3;
									players.MasterData.STYHD = 0;
									players.MasterData.TLEVEL = players.Player_Level;
									players.MasterData.STWG1 = 0;
									players.MasterData.STWG2 = 0;
									players.MasterData.STWG3 = 0;
									ApprenticeData[num3].TID = num3;
									ApprenticeData[num3].STNAME = FLD_TemporaryMasters;
									ApprenticeData[num3].STLEVEL = 5;
									ApprenticeData[num3].STYHD = 0;
									ApprenticeData[num3].TLEVEL = players.Player_Level;
									ApprenticeData[num3].STWG1 = 0;
									ApprenticeData[num3].STWG2 = 0;
									ApprenticeData[num3].STWG3 = 0;
									MasterAndApprenticeRequestAcceptance(257, string.Empty);
									players.MasterAndApprenticeRequestAcceptance(769, players.FLD_TemporaryMasters);
									players.HeThongNhacNho("Bái sư thành công, sư phụ của đại hiệp là " + players.FLD_TemporaryMasters + "!", 9, "Truyền Âm Các");
									players.ApprenticeUpdateMentoringSystem();
									MasterUpdatesTheMentoringSystem(num3);
									FLD_TemporaryMasters = "";
									players.FLD_TemporaryMasters = "";
								}
								break;
							}
					}
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thạc sĩ và học việc Thông báo hỏi error 11 " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}

	public void TheMentoringSystemCanceled(byte[] packetData, int packetSize)
	{
		try
		{
			int num = packetData[10];
			int num2 = packetData[11];
			var characterData = GetCharacterData(FLD_TemporaryMasters);
			if (characterData == null)
			{
				return;
			}
			switch (num)
			{
				case 2:
					if (num2 == 3)
					{
						CancelMentorshipRequest(770, FLD_TemporaryMasters);
						characterData.CancelMentorshipRequest(769, FLD_TemporaryMasters);
					}
					break;
				case 1:
					if (num2 == 3)
					{
						CancelMentorshipRequest(769, FLD_TemporaryMasters);
						characterData.CancelMentorshipRequest(769, FLD_TemporaryMasters);
					}
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thạc sĩ và học việc Thông báo Hủy bỏ error 22 " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}

	public void MasterAndApprenticeRequestAcceptance(int id, string doiPhuongTen)
	{
		using SendingClass sendingClass = new();
		sendingClass.Write2(id);
		sendingClass.Write(15);
		sendingClass.WriteName(doiPhuongTen);
		Client?.SendPak(sendingClass, 17168, SessionID);
	}

	public void CancelMentorshipRequest(int id, string doiPhuongTen)
	{
		var array = Converter.HexStringToByte("AA552000FF034510120001020F0000000000000000000000000000000000000000008BA555AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		var bytes = Encoding.Default.GetBytes(doiPhuongTen);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		Buffer.BlockCopy(bytes, 0, array, 13, bytes.Length);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void RequestForTerminationOfMentorship(int id, string doiPhuongTen)
	{
		var array = Converter.HexStringToByte("AA552000D5034710120001010F000000000000000000000000000000000000000000DBE955AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		var bytes = Encoding.Default.GetBytes(doiPhuongTen);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		Buffer.BlockCopy(bytes, 0, array, 14, bytes.Length);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public async Task DismissalOfTheMentoringSystem(byte[] packetData, int packetSize)
	{
		try
		{
			int num = packetData[10];
			var array = new byte[15];
			for (var i = 0; i < 15 && packetData[12 + i] != 0; i++)
			{
				array[i] = packetData[12 + i];
			}
			var text = Encoding.Default.GetString(array).Replace("\0", "").Trim();
			switch (num)
			{
				case 2:
					{
						if (!await GameDb.RemoveMasterApprenticeRelation(text, CharacterName))
						{
							break;
						}
						MasterData.TID = -1;
						RequestForTerminationOfMentorship(257, text);
						var players2 = World.KiemTra_Ten_NguoiChoi(text);
						if (players2 == null)
						{
							break;
						}
						var num3 = 0;
						for (var k = 0; k < 3; k++)
						{
							if (players2.ApprenticeData[k].STNAME == text)
							{
								num3 = k;
								break;
							}
						}
						players2.ApprenticeData[num3].TID = -1;
						players2.RequestForTerminationOfMentorship(257, CharacterName);
						break;
					}
				case 1:
					{
						var num2 = 0;
						for (var j = 0; j < 3; j++)
						{
							if (ApprenticeData[j].STNAME == text)
							{
								num2 = j;
								break;
							}
						}
						if (!await GameDb.RemoveMasterApprenticeRelation(CharacterName, text))
						{
							break;
						}
						ApprenticeData[num2].TID = -1;
						RequestForTerminationOfMentorship(257, text);
						var players = World.KiemTra_Ten_NguoiChoi(text);
						if (players != null)
						{
							players.MasterData.TID = -1;
							players.RequestForTerminationOfMentorship(257, CharacterName);
						}
						break;
					}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thạc sĩ và học việc Thông báo nâng lên 11 error " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}
	
	public void SuDo_VoCong_KiemTra(byte[] data, int length)
	{
		try
		{
			var array = new byte[15];
			for (var i = 0; i < 15 && data[10 + i] != 0; i++)
			{
				array[i] = data[10 + i];
			}
			var players = World.KiemTra_Ten_NguoiChoi(Encoding.Default.GetString(array).Replace("\0", "").Trim());
			if (players != null)
			{
				var array2 = Converter.HexStringToByte("AA550F00D5036110010003000000000000EC0F55AA");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(players.Player_Job), 0, array2, 10, 1);
				Client?.Send_Map_Data(array2, array2.Length);
			}
			else
			{
				var array3 = Converter.HexStringToByte("AA550F00D5036110010003000000000000EC0F55AA");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(Player_Job), 0, array3, 10, 1);
				Client?.Send_Map_Data(array3, array3.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thạc sĩ và học việc VoCongKiemTra error " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}

	public void SuDo_TeachMartialArts(byte[] packetData, int length)
	{
		try
		{
			var array = new byte[15];
			for (var i = 0; i < 15 && packetData[12 + i] != 0; i++)
			{
				array[i] = packetData[12 + i];
			}
			var text = Encoding.Default.GetString(array).Replace("\0", "").Trim();
			var array2 = new byte[4];
			int num = packetData[27];
			Buffer.BlockCopy(packetData, 28, array2, 0, 4);
			var num2 = BitConverter.ToInt32(array2, 0);
			var players = World.KiemTra_Ten_NguoiChoi(text);
			if (players == null || !World.MagicList.TryGetValue(num2, out var value))
			{
				return;
			}
			if (players.Player_Job_level < value.FLD_JOBLEVEL - 1)
			{
				var text2 = players.Player_Job_level.ToString();
				var text3 = 1.ToString();
				HeThongNhacNho("Đồ đệ chỉ có thể truyền thụ tối đa (" + text2 + text3 + ") võ công thăng chức!", 10, "Thiên cơ các");
				return;
			}
			var num3 = -1;
			for (var j = 0; j < 3; j++)
			{
				if (ApprenticeData[j].STNAME == text)
				{
					num3 = j;
					break;
				}
			}
			if (num3 == -1)
			{
				return;
			}
			var fLdAt = value.FLD_AT;
			var fLdMp = value.FLD_MP;
			switch (players.MasterData.STLEVEL)
			{
				case 4:
				case 5:
					{
						if (num == 1 || num == 2)
						{
							HeThongNhacNho("Cấp sư đồ 4/5 chỉ có thể truyền thụ võ công thứ nhất!", 10, "Thiên cơ các");
							return;
						}
						var sTlevel = players.MasterData.STLEVEL;
						break;
					}
				case 2:
				case 3:
					if (num == 2)
					{
						HeThongNhacNho("Cấp sư đồ 3/4 chỉ có thể truyền thụ hai môn võ công!", 10, "Thiên cơ các");
						return;
					}
					if (players.MasterData.STLEVEL != 3)
					{
					}
					break;
			}
			switch (num)
			{
				default:
					ApprenticeData[num3].STWG3 = num2;
					players.MasterData.STWG3 = num2;
					break;
				case 1:
					ApprenticeData[num3].STWG2 = num2;
					players.MasterData.STWG2 = num2;
					break;
				case 0:
					ApprenticeData[num3].STWG1 = num2;
					players.MasterData.STWG1 = num2;
					break;
			}
			TeachMartialArts(num, num2);
			players.TeachMartialArts(num, num2);
			MasterUpdatesTheMentoringSystem(num3);
			players.ApprenticeUpdateMentoringSystem();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Cố vấn VoCong error " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}

	public void TeachMartialArts(int position, int voCongId)
	{
		var array = Converter.HexStringToByte("AA551B00D50349100D0001CBE204000000000000000000000000000000A15D55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(voCongId), 0, array, 11, 4);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void ApprenticeUpdateMentoringSystem()
	{
		if (MasterData.STLEVEL <= 0)
		{
			return;
		}
		if (MasterData.STLEVEL <= 3)
		{
			var num = ********;
			if (!AppendStatusList.ContainsKey(*********))
			{
				int thoiGian;
				StatusEffect(BitConverter.GetBytes(*********), 1, thoiGian = num + 3000);
				StatusEffect xThemVaoTrangThaiLoai = new(this, thoiGian, *********, 1);
				AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
				addFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
				CharactersToAddMax_HP += 20;
				UpdateMartialArtsAndStatus();
				CapNhat_HP_MP_SP();
			}
		}
		if (MasterData.STLEVEL <= 2)
		{
			var num2 = ********;
			if (!AppendStatusList.ContainsKey(*********))
			{
				int thoiGian2;
				StatusEffect(BitConverter.GetBytes(*********), 1, thoiGian2 = num2 + 3000);
				StatusEffect xThemVaoTrangThaiLoai2 = new(this, thoiGian2, *********, 1);
				AppendStatusList.Add(xThemVaoTrangThaiLoai2.FLD_PID, xThemVaoTrangThaiLoai2);
				addFLD_ThemVaoTiLePhanTram_Attack(0.05);
				CharactersToAddMax_HP += 20;
				UpdateMartialArtsAndStatus();
				CapNhat_HP_MP_SP();
			}
		}
		if (MasterData.STLEVEL == 1)
		{
			var num3 = ********;
			if (!AppendStatusList.ContainsKey(900000462))
			{
				int thoiGian3;
				StatusEffect(BitConverter.GetBytes(900000462), 1, thoiGian3 = num3 + 3000);
				StatusEffect xThemVaoTrangThaiLoai3 = new(this, thoiGian3, 900000462, 1);
				AppendStatusList.Add(xThemVaoTrangThaiLoai3.FLD_PID, xThemVaoTrangThaiLoai3);
				FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
				CharactersToAddMax_HP += 20;
				UpdateMartialArtsAndStatus();
				CapNhat_HP_MP_SP();
			}
		}
		try
		{
			var array = Converter.HexStringToByte("AA554E00BF054A1048000FC5A00000D000000000000000000000020A0204E40D00A025260001000000000000000000000000323032303032313330300000000000000000000000000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (MasterData.STNAME != "")
			{
				var bytes = Encoding.Default.GetBytes(MasterData.STNAME);
				Buffer.BlockCopy(bytes, 0, array, 11, bytes.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(MasterData.STLEVEL), 0, array, 28, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(MasterData.TLEVEL), 0, array, 27, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(MasterData.STYHD), 0, array, 30, 2);
				var characterData = GetCharacterData(MasterData.STNAME);
				if (characterData != null)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 26, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(characterData.Player_Level), 0, array, 27, 1);
				}
				else
				{
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 26, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(MasterData.TLEVEL), 0, array, 27, 1);
				}
			}
			Buffer.BlockCopy(BitConverter.GetBytes(MasterData.STWG1), 0, array, 38, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(MasterData.STWG2), 0, array, 42, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(MasterData.STWG3), 0, array, 46, 4);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Thiên cơ các cố vấn cập nhật học viên 111 [" + AccountID + "][" + CharacterName + "] - CharID:[" + SessionID + "] - " + ex.Message);
		}
	}

	public void CalculateMentorAndApprenticeAttributes(int index)
	{
		var num = ********;
		if (ApprenticeData[index].STLEVEL <= 0)
		{
			return;
		}
		if (ApprenticeData[index].STLEVEL <= 3)
		{
			if (!AppendStatusList.ContainsKey(*********))
			{
				int thoiGian;
				StatusEffect(BitConverter.GetBytes(*********), 1, thoiGian = num + 3000);
				StatusEffect xThemVaoTrangThaiLoai = new(this, thoiGian, *********, 1);
				AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
			}
			addFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
			CharactersToAddMax_HP += 20;
			UpdateMartialArtsAndStatus();
			CapNhat_HP_MP_SP();
		}
		if (ApprenticeData[index].STLEVEL <= 2)
		{
			if (!AppendStatusList.ContainsKey(*********))
			{
				int thoiGian2;
				StatusEffect(BitConverter.GetBytes(*********), 1, thoiGian2 = num + 3000);
				StatusEffect xThemVaoTrangThaiLoai2 = new(this, thoiGian2, *********, 1);
				AppendStatusList.Add(xThemVaoTrangThaiLoai2.FLD_PID, xThemVaoTrangThaiLoai2);
			}
			addFLD_ThemVaoTiLePhanTram_Attack(0.05);
			CharactersToAddMax_HP += 20;
			UpdateMartialArtsAndStatus();
			CapNhat_HP_MP_SP();
		}
		if (ApprenticeData[index].STLEVEL == 1)
		{
			if (!AppendStatusList.ContainsKey(900000462))
			{
				int thoiGian3;
				StatusEffect(BitConverter.GetBytes(900000462), 1, thoiGian3 = num + 3000);
				StatusEffect xThemVaoTrangThaiLoai3 = new(this, thoiGian3, 900000462, 1);
				AppendStatusList.Add(xThemVaoTrangThaiLoai3.FLD_PID, xThemVaoTrangThaiLoai3);
			}
			FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			CharactersToAddMax_HP += 20;
			UpdateMartialArtsAndStatus();
			CapNhat_HP_MP_SP();
		}
	}

	public void MasterUpdatesTheMentoringSystem(int index)
	{
		var array = Converter.HexStringToByte("AA554E0077044B104800020F000000000000000000000000000000010A02D0E50D00A0252600000000000000000000000000323032303032313330300020202020202020202020202020202000202020202055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		var bytes = Encoding.Default.GetBytes(ApprenticeData[index].STNAME);
		Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
		if (GetCharacterData(ApprenticeData[index].STNAME) != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 27, 1);
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 27, 1);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(ApprenticeData[index].STLEVEL), 0, array, 29, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(ApprenticeData[index].TLEVEL), 0, array, 28, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(ApprenticeData[index].STYHD), 0, array, 31, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(ApprenticeData[index].STWG1), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(ApprenticeData[index].STWG2), 0, array, 42, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(ApprenticeData[index].STWG3), 0, array, 46, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(index), 0, array, 10, 1);
		Client?.Send_Map_Data(array, array.Length);
	}


}