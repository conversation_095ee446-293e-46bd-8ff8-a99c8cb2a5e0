using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using HeroYulgang.Helpers;

namespace RxjhServer.Quest;

/// <summary>
/// Class chứa dữ liệu quest được load từ quest_data.json
/// </summary>
public static class QuestClass
{
    /// <summary>
    /// Dictionary chứa tất cả quest data, key là questId
    /// </summary>
    public static Dictionary<int, QuestData> QuestDictionary { get; private set; } = new();

    /// <summary>
    /// Thông tin metadata của file quest
    /// </summary>
    public static QuestMetadata Metadata { get; private set; }

    /// <summary>
    /// Helper method để safely get property value
    /// </summary>
    private static T GetPropertySafe<T>(JsonElement element, string propertyName, T defaultValue = default(T))
    {
        if (!element.TryGetProperty(propertyName, out JsonElement property))
            return defaultValue;

        try
        {
            return typeof(T) switch
            {
                var t when t == typeof(string) => (T)(object)property.GetString(),
                var t when t == typeof(int) => (T)(object)property.GetInt32(),
                var t when t == typeof(long) => (T)(object)property.GetInt64(),
                var t when t == typeof(bool) => (T)(object)property.GetBoolean(),
                var t when t == typeof(int?) => property.ValueKind == JsonValueKind.Null ? default(T) : (T)(object)property.GetInt32(),
                var t when t == typeof(long?) => property.ValueKind == JsonValueKind.Null ? default(T) : (T)(object)property.GetInt64(),
                _ => defaultValue
            };
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Parse QuestRequirement from JsonElement
    /// </summary>
    private static QuestRequirement ParseQuestRequirement(JsonElement element)
    {
        return new QuestRequirement
        {
            Type = GetPropertySafe<string>(element, "type"),
            Value = GetPropertySafe<long>(element, "value"),
            ItemId = GetPropertySafe<long?>(element, "itemId"),
            ItemAmount = GetPropertySafe<long?>(element, "itemAmount"),
            MapId = GetPropertySafe<int?>(element, "mapId"),
            CoordsX = GetPropertySafe<int?>(element, "coordsX"),
            CoordsY = GetPropertySafe<int?>(element, "coordsY"),
            CoordsZ = GetPropertySafe<int?>(element, "coordsZ"),
            Description = GetPropertySafe<string>(element, "description"),
        };
    }

    /// <summary>
    /// Parse QuestReward from JsonElement
    /// </summary>
    private static QuestReward ParseQuestReward(JsonElement element)
    {
        return new QuestReward
        {
            Type = GetPropertySafe<string>(element, "type"),
            Value = GetPropertySafe<long>(element, "value"),
            ItemId = GetPropertySafe<long?>(element, "itemId"),
            ItemAmount = GetPropertySafe<long?>(element, "itemAmount"),
            Description = GetPropertySafe<string>(element, "description")
        };
    }

    /// <summary>
    /// Parse QuestGiver from JsonElement
    /// </summary>
    private static QuestGiver ParseQuestGiver(JsonElement element)
    {
        return new QuestGiver
        {
            NpcId = GetPropertySafe<int>(element, "npcId"),
            MapId = GetPropertySafe<int>(element, "mapId"),
            CoordsX = GetPropertySafe<int>(element, "coordsX"),
            CoordsY = GetPropertySafe<int>(element, "coordsY"),
            CoordsZ = GetPropertySafe<int>(element, "coordsZ"),
            Unknown1 = GetPropertySafe<int>(element, "unknown1")
        };
    }

    /// <summary>
    /// Parse QuestStageDialogs from JsonElement
    /// </summary>
    private static QuestStageDialogs ParseQuestStageDialogs(JsonElement element)
    {
        var dialogs = new QuestStageDialogs();
        
        if (element.TryGetProperty("conditionMatch", out var conditionMatchElement))
        {
            dialogs.ConditionMatch = conditionMatchElement.EnumerateArray()
                .Select(x => x.GetString())
                .Where(x => !string.IsNullOrEmpty(x))
                .ToList();
        }

        if (element.TryGetProperty("conditionNoMatch", out var conditionNoMatchElement))
        {
            dialogs.ConditionNoMatch = conditionNoMatchElement.EnumerateArray()
                .Select(x => x.GetString())
                .Where(x => !string.IsNullOrEmpty(x))
                .ToList();
        }

        return dialogs;
    }

    /// <summary>
    /// Load dữ liệu quest từ file quest_data.json
    /// </summary>
    /// <param name="filePath">Đường dẫn đến file quest_data.json</param>
    public static void LoadQuestData(string filePath = "quest_data.json", bool isReload = false)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy file quest data: {filePath}");
                return;
            }

            string jsonContent = File.ReadAllText(filePath);
            using JsonDocument document = JsonDocument.Parse(jsonContent);
            JsonElement root = document.RootElement;

            var questFile = new QuestFile
            {
                Version = GetPropertySafe<string>(root, "version"),
                ExportDate = GetPropertySafe<string>(root, "exportDate"),
                TotalQuests = GetPropertySafe<int>(root, "totalQuests"),
                Quests = new List<QuestData>()
            };

            if (root.TryGetProperty("quests", out var questsElement))
            {
                foreach (var questElement in questsElement.EnumerateArray())
                {
                    // Parse accept requirements
                    var acceptRequirements = new List<QuestRequirement>();
                    if (questElement.TryGetProperty("acceptRequirements", out var acceptReqElement))
                    {
                        foreach (var requirement in acceptReqElement.EnumerateArray())
                        {
                            acceptRequirements.Add(ParseQuestRequirement(requirement));
                        }
                    }

                    // Parse completion requirements
                    var completionRequirements = new List<QuestRequirement>();
                    if (questElement.TryGetProperty("completionRequirements", out var completionReqElement))
                    {
                        foreach (var requirement in completionReqElement.EnumerateArray())
                        {
                            completionRequirements.Add(ParseQuestRequirement(requirement));
                        }
                    }

                    // Parse rewards
                    var rewards = new List<QuestReward>();
                    if (questElement.TryGetProperty("rewards", out var rewardsElement))
                    {
                        foreach (var reward in rewardsElement.EnumerateArray())
                        {
                            rewards.Add(ParseQuestReward(reward));
                        }
                    }

                    var acceptRewards = new List<QuestReward>();
                    if (questElement.TryGetProperty("acceptRewards", out var acceptRewardsElement))
                    {
                        foreach (var reward in acceptRewardsElement.EnumerateArray())
                        {
                            acceptRewards.Add(ParseQuestReward(reward));
                        }
                    }

                    // Parse stages
                    var stages = new List<QuestStage>();
                    if (questElement.TryGetProperty("stages", out var stagesElement))
                    {
                        foreach (var stageElement in stagesElement.EnumerateArray())
                        {
                            var stage = new QuestStage
                            {
                                StageId = GetPropertySafe<int>(stageElement, "stageId"),
                                Content = GetPropertySafe<string>(stageElement, "content")
                            };

                            // Parse stage NPC
                            if (stageElement.TryGetProperty("npc", out var stageNpcElement))
                            {
                                stage.Npc = ParseQuestGiver(stageNpcElement);
                            }

                            // Parse stage requirements
                            if (stageElement.TryGetProperty("requirements", out var stageReqElement))
                            {
                                stage.Requirements = stageReqElement.EnumerateArray()
                                    .Select(ParseQuestRequirement)
                                    .ToList();
                            }

                            // Parse stage dialogs
                            // if (stageElement.TryGetProperty("dialogs", out var stageDialogsElement))
                            // {
                            //     var stageDialogsList = new List<QuestStageDialogs>();
                            //     foreach (var dialogElement in stageDialogsElement.EnumerateArray())
                            //     {
                            //         stageDialogsList.Add(ParseQuestStageDialogs(dialogElement));
                            //     }
                            //     // Note: Bạn có thể cần điều chỉnh cách lưu trữ dialogs ở đây
                            //     // vì hiện tại QuestStage.Dialogs là QuestStageDialogs, không phải List
                            // }

                            stages.Add(stage);
                        }
                    }

                    // Parse quest giver
                    QuestGiver questGiver = null;
                    if (questElement.TryGetProperty("questGiver", out var questGiverElement))
                    {
                        questGiver = ParseQuestGiver(questGiverElement);
                    }

                    var quest = new QuestData
                    {
                        QuestId = GetPropertySafe<int>(questElement, "questId"),
                        QuestName = GetPropertySafe<string>(questElement, "questName"),
                        QuestLevel = GetPropertySafe<int>(questElement, "questLevel"),
                        QuestType = GetPropertySafe<string>(questElement, "questType"),
                        Description = GetPropertySafe<string>(questElement, "description"),
                        AcceptRequirements = acceptRequirements,
                        CompletionRequirements = completionRequirements,
                        Rewards = rewards,
                        AcceptRewards = acceptRewards,
                        Stages = stages,
                        UnknownFields = new QuestUnknownFields(),
                        QuestGiver = questGiver ?? new QuestGiver(),
                        Dialogs = new QuestDialogs(),
                        IsSpecialQuest = GetPropertySafe<bool>(questElement, "isSpecialQuest"),
                        Category = GetPropertySafe<string>(questElement, "category"),
                        FooterExtend = GetPropertySafe<string>(questElement, "footerExtend")
                    };

                    questFile.Quests.Add(quest);
                }
            }

            if (questFile.Quests == null || questFile.Quests.Count == 0)
            {
                LogHelper.WriteLine(LogLevel.Error, "Dữ liệu quest không hợp lệ hoặc rỗng");
                return;
            }

            QuestDictionary.Clear();
            Metadata = new QuestMetadata
            {
                Version = questFile.Version,
                ExportDate = questFile.ExportDate,
                TotalQuests = questFile.TotalQuests
            };

            foreach (var quest in questFile.Quests)
            {
                if (!QuestDictionary.ContainsKey(quest.QuestId))
                {
                    QuestDictionary.Add(quest.QuestId, quest);
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Quest ID {quest.QuestId} đã tồn tại, bỏ qua");
                }
            }

            LogHelper.WriteLine(LogLevel.Info, $"Đã load thành công {QuestDictionary.Count} quest từ {filePath}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi load quest data: {ex.Message} {ex.StackTrace}");
        }
    }

    /// <summary>
    /// Lấy quest data theo ID
    /// </summary>
    /// <param name="questId">ID của quest</param>
    /// <returns>QuestData hoặc null nếu không tìm thấy</returns>
    public static QuestData GetQuest(int questId)
    {
        return QuestDictionary.TryGetValue(questId, out var quest) ? quest : null;
    }

    /// <summary>
    /// Kiểm tra quest có tồn tại không
    /// </summary>
    /// <param name="questId">ID của quest</param>
    /// <returns>True nếu quest tồn tại</returns>
    public static bool QuestExists(int questId)
    {
        return QuestDictionary.ContainsKey(questId);
    }

    /// <summary>
    /// Lấy danh sách quest theo level
    /// </summary>
    /// <param name="level">Level của player</param>
    /// <returns>Danh sách quest phù hợp với level</returns>
    public static List<QuestData> GetQuestsByLevel(int level)
    {
        var result = new List<QuestData>();
        foreach (var quest in QuestDictionary.Values)
        {
            if (quest.QuestLevel <= level)
            {
                result.Add(quest);
            }
        }
        return result;
    }

    /// <summary>
    /// Lấy danh sách quest theo category
    /// </summary>
    /// <param name="category">Category của quest</param>
    /// <returns>Danh sách quest thuộc category</returns>
    public static List<QuestData> GetQuestsByCategory(string category)
    {
        var result = new List<QuestData>();
        foreach (var quest in QuestDictionary.Values)
        {
            if (string.Equals(quest.Category, category, StringComparison.OrdinalIgnoreCase))
            {
                result.Add(quest);
            }
        }
        return result;
    }
}

// Các class model giữ nguyên như code gốc
public class QuestFile
{
    public string Version { get; set; }
    public string ExportDate { get; set; }
    public int TotalQuests { get; set; }
    public List<QuestData> Quests { get; set; }
}

public class QuestMetadata
{
    public string Version { get; set; }
    public string ExportDate { get; set; }
    public int TotalQuests { get; set; }
}

public class QuestData
{
    public int QuestId { get; set; }
    public string QuestName { get; set; }
    public int QuestLevel { get; set; }

    public string QuestType { get; set; }
    public string Description { get; set; }
    public List<QuestRequirement> AcceptRequirements { get; set; } = new();
    public List<QuestRequirement> CompletionRequirements { get; set; } = new();
    public List<QuestReward> Rewards { get; set; } = new();
    public List<QuestReward> AcceptRewards { get; set; } = new();
    public QuestGiver QuestGiver { get; set; }
    public QuestDialogs Dialogs { get; set; }
    public List<QuestStage> Stages { get; set; } = new();
    public bool IsSpecialQuest { get; set; }
    public string Category { get; set; }
    public string FooterExtend { get; set; }
    public QuestUnknownFields UnknownFields { get; set; }
}

public class QuestRequirement
{
    public string Type { get; set; }
    public long Value { get; set; }
    public long? ItemId { get; set; }
    public long? ItemAmount { get; set; }
    public int? MapId { get; set; }
    public int? CoordsX { get; set; }
    public int? CoordsY { get; set; }
    public int? CoordsZ { get; set; }
    public string Description { get; set; }

}

public class QuestReward
{
    public string Type { get; set; }
    public long Value { get; set; }
    public long? ItemId { get; set; }
    public long? ItemAmount { get; set; }
    public string Description { get; set; }
}

public class QuestGiver
{
    public int NpcId { get; set; }
    public int MapId { get; set; }
    public int CoordsX { get; set; }
    public int CoordsY { get; set; }
    public int CoordsZ { get; set; }
    public int Unknown1 { get; set; }
}

public class QuestDialogs
{
    public List<string> Accept { get; set; } = new();
    public List<string> Refuse { get; set; } = new();
    public List<string> WelcomeAccept { get; set; } = new();
    public List<string> WelcomeRefuse { get; set; } = new();
}

public class QuestStage
{
    public int StageId { get; set; }
    public string Content { get; set; }
    public QuestGiver Npc { get; set; }
    public List<QuestRequirement> Requirements { get; set; } = new();
    public QuestStageDialogs Dialogs { get; set; }
}

public class QuestStageDialogs
{
    public List<string> ConditionMatch { get; set; } = new();
    public List<string> ConditionNoMatch { get; set; } = new();
}

public class QuestUnknownFields
{
    public int Unknown1 { get; set; }
    public int Unknown2 { get; set; }
    public int Unknown3 { get; set; }
    public int Unknown4 { get; set; }
    public int Unknown5 { get; set; }
    public int Unknown6 { get; set; }
    public int Unknown7 { get; set; }
    public int Unknown8 { get; set; }
    public int Unknown9 { get; set; }
    public int Unknown10 { get; set; }
    public int Unknown11 { get; set; }
}