﻿using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public void AddItemScript(int vatPhamId, int vacancy, int soLuong)
	{
		var array = new byte[56];
		if (World.ItemList.TryGetValue(vatPhamId, out var value))
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC0), 0, array, 0, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC1), 0, array, 4, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC2), 0, array, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC3), 0, array, 12, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC4), 0, array, 16, 4);
			IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(vatPhamId), vacancy, BitConverter.GetBytes(soLuong), array);
		}
	}

	public bool CheckItem(int vatPhamId, int vatPhamSoLuong)
	{
		var result = false;
		if (World.ItemList.TryGetValue(vatPhamId, out var value))
		{
			if (value.FLD_QUESTITEM == 1)
			{
				for (var i = 0; i < NhiemVu_VatPham.Length; i++)
				{
					if (NhiemVu_VatPham[i].VatPham_ID == vatPhamId && NhiemVu_VatPham[i].VatPhamSoLuong >= vatPhamSoLuong)
					{
						result = true;
					}
				}
			}
			else
			{
				switch (vatPhamId)
				{
					case 909000001:
						if (CharacterExperience >= vatPhamSoLuong)
						{
							result = true;
						}
						break;
					case 909000002:
						if (SkillExperience >= vatPhamSoLuong)
						{
							result = true;
						}
						break;
					default:
						{
							var equipmentBarPackage = Item_In_Bag;
							var array = equipmentBarPackage;
							foreach (var xVatPhamLoai in array)
							{
								if (xVatPhamLoai.GetVatPham_ID == vatPhamId && xVatPhamLoai.GetVatPhamSoLuong >= vatPhamSoLuong)
								{
									result = true;
								}
							}
							break;
						}
					case 909000004:
						if (Player_Money >= vatPhamSoLuong)
						{
							result = true;
						}
						break;
					case 909000005:
						if (Player_WuXun >= vatPhamSoLuong)
						{
							result = true;
						}
						break;
				}
			}
		}
		return result;
	}
	
	public void TruongBachDan(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 18, array, 0, 2);
			if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) != 1008000009 && BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) != 1008000010)
			{
				return;
			}
			SubtractItem(BitConverter.ToInt32(array, 0), 1);
			var num = 0;
			for (var i = 0; i < 15; i++)
			{
				if (packetData[18 + i * 4] != 0)
				{
					int num2;
					for (num2 = 0; num2 < 15; num2++)
					{
						if (KhiCong[num2].KhiCongID == packetData[18 + i * 4])
						{
							int num3 = packetData[20 + i * 4];
							num += packetData[20 + i * 4];
							if (KhiCong[i].KhiCong_SoLuong >= num3)
							{
								KhiCong[i].KhiCong_SoLuong -= num3;
								break;
							}
							HeThongNhacNho("Điểm phân phối không khớp với khí công thực tế, đại hiệp hãy phân phối lại!");
							return;
						}
						num2++;
					}
				}
				else
				{
					i++;
				}
			}
			Player_Qigong_point += num;
			var array2 = Converter.HexStringToByte("AA551F002C01021011000000050000000000000000000000000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			Client?.SendMultiplePackage(array2, array2.Length);
			UpdateKhiCong();
			UpdateMartialArtsAndStatus();
			CapNhat_HP_MP_SP();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Changbaidan error：" + ex.Message);
		}
	}

	public void Item_TaySkill(byte[] packetData, int packetSize)
	{
		try
		{
			int num = BitConverter.ToInt16(packetData, 8);
			// int num2 = BitConverter.ToInt16(packetData, 10);
			// int num3 = BitConverter.ToInt16(packetData, 12);
			// int num4 = BitConverter.ToInt16(packetData, 14);
			// int num5 = BitConverter.ToInt16(packetData, 16);
			// int num6 = BitConverter.ToInt16(packetData, 18);
			var num7 = num;
			var num8 = num7;
			if (num8 == 15)
			{
				HeThongNhacNho("Bảo vật chưa được kích hoạt, không thể sử dụng!!", 10, "Thiên cơ các");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Item_TaySkill error：" + ex.Message);
		}
	}

	public void ThuocTay_Debuff_ThanNu(byte[] packetData, int packetSize)
	{
		var array = new byte[4];
		Buffer.BlockCopy(packetData, 10, array, 0, 2);
		if (Player_Job != 13)
		{
			HeThongNhacNho("Chỉ Thần Nữ mới có thể sử dụng bảo vật này!", 20, "Thiên cơ các");
		}
		else
		{
			if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) != 1008002416 && BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) != 1008002417 && BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) != 1008002418)
			{
				return;
			}
			var num = 0;
			SubtractItem(BitConverter.ToInt32(array, 0), 1);
			for (var i = 5; i <= 22; i++)
			{
				if (VoCongMoi[1, i] != null)
				{
					var num2 = ((VoCongMoi[1, i].VoCong_DangCap == 1) ? 1 : (VoCongMoi[1, i].VoCong_DangCap + 1));
					num = ((VoCongMoi[1, i].VoCong_DangCap >= 3) ? (num + 6) : (num + num2));
					VoCongMoi[1, i].VoCong_DangCap = 0;
					VoCongMoi[1, i] = null;
				}
			}
			HeThongNhacNho("Đã xóa bỏ lời nguyền, đại hiệp nhận [" + num + "] điểm!", 10, "Thiên cơ các");
			ThanNuVoCongDiemSo += num;
			SaveCharacterData();
			LearningSkillsTips();
			Init_Item_In_Bag();
			UpdateMartialArtsAndStatus();
		}
	}

	public void XoaBo_CheTac_KyThuat(byte[] packetData, int packetSize)
	{
		if (FLD_LoaiSanXuat == 0)
		{
			DeleteCraftingSkillTips(-9);
			return;
		}
		if (Player_Money < 30000000)
		{
			DeleteCraftingSkillTips(-8);
			return;
		}
		Player_Money -= 30000000L;
		FLD_LoaiSanXuat = 0;
		FLD_TrinhDoSanXuat = 0;
		var array = Converter.HexStringToByte("AA550E001400431708000100000080C3C90155AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		CalculateCharacterProductionLevel();
		UpdateKinhNghiemVaTraiNghiem();
		UpdateProductionSystem();
		UpdateMoneyAndWeight();
	}

	public void DeleteCraftingSkillTips(int id)
	{
		var array = Converter.HexStringToByte("AA550E00140043170800F7FFFFFF0000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 4);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void MakeSystemActions(byte[] packetData, int packetSize)
	{
		var array = new byte[4];
		Buffer.BlockCopy(packetData, 10, array, 0, 2);
		var array2 = Converter.HexStringToByte("AA551200FA0341170400FA030200000000000000000055AA");
		Buffer.BlockCopy(array, 0, array2, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
		Client?.SendMultiplePackage(array2, array2.Length);
		Send_Nhieu_Packet_PhamVi_HienTai(array2, array2.Length);
	}

	public void ProductionDecompositionInspection(byte[] data, int length)
	{
	}

	public void MakeADecompositionSystem(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		var array = new byte[4];
		var array2 = new byte[4];
		Buffer.BlockCopy(packetData, 18, array, 0, 4);
		Buffer.BlockCopy(packetData, 26, array2, 0, 4);
		if (Item_In_Bag[BitConverter.ToInt32(array2, 0)].VatPham_KhoaLai)
		{
			PhanGiaiVatPham_NhacNho(-1);
			return;
		}
		if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array2, 0)].VatPham_ID, 0) != BitConverter.ToInt32(array, 0))
		{
			PhanGiaiVatPham_NhacNho(-1);
			return;
		}
		var parcelVacancy = GetParcelVacancy(this);
		if (parcelVacancy == -1)
		{
			PhanGiaiVatPham_NhacNho(-1);
			return;
		}
		switch (FLD_LoaiSanXuat)
		{
			case 1:
				{
					if (World.ItemList.TryGetValue(BitConverter.ToInt32(array, 0), out var value3) && value3.FLD_RESIDE2 == 4)
					{
						switch (value3.FLD_JOB_LEVEL)
						{
							case 2:
								{
									var value7 = RNG.Next(7, 9);
									SubtractItem(BitConverter.ToInt32(array2, 0), 1);
									IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000721), parcelVacancy, BitConverter.GetBytes(value7), new byte[56]);
									break;
								}
							case 3:
								{
									var value6 = RNG.Next(19, 21);
									SubtractItem(BitConverter.ToInt32(array2, 0), 1);
									IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000722), parcelVacancy, BitConverter.GetBytes(value6), new byte[56]);
									break;
								}
							case 4:
								{
									var value5 = RNG.Next(7, 11);
									SubtractItem(BitConverter.ToInt32(array2, 0), 1);
									IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000723), parcelVacancy, BitConverter.GetBytes(value5), new byte[56]);
									break;
								}
							case 5:
								{
									var value4 = RNG.Next(7, 11);
									SubtractItem(BitConverter.ToInt32(array2, 0), 1);
									IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000724), parcelVacancy, BitConverter.GetBytes(value4), new byte[56]);
									break;
								}
						}
					}
					break;
				}
			case 2:
				{
					var value = RNG.Next(7, 9);
					if (World.ItemList.TryGetValue(BitConverter.ToInt32(array, 0), out var value2))
					{
						if (value2.FLD_LEVEL >= 100)
						{
							SubtractItem(BitConverter.ToInt32(array2, 0), 1);
							IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000736), parcelVacancy, BitConverter.GetBytes(value), new byte[56]);
						}
						else if (value2.FLD_LEVEL >= 80 && value2.FLD_LEVEL < 99)
						{
							SubtractItem(BitConverter.ToInt32(array2, 0), 1);
							IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000735), parcelVacancy, BitConverter.GetBytes(value), new byte[56]);
						}
						else if (value2.FLD_LEVEL >= 60 && value2.FLD_LEVEL < 79)
						{
							SubtractItem(BitConverter.ToInt32(array2, 0), 1);
							IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000734), parcelVacancy, BitConverter.GetBytes(value), new byte[56]);
						}
						else if (value2.FLD_LEVEL >= 40 && value2.FLD_LEVEL < 59)
						{
							SubtractItem(BitConverter.ToInt32(array2, 0), 1);
							IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000733), parcelVacancy, BitConverter.GetBytes(value), new byte[56]);
						}
					}
					break;
				}
			case 3:
				switch (BitConverter.ToInt32(array, 0))
				{
					case ItemDef.Item.HanNgocThach:
						SubtractItem(BitConverter.ToInt32(array2, 0), 1);
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000746), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
						break;
					case ItemDef.Item.KimCuongThach:
						SubtractItem(BitConverter.ToInt32(array2, 0), 1);
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000745), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
						break;
					case ItemDef.Item.ThuocTinhThach:
						SubtractItem(BitConverter.ToInt32(array2, 0), 1);
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000749), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
						break;
					case ItemDef.Item.HanNgocThachCaoCap:
						SubtractItem(BitConverter.ToInt32(array2, 0), 1);
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000748), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
						break;
					case ItemDef.Item.KimCuongThachCaoCap:
						SubtractItem(BitConverter.ToInt32(array2, 0), 1);
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000747), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
						break;
				}
				break;
		}
		var array3 = Converter.HexStringToByte("AA55120054003117040001000000000000000000477B55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
		Client?.SendMultiplePackage(array3, array3.Length);
	}

	public void ProductionSystemCheck(byte[] packetData, int length)
	{
		PacketModification(packetData, length);
		var key = BitConverter.ToInt32(packetData, 10);
		X_Che_Tac_Vat_Pham_Loai value;
		if (GetParcelVacancy(this) == -1)
		{
			PhanGiaiVatPham_NhacNho(-1);
		}
		else if (World.CheTacVatPhamDanhSach.TryGetValue(key, out value))
		{
			if (FLD_LoaiSanXuat == value.CheTaoLoaiHinh && FLD_LevelSanXuat >= value.CheTaoDangCap)
			{
				if (value.CanVatPham.Count > 0)
				{
					foreach (var item in value.CanVatPham)
					{
						var packagedItems = FindItemByItemID(item.Id);
						if (packagedItems == null || BitConverter.ToInt32(packagedItems.VatPhamSoLuong, 0) < item.number)
						{
							PhanGiaiVatPham_NhacNho(-3);
							break;
						}
					}
					return;
				}
				PhanGiaiVatPham_NhacNho(-3);
				LogHelper.WriteLine(LogLevel.Error, "Che Tac Vat Pham Danh Sach Sai lầm: " + value.VatPhamTen);
			}
			else
			{
				PhanGiaiVatPham_NhacNho(-1);
			}
		}
		else
		{
			PhanGiaiVatPham_NhacNho(-1);
		}
	}

	public void CraftingItemsNew(X_Che_Tac_Vat_Pham_Loai vatPham, int vacancy)
	{
		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		var array = new byte[56];
		var num = new Random(DateTime.Now.Millisecond).Next(0, 1501 - FLD_TrinhDoSanXuat);
		var value = 0;
		if (num < 10)
		{
			value = 2;
		}
		else if (num < 45)
		{
			value = 1;
		}
		Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 52, 2);
		IncreaseItem2(bytes, BitConverter.GetBytes(vatPham.VatPham_ID), vacancy, BitConverter.GetBytes(1), array);
		LogHelper.WriteLine(LogLevel.Debug, "CheTaoVatPham: [" + AccountID + "]  [" + CharacterName + "] VatPhamTen[" + vatPham.VatPhamTen + "] Global_ID=[" + BitConverter.ToInt32(bytes, 0) + "]        LV[" + value + "]");
	}

	public void ProductionSystemProduction(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		var key = BitConverter.ToInt32(packetData, 10);
		var parcelVacancy = GetParcelVacancy(this);
		if (parcelVacancy == -1)
		{
			PhanGiaiVatPham_NhacNho(-1);
			return;
		}
		if (World.CheTacVatPhamDanhSach.TryGetValue(key, out var value))
		{
			if (FLD_LoaiSanXuat == value.CheTaoLoaiHinh && FLD_LevelSanXuat >= value.CheTaoDangCap)
			{
				if (value.CanVatPham.Count <= 0)
				{
					PhanGiaiVatPham_NhacNho(-3);
					LogHelper.WriteLine(LogLevel.Error, "Che Tac Vat Pham Danh Sach Sai lầm: " + value.VatPhamTen);
					return;
				}
				foreach (var item in value.CanVatPham)
				{
					var packagedItems = FindItemByItemID(item.Id);
					if (packagedItems == null || BitConverter.ToInt32(packagedItems.VatPhamSoLuong, 0) < item.number)
					{
						PhanGiaiVatPham_NhacNho(-3);
						return;
					}
				}
				foreach (var item2 in value.CanVatPham)
				{
					var packagedItems2 = FindItemByItemID(item2.Id);
					if (packagedItems2 != null)
					{
						SubtractItem(packagedItems2.VatPhamViTri, item2.number);
						break;
					}
				}
				switch (FLD_LoaiSanXuat)
				{
					case 1:
						{
							if (!World.ItemList.TryGetValue(value.VatPham_ID, out var value2))
							{
								break;
							}
							if (value2.FLD_RESIDE2 != 0)
							{
								CraftingItemsNew(value, parcelVacancy);
								if (FLD_LevelSanXuat <= value.CheTaoDangCap)
								{
									FLD_TrinhDoSanXuat++;
									UpdateKinhNghiemVaTraiNghiem();
									CalculateCharacterProductionLevel();
								}
							}
							else
							{
								IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value.VatPham_ID), parcelVacancy, BitConverter.GetBytes(value.VatPhamSoLuong), new byte[56]);
							}
							break;
						}
					case 2:
						{
							if (!World.ItemList.TryGetValue(value.VatPham_ID, out var value3))
							{
								break;
							}
							if (value3.FLD_RESIDE2 != 0)
							{
								CraftingItemsNew(value, parcelVacancy);
								if (FLD_LevelSanXuat <= value.CheTaoDangCap)
								{
									FLD_TrinhDoSanXuat++;
									UpdateKinhNghiemVaTraiNghiem();
									CalculateCharacterProductionLevel();
								}
							}
							else
							{
								IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value.VatPham_ID), parcelVacancy, BitConverter.GetBytes(value.VatPhamSoLuong), new byte[56]);
							}
							break;
						}
					case 3:
						IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value.VatPham_ID), parcelVacancy, BitConverter.GetBytes(value.VatPhamSoLuong), new byte[56]);
						if (FLD_LevelSanXuat <= value.CheTaoDangCap)
						{
							FLD_TrinhDoSanXuat++;
							UpdateKinhNghiemVaTraiNghiem();
							CalculateCharacterProductionLevel();
						}
						break;
				}
			}
			else
			{
				PhanGiaiVatPham_NhacNho(-2);
			}
		}
		else
		{
			PhanGiaiVatPham_NhacNho(-2);
		}
		var array = Converter.HexStringToByte("AA55120084023617040001000000000000000000D80855AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}
	
	public void LearnProductionSkills(byte[] packetData, int packetSize)
	{
		if (FLD_LoaiSanXuat == 0)
		{
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 10, array, 0, 2);
			FLD_LoaiSanXuat = BitConverter.ToInt32(array, 0);
			FLD_TrinhDoSanXuat = 0;
			var array2 = Converter.HexStringToByte("AA556600BF013917580002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000784A55AA");
			Buffer.BlockCopy(array, 0, array2, 11, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			Client?.SendMultiplePackage(array2, array2.Length);
			CalculateCharacterProductionLevel();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateProductionSystem();
		}
	}

	public void HeThong_DuocPham(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		var key = BitConverter.ToInt32(packetData, 14);
		var parcelVacancy = GetParcelVacancy(this);
		var num = BitConverter.ToInt32(packetData, 22);
		Random random = new();
		if (!World.CheDuocVatPhamDanhSach.TryGetValue(key, out var value))
		{
			return;
		}
		foreach (var item in value.CanVatPham)
		{
			for (var i = 0; i < Item_In_Bag.Length; i++)
			{
				if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == item.Id && BitConverter.ToInt32(Item_In_Bag[i].VatPhamSoLuong, 0) >= item.number)
				{
					SubtractItem(i, item.number * num);
					break;
				}
			}
		}
		for (var j = 0; j < num; j++)
		{
			if (random.Next(0, 110) <= 80.0 + DAIPHU_ThanNongTienThuat)
			{
				IncreaseItem4(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value.VatPham_ID), parcelVacancy, BitConverter.GetBytes(j), new byte[56]);
				var array = Converter.HexStringToByte("AA55160088041B1710000B00010045CD9A3B000000000100000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(parcelVacancy), 0, array, 10, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value.VatPham_ID), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(j), 0, array, 22, 2);
				Client?.Send_Map_Data(array, array.Length);
			}
			else
			{
				HeThongNhacNho("Chế dược thất bại, Đại Phu Sinh có thể nâng cao tỷ lệ thành công!");
			}
		}
	}

	public void HaiThuoc_BanDo_NamLam(byte[] packetData, int packetSize)
	{
		if (World.Event_HaiThuoc_Progress != 0)
		{
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			if (!MapClass.GetnpcTemplate(MapID).TryGetValue(BitConverter.ToInt32(array, 0), out var value))
			{
				return;
			}
			value.NPCDeath = false;
			value.GuiDiHaiThuocSoLieu();
			value.GuiDiTuVongSoLieuWrapper(SessionID);
			var parcelVacancy = GetParcelVacancy(this);
			var goldNhanHopQuaHkgh = World.Gold_Nhan_Hop_Qua_HKGH;
			var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
			var playerMoney = Player_Money;
			switch (value.FLD_PID)
			{
				case 15281:
					{
						var num3 = RNG.Next(1, 100);
						if (num3 < 20)
						{
							PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
						}
						else if (num3 < 35)
						{
							PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
						}
						else if (num3 < 45)
						{
							PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
						}
						else if (num3 < 50)
						{
							PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
						}
						else if (Player_Money >= goldNhanHopQuaHkgh)
						{
							PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(*********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
							Player_Money -= goldNhanHopQuaHkgh;
							UpdateMoneyAndWeight();
							HeThongNhacNho("Bạn bị trừ [" + goldNhanHopQuaHkgh + "] lượng.");
							var userName3 = "[" + AccountID + "][" + CharacterName + "] Gold_old: [" + playerMoney + "] Gold_new: [" + Player_Money + "]";
							LogHelper.WriteLine(LogLevel.Error, userName3);
						}
						else
						{
							HeThongNhacNho("Bạn không đủ [" + goldNhanHopQuaHkgh + "] lượng, không thể nhận   vật phẩm này !!");
						}
						break;
					}
				case 15282:
					{
						var num2 = RNG.Next(1, 100);
						switch (num2)
						{
							case < 20:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							case < 35:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							case < 45:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							case < 50:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							default:
								{
									if (Player_Money >= goldNhanHopQuaHkgh)
									{
										PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(*********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
										Player_Money -= goldNhanHopQuaHkgh;
										UpdateMoneyAndWeight();
										HeThongNhacNho("Bạn bị trừ [" + goldNhanHopQuaHkgh + "] lượng.");
										var userName2 = "[" + AccountID + "][" + CharacterName + "] Gold_old: [" + playerMoney + "] Gold_new: [" + Player_Money + "] Phi:[" + goldNhanHopQuaHkgh + "]";
										LogHelper.WriteLine(LogLevel.Error, userName2);
									}
									else
									{
										HeThongNhacNho("Bạn không đủ [" + goldNhanHopQuaHkgh + "] lượng, không thể nhận   vật phẩm này !!");
									}

									break;
								}
						}
						break;
					}
				case 15283:
					{
						var num = RNG.Next(1, 100);
						switch (num)
						{
							case < 20:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							case >= 20 and < 35:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							case >= 35 and < 45:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							case >= 45 and < 50:
								PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(**********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
								break;
							default:
								{
									if (Player_Money >= goldNhanHopQuaHkgh)
									{
										PickedItems(parcelVacancy, BitConverter.GetBytes(1), bytes, BitConverter.GetBytes(*********), Item_In_Bag[parcelVacancy].VatPham_ThuocTinh);
										Player_Money -= goldNhanHopQuaHkgh;
										UpdateMoneyAndWeight();
										HeThongNhacNho("Bạn bị trừ [" + goldNhanHopQuaHkgh + "] lượng.");
										var userName = "[" + AccountID + "][" + CharacterName + "] Gold_old: [" + playerMoney + "] Gold_new: [" + Player_Money + "]";
										LogHelper.WriteLine(LogLevel.Error, userName);
									}
									else
									{
										HeThongNhacNho("Bạn không đủ [" + goldNhanHopQuaHkgh + "] lượng, không thể nhận   vật phẩm này !!");
									}

									break;
								}
						}
						break;
					}
			}
		}
		else
		{
			HeThongNhacNho("Sự kiện Hái Thuốc Huyền Bột đã khép lại, không thể thu thập bảo vật nữa!!", 20, "Thiên cơ các");
		}
	}

	public void PickedItems(int position, byte[] soLuong, byte[] itemGlobalId, byte[] vatPhamId, byte[] vatPhamThuocTinh)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA55720094020D006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
				var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
				if (BitConverter.ToInt32(vatPhamId, 0) != 1008000044 && BitConverter.ToInt32(vatPhamId, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						position = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					itemGlobalId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					soLuong = BitConverter.GetBytes(BitConverter.ToInt32(soLuong, 0));
				}
			}
			else
			{
				soLuong = BitConverter.GetBytes(1);
			}
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(soLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, vatPhamThuocTinh.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(position), 0, array, 40, 2);
			Buffer.BlockCopy(array2, 0, array, 14, 12);
			Buffer.BlockCopy(array2, 12, array, 30, 4);
			Buffer.BlockCopy(array2, 16, array, 43, vatPhamThuocTinh.Length);
			Item_In_Bag[position].VatPham_byte = array2;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Increase Item error 2: [" + AccountID + "][" + CharacterName + "]  Position[" + position + "]  SoLuong[" + BitConverter.ToInt32(soLuong, 0) + "]" + ex.Message);
		}
	}
	

	public void CleanUpTheBackpack()
	{
		try
		{
			for (var i = 0; i < 36; i++)
			{
				if (!World.ItemList.TryGetValue((int)Item_In_Bag[i].GetVatPham_ID, out var value))
				{
					continue;
				}
				switch (value.FLD_RESIDE2)
				{
					case 1:
					case 2:
					case 4:
					case 5:
					case 6:
					case 7:
					case 8:
					case 10:
						if (Item_In_Bag[i].FLD_CuongHoaSoLuong > 0)
						{
							continue;
						}
						break;
				}
				if (!Item_In_Bag[i].VatPham_KhoaLai)
				{
					Item_In_Bag[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
			Init_Item_In_Bag();
			HeThongNhacNho("Xóa toàn bộ hành trang thành công, giang hồ nhẹ gánh!");
		}
		catch
		{
		}
	}
	
	public void ManufacturedItems(int position, byte[] vatPhamId, int amount = 1)
	{
		try
		{
			var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
			var array = new byte[56];
			new ItmeClass();
			var itmeId = ItmeClass.GetItmeID(BitConverter.ToInt32(vatPhamId, 0));
			switch (BitConverter.ToInt32(vatPhamId, 0))
			{
				case ItemDef.Item.HanNgocThach:
					{
						var fLdMagic17 = new Random().Next(200004, 200006);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.HanNgocThach);
						itmeId.FLD_MAGIC0 = fLdMagic17;
						break;
					}
				case ItemDef.Item.KimCuongThachCaoCap:
					{
						var fLdMagic19 = new Random().Next(700028, 700031);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThachCaoCap);
						itmeId.FLD_MAGIC0 = fLdMagic19;
						break;
					}
				case ItemDef.Item.KimCuongThach_DaKich:
					{
						var fLdMagic18 = new Random().Next(1000020, 1000020);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThach);
						itmeId.FLD_MAGIC0 = fLdMagic18;
						break;
					}
				case ItemDef.Item.KimCuongThach_VoCong:
					{
						var fLdMagic15 = new Random().Next(700023, 700025);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThach);
						itmeId.FLD_MAGIC0 = fLdMagic15;
						break;
					}
				case ItemDef.Item.ThuocTinhThachRandom:
					{
						var s = "200" + new Random().Next(0, 7) + "000";
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.ThuocTinhThach);
						itmeId.FLD_MAGIC0 = int.Parse(s);
						break;
					}
				case 800000030:
					{
						var fLdMagic12 = new Random().Next(700001, 700001);
						vatPhamId = BitConverter.GetBytes(800000030);
						itmeId.FLD_MAGIC0 = fLdMagic12;
						break;
					}
				case 800000034:
					{
						var fLdMagic11 = new Random().Next(700002, 700002);
						vatPhamId = BitConverter.GetBytes(800000034);
						itmeId.FLD_MAGIC0 = fLdMagic11;
						break;
					}
				case ItemDef.Item.KimCuongThachRandom:
					{
						var fLdMagic8 = new Random().Next(100013, 100015);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThach);
						itmeId.FLD_MAGIC0 = fLdMagic8;
						break;
					}
				case ItemDef.Item.HanNgocThachRandom:
					{
						var fLdMagic7 = new Random().Next(200006, 200008);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.HanNgocThach);
						itmeId.FLD_MAGIC0 = fLdMagic7;
						break;
					}
				case ItemDef.Item.NhietHuyetThach:
					{
						new Random();
						var fLdMagic4 = new Random().Next(800001, 800001);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.NhietHuyetThach);
						itmeId.FLD_MAGIC0 = fLdMagic4;
						break;
					}
				case ItemDef.Item.KimCuongThach:
					{
						var fLdMagic3 = new Random().Next(1000020, 1000020);
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThach);
						itmeId.FLD_MAGIC0 = fLdMagic3;
						break;
					}
				case ItemDef.Item.HanNgocThachSieuCap:
					{
						new Random();
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.HanNgocThachSieuCap);
						var fLdMagic16 = new Random().Next(200010, 200015);
						var fLdMagic17 = new Random().Next(1100084, 1100100);
						HuanRandom = !HuanRandom;
						if (HuanRandom)
						{
							itmeId.FLD_MAGIC0 = fLdMagic16;
						}
						else
						{
							itmeId.FLD_MAGIC0 = fLdMagic17;
						}
						break;
					}
				case ItemDef.Item.HanNgocThachHonNguyen:
					{
						new Random();
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.HanNgocThachHonNguyen);
						var fLdMagic13 = new Random().Next(200016, 200020);
						var fLdMagic14 = new Random().Next(1100101, 1100130);
						HuanRandom = !HuanRandom;
						if (HuanRandom)
						{
							itmeId.FLD_MAGIC0 = fLdMagic13;
						}
						else
						{
							itmeId.FLD_MAGIC0 = fLdMagic14;
						}
						break;
					}
				case ItemDef.Item.KimCuongThachSieuCap:
					{
						new Random();
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThachSieuCap);
						var fLdMagic9 = new Random().Next(700030, 700035);
						var fLdMagic10 = new Random().Next(800002, 800002);
						HuanRandom = !HuanRandom;
						if (HuanRandom)
						{
							itmeId.FLD_MAGIC0 = fLdMagic9;
						}
						else
						{
							itmeId.FLD_MAGIC0 = fLdMagic10;
						}
						break;
					}
				case ItemDef.Item.KimCuongThachHonNguyen:
					{
						new Random();
						vatPhamId = BitConverter.GetBytes(ItemDef.Item.KimCuongThachHonNguyen);
						var fLdMagic5 = new Random().Next(700036, 700040);
						var fLdMagic6 = new Random().Next(800002, 800002);
						HuanRandom = !HuanRandom;
						if (HuanRandom)
						{
							itmeId.FLD_MAGIC0 = fLdMagic5;
						}
						else
						{
							itmeId.FLD_MAGIC0 = fLdMagic6;
						}
						break;
					}
				case 1000001620:
					{
						new Random();
						vatPhamId = BitConverter.GetBytes(1000001620);
						var fLdMagic = new Random().Next(700040, 700050);
						var fLdMagic2 = new Random().Next(800002, 800003);
						HuanRandom = !HuanRandom;
						if (HuanRandom)
						{
							itmeId.FLD_MAGIC0 = fLdMagic;
						}
						else
						{
							itmeId.FLD_MAGIC0 = fLdMagic2;
						}
						break;
					}
			}
			Buffer.BlockCopy(BitConverter.GetBytes(itmeId.FLD_MAGIC0), 0, array, 0, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(itmeId.FLD_MAGIC1), 0, array, 4, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(itmeId.FLD_MAGIC2), 0, array, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(itmeId.FLD_MAGIC3), 0, array, 12, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(itmeId.FLD_MAGIC4), 0, array, 16, 4);
			AddItems(bytes, vatPhamId, position, BitConverter.GetBytes(amount), array);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi add ngọc tại đây !!" + ex.Message);
		}
	}

}
