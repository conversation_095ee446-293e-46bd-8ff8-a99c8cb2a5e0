﻿

using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using RxjhServer.GroupQuest;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public string GetMacByIp(string ipAddress)
	{
		var source = from n in NetworkInterface.GetAllNetworkInterfaces()
					 where n.OperationalStatus == OperationalStatus.Up && n.NetworkInterfaceType != NetworkInterfaceType.Loopback
					 select n into _
					 select new
					 {
						 PhysicalAddress = _.GetPhysicalAddress(),
						 IPProperties = _.GetIPProperties()
					 };
		var anon = source.Where(q => q.IPProperties.UnicastAddresses.Any((UnicastIPAddressInformation ua) => ua.Address.ToString() == ipAddress)).FirstOrDefault();
		if (anon != null)
		{
			var physicalAddress = anon.PhysicalAddress;
			var value = (from b in physicalAddress.GetAddressBytes()
						 select b.ToString("X2")).ToArray();
			return string.Join("-", value);
		}
		return "Local IP";
	}
		public void Phat_Hien_Nhip_Tim(byte[] packetData, int packetSize)
	{
		try
		{
			PhaiChangKiemTraNhipTim = true;
			if (World.CongTac_PhatHienNhipTim != 1)
			{
				return;
			}
			if ((int)DateTime.Now.Subtract(XTtime).TotalMilliseconds < World.NguongThoiGian_PhatHienNhipTim)
			{
				Times++;
				if (Times >= World.NguongSo_NhipTim && Client?.ToString() != "127.0.0.1")
				{
					HeThongNhacNho("Đại hiệp có tốc độ xuất chiêu bất thường, giang hồ nghi hoặc!!", 7, "Thiên cơ các");
					HeThongNhacNho("Đại hiệp có tốc độ xuất chiêu bất thường, cần kiểm tra lại võ công!!", 20, "Thiên cơ các");
					HeThongNhacNho("Đại hiệp có tốc độ xuất chiêu bất thường, giang hồ nghi hoặc!!", 7, "Thiên cơ các");
					HeThongNhacNho("Đại hiệp có tốc độ xuất chiêu bất thường, cần kiểm tra lại võ công!!", 20, "Thiên cơ các");
					var txt = "[" + AccountID + "]-[" + CharacterName + "]-Speed:[" + DateTime.Now.Subtract(XTtime).TotalMilliseconds + "/" + World.NguongThoiGian_PhatHienNhipTim + "]";
					LogHelper.WriteLine(LogLevel.Error, txt);
					Client.Dispose();
				}
			}
			XTtime = DateTime.Now;
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Thiên cơ các bị lỗi không thể xử lý");
		}
	}

	
	public void XacMinhThongTinDangNhapID(byte[] PacketData, int PacketSize)
	{
		//byte[] array = Converter.HexStringToByte("AA552800C70345031A00010001007761746572696E676100000000000000000000000000000000000000000055AA");
		byte[] array =
			Converter.HexStringToByte(
				"aa5********0a90320003f0000000000000000000000000000000000000000000000000000000000000055aa");
		//System.Buffer.BlockCopy(PacketData, 14, array, 14, 12);
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, array, 4, 2); //937
		base.Client?.Send(array, array.Length);

		array = Converter.HexStringToByte("aa550000ee05bc040c003f000000000000000000000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, array, 4, 2); //1212
		base.Client?.Send(array, array.Length);
		VerifyCumulativeRewardAsync();
	}
	
	public async void KetNoi_DangNhap(byte[] packetData, int packetSize)
	{
		try
		{

			PacketReader packetReader = new(packetData, packetSize, bool_0: false);
			packetReader.Seek(10, SeekOrigin.Begin);
			var text = packetReader.ReadString(29).Trim();
			if (text.Length > 16)
			{
				if (Client == null) return;
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 15]");
				return;
			}
			// Kiểm tra Client không null trước khi sử dụng
			if (Client == null)
			{
				LogHelper.WriteLine(LogLevel.Error, $"KetNoi_DangNhap: Client is null for account {text}, SessionID: {SessionID}");
				return;
			}
			packetReader.Seek(74, SeekOrigin.Begin);
			int clientVer = packetReader.ReadInt16();
			packetReader.Seek(78, SeekOrigin.Begin);
			LanIp = packetReader.ReadString(28).Replace("\0", string.Empty).Trim();
			packetReader.Seek(94, SeekOrigin.Begin);
			MacAddress = packetReader.ReadString(16).Trim();
			AccountID = text;
			LoginTime = DateTime.Now;
			var clientString = Client.ToString();
			var clientPort = Client.GetPort();
			LogHelper.WriteLine(LogLevel.Info, $"Acc: {AccountID}, clientVer: {clientVer} lanip: {LanIp} mac: {MacAddress}");
			//World.conn.Transmit("USER_LOGIN_X|" + text + "|" + Client.ToString() + "|" + World.ServerID + "|" + SessionID + "|" + BindAccount + "|0|NULL");

			var res = await World.Instance.loginServerClient.ValidateAccountLoginAsync(
				text,
				"",
				clientString,
				clientPort,
				LanIp,
				clientPort
			);
			LogHelper.WriteLine(LogLevel.Debug, "ValidateAccountLoginResponse: " + res.ToString());
			if (res.IsValid)
			{
				await KetNoi_DangNhap2(text, "NOT_ONLINE", res.LoginIp, res.LoginPort, "", "", "", "");
			}
			else
			{
				Client?.Dispose();
				World.allConnectedChars.TryRemove(SessionID, out _);
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 18]");
			}
		}
		catch (Exception ex)
		{
			var clientInfo = Client?.ToString() ?? "NULL";
			LogHelper.WriteLine(LogLevel.Error, " KetNoi_DangNhap Error[" + SessionID + "]-[" + clientInfo + "]" + ex.Message);
		}
	}


	public bool DetectionLimit(string ipaddr)
	{
		try
		{
			var num = 0;
			if (FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (value.Client.ToString() == ipaddr && !value.Client.TreoMay && value.IsJoinWorld)
					{
						num++;
					}
				}
				if (num > World.ChoPhep_MoSoLuong_NhieuHon && ipaddr != "127.0.0.1")
				{
					LogHelper.WriteLine(LogLevel.Error, "Đạt tới giới hạn login");
					return true;
				}
			}
			else
			{
				AccountDb.UpdateRemoveUserLogin(AccountID);
			}
		}
		catch
		{
			return false;
		}
		return false;
	}

	public byte[] SetMsg(string msg)
	{
		var array = Converter.HexStringToByte("****************");
		var bytes = Encoding.Default.GetBytes(msg);
		var array2 = new byte[array.Length + bytes.Length + 1];
		Buffer.BlockCopy(array, 0, array2, 0, array.Length);
		Buffer.BlockCopy(bytes, 0, array2, 8, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(bytes.Length + 1), 0, array2, 2, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(bytes.Length), 0, array2, 6, 2);
		return array2;
	}

	public async Task KetNoi_DangNhap2(string id, string aa, string originalServerIp, string originalPort, string coinIp, string silverCoinPort, string originalServerSerialNumber, string originalServerId)
	{
		var num = 0;
		try
		{
			// Kiểm tra Client không null trước khi sử dụng
			if (Client == null)
			{
				LogHelper.WriteLine(LogLevel.Error, $"KetNoi_DangNhap2: Client is null for account {id}, SessionID: {SessionID}");
				return;
			}

			if (FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han && World.AccountList.Count > World.MaximumOnline)
			{
				var clientInfo = Client?.ToString() ?? "NULL";
				LogHelper.WriteLine(LogLevel.Error, "Vào kênh Full ---------- [" + AccountID + "][" + clientInfo + "] ----------");
				Client?.Dispose();
				return;
			}
			Client.Online = true;
			if (aa != null && !(aa == "Online"))
			{
				num = 1;
				Client.Online = true;
				Client.Login = true;

				var accountData = await AccountDb.FindAccount(id);
				if (accountData == null)
				{
					LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy tài khoản: {id}");
					Client?.Dispose();
					return;
				}

				Password = accountData.fld_password.ToString() ?? "";
				FLD_RXPIONT = Convert.ToInt32(accountData.fld_rxpiont ?? 0);
				FLD_RXPIONTX = Convert.ToInt32(accountData.fld_rxpiontx ?? 0);
				FLD_Coin = Convert.ToInt32(accountData.fld_coin ?? 0);
				FLD_VIP = Convert.ToInt32(accountData.fld_vip ?? 0);
				FLD_VIPTIM = DateTime.Parse(accountData.fld_viptim?.ToString() ?? DateTime.Now.ToString());
				Player_Sex = Convert.ToInt32(accountData.fld_sex ?? 0);
				lastloginip = accountData.fld_lastloginip?.ToString() ?? "";
				Kich_Hoat_Tai_Khoan = accountData.fld_machineid?.ToString() ?? "";

				if (World.CoHayKo_KichHoat_TaiKhoan_VaoDuocGame == 1 && Kich_Hoat_Tai_Khoan != string.Empty && Client != null)
				{
					var userid = AccountID;
					LogHelper.WriteLine(LogLevel.Error, "ACCOUNT: [" + userid + "] CHƯA ĐƯỢC KÍCH HOẠT, HÃY ĐĂNG KÝ TẠI TRANG CHỦ TRÒ CHƠI !!!");
					// logo.Log_Kich_Hoat_Tai_Khoan(userid);
					Client.Dispose();
				}
				GameSecurityCode = accountData.fld_safeword?.ToString() ?? "********";
				if (World.VIPLine == 1)
				{
					return;
				}
				OriginalServerSerialNumber = World.ServerID;
				OriginalServerIP = originalServerIp;
				OriginalServerPort = 0;
				OriginalServerID = World.ServerID;
				SilverCoinSquareServerIP = coinIp;
				SilverCoinSquareServerPort = int.TryParse(silverCoinPort, out int port) ? port : 0;
				num = 3;
				StringBuilder stringBuilder = new();
				stringBuilder.Append("aa550000c004020038000000000001000000000000000100000000000000010000000000f8916d88ffffffff78bb898600000000e80300000000000000000a5d6d0355aa");
				//var array = Converter.HexStringToByte("aa550000d5040200400081decb9e771da471e478ecba9cf0984624134ef26e5a157de4fb564a5fbe6bcd4d778724640e1303f29e5e3ae363417741d842757c95e42e24298b8b6363bfa655aa");
				var array = Converter.HexStringToByte(stringBuilder.ToString());
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(Player_Sex), 0, array, 14, 2);
				//Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(World.ServerID - 1), 0, array, 26, 4);
				num = 5;
				Client?.Send_Map_Data(array, array.Length);
				var clientInfo = Client?.ToString() ?? "NULL";
				// LogHelper.WriteLine(LogLevel.Info, "Máy chủ ["+World.ServerID+"] Chọn kênh [" + SessionID + "] - [" + id.ToString() + "]] - [" + clientInfo + "]");
				// if (DetectionLimit(Client.ToString()) && FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
				// {
				// 	foreach (var value2 in World.allConnectedChars.Values)
				// 	{
				// 		if (value2.Client.ToString() == Client.ToString())
				// 		{
				// 			if (!value2.Client.TreoMay)
				// 			{
				// 				value2.HeThongNhacNho("Đại hiệp đã vượt quá số lượng tài khoản cho phép. Thiên Cơ Các tự ngắt một tài khoản ngẫu nhiên!", 23, "Thiên cơ các");
				// 				value2.Client.Dispose();
				// 			}
				// 			break;
				// 		}
				// 	}
				// }
				return;
			}
			if (Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 22]");
			}
		}
		catch (Exception ex)
		{
			if (Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 23]");
			}
			LogHelper.WriteLine(LogLevel.Error, "Đăng nhập kênh Admin：[" + AccountID + "][" + CharacterName + "] - Lỗi: [" + num + "] - " + ex.ToString());
		}
	}

	public async Task ChangeLineAccountLogin(string id, int serverId, int soKyTu, int worldId, string bindAccount, string originalServerIp, string originalServerPort, string silverSquareIp, string silverSquarePort, string originalServerSerialNumber, string originalServerId, string newServerId, string sealPackageLogin)
	{
		var text = "";
		try
		{
			Client.BindAccount = false;
			Client.VersionVerification = true;
			if (World.ChoPhep_TreoMay == 1)
			{
				Client.TreoMay = false;
			}
			Client.Online = true;
			Client.Login = true;
			//var dataTable = DBA.GetDBToDataTable($"select  FLD_PASSWORD,FLD_SEX,FLD_RXPIONT,FLD_RXPIONTX,FLD_VIP,FLD_VIPTIM,FLD_COIN,FLD_SAFEWORD,FLD_ZT,FLD_LASTLOGINIP  from  [TBL_ACCOUNT]  where  FLD_ID=@Userid", new[] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, id) }, "rxjhaccount");
			var dataTable = await AccountDb.FindAccount(id);
			if (dataTable == null && Client != null)
			{
				kickidlog("Change line account Login      error      table1==null");
				Client.Dispose();
				return;
			}
			if (sealPackageLogin != "1")
			{
				if (Client == null)
				{
					return;
				}
				OpClient(1);
				Client.Dispose();
			}
			text = "1";
			if ((int)dataTable.fld_zt > 0)
			{
				dataTable = null;
				if (Client != null)
				{
					kickidlog("Change line account Login      Account has been suspended");
					OpClient(1);
					Client.Dispose();
					return;
				}
			}
			text = "2";
			OriginalServerSerialNumber = int.TryParse(originalServerSerialNumber, out var result) ? result : 0;
			text = "3      变量值" + originalServerSerialNumber;
			OriginalServerIP = originalServerIp;
			try
			{
				OriginalServerPort = int.TryParse(originalServerPort, out var result2) ? result2 : World.GameServerPort;
			}
			catch (Exception)
			{
				OriginalServerPort = World.GameServerPort;
			}
			try
			{
				OriginalServerID = int.TryParse(newServerId, out var result3) ? result3 : World.ServerID;
			}
			catch (Exception)
			{
				OriginalServerID = World.ServerID;
			}
			text = "5      变量值" + newServerId;
			SilverCoinSquareServerIP = silverSquareIp;
			try
			{
				SilverCoinSquareServerPort = int.Parse(silverSquarePort);
			}
			catch (Exception)
			{
				SilverCoinSquareServerPort = World.GameServerPort;
			}
			text = "6      变量值" + silverSquarePort;
			AccountID = id;
			Password = dataTable.fld_password.ToString();
			FLD_RXPIONT = (int)dataTable.fld_rxpiont;
			FLD_RXPIONTX = (int)dataTable.fld_rxpiontx;
			FLD_Coin = (int)dataTable.fld_coin;
			FLD_VIP = int.Parse(dataTable.fld_vip.ToString());
			FLD_VIPTIM = DateTime.Parse(dataTable.fld_viptim.ToString());
			Player_Sex = int.Parse(dataTable.fld_sex.ToString());
			lastloginip = dataTable.fld_lastloginip.ToString();
			Kich_Hoat_Tai_Khoan = dataTable.fld_machineid.ToString();
			if (dataTable.fld_safeword.ToString().Length == 0)
			{
				GameSecurityCode = "********";
			}
			else
			{
				GameSecurityCode = dataTable.fld_safeword.ToString();
			}
			if (World.VIPLine == 1)
			{
				return;
			}
			text = "7";
			LogHelper.WriteLine(LogLevel.Info, "Successfully changed line Kết nối đăng nhập: [" + SessionID + "]-[" + id.ToString() + "]");
			text = "8";
			// var dBToDataTable = DBA.GetDBToDataTable($"select  FLD_NAME,FLD_X,FLD_Y,FLD_MENOW  from  [TBL_XWWL_Char]  where  FLD_ID=@Userid  and  FLD_INDEX=@index", new SqlParameter[2]
			// {
			// 	SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, id),
			// 	SqlDBA.MakeInParam("@index", SqlDbType.Int, 0, soKyTu)
			// });
			var dBToDataTable = await GameDb.FindCharacter(id, soKyTu);
			if (dBToDataTable != null)
			{
				if (dBToDataTable == null && Client != null)
				{
					LogHelper.WriteLine(LogLevel.Error, "Change line to get character error3，[" + AccountID + "][" + CharacterName + "]");

					Client.Dispose();
				}
				else
				{
					var text2 = dBToDataTable.fld_name.ToString();
					var num = float.Parse(dBToDataTable.fld_x.ToString());
					var num2 = float.Parse(dBToDataTable.fld_y.ToString());
					var num3 = (int)dBToDataTable.fld_menow;
					new Thread(new ThreadWithState(this, text2, num.ToString(), num2.ToString(), num3).ThreadProc2).Start();
				}
			}
			else if (Client != null)
			{
				kickidlog("Get character error4");
				LogHelper.WriteLine(LogLevel.Error, "Get character error4，[" + AccountID + "][" + CharacterName + "]");
				OpClient(1);
				Client.Dispose();
			}
		}
		catch (Exception ex4)
		{
			if (Client != null)
			{
				kickidlog("Change line account Loginerror");
				Client.Dispose();
			}
			LogHelper.WriteLine(LogLevel.Error, "Change Line Account Login() error Code:" + text + " Information 2:" + ex4);
		}
	}

	public void ChangeLineConfirmation()
	{
		try
		{
			var array = Converter.HexStringToByte("AA551B00AE04D60015000100000000B1040000000050C1000070410000AAC255AA");
			Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 15, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 19, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 23, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 27, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			var clientInfo = Client?.ToString() ?? "NULL";
			LogHelper.WriteLine(LogLevel.Error, "Change Line Verification[" + SessionID + "]-[" + clientInfo + "]" + ex.Message);
		}
	}

	public void ReviewUserLogin()
	{
		try
		{
			StringBuilder stringBuilder = new();
			foreach (var value12 in World.AccountList.Values)
			{
				var value = "NULL";
				var value2 = 0;
				if (value12.TreoMay)
				{
					value2 = 1;
				}
				var value3 = 0;
				var value4 = string.Empty;
				var value5 = string.Empty;
				var value6 = 0;
				var value7 = string.Empty;
				var value8 = string.Empty;
				var value9 = 0;
				var value10 = 0;
				var players = World.FindPlayerBySession((int)value12.PlayerSessionID);
				if (players != null)
				{
					value = players.CharacterName;
					value3 = players.OriginalServerSerialNumber;
					value4 = players.OriginalServerIP;
					value5 = players.OriginalServerPort.ToString();
					value6 = players.OriginalServerID;
					value7 = players.SilverCoinSquareServerIP;
					value8 = players.SilverCoinSquareServerPort.ToString();
					value10 = players.Player_Job;
					if (players.PublicDrugs.TryGetValue(**********, out var _))
					{
						value9 = 1;
					}
				}
				stringBuilder.Append(value12.Player.AccountID);
				stringBuilder.Append("-");
				stringBuilder.Append(value12.ToString());
				stringBuilder.Append("-");
				stringBuilder.Append(value12.BindAccount);
				stringBuilder.Append("-");
				stringBuilder.Append(value2);
				stringBuilder.Append("-");
				stringBuilder.Append(value);
				stringBuilder.Append("-");
				stringBuilder.Append(value3);
				stringBuilder.Append("-");
				stringBuilder.Append(value4);
				stringBuilder.Append("-");
				stringBuilder.Append(value5);
				stringBuilder.Append("-");
				stringBuilder.Append(value6);
				stringBuilder.Append("-");
				stringBuilder.Append(value7);
				stringBuilder.Append("-");
				stringBuilder.Append(value8);
				stringBuilder.Append("-");
				stringBuilder.Append(value12.PlayerSessionID);
				stringBuilder.Append("-");
				stringBuilder.Append(value9);
				stringBuilder.Append("-");
				stringBuilder.Append(value10);
				stringBuilder.Append(",");
			}
			if (stringBuilder.Length > 0)
			{
				stringBuilder.Remove(stringBuilder.Length - 1, 1);
			}
			World.conn.Transmit("REVIEW_USER_LOGIN|" + stringBuilder);
			if (World.AutGC != 0)
			{
				GC.Collect();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Review NguoiChoiDangNhap error" + ex.Message);
		}
	}

	public void ChangeLineCharacterLogin(string uName, string x, string y, int map)
	{
		var num = 0;
		try
		{
			if (IsJoinWorld)
			{
				ReviewUserLogin();
				kickidlog("The loggedin character changes the line Login");
				World.conn.Transmit("KICK_PLAYER_ID|" + World.ServerID + "|" + AccountID);
				if (Client != null)
				{
					OpClient(1);
					Client.Dispose();
				}
				return;
			}
			IsJoinWorld = true;
			num = 1;
			CharacterName = uName;
			if (CharacterName.Length == 0)
			{
				kickidlog("Change Line Character Login      TenNhanVat长度0");
				OpClient(1);
				Client.Dispose();
				return;
			}
			if (Encoding.Default.GetBytes(CharacterName).Length > 18)
			{
				var clientInfo = Client?.ToString() ?? "NULL";
				var text = "Change Line Character Login      TenNhanVat lon hon > 18[" + AccountID + "][" + CharacterName + "]      [" + clientInfo + "]";
				kickidlog(text);
				LogHelper.WriteLine(LogLevel.Debug, text);
				OpClient(1);
				Client?.Dispose();
				return;
			}
			ReadCharacterData();
			num = 2;
			ChangeLineConfirmation();
			ServerTime();
			num = 3;
			Detection();
			HonoraryTitle();
			if (FLD_Couple != "")
			{
				WhetherMarried = 1;
			}
			ObtainTheAttributeOfTheRoseTitle();
			num = 77;
			UpdateGuildBuff().GetAwaiter().GetResult();
			num = 4;
			KhoiTaoKhiCong();
			num = 5;
			Init_Item_In_Bag();
			GuiDi_NhiemVu_VatPham_List();
			num = 6;
			LoadCharacterWearItem();
			num = 7;
			SetPersonalMedicine();
			num = 8;
			Packet_TuyetRoi();
			num = 9;
			UpdateMoneyAndWeight();
			num = 10;
			NumberOfSpiritBeasts();
			num = 11;
			num = 74;
			UpdateMartialArtsCoolDown();
			num = 12;
			SetPublicPills();
			SetAdditionalStatusItems();
			SetTitleItems();
			num = 13;
			CalculateCharacterEquipmentData();
			num = 15;
			UpdateMartialArtsAndStatus();
			num = 16;
			UpdateKinhNghiemVaTraiNghiem();
			num = 29;
			UpdateHonor();
			num = 17;
			if (MasterData.TID != -1)
			{
				ApprenticeUpdateMentoringSystem();
			}
			else
			{
				for (var i = 0; i < 3; i++)
				{
					if (ApprenticeData[i].TID != -1)
					{
						MasterUpdatesTheMentoringSystem(i);
					}
					CalculateMentorAndApprenticeAttributes(i);
				}
			}
			SendEarthenTalismanData();
			num = 18;
			UpdateMartialArtsCoolDown();
			if (Logoin())
			{
				num = 19;
				Thread.Sleep(20);
				GetReviewScopeNpc();
				num = 20;
				Thread.Sleep(20);
				GetTheReviewRangePlayers();
				num = 21;
				Thread.Sleep(20);
				ScanGroundItems();
				if (GuildId != 0)
				{
					ChaGang().GetAwaiter().GetResult();
				}
				num = 25;
				if (Player_Job < 1 || Player_Job > 13)
				{
					BanAccount(15, AccountID, "非法Character Login");
				}
				if (NhanVat_HP <= 0)
				{
					Mobile(PosX, PosY, PosZ, MapID, 0);
					NhanVat_HP = CharacterMax_HP;
					CapNhat_HP_MP_SP();
					PlayerTuVong = false;
				}
				num = 24;
				switch (map)
				{
					default:
						CloseTheStore(73);
						break;
					case 801:
						CloseTheStore(73);
						if (int.Parse(x) == 520)
						{
							TheLucChien_PhePhai = "CHINH_PHAI";
						}
						else if (int.Parse(x) == -520)
						{
							TheLucChien_PhePhai = "TA_PHAI";
						}
						break;
					case 101:
						CloseTheStore(6);
						break;
				}
				num = 27;
				UpdateMovementSpeed();
				num = 28;
				Recovery_ALL();
				if (World.ServerID == 28)
				{
					HeThongNhacNho("Kênh đại hiệp đang truy cập là kênh tổng hợp!", 10, "Thiên cơ các");
				}
				else if (MapID == 801)
				{
					HeThongNhacNho("Chào mừng đại hiệp đến với Thế Lực Chiến!", 10, "Thiên cơ các");
				}
				else
				{
					HeThongNhacNho("Dịch chuyển đến [Kênh " + World.ServerID + "] thành công!", 10, "Thiên cơ các");
				}
				LoadCongThanhChienData();
				Thread.Sleep(20);
				var conn = World.conn;
				var text2 = "CHANGE_LINE_LOGIN_X|" + AccountID + "|" + Client.ToString() + "|" + World.ServerID + "|" + SessionID + "|" + Client.BindAccount + "|0|" + ClientSettings;
				conn.Transmit(text2);
				this_RoiKhoi_ToDoi_NhacNho();
				Thread.Sleep(20);
				CloseTheTradingWindow();
				Online = true;
				XacDinhXemCoDangNhapBangPacketHayKhong = 1;
				var conn2 = World.conn;
				var text3 = "GET_SERVER_LIST|" + AccountID + "|" + OriginalServerSerialNumber + "|" + OriginalServerIP + "|" + OriginalServerPort + "|" + OriginalServerID;
				conn2.Transmit(text3);
			}
			else
			{
				Client?.Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Character Loginerror[" + AccountID + "]-[" + CharacterName + "]      " + num + "      " + ex.Message);
			if (Client != null)
			{
				kickidlog("Change Line Character Login error      " + num);
				OpClient(1);
				Client.Dispose();
			}
		}
	}
	
	public void VersionVerification(byte[] data, int length)
	{
		verifyVersion = true;
		try
		{
			//Client.g_cur_key = World.g_cur_key;
			//var array = Converter.HexStringToByte("aa550000f0031520040070070000ccda2343677c3790000000000000000055aa");
			//Buffer.BlockCopy(World.g_cur_key, 0, array, 14, 8);
			//Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			//Client?.Send_Map_Data(array, array.Length);
			//Client.VersionVerification = true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kết nối đăng nhập Version Verification error[" + SessionID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}

	public void ChangeLineVerification(byte[] data, int length)
	{
		var num = 0;
		verifyVersion = true;
		try
		{
			num = 1;
			var array = Converter.HexStringToByte("AA559200100515208400700700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			num = 2;
			Random random = new();
			num = 3;
			var array2 = new byte[32];
			num = 4;
			for (var i = 0; i < array2.Length; i++)
			{
				array2[i] = (byte)random.Next(1, 255);
			}
			num = 5;
			Client.g_cur_key = array2;
			num = 6;
			Buffer.BlockCopy(Client.g_cur_key, 0, array, 14, 32);
			num = 7;
			CharacterPacketID = BitConverter.ToUInt16(data, 4);
			num = 8;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			num = 9;
			Client?.Send_Map_Data(array, array.Length);
			num = 10;
			var array3 = new byte[20];
			num = 11;
			for (var j = 0; j < 20 && data[10 + j] != 0; j++)
			{
				array3[j] = data[10 + j];
			}
			num = 12;
			var text = Encoding.Default.GetString(array3).Trim();
			num = 13;
			var empty = string.Empty;
			var text2 = text.Replace("\0", empty).Trim();
			num = 14;
			int num2 = data[30];
			int num3 = data[34];
			num = 15;
			World.conn.Transmit("CHANGE_LINE_LOGIN|" + text2 + " | " + num2 + " | " + num3 + "|" + Client.PlayerSessionID + " | " + Client.ToString() + " | " + World.ServerID + " | " + XacDinhXemCoDangNhapBangPacketHayKhong);
			var array4 = Converter.HexStringToByte("AA550700AE04DB0001000155AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
			Client?.Send_Map_Data(array4, array4.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, num + " Change Line Verification [" + SessionID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}

	public async Task XoaBoNhanVat(byte[] packetData, int packetSize)
	{
		try
		{
			if (FLD_VIP != 0)
			{
				var array = Converter.HexStringToByte("AA55170136001F0008006300000009000000000000000000811855AA");
				var array2 = Converter.HexStringToByte("AA5517010F001F0008000100000000000000000000000000B11855AA");
				int key = packetData[18];
				var value = CharacterName;
				if (!allChars.TryGetValue(key, out value) && Client != null)
				{
					Client.Dispose();
				}
				var array3 = new byte[32];
				Buffer.BlockCopy(packetData, 22, array3, 0, array3.Length);
				var text = Encoding.GetEncoding(1252).GetString(array3).Trim();
				var account = await AccountDb.FindAccount(AccountID);
				var text3 = account.fld_safeword;
				if (text3 == text || FLD_VIP != 0)
				{
					if (!GameDb.DeleteCharacter(value, AccountID))
					{
						Khong_The_Xoa_Bo_Nhan_Vat();
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
						Client?.SendMultiplePackage(array, array.Length);
						return;
					}
					GameDb.UpdateMaritalStatus(FLD_Couple, 0);
					GameDb.UpdateDivorce(FLD_Couple);
					LogHelper.WriteLine(LogLevel.Error, "XÓA NHÂN VẬT THÀNH CÔNG [" + value + "]");
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
					Client?.SendMultiplePackage(array2, array2.Length);
					DangXuat(packetData, packetSize);
				}
				else
				{
					Khong_The_Xoa_Bo_Nhan_Vat();
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
					Client?.SendMultiplePackage(array, array.Length);
				}
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, "NGƯỜI CHƠI MUỐN XÓA NHÂN VẬT !!! - [" + AccountID + "]-[" + CharacterName + "]");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Xoá nhân vật error [" + AccountID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}

	public void Khong_The_Xoa_Bo_Nhan_Vat()
	{
		var text = "AA550E002D011F000800640000000900000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public async Task CreateCharacter(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = Converter.HexStringToByte("AA55120000001500040000000000000000000000000055AA");
			var array2 = Converter.HexStringToByte("AA55120000001500040001000000000000000000000055AA");
			var array3 = new byte[14];
			Buffer.BlockCopy(packetData, 10, array3, 0, 14);
			var array4 = new byte[10];
			Buffer.BlockCopy(packetData, 27, array4, 0, 10);
			var text2 = "0x" + Converter.ToString(array4);
			var text3 = Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim();
			int num = packetData[26];
			var b = packetData[32];
			if ((uint)(b - 1) <= 1u)
			{
				if (num >= 1 && num <= 13)
				{
					var flag = false;
					if (World.GioiHan_LopNhanVat_KhongDuocKhoiTao != "")
					{
						var array5 = World.GioiHan_LopNhanVat_KhongDuocKhoiTao.Split(',');
						for (var i = 0; i < array5.Length; i++)
						{
							if (array5[i] != "" && int.Parse(array5[i]) == num)
							{
								flag = true;
							}
						}
					}
					if (flag)
					{
						return;
					}
					if (text3.Length == 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
						Client?.Send_Map_Data(array, array.Length);
					}
					//if (RxjhClass.SetUserName(AccountID, text3, num, array4) != 1)		
					if (!await GameDb.CreateCharacter(AccountID, text3, num, array4))
					{
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
						Client?.Send_Map_Data(array, array.Length);
					}
					else
					{
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
						Client?.Send_Map_Data(array2, array2.Length);
					}
				}
				else
				{
					BanAccount(42, AccountID, "非法NhanVatNgheNghiep");
				}
			}
			else
			{
				BanAccount(32, AccountID, "非法人物GioiTinh");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "连 Create Character error[" + AccountID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}

	public void OpenChangeCharacter(byte[] data, int length)
	{
		var num = BitConverter.ToInt32(data, 15);
		if (num == **********)
		{
			SendingClass sendingClass = new();
			sendingClass.Write4(0);
			sendingClass.Write8(num);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(1);
			Client?.SendPak(sendingClass, 29744, SessionID);
		}
	}

	public bool IsAlphaNumeric(string strToCheck)
	{
		Regex regex = new("[^a-zA-Z0-9]");
		return !regex.IsMatch(strToCheck);
	}

	public void ChangeTenNhanVat(byte[] data, int length)
	{
		PacketModification(data, length);
		var array = Converter.HexStringToByte("AA551E002C01910010002F000000000000000000000000000000000000000000000055AA");
		try
		{
			var array2 = new byte[4];
			Buffer.BlockCopy(data, 24, array2, 0, 1);
			var text = "--Acc: [" + AccountID + "]--Ten cu: [" + CharacterName + "]";
			// logo.LOG_Doi_Ten(text, UserName);
			if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array2, 0)].VatPham_ID, 0) == **********)
			{
				var array3 = new byte[18];
				for (var i = 0; i < 18 && data[10 + i] != 0; i++)
				{
					array3[i] = data[10 + i];
				}
				var text2 = Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim();
				if (text2.Length > 3)
				{
					foreach (var item in World.Kill)
					{
						if (item.Sffh == 4 && text2.IndexOf(item.Txt) != -1)
						{
							LogHelper.WriteLine(LogLevel.Error, "Tạo danh từ bất hợp pháp： " + text2 + " số tài khoản: " + AccountID);
							HeThongNhacNho("Danh xưng nhân vật trái phép, mời đại hiệp nhập lại lần nữa!");
							return;
						}
					}
					if (GuildId != 0)
					{
						HeThongNhacNho("Đại hiệp cần rời khỏi bang phái trước khi đổi danh xưng!");
					}
					else if (TeamID != 0)
					{
						HeThongNhacNho("Đại hiệp hãy thoát tổ đội trước khi thay đổi danh xưng!");
					}
					else if (FLD_Couple.Length != 0)
					{
						HeThongNhacNho("Đại hiệp cần ly hôn trước khi thay đổi danh xưng!");
					}
					else if (MasterData.TID != -1)
					{
						HeThongNhacNho("Thoát mối quan hệ sư đồ trước khi đổi danh xưng!");
					}
					else if (ApprenticeData[0].TID == -1 && ApprenticeData[1].TID == -1 && ApprenticeData[2].TID == -1)
					{
						if (!GameDb.ChangeCharacterName(AccountID, CharacterName, text2))
						{
							HeThongNhacNho("Đổi danh xưng thất bại, vui lòng thử lại sau!");
							return;
						}
						CharacterName = text2;
						SubtractItem(BitConverter.ToInt32(array2, 0), 1);
						LoadCharacterWearItem();
						PlayerLeaveMap(MapID);

						// Force AOI refresh for character name change
						try
						{
							// if (// AOIConfiguration.Instance. ShouldUseAOI(MapID))
							// {
								// Force AOI update to refresh visibility with new character name
								AOIExtensions.ForceUpdateAOI(this);
							// }
							// else
							// {
							// 	// Fallback to old system
							// 	GetTheReviewRangePlayers();
							// }
						}
						catch (Exception aoiEx)
						{
							LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI after character name change: {aoiEx.Message}");
							// Fallback to old system
							GetTheReviewRangePlayers();
						}

						HeThongNhacNho("Đổi danh xưng thành công, đại hiệp hãy thay đổi nhân vật!", 23, "Thiên cơ các");
						var text3 = "--Acc: [" + AccountID + "] -> Ten moi: [" + text2 + "]";
						// logo.LOG_Doi_Ten(text3, UserName);
						Client?.Send_Map_Data(array, array.Length);
						DangXuat(data, length);
					}
					else
					{
						HeThongNhacNho("Mời đại hiệp giải trừ mối quan hệ sư đồ trước khi đổi danh xưng!");
					}
				}
				else
				{
					HeThongNhacNho("Chỉ chấp nhận ký tự [a-z, A-Z, 0-9]. Đại hiệp hãy nhập danh xưng khác, dài hơn ba ký tự!");
				}
			}
			else
			{
				HeThongNhacNho("Đại hiệp hãy đặt bảo vật đổi danh xưng vào ô đầu tiên [0] trong hành trang!", 10, "Thiên cơ các");
			}
		}
		catch (Exception)
		{
			HeThongNhacNho("Thiên Cơ Các gặp lỗi, xin liên lạc với Võ Lâm Minh Chủ!");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
	}

	public async Task KiemTraNhanVat_CoTonTaiHayKhong(byte[] packetData, int packetSize)
	{
		try
		{
			var array = Converter.HexStringToByte("AA5522000000390014000000000000000000000000000000000000000000000000000000000055AA");
			var array2 = Converter.HexStringToByte("AA5522000000390014000100000000000000000000000000000000000000000000000000000055AA");
			var array3 = new byte[14];
			Buffer.BlockCopy(packetData, 10, array3, 0, 14);
			var text = Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim();
			var bytes = Encoding.Default.GetBytes(text);
			Buffer.BlockCopy(bytes, 0, array, 14, bytes.Length);
			Buffer.BlockCopy(bytes, 0, array2, 14, bytes.Length);
			if (text.Length <= 2)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
			}
			else if (await GameDb.CheckCharacterExists(text))
			{
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
				Client?.Send_Map_Data(array2, array2.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Create Character KiemTraNhanVat CoTonTaiHayKhong error[" + AccountID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}
	public void Send361()
	{
		Client.VersionVerification = true;
		byte[] array2 = Converter.HexStringToByte("aa550000fc0069010200060055aa");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
		Client?.Send_Map_Data(array2, array2.Length);
	}

	public async Task GetAListOfPeople(byte[] data, int length)
	{
		try
		{
			if (data.Length > 0)
				PacketModification(data, length);
			Send361();
			//CharacterName = string.Empty;
			allChars = new Dictionary<int, string>();
			byte[] src = new byte[World.Item_Db_Byte_Length];
			byte[] array = Converter.HexStringToByte("**********************************");
			string initialPacket = "aa550000c0041100310f0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa";

			var characters = await GameDb.FindAllCharactersByAccount(AccountID);
			if (characters == null)
			{
				Logger.Instance.Error("FindAllCharactersByAccount Error");
				Client.Dispose();
				return;
			}
			if (characters.Count == 0)
			{
				Logger.Instance.Error("No characters found");
				Client?.Send_Map_Data(array, array.Length);
				return;
			}
			foreach (var row in characters)
			{
				int FLD_INDEX = (int)row.fld_index;
				string FLD_NAME = row.fld_name.ToString();
				if (allChars.ContainsKey(FLD_INDEX))
				{
					allChars.Remove(FLD_INDEX);
				}
				allChars.Add(FLD_INDEX, FLD_NAME);

				byte[] sendingData = Converter.HexStringToByte(initialPacket);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, sendingData, 4, 2);

				byte[] character_name = Encoding.GetEncoding(World.Language_Charset).GetBytes(FLD_NAME);

				BufferBlockCopyData(sendingData, row, FLD_INDEX, character_name);

				Client?.Send_Map_Data(sendingData, sendingData.Length);
			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"GetAListOfPeopleerror[{AccountID}] {ex.Message} {ex.StackTrace}");
		}
	}

	private static void BufferBlockCopyData(byte[] sendingData, tbl_xwwl_char row, int FLD_INDEX, byte[] character_name)
	{
		int offset = 10;
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, sendingData, offset, 1);
		Buffer.BlockCopy(character_name, 0, sendingData, offset + 1, character_name.Length);
		offset = 49;
		Buffer.BlockCopy(BitConverter.GetBytes((int)row.fld_zx), 0, sendingData, offset, 2);
		Buffer.BlockCopy(BitConverter.GetBytes((int)row.fld_level), 0, sendingData, offset + 2, 2);
		Buffer.BlockCopy(BitConverter.GetBytes((int)row.fld_job_level), 0, sendingData, offset + 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes((int)row.fld_job), 0, sendingData, offset + 6, 1);

		var characterTemplate = new X_Character_Template_Class((byte[])row.fld_face);
		Buffer.BlockCopy(BitConverter.GetBytes(characterTemplate.MauToc), 0, sendingData, offset + 7, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(characterTemplate.KieuToc), 0, sendingData, offset + 9, 2);
		Buffer.BlockCopy(new byte[] { characterTemplate.GioiTinh }, 0, sendingData, offset + 21, 1);
		byte[] character_x = BitConverter.GetBytes(float.Parse(row.fld_x.ToString()));
		byte[] character_y = BitConverter.GetBytes(float.Parse(row.fld_y.ToString()));
		byte[] character_z = BitConverter.GetBytes(float.Parse(row.fld_z.ToString()));
		byte[] character_map = BitConverter.GetBytes((int)row.fld_menow);

		Buffer.BlockCopy(character_x, 0, sendingData, offset + 22, 4);

		byte[] dst = sendingData;
		Buffer.BlockCopy(character_z, 0, dst, offset + 26, 4);

		byte[] dst2 = sendingData;
		Buffer.BlockCopy(character_y, 0, dst2, offset + 30, 4);

		Buffer.BlockCopy(character_map, 0, sendingData, offset + 34, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Math.Min((int)row.fld_hp, 30000)), 0, sendingData, offset + 118, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Math.Min((int)row.fld_mp, 30000)), 0, sendingData, offset + 120, 2);

		FillCharacterWearItems(sendingData, (byte[])row.fld_wearitem);
	}

	private static void FillCharacterWearItems(byte[] sendingData, byte[] character_wearitem)
	{
		byte[] src = new byte[World.Item_Db_Byte_Length];

		for (int j = 0; j < 15; j++)
		{
			try
			{
				byte[] itemData = new byte[12];
				Buffer.BlockCopy(character_wearitem, j * World.Item_Db_Byte_Length, itemData, 0, 12);
				int itemId = BitConverter.ToInt32(itemData, 8);
				// LogHelper.WriteLine(LogLevel.Error, $"{j} {itemId}");
				if (itemId != 0)
				{
					if (j == 11 && World.ItemList[itemId].FLD_SERIES == 1)
					{
						itemId = GetNextItemId(itemId);
					}
					else if (World.ItemList[itemId].FLD_INTEGRATION == 1)
					{
						itemId -= 5000;
					}
					Buffer.BlockCopy(BitConverter.GetBytes(itemId), 0, itemData, 8, 4);
				}

				Buffer.BlockCopy(itemData, 0, sendingData, 203 + j * World.Item_Byte_Length_92, 12);
				Buffer.BlockCopy(character_wearitem, j * World.Item_Db_Byte_Length + 12, sendingData, 203 + j * World.Item_Byte_Length_92 + 16, 60);
			}
			catch
			{
				Buffer.BlockCopy(src, 0, sendingData, 203 + j * World.Item_Byte_Length_92, 72);
			}
		}
	}

	private static int GetNextItemId(int itemId)
	{
		itemId = int.Parse(itemId.ToString().Remove(7) + "0");
		for (var k = 0; k < 7; k++)
		{
			if (World.ItemList.TryGetValue(itemId, out var value) && value.FLD_SERIES == 2)
			{
				itemId = value.FLD_PID;
				break;
			}
			itemId++;
		}
		return itemId >= 16900830 && itemId <= 16900836 ? 16900832 : itemId;
	}
	
	public void QuayLaiChonNhanVat(byte[] packetData, int packetSize)
	{
		try
		{
			IsJoinWorld = false;
			Exiting = true;
			Logout();
			var array = Converter.HexStringToByte("AA5512000C035700040004000000000000000000000055AA");
			var array2 = Converter.HexStringToByte("AA5516000C03630008000100000000000001000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
			Client?.Send_Map_Data(array2, array2.Length);
		}
		catch
		{
			HeThongNhacNho("Thiên Cơ Các gặp lỗi nghiêm trọng, đại hiệp hãy thử lại sau!!");
		}
	}

	public void DangXuat(byte[] data, int length)
	{
		var num = 0;
		try
		{
			PacketModification(data, length);
			IsJoinWorld = false;
			Exiting = true;
			Logout();
			var array = Converter.HexStringToByte("AA553000000004002A0001000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			var array2 = new byte[AccountID.Length];
			var bytes = Encoding.Default.GetBytes(AccountID);
			if (CharacterName.Length > 0)
			{
				var array3 = new byte[CharacterName.Length];
				var bytes2 = Encoding.Default.GetBytes(CharacterName);
				Buffer.BlockCopy(bytes2, 0, array, 18, bytes2.Length);
			}
			Buffer.BlockCopy(bytes, 0, array, 31, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
			if (Client != null)
			{
				Client.Dispose();
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Đăng Xuất lỗi tại num [" + num + "]");
			Client.Dispose();
		}
	}
	
	public void LoadCharacterData()
	{
	
		HonoraryTitle(); //done
		ObtainTheAttributeOfTheRoseTitle(); //done
		ServerTime(); //done
		Send4116();
		Send12579();
		Send249();
		Send8496();
		Detection(); //done
					 // Send59(171, 0, **********, 100000);
					 // Send6368(1, 0, **********, 100000);
					 // Send59(171, 1, **********, ********);
					 // Send6368(1, 1, **********, ********);
					 // Send59(171, 5, **********, 100000);
					 // Send6368(1, 5, **********, 100000);
					 // Send4237();
		Send1370();
		KhoiTaoKhiCong(); //done
		UpdateCharacterData(this);
		CapNhat_HP_MP_SP(); //done
		Init_Item_In_Bag(); //done
		KhoiTaoTuiNgungThanChau_BaoBoc(); //done
		PhaiChangMangTheoAoChang_HanhLy = true; //done
		KhoiTaoAoChoang_HanhLy(); //done
		INITIALIZE_PINK_BAG(); //done
		GuiDi_NhiemVu_VatPham_List(); //done
		UpdateNTC_Bag();
		LogHelper.WriteLine(LogLevel.Debug, $"LoadCharacterData completed for {CharacterName}, NpcList count: {NearbyNpcs?.Count ?? 0}");
		LoadCharacterWearItem(); //done
		KhoiTaoIconTui(); //done
		SetPersonalMedicine(); //done
		UpdateMoneyAndWeight(); //done
		NumberOfSpiritBeasts(); //done
		Checkgianhapbang(); //done
		SetPublicPills(); //done
		SetThoiGianVatPham(); //done
		SetTitleItems(); // ok drug
		SetAdditionalStatusItems(); //done
		CalculateCharacterEquipmentData(); // calculate dât
										   // UpdateCharacterTask2(0); //update quest
		UpdateCharacterQuest();
		UpdateHonor(); //done
		UpdateMartialArtsAndStatus(); // --
		UpdateKinhNghiemVaTraiNghiem(); // --
		SendEarthenTalismanData(); //done

		if (Logoin()) //done
		{
			Online = true; //done
			Exiting = false;
			IsInLobby = false;
			UpdateProductionSystem(); //done
			
			this.AddToAOI();
			// this.UpdateAOI();
			if (AppendStatusList.ContainsKey(1008000082))
			{
				AppendStatusList[1008000082].ThoiGianKetThucSuKien(); //done
			}
			if (GMMode != 0)
			{
				HinhThuc_TangHinh = 1;
				CheDo_TangHinh(1); //done
			}
			if (GuildId != 0)
			{
				ChaGang().GetAwaiter().GetResult(); //done
			}
			InitializeCareerSkills(); //done
			if (MasterData.TID != -1)
			{
				ApprenticeUpdateMentoringSystem(); //done
			}
			else
			{
				for (var i = 0; i < 3; i++)
				{
					if (ApprenticeData[i].TID != -1)
					{
						MasterUpdatesTheMentoringSystem(i); //done
					}
					CalculateMentorAndApprenticeAttributes(i); //done
				}
			}

			if (World.CoHayKhongMoRaGuiThu == 1)
			{
				DangNhapVaoTroChoi_GuiDiTruyenThu(string.Format(World.DangNhap_TruyenThuNoiDung, CharacterName), CharacterName, World.Nguoi_GuiThu);
			}
			NewMailNotification(2, 0); //done
			GetAllMails(); //done
			UpdateMovementSpeed(); //done
			Recovery_ALL(); //done
			UpdateGuildBuff().GetAwaiter().GetResult(); //done
			UpdateCharacterData(this); //done
			UpdateMartialArtsCoolDown(); //done
			CapNhat_HP_MP_SP(); //done
			NhanVat_HoiSinh_BatTu(); //done
			NoviceOnline(); //done
			UpdateKinhNghiemVaTraiNghiem(); //done
			CheckTheMap(); //done
			CheckMaritalStatus(); //done
			UpdateMartialArtsAndStatus(); 
			// UpdateMartialArtsCoolDown(); //done
			UpdateKinhNghiemVaTraiNghiem(); // --
			if (World.NguoiChiemGiu_Den_Tenma.Length > 0)
			{
				HeThongNhacNho("Thiên Ma Thần Cung bị môn phái 《" + World.NguoiChiemGiu_Den_Tenma + "》 chiếm lĩnh!", 10, "Thiên cơ các");
			}
			if (ThongBao_CongThanh == 1)
			{
				GuiDi_CongThanhChien_TuongQuan_BUFF(this, isDisappear: false); //done
			}
			LoadCongThanhChienData(); //done
									  // Get Server list dung de chuyen kenh
									  // var conn = World.conn; //done
									  // var text = "Thu hoach Server liet bieu |" + AccountID + "|" + OriginalServerSerialNumber + "|" + OriginalServerIP + "|" + OriginalServerPort + "|" + OriginalServerID;
									  // conn.Transmit(text); //done
			IsJoinWorld = true; //done
			XacDinhXemCoDangNhapBangPacketHayKhong = 1; //done
			Reload_CongThanhChien(); //done
			ResetMap(); //done
			GameDb.SetCharacterOnline(AccountID, CharacterName, 1); //done
			CheckIfUserHaveMailCod(this); //done
										  // AutoLearnSkill(); //done
			// Trả thưởng group quest
			foreach (var quest in GroupQuestEvent.Instance.QuestDefinitions.Values)
			{
				if (quest.QuestType == GroupQuestType.Guild)
				{
					GroupQuestEvent.Instance.ReceiveGuildQuestRewardAsync(this, quest.ID).GetAwaiter().GetResult();
				}
			}
			LoadCharacterWearItem(); //done
			Display();
			send4325();
			UpdateCharacterCompleteQuest();
			// Load attendance data for player
			Task.Run(async () => await LoadPlayerAttendanceAsync());

			this.UpdateAOI();
			World.CheckAndRejoinParty(this);

		}
		else if (Client != null)
		{
			LogHelper.WriteLine(LogLevel.Error, "Dòng thay đổi ký tự Login error");
			OpClient(1);
			Client.Dispose();
			LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 43]");
		}
	}
	
	public void CharacterLogin(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		var num = 0;
		if (Client != null && !Client.Login)
		{
			if (Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 39]");
			}
			return;
		}
		try
		{
			if (IsJoinWorld)
			{
				BanAccount(12, AccountID, "非法修改PacketTitle_Character Login2");
				return;
			}
			IsJoinWorld = true;
			num = 1;
			int characterIndex = packetData[10];
			HandleCharacterLogin(characterIndex);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Character Login error 2 - tại Num: [" + num + "]  [" + AccountID + "]-[" + CharacterName + "] - " + ex.Message);
			if (Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 45]");
			}
		}
	}
	public X_Vo_Cong_Loai FindHighestLevelVoCong()
	{
		X_Vo_Cong_Loai highestLevelVoCong = null;

		for (int i = 0; i < VoCongMoi.GetLength(0); i++)
		{
			for (int j = 0; j < VoCongMoi.GetLength(1); j++)
			{
				X_Vo_Cong_Loai currentVoCong = VoCongMoi[i, j];
				highestLevelVoCong ??= currentVoCong;
				if (currentVoCong != null && currentVoCong.VoCong_DangCap > 1)
				{
					if (currentVoCong.FLD_LEVEL >= highestLevelVoCong.FLD_LEVEL && currentVoCong.FLD_CongKichSoLuong >= highestLevelVoCong.FLD_CongKichSoLuong)
					{
						highestLevelVoCong = currentVoCong;
					}
				}
			}
		}

		return highestLevelVoCong;
	}

	public void HandleCharacterLogin(int characterIndex)
	{
		try
		{
			if (!allChars.TryGetValue(characterIndex, out var value2))
			{
				if (Client != null)
				{
					Client.Dispose();
					LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 40]");
				}
				return;
			}

			if (NearbyNpcs != null)
			{
				NearbyNpcs.Clear();
			}

			CharacterName = value2;

			if (CharacterName.Length == 0)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 41]");
			}
			if (CharacterName.Length > 14)
			{
				LogHelper.WriteLine(LogLevel.Error, "TenNhanVat Error [" + AccountID + "][" + CharacterName + "]      [" + Client.ToString() + "]");
				Client.Dispose();
				// LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 42]");
			}
			ReadCharacterData();
			LoadCharacterData();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "HandleCharacterLogin error：" + ex);
		}
	}

	private void Send4116()
	{
		byte[] array = Converter.HexStringToByte("aa55000033051410220009000000000000000000ffff000001000e00312d30312d30312030303a303000010055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	private void Send12579()
	{
		var res = Converter.HexStringToByte("aa554a0000002331400002000000010000009006924fa0b7944f020000002076914120a59e4100000000000000000000000000000000000000000000000000000000000000000000000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
		Client?.Send_Map_Data(res, res.Length);
	}

	public void Send249()
	{
		var res = Converter.HexStringToByte("aa551400b503f9000a0000f8916d8800ffffffff55aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
		Client?.Send_Map_Data(res, res.Length);
	}

	public void Send8496()
	{
		var res = Converter.HexStringToByte("aa550e00b5033021040086f7a76855aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
		Client?.Send_Map_Data(res, res.Length);
	}

	public void Send1370()
	{
		var res = Converter.HexStringToByte("aa550e000f275a0504000000000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
		Client?.Send_Map_Data(res, res.Length);
	}
	public void Send59(int type, int type2, int itemId, int time)
	{
		var res = Converter.HexStringToByte("aa552600b5033b001c00ab000000eddc143c000000000100000001000000a08601000000000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, res, 10, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(type2), 0, res, 11, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(itemId), 0, res, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(time), 0, res, 0x1E, 4);
		Client?.Send_Map_Data(res, res.Length);
	}
	public void Send6368(int active, int itemId, int time, int footer)
	{
		var res = Converter.HexStringToByte("aa552200b503e01818000100000000000000eddc143c00000000a0860100b697000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(active), 0, res, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(itemId), 0, res, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(time), 0, res, 26, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(footer), 0, res, 30, 4);
		Client?.Send_Map_Data(res, res.Length);
	}

	public void Send4237()
	{
		var res = Converter.HexStringToByte("aa550000b5038d10c40108000000b028000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a428000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000af28000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000b2280000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007f28000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ae280000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007f2800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080280000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
		Client?.Send_Map_Data(res, res.Length);
	}


	public void send4325()
	{
		var array = Converter.HexStringToByte("AA 55 00 00 EE 05 8B 10 0C 00 B9 01 00 00 EE 05 00 00 01 00 00 00 55 AA".Replace(" ", "")); //4325
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, array, 4, 2); //1212
		base.Client?.Send_Map_Data(array, array.Length);
	}

	public void Reload_CongThanhChien()
	{
		var num = 0;
		try
		{
			num = 1;
			foreach (var value in World.CongThanhSoLieu_list.Values)
			{
				num = 2;
				if (value == null)
				{
					continue;
				}
				if (value.CongThanhChien_TenBang == GuildName && DateTime.Now < value.ThienMaCongThanhChienBanThuongThoiGian)
				{
					num = 5;
					if (!AppendStatusList.ContainsKey(**********))
					{
						num = 6;
						var ticks = value.ThienMaCongThanhChienBanThuongThoiGian.ToUniversalTime().Ticks;
						num = 7;
						var ticks2 = DateTime.Now.ToUniversalTime().Ticks;
						num = 8;
						var num2 = (ticks - ticks2) / 10000;
						num = 9;
						StatusEffect xThemVaoTrangThaiLoai = new(this, num2, **********.0, 1.0);
						num = 10;
						AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
						num = 11;
						StatusEffect(BitConverter.GetBytes(**********), 1, (int)num2);
						num = 12;
					}
					HeThongNhacNho("Công Thành Chiến đến thời điểm ban thưởng: " + value.ThienMaCongThanhChienBanThuongThoiGian.ToString("năm yyyy tháng MM ngày dd, giờ hh khắc mm"), 9, "Truyền Âm Các");
				}
				else if (value.CongThanhChien_TenBang != GuildName && AppendStatusList.ContainsKey(**********))
				{
					num = 13;
					AppendStatusList[**********].ThoiGianKetThucSuKien();
				}
				num = 14;
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi CTC value3 = NULL tại Num: [" + num + "] - [" + AccountID + "][" + CharacterName + "]");
		}
	}

	public void CheckTheMap()
	{
		if (MapID == 40101 || MapID == 43001 || MapID == 1201 || MapID == 7001 || MapID == 7301 || MapID == 42001 || MapID == 42002 || MapID == 30000 || MapID == 30100 || MapID == 30200 || MapID == 30300 || MapID == 32001 || MapID == 32002 || MapID == 2301 || (MapID >= World.KhuLuyenTap1 && MapID <= World.KhuLuyenTap9))
		{
			Mobile(420f, 1740f, 15f, 101, 0);
		}
	}

	public void Recovery_ALL()
	{
		try
		{
			if (Player_Job >= 1 && Player_Job <= 13)
			{
				if (Player_Job == 7)
				{
					Save_NgocLienHoan_CamSu();
				}
				if (NhanVat_HP <= 0)
				{
					Mobile(PosX, PosY, PosZ, MapID, 0);
					NhanVat_HP = CharacterMax_HP;
					PlayerTuVong = false;
				}
				if (FLD_Couple.Length != 0)
				{
					CalculateLoveLevel(FLD_Couple_Love);
					var players = World.KiemTra_Ten_NguoiChoi(FLD_Couple);
					if (players != null)
					{
						players.UpdateCoupleSystem(2, CharacterName, players.NhanCuoiKhacChu, players.GiaiTruQuanHe_Countdown, DateTime.Now);
						players.CoupleTips(1, players.CharacterName, CharacterName);
						UpdateCoupleSystem(2, players.CharacterName, NhanCuoiKhacChu, GiaiTruQuanHe_Countdown, DateTime.Now);
						CoupleTips(1, CharacterName, players.CharacterName);
					}
					else
					{
						UpdateCoupleSystem(1, FLD_Couple, NhanCuoiKhacChu, GiaiTruQuanHe_Countdown, DateTime.Now);
					}
					CalculateMartialArtsAttackPowerOfHusbandAndWifeData();
				}
				_toaDoCuoiCungX = PosX;
				_toaDoCuoiCungY = PosY;
				if (Player_Job == 11 && NhanVat_AP <= CharacterMax_AP)
				{
					NhanVat_AP = CharacterMax_AP;
					if (RecoveryTimeCounter != null)
					{
						RecoveryTimeCounter.Enabled = false;
						RecoveryTimeCounter.Close();
						RecoveryTimeCounter.Dispose();
					}
					RecoveryTimeCounter = new System.Timers.Timer(2000.0);
					RecoveryTimeCounter.Elapsed += ChuongLuc_Recovery;
					RecoveryTimeCounter.Enabled = true;
					RecoveryTimeCounter.AutoReset = true;
				}
				if (Recovery_CheckPill_TTTP != null)
				{
					Recovery_CheckPill_TTTP.Enabled = false;
					Recovery_CheckPill_TTTP.Close();
					Recovery_CheckPill_TTTP.Dispose();
				}
				Recovery_CheckPill_TTTP = new System.Timers.Timer(2000.0);
				Recovery_CheckPill_TTTP.Elapsed += Check_Pill_TTTP_Recovery;
				Recovery_CheckPill_TTTP.Enabled = true;
				Recovery_CheckPill_TTTP.AutoReset = true;
				// if (World.CheckBugGold_Recovery != 0)
				// {
				// 	if (RecoveryCheckBugGold != null)
				// 	{
				// 		RecoveryCheckBugGold.Enabled = false;
				// 		RecoveryCheckBugGold.Close();
				// 		RecoveryCheckBugGold.Dispose();
				// 	}
				// 	RecoveryCheckBugGold = new System.Timers.Timer(2000.0);
				// 	RecoveryCheckBugGold.Elapsed += BugGold_Recovery;
				// 	RecoveryCheckBugGold.Enabled = true;
				// 	RecoveryCheckBugGold.AutoReset = true;
				// }
				if (Recovery_Exp_ALL != null)
				{
					Recovery_Exp_ALL.Enabled = false;
					Recovery_Exp_ALL.Close();
					Recovery_Exp_ALL.Dispose();
				}
				Recovery_Exp_ALL = new System.Timers.Timer(2000.0);
				Recovery_Exp_ALL.Elapsed += TongHop_Event_Exp_Time;
				Recovery_Exp_ALL.Enabled = true;
				Recovery_Exp_ALL.AutoReset = true;
				if (Check_Item_HetHanSuDung != null)
				{
					Check_Item_HetHanSuDung.Enabled = false;
					Check_Item_HetHanSuDung.Close();
					Check_Item_HetHanSuDung.Dispose();
				}
				Check_Item_HetHanSuDung = new System.Timers.Timer(3000.0);
				Check_Item_HetHanSuDung.Elapsed += Check_Item_Het_Han_su_dung;
				Check_Item_HetHanSuDung.Enabled = true;
				Check_Item_HetHanSuDung.AutoReset = true;
				if (World.Check_LoginAcc_2_Recovery != 0)
				{
					if (Recovery_2_CheckLogin != null)
					{
						Recovery_2_CheckLogin.Enabled = false;
						Recovery_2_CheckLogin.Close();
						Recovery_2_CheckLogin.Dispose();
					}
					Recovery_2_CheckLogin = new System.Timers.Timer(2000.0);
					Recovery_2_CheckLogin.Elapsed += LoginAccount_2_Recovery;
					Recovery_2_CheckLogin.Enabled = true;
					Recovery_2_CheckLogin.AutoReset = true;
				}
			}
			else
			{
				BanAccount(1313, AccountID, "Bug 13 Char");
			}
		}
		catch
		{
		}
	}

	public void CalculateLoveLevel(int coupleLoveLevel)
	{
		try
		{
			if (FLD_Couple_Love >= 35000)
			{
				FLD_Couple_Love = 35000;
				FLD_loveDegreeLevel = 1;
			}
			else if (FLD_Couple_Love > 30000)
			{
				FLD_loveDegreeLevel = 1;
			}
			else if (FLD_Couple_Love > 21000)
			{
				FLD_loveDegreeLevel = 2;
			}
			else if (FLD_Couple_Love > 14700)
			{
				FLD_loveDegreeLevel = 3;
			}
			else if (FLD_Couple_Love > 10290)
			{
				FLD_loveDegreeLevel = 4;
			}
			else if (FLD_Couple_Love > 7203)
			{
				FLD_loveDegreeLevel = 5;
			}
			else if (FLD_Couple_Love > 5042)
			{
				FLD_loveDegreeLevel = 6;
			}
			else if (FLD_Couple_Love > 3025)
			{
				FLD_loveDegreeLevel = 7;
			}
			else if (FLD_Couple_Love > 1513)
			{
				FLD_loveDegreeLevel = 8;
			}
			else if (FLD_Couple_Love > 605)
			{
				FLD_loveDegreeLevel = 9;
			}
			else
			{
				FLD_loveDegreeLevel = 10;
			}
		}
		catch
		{
		}
	}

	public void ResetMap()
	{
		try
		{
			switch (MapID)
			{
				case 801:
					if (World.TheLucChien_Giam_NguoiChoi != null)
					{
						var value = string.Empty;
						if (World.TheLucChien_Giam_NguoiChoi.TryGetValue(CharacterName, out value))
						{
							TheLucChien_PhePhai = value;
							World.conn.Transmit("RDISCONNECTED_FACTION|" + CharacterName + "|" + TheLucChien_PhePhai);
							if (TheLucChien_PhePhai == "CHINH_PHAI")
							{
								ChangeLineMove(520f, 435f, 15f, 801);
								World.TheLucChien_ChinhPhai_SoNguoi++;
							}
							else if (TheLucChien_PhePhai == "TA_PHAI")
							{
								ChangeLineMove(-520f, 435f, 15f, 801);
								World.TheLucChien_TaPhai_SoNguoi++;
							}
							World.conn.Transmit("FACTION_WAR_TOTAL|" + World.TheLucChien_ChinhPhai_SoNguoi + "|" + World.TheLucChien_TaPhai_SoNguoi);
						}
						else
						{
							Mobile(420f, 1740f, 15f, 101, 0);
						}
					}
					else
					{
						Mobile(420f, 1740f, 15f, 101, 0);
					}
					break;
				case 2301:
				case 2311:
				case 2341:
				case 3001:
				case 3101:
				case 7001:
				case 7101:
				case 7301:
				case 9001:
				case 9101:
				case 9201:
				case 25208:
				case 25209:
				case 25210:
				case 30000:
				case 30100:
				case 30200:
				case 30300:
				case 32002:
				case 40101:
				case 42001:
					Mobile(420f, 1740f, 15f, 101, 0);
					break;
			}
		}
		catch
		{
		}
	}

	public void NoviceOnline()
	{
		try
		{
			if (World.CoMo_TrucTuyen_ChoNguoiMoi_LamQuenHayKhong == 1 && Player_Level == 1)
			{
				//World.ScriptClass.ThietLapLevelOnlineLanDau(SessionID, World.CapDo_TrucTuyen);
				// NewLearningQigong(5, 1);
				// NewLearningQigong(6, 1);
				// NewLearningQigong(7, 1);
				// NewLearningQigong(8, 1);
				// NewLearningQigong(9, 1);
				// NewLearningQigong(10, 1);
				//UpdateCharacterData(this);
				//UpdateMartialArtsAndStatus();
				//SaveCharacterData();
			}
		}
		catch
		{
		}
	}

	public void MarriedBuff()
	{
		try
		{
			if (WhetherMarried == 1)
			{
				if (GetAddState(242))
				{
					AppendStatusList[242].ThoiGianKetThucSuKien();
				}
				AppendStatusList.Add(242, new StatusEffect(this, 604800000, 242, 0));
				StatusEffect(242, 1, 1, 3600000);
				FLD_NhanVat_ThemVao_CongKich += 15;
				FLD_NhanVat_ThemVao_PhongNgu += 15;
				CharactersToAddMax_HP += 300;
				CharactersToAddMax_MP += 300;
				FLD_NhanVat_ThemVao_NeTranh += 15;
				FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.2;
				FLD_KetHonLeVat_ThemVaoThuocTinhThach = 1;
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Buff Kết Hôn id: 242");
		}
	}

	public void UpdateMovementSpeed()
	{
		var num = 0;
		try
		{
			num = 1;
			TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[1]);
			num = 2;
			if (AppendStatusList == null)
			{
				num = 3;
				AppendStatusList = new ThreadSafeDictionary<int, StatusEffect>();
			}
			num = 4;
			if (AppendStatusList.ContainsKey(601101))
			{
				num = 5;
				TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[2]);
			}
			else if (AppendStatusList.ContainsKey(601102))
			{
				num = 6;
				TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[3]);
			}
			else if (AppendStatusList.ContainsKey(601103))
			{
				num = 7;
				TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[4]);
			}
			else if (CharacterBeast != null && CharacterBeast.CuoiThu == 1)
			{
				num = 8;
				if (CharacterBeast.ThuBay == 1)
				{
					num = 9;
					TocDoDiChuyen_Max = 50f;
				}
				else
				{
					num = 10;
					TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[5]);
				}
			}
			else if (ChayTron)
			{
				num = 11;
				TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[1]);
			}
			else if (!ChayTron)
			{
				num = 12;
				TocDoDiChuyen_Max = float.Parse(World.DiDong_TocDo[0]);
			}
		}
		catch (Exception ex)
		{
			TocDoDiChuyen_Max = 100f;
			LogHelper.WriteLine(LogLevel.Error, "Cập nhật tốc độ di chuyển lỗi !! [" + AccountID + "]-[" + CharacterName + "] num:[" + num + "] - " + ex.Message);
		}
	}
		public void ChangeTheLineToUpdateTheConfiguration(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		PacketReader packetReader = new(packetData, packetSize, bool_0: false);
		packetReader.Seek(10, SeekOrigin.Begin);
		//if (packetSize != 26)
		//{
		//	HeThongNhacNho("Phiên bản đang thử nghiệm là V24.0!", 10, "Truyền Âm Các");
		//}
		Config.ToDoi = packetReader.ReadInt8();
		Config.GiaoDich = packetReader.ReadInt8();
		Config.TruyenAm = packetReader.ReadInt8();
		packetReader.ReadInt8();
		var yPhucBanDau = Config.YPhuc_BanDau;
		Config.YPhuc_BanDau = packetReader.ReadInt8();
		Config.KiemTraTrangBi = packetReader.ReadInt8();
		var chuyenDoiTocOnOff = Config.ChuyenDoiToc_OnOff;
		Config.ChuyenDoiToc_OnOff = packetReader.ReadInt8();
		var voHuanSwitchOnOff = Config.VoHuanSwitchOnOff;
		Config.VoHuanSwitchOnOff = packetReader.ReadInt8();
		if (Config.VoHuanSwitchOnOff == 2 && Player_WuXun > World.Wxlever[7] + 50000.0 && Player_WuXun >= 2500000 && VoHuanGiaiDoan == 7 && Player_Zx != 0)
		{
			var dateTime = DateTime.Now.AddDays(14.0);
			var now = DateTime.Now;
			var thoiGian = Convert.ToUInt32(dateTime.ToString("yyMMddHHmm"));
			var timeSpan = dateTime - now;
			PillItem xXungHaoDuocPhamLoai = new()
			{
				ThoiGian = thoiGian
			};
			Player_WuXun -= 50000;
			if (Player_Zx == 1)
			{
				xXungHaoDuocPhamLoai.DuocPhamID = 1008001042;
				NewDrugEffects(1008001042, 1, xXungHaoDuocPhamLoai.ThoiGian, (uint)timeSpan.TotalMinutes);
			}
			else
			{
				xXungHaoDuocPhamLoai.DuocPhamID = 1008001043;
				NewDrugEffects(1008001043, 1, xXungHaoDuocPhamLoai.ThoiGian, (uint)timeSpan.TotalMinutes);
			}
			TitleDrug.Add(xXungHaoDuocPhamLoai.DuocPhamID, xXungHaoDuocPhamLoai);
			World.Gui_Full_VoHuan_Title_TinTuc_Effect_VoHuan_8(CharacterName, Player_Zx);
		}
		else if (Config.VoHuanSwitchOnOff == 2 && Player_WuXun > World.Wxlever[8] + 50000.0 && Player_WuXun >= ******** && VoHuanGiaiDoan == 7 && Player_Zx != 0)
		{
			var dateTime2 = DateTime.Now.AddDays(14.0);
			var now2 = DateTime.Now;
			var thoiGian2 = Convert.ToUInt32(dateTime2.ToString("yyMMddHHmm"));
			var timeSpan2 = dateTime2 - now2;
			PillItem xXungHaoDuocPhamLoai2 = new()
			{
				ThoiGian = thoiGian2
			};
			Player_WuXun -= 75000;
			if (Player_Zx == 1)
			{
				xXungHaoDuocPhamLoai2.DuocPhamID = 1008002376;
				NewDrugEffects(1008002376, 1, xXungHaoDuocPhamLoai2.ThoiGian, (uint)timeSpan2.TotalMinutes);
			}
			else
			{
				xXungHaoDuocPhamLoai2.DuocPhamID = 1008002377;
				NewDrugEffects(1008002377, 1, xXungHaoDuocPhamLoai2.ThoiGian, (uint)timeSpan2.TotalMinutes);
			}
			TitleDrug.Add(xXungHaoDuocPhamLoai2.DuocPhamID, xXungHaoDuocPhamLoai2);
			World.Gui_Full_VoHuan_Title_TinTuc_Effect_VoHuan_9(CharacterName, Player_Zx);
		}
		Config.SearchSwitchOnOff = packetReader.ReadInt8();
		Config.ToTinh_SwitchOnOff = packetReader.ReadInt8();
		var rauQuaSwitchOnOff = Config.RauQua_SwitchOnOff;
		Config.RauQua_SwitchOnOff = packetReader.ReadInt8();
		packetReader.ReadInt8();
		Config.PetKinhNghiem = packetData[30];
		UpdateCharacterData(this);
		//UpdateEquipmentEffectTo(this, this);
		if (yPhucBanDau != Config.YPhuc_BanDau || rauQuaSwitchOnOff != Config.RauQua_SwitchOnOff || chuyenDoiTocOnOff != Config.ChuyenDoiToc_OnOff || voHuanSwitchOnOff != Config.VoHuanSwitchOnOff)
		{
			UpdateMartialArtsAndStatus();
			UpdateBroadcastCharacterData();
			UpdateEquipmentEffects();
			UpdateKhiCong();
		}
		Packet_TuyetRoi();
		ServerTime();
		ClientSettings = Converter.ToString(packetData);
	}

	public void UpdateConfiguration(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			PacketReader packetReader = new(packetData, packetSize, bool_0: false);
			packetReader.Seek(10, SeekOrigin.Begin);
			// if (packetSize != 26)
			// {
			// 	HeThongNhacNho("Phiên bản đang thử nghiệm là V24.0!", 10, "Truyền Âm Các");
			// }
			Config.ToDoi = packetReader.ReadInt8();
			Config.GiaoDich = packetReader.ReadInt8();
			Config.TruyenAm = packetReader.ReadInt8();
			packetReader.ReadInt8();
			var yPhucBanDau = Config.YPhuc_BanDau;
			Config.YPhuc_BanDau = packetReader.ReadInt8();
			Config.KiemTraTrangBi = packetReader.ReadInt8();
			var chuyenDoiTocOnOff = Config.ChuyenDoiToc_OnOff;
			Config.ChuyenDoiToc_OnOff = packetReader.ReadInt8();
			var voHuanSwitchOnOff = Config.VoHuanSwitchOnOff;
			Config.VoHuanSwitchOnOff = packetReader.ReadInt8();
			if (Config.VoHuanSwitchOnOff == 2 && Player_WuXun > World.Wxlever[7] + 50000.0 && Player_WuXun >= 2000000 && VoHuanGiaiDoan == 7 && Player_Zx != 0)
			{
				var dateTime = DateTime.Now.AddDays(30.0);
				var now = DateTime.Now;
				var thoiGian = Convert.ToUInt32(dateTime.ToString("yyMMddHHmm"));
				var timeSpan = dateTime - now;
				PillItem xXungHaoDuocPhamLoai = new()
				{
					ThoiGian = thoiGian
				};
				Player_WuXun -= 50000;
				if (Player_Zx == 1)
				{
					xXungHaoDuocPhamLoai.DuocPhamID = 1008001042;
					NewDrugEffects(1008001042, 1, xXungHaoDuocPhamLoai.ThoiGian, (uint)timeSpan.TotalMinutes);
				}
				else
				{
					xXungHaoDuocPhamLoai.DuocPhamID = 1008001043;
					NewDrugEffects(1008001043, 1, xXungHaoDuocPhamLoai.ThoiGian, (uint)timeSpan.TotalMinutes);
				}
				TitleDrug.Add(xXungHaoDuocPhamLoai.DuocPhamID, xXungHaoDuocPhamLoai);
				World.Gui_Full_VoHuan_Title_TinTuc_Effect_VoHuan_8(CharacterName, Player_Zx);
			}
			else if (Config.VoHuanSwitchOnOff == 3 && Player_WuXun > World.Wxlever[8] + 75000.0 && Player_WuXun >= 5000000 && VoHuanGiaiDoan == 7 && Player_Zx != 0)
			{
				var dateTime2 = DateTime.Now.AddDays(30.0);
				var now2 = DateTime.Now;
				var thoiGian2 = Convert.ToUInt32(dateTime2.ToString("yyMMddHHmm"));
				var timeSpan2 = dateTime2 - now2;
				PillItem xXungHaoDuocPhamLoai2 = new()
				{
					ThoiGian = thoiGian2
				};
				Player_WuXun -= 150000;
				if (Player_Zx == 1)
				{
					xXungHaoDuocPhamLoai2.DuocPhamID = 1008002376;
					NewDrugEffects(1008002376, 1, xXungHaoDuocPhamLoai2.ThoiGian, (uint)timeSpan2.TotalMinutes);
				}
				else
				{
					xXungHaoDuocPhamLoai2.DuocPhamID = 1008002377;
					NewDrugEffects(1008002377, 1, xXungHaoDuocPhamLoai2.ThoiGian, (uint)timeSpan2.TotalMinutes);
				}
				TitleDrug.Add(xXungHaoDuocPhamLoai2.DuocPhamID, xXungHaoDuocPhamLoai2);
				World.Gui_Full_VoHuan_Title_TinTuc_Effect_VoHuan_9(CharacterName, Player_Zx);
			}
			Config.SearchSwitchOnOff = packetReader.ReadInt8();
			Config.ToTinh_SwitchOnOff = packetReader.ReadInt8();
			var rauQuaSwitchOnOff = Config.RauQua_SwitchOnOff;
			Config.RauQua_SwitchOnOff = packetReader.ReadInt8();
			packetReader.ReadInt8();
			Config.PetKinhNghiem = packetData[30];
			UpdateCharacterData(this);
			//UpdateEquipmentEffectTo(this, this);
			if (yPhucBanDau != Config.YPhuc_BanDau || rauQuaSwitchOnOff != Config.RauQua_SwitchOnOff || chuyenDoiTocOnOff != Config.ChuyenDoiToc_OnOff || voHuanSwitchOnOff != Config.VoHuanSwitchOnOff)
			{
				UpdateMartialArtsAndStatus();
				UpdateBroadcastCharacterData();
				UpdateEquipmentEffects();
				UpdateKhiCong();
				CapNhat_HP_MP_SP();
			}
			//Packet_TuyetRoi();
			ServerTime();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đổi mới UpdateConfig phạm sai lầm: " + ex.StackTrace);
		}
	}


}