﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public async void Xin_Cancel_CongThanh(byte[] packetData, Players players)
	{
		if (players.GuildName == "" || players.GangCharacterLevel != 6)
		{
			return;
		}
		if (players.MonPhai_LienMinh_MinhChu.Length > 0)
		{
			if (players.GuildName != players.MonPhai_LienMinh_MinhChu)
			{
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MonPhai_LienMinh_MinhChu == players.MonPhai_LienMinh_MinhChu)
				{
					GuiDi_CongThanhChien_TuongQuan_BUFF(value, isDisappear: true);
					value.ThongBao_CongThanh = 0;
				}
			}
			//var dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Guild]  where  LienMinh_MinhChu='" + players.MonPhai_LienMinh_MinhChu + "'");
			var dBToDataTable = await GameDb.FindAllSiegeParticipants();
			if (dBToDataTable != null)
			{
				if (dBToDataTable.Count == 1)
				{
					//RxjhClass.Update_MonPhai_LienMinh_MinhChu(players.MonPhai_LienMinh_MinhChu, "", 0);
					await GameDb.UpdateGuildAllianceMaster(players.MonPhai_LienMinh_MinhChu, "", 0);
					players.MonPhai_LienMinh_MinhChu = "";
				}
				else
				{
					await GameDb.UpdateSiegeNotice(players.MonPhai_LienMinh_MinhChu, 0);
				}
			}
		}
		else
		{
			//RxjhClass.Update_MonPhai_LienMinh_MinhChu(players.GuildName, "", 0);
			await GameDb.UpdateGuildAllianceMaster(players.GuildName, "", 0);
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (value2.GuildName == players.GuildName)
				{
					GuiDi_CongThanhChien_TuongQuan_BUFF(value2, isDisappear: true);
					value2.ThongBao_CongThanh = 0;
				}
			}
		}
		var text = "AA5522002100D6041C0007000000BDADBAFEB5DAD2BBB4F3B0EF00000000020000000100000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDi_CongThanhChien_TuongQuan_BUFF(Players players, bool isDisappear)
	{
		var text = "AA553200210043012C0000000000000000007AE4143C0000000070000100585D94770000000001000000E84B9377000000000100000055AA";
		var array = Converter.HexStringToByte(text);
		var value = uint.Parse(DateTime.Now.ToString("yyMMddHHmm"));
		var value2 = uint.Parse(DateTime.Now.AddDays(7.0).ToString("yyMMddHHmm"));
		if (isDisappear)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(-1), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 38, 4);
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 42, 4);
			if (players.GuildName != players.MonPhai_LienMinh_MinhChu || players.GangCharacterLevel != 6)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 50, 4);
			}
		}
		if (players.MonPhai_LienMinh_MinhChu.Length > 0 && players.MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(1008002169), 0, array, 18, 4);
			if (!players.AppendStatusList.ContainsKey(1008002169))
			{
				StatusEffect value3 = new(players, 604800000, 1008002169, 0);
				players.AppendStatusList.Add(1008002169, value3);
			}
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(1008002170), 0, array, 18, 4);
			if (!players.AppendStatusList.ContainsKey(1008002170))
			{
				StatusEffect value4 = new(players, 604800000, 1008002170, 0);
				players.AppendStatusList.Add(1008002170, value4);
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDi_CongThanhChien_TuongQuan_BUFF(Players players, int buffid, DateTime 到期ThoiGian, bool 是否消失)
	{
		var text = "AA5532001F0443012C0000000000000000008FE4143C00000000700000003C00000000000000010000008EE69377000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		var value = uint.Parse(到期ThoiGian.ToString("yyMMddHHmm"));
		var value2 = (uint)到期ThoiGian.Subtract(DateTime.Now).TotalSeconds;
		if (是否消失)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(-1), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 38, 4);
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 42, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(buffid), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDi_LoiMoi_CongThanhChien_Lan_1(Players players)
	{
		var text = "AA5514002C0503510E0000000000FFFFFFFFFFFF0700000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDi_LoiMoi_CongThanhChien_Lan_2(Players players)
	{
		var text = "AA551400B90005510E000000000001000000020011A4000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDi_NguoiChoi_BiKick_Thua(string thang利者, string 失Thua者)
	{
		var text = "AA552C007D03E3042600B30000000000000000000000000000CA0000000000000000000000000000020000000200000055AA";
		var array = Converter.HexStringToByte(text);
		var array2 = Converter.TangHoaKetHon(thang利者);
		Buffer.BlockCopy(array2, 0, array, 10, array2.Length);
		array2 = Converter.TangHoaKetHon(失Thua者);
		Buffer.BlockCopy(array2, 0, array, 25, array2.Length);
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.MapID == 42001)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(value.SessionID), 0, array, 4, 2);
				value.Client?.Send_Map_Data(array, array.Length);
			}
		}
	}

	public async Task DangKy_CongThanh(byte[] packetData, Players players)
	{
		try
		{
			if (players.ThongBao_CongThanh == 1)
			{
				players.HeThongNhacNho("Môn phái đã đăng ký công thành không thể lặp lại yêu cầu!", 10, "Thiên cơ các");
				return;
			}
			if (players.GangCharacterLevel != 6)
			{
				players.HeThongNhacNho("Chỉ có môn chủ mới có thể xin công thành!", 10, "Thiên cơ các");
				return;
			}
			var text = "AA5512000400BC040C000ItemConstants.CHIEN_PHIEU0000000100000055AA";
			var array = Converter.HexStringToByte(text);
			Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
			players.Client?.Send_Map_Data(array, array.Length);
			var dBToDataTable = await GameDb.TotalSiege();
			if (dBToDataTable == 0)
			{
				World.ThienMa_ThanCung_TheLuc_ChiemLinh = players.GuildName;
				//DBA.ExeSqlCommand(string.Format("INSERT INTO ThienMaThanCung_DanhSach(Bang_Chiem_Thanh,Ngay_Chiem_Thanh,Cong_Thanh_CuongHoa_Level) VALUES ('{0}',{1},{2})", players.GuildName, Converter.DateTimeToString(DateTime.Now), "0")).GetAwaiter().GetResult();
				GameDb.RegisterSiege(players.GuildName);
			}
			if (players.MonPhai_LienMinh_MinhChu != "")
			{
				if (players.MonPhai_LienMinh_MinhChu != players.GuildName)
				{
					players.HeThongNhacNho("Chỉ có minh chủ liên minh môn phái mới có thể thông báo công thành!", 10, "Thiên cơ các");
					return;
				}
				await GameDb.UpdateSiegeNotice(players.GuildName, 1);
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (value.MonPhai_LienMinh_MinhChu == players.MonPhai_LienMinh_MinhChu)
						{
							value.MonPhai_LienMinh_MinhChu = players.MonPhai_LienMinh_MinhChu;
							value.ThongBao_CongThanh = 1;
							GuiDi_CongThanhChien_TuongQuan_BUFF(value, isDisappear: false);
						}
					}
					return;
				}
			}
			await GameDb.UpdateGuildAllianceMaster(players.GuildName, players.GuildName, 1);
			//RxjhClass.Update_MonPhai_LienMinh_MinhChu(players.GuildName, players.GuildName, 1);
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (value2.GuildName == players.GuildName)
				{
					value2.ThongBao_CongThanh = 1;
					value2.MonPhai_LienMinh_MinhChu = players.GuildName;
					GuiDi_CongThanhChien_TuongQuan_BUFF(value2, isDisappear: false);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Xin công thành phạm sai lầm " + players.Client.PlayerSessionID + "|" + players.AccountID + "|" + players.CharacterName + "  " + ex);
		}
	}

	public async void KiemTra_CongThanh_DongMinh(byte[] packetData, Players players)
	{
		try
		{
			int num = packetData[22];
			var text = "AA550E01C405BE04080107000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000055AA";
			var array = Converter.HexStringToByte(text);

			// Load guild alliance data using GameDb
			var guildAllianceList = await GameDb.LoadGuildAlliances();
			var guildAllianceName = await GameDb.LoadGuildAllianceName(players.GuildName);
			players.MonPhai_LienMinh_MinhChu = guildAllianceName;

			if (guildAllianceList != null && guildAllianceList.Count > num * 10 - 10)
			{
				var num2 = 0;
				var startIndex = num * 10 - 10;
				var endIndex = Math.Min(startIndex + 10, guildAllianceList.Count);

				for (var i = startIndex; i < endIndex; i++)
				{
					var text2 = guildAllianceList[i].g_name;
					if (text2 != World.NguoiChiemGiu_Den_Tenma)
					{
						var array2 = Converter.TangHoaKetHon(text2);
						Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14 + 24 * num2, 4);
						Buffer.BlockCopy(array2, 0, array, 18 + 24 * num2, array2.Length);

						if (text2 == players.MonPhai_LienMinh_MinhChu)
						{
							Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 34 + 24 * num2, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 270, 4);
						}
						else if (text2 == KiemTra_BangPhai_Xin_LienMinh(players.GuildName))
						{
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 34 + 24 * num2, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 270, 4);
						}
						else
						{
							Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 34 + 24 * num2, 4);
						}
						num2++;
					}
				}
				Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 262, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(num2 / 10 + 1), 0, array, 266, 4);
			}

			Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
			players.Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Error in KiemTra_CongThanh_DongMinh: {ex.Message}");
		}

		// Old DBA implementation (commented out)
		// int num = packetData[22];
		// var text = "AA550E01C405BE04080107000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000055AA";
		// var array = Converter.HexStringToByte(text);
		// var dataTable = RxjhClass.Load_ThongTinLienMinh();
		// players.MonPhai_LienMinh_MinhChu = RxjhClass.DatDuoc_MonPhai_LienMinh_MinhChu(players.GuildName);
		// if (dataTable != null && dataTable.Rows.Count > num * 10 - 10)
		// {
		// 	var num2 = 0;
		// 	for (var i = 0; i < dataTable.Rows.Count; i++)
		// 	{
		// 		var text2 = dataTable.Rows[num * 10 - 10 + i]["G_Name"].ToString();
		// 		if (text2 != World.NguoiChiemGiu_Den_Tenma)
		// 		{
		// 			var array2 = Converter.TangHoaKetHon(text2);
		// 			Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14 + 24 * num2, 4);
		// 			Buffer.BlockCopy(array2, 0, array, 18 + 24 * num2, array2.Length);
		// 			if (text2 == players.MonPhai_LienMinh_MinhChu)
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 34 + 24 * num2, 4);
		// 				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 270, 4);
		// 			}
		// 			else if (text2 == KiemTra_BangPhai_Xin_LienMinh(players.GuildName))
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 34 + 24 * num2, 4);
		// 				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 270, 4);
		// 			}
		// 			else
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 34 + 24 * num2, 4);
		// 			}
		// 			num2++;
		// 		}
		// 	}
		// 	Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 262, 4);
		// 	Buffer.BlockCopy(BitConverter.GetBytes(num2 / 10 + 1), 0, array, 266, 4);
		// }
		// Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		// players.Client?.Send_Map_Data(array, array.Length);
	}

	public void LienMinh_XinPhep(byte[] packetData, Players players)
	{
		var text = "AA5522001000C0041C0007000000B4000000000000000000000000000000010000000100000055AA";
		var array = Converter.HexStringToByte(text);
		var array2 = new byte[20];
		for (var i = 0; i < 20 && packetData[i + 14] != 0; i++)
		{
			array[i + 14] = packetData[i + 14];
			array2[i] = packetData[i + 14];
		}
		var 盟主门派 = Encoding.Default.GetString(array2).Replace("\0", "");
		XinMonPhaiLienMinh(players, 盟主门派);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public async Task ChapNhan_MonPhaiKhac_LienMinh(byte[] packetData, Players players)
	{
		var array = new byte[20];
		for (var i = 0; i < 20 && packetData[i + 14] != 0; i++)
		{
			array[i] = packetData[i + 14];
		}
		var text = Encoding.Default.GetString(array).Replace("\0", "");
		switch (packetData[30])
		{
			case 2:
				{
					if (players.GuildName == players.MonPhai_LienMinh_MinhChu)
					{
						//RxjhClass.Update_MonPhai_LienMinh_MinhChu(players.GuildName, "", 0);
						await GameDb.UpdateGuildAllianceMaster(players.GuildName, "", 0);
						foreach (var value in World.allConnectedChars.Values)
						{
							if (value.GuildName == text)
							{
								value.MonPhai_LienMinh_MinhChu = "";
								value.ThongBao_CongThanh = 0;
								GuiDi_CongThanhChien_TuongQuan_BUFF(value, isDisappear: true);
							}
						}
						break;
					}
					if (players.MonPhai_LienMinh_MinhChu != "")
					{
						await GameDb.UpdateGuildAllianceMaster(players.GuildName, "", 0);
						foreach (var value2 in World.allConnectedChars.Values)
						{
							if (value2.GuildName == players.GuildName)
							{
								value2.MonPhai_LienMinh_MinhChu = "";
								value2.ThongBao_CongThanh = 0;
								GuiDi_CongThanhChien_TuongQuan_BUFF(value2, isDisappear: true);
							}
						}
						break;
					}
					for (var k = 0; k < World.MonPhaiLienMinhTrangThai.Count; k++)
					{
						var xMonPhaiLienMinhTrangThai2 = World.MonPhaiLienMinhTrangThai[k];
						if (xMonPhaiLienMinhTrangThai2.XinMonPhaiDanhDu == players.GuildName && xMonPhaiLienMinhTrangThai2.MinhChu_MonPhai_DanhDu == text)
						{
							World.MonPhaiLienMinhTrangThai.RemoveAt(k);
							break;
						}
					}
					break;
				}
			case 1:
				{
					for (var j = 0; j < World.MonPhaiLienMinhTrangThai.Count; j++)
					{
						var xMonPhaiLienMinhTrangThai = World.MonPhaiLienMinhTrangThai[j];
						if (!(xMonPhaiLienMinhTrangThai.XinMonPhaiDanhDu == text) || !(xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu == players.GuildName))
						{
							continue;
						}
						World.MonPhaiLienMinhTrangThai.RemoveAt(j);
						await GameDb.UpdateGuildAllianceMaster(xMonPhaiLienMinhTrangThai.XinMonPhaiDanhDu, xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu, players.ThongBao_CongThanh);
						//RxjhClass.Update_MonPhai_LienMinh_MinhChu(xMonPhaiLienMinhTrangThai.XinMonPhaiDanhDu, xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu, players.ThongBao_CongThanh);
						foreach (var value3 in World.allConnectedChars.Values)
						{
							if (value3.GuildName == text)
							{
								value3.ThongBao_CongThanh = players.ThongBao_CongThanh;
								value3.MonPhai_LienMinh_MinhChu = xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu;
								GuiDi_CongThanhChien_TuongQuan_BUFF(value3, isDisappear: false);
							}
						}
						break;
					}
					break;
				}
		}
		var text2 = "AA5522000400C4041C000700000000000000000000000000000000000000010000000100000055AA";
		var array2 = Converter.HexStringToByte(text2);
		var array3 = Converter.TangHoaKetHon(text);
		Buffer.BlockCopy(array3, 0, array2, 14, array3.Length);
		array2[30] = packetData[30];
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array2, 4, 2);
		players.Client?.Send_Map_Data(array2, array2.Length);
	}

	public async Task LienMinh_QuanLy(byte[] packetData, Players players)
	{
		var text = "AA550E010400C604080107000000330000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000008904000001000000010000000100000055AA";
		var array = Converter.HexStringToByte(text);
		var dataTable = await GameDb.LoadGuildAlliance(players.GuildName);
		var num = 0;
		if (dataTable != null)
		{
			for (var i = 0; i < dataTable.Count; i++)
			{
				var text2 = dataTable[i].g_name;
				if (text2 != players.GuildName)
				{
					var array2 = Converter.TangHoaKetHon(text2);
					Buffer.BlockCopy(array2, 0, array, 18 + 24 * num, array2.Length);
					Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 34 + 24 * num, 4);
					num++;
				}
			}
		}
		for (var j = 0; j < World.MonPhaiLienMinhTrangThai.Count; j++)
		{
			var xMonPhaiLienMinhTrangThai = World.MonPhaiLienMinhTrangThai[j];
			if (xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu == players.GuildName)
			{
				var array3 = Converter.TangHoaKetHon(xMonPhaiLienMinhTrangThai.XinMonPhaiDanhDu);
				Buffer.BlockCopy(array3, 0, array, 18 + 24 * (j + num), array3.Length);
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public async Task KiemTra_LienMinh_XinMonPhai(byte[] packetData, Players players)
	{
		var array = new byte[20];
		for (var i = 0; i < 20 && packetData[i + 10] != 0; i++)
		{
			array[i] = packetData[i + 10];
		}
		var text = Encoding.Default.GetString(array).Replace("\0", "");
		var dataTable = await GameDb.FindGuild(text);
		var text2 = "AA552D000400C8042700B9000000000000000000000000000000D70000000000000000000000000000010000000100000055AA";
		var array2 = Converter.HexStringToByte(text2);
		if (dataTable != null)
		{
			var array3 = new byte[20];
			array3 = Converter.TangHoaKetHon(dataTable.g_name);
			Buffer.BlockCopy(array3, 0, array2, 10, array3.Length);
			array3 = Converter.TangHoaKetHon(dataTable.g_master);
			Buffer.BlockCopy(array3, 0, array2, 26, array3.Length);
			Buffer.BlockCopy(BitConverter.GetBytes((int)dataTable.leve), 0, array2, 45, 4);
			var members = await GameDb.TotalMemberInGuild(text);
			Buffer.BlockCopy(BitConverter.GetBytes(members), 0, array2, 41, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array2, 4, 2);
		players.Client?.Send_Map_Data(array2, array2.Length);
	}

	public async void Doc_KetQua_ThienMaThanCung_SoLieu()
	{
		try
		{
			var thienMaThanCung = await GameDb.LoadThienMaThanCung();
			if (thienMaThanCung != null)
			{
				var ngay_chiem_thanh = thienMaThanCung.ngay_chiem_thanh;
				var cong_thanh_cuonghoa_level = thienMaThanCung.cong_thanh_cuonghoa_level;
				World.NguoiChiemGiu_Den_Tenma = thienMaThanCung.bang_chiem_thanh;
				World.BanDau_ChiemLinh_Ngay = int.Parse(ngay_chiem_thanh);
				World.Cong_Thanh_CuongHoa_Level = int.Parse(cong_thanh_cuonghoa_level);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Error Thiên Ma Thanh Cung " + ex.Message);
		}
	}

	public void CongThanh_CuongHoa_XacNhan(byte[] packetData, Players players)
	{
		var text = "AA551A00B003DB041400070000000000000000000000000000000100000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		if (players.Player_Money < 50000000)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(10138), 0, array, 26, 4);
		}
		else if (players.Player_Whtb < 10000)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(10236), 0, array, 26, 4);
		}
		else
		{
			players.Player_Money -= 50000000L;
			players.Player_Whtb -= 10000;
			World.Cong_Thanh_CuongHoa_Level++;
			players.Gold_TroChoi_TieuHao_NhacNho(50000000u, -1);
			players.UpdateMartialArtsAndStatus();
			players.UpdateMoneyAndWeight();
		}

		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void KiemTra_CongThanh_Cuong_Hoa(byte[] packetData, Players players)
	{
		var text = "AA551A00B003D90414000700000005000000A0252600F40100000100000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(World.Cong_Thanh_CuongHoa_Level), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(World.Cong_Thanh_CuongHoa_Level * 250000), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(World.Cong_Thanh_CuongHoa_Level * 50), 0, array, 22, 4);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public string KiemTra_BangPhai_Xin_LienMinh(string 门派名字)
	{
		var num = 0;
		X_Mon_Phai_Lien_Minh_Trang_Thai xMonPhaiLienMinhTrangThai;
		while (true)
		{
			if (num < World.MonPhaiLienMinhTrangThai.Count)
			{
				xMonPhaiLienMinhTrangThai = World.MonPhaiLienMinhTrangThai[num];
				if (xMonPhaiLienMinhTrangThai.XinMonPhaiDanhDu == 门派名字)
				{
					break;
				}
				num++;
				continue;
			}
			return "";
		}
		return xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu;
	}
}
