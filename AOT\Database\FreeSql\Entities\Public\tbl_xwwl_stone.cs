﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_stone {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_stone_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? fld_type { get; set; }

		[JsonProperty]
		public int? fld_value { get; set; }

		[JsonProperty]
		public int? fld_tanggiam { get; set; }

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_stone_index_seq'::regclass)")]
		public short index { get; set; }

	}

}
