
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;

namespace RxjhServer
{
    public partial class Players
    {
        public async void ClaimCumulativeReward(byte[] data, int length)
        {
            try
            {
                if (World.CurrentCumulativeReward == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"No active cumulative reward template for {CharacterName}");
                    return;
                }

                // Parse milestone number từ packet data
                var milestoneNumber = data[10]; // Milestone number (1-10)

                if (milestoneNumber < 1 || milestoneNumber > 10)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Invalid milestone number {milestoneNumber} for {CharacterName}");
                    return;
                }

                // Kiểm tra xem player đã nhận milestone này chưa
                var claimedMilestones = await GetPlayerClaimedMilestones(CharacterName, World.CurrentCumulativeReward.Id);
                if (claimedMilestones.Contains(milestoneNumber))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Player {CharacterName} already claimed milestone {milestoneNumber}");
                    return;
                }

                // Tính cash đã sử dụng trong tháng
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                var monthStart = new DateTime(currentYear, currentMonth, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                var totalCashSpent = await GetPlayerCashSpentInMonth(CharacterName, monthStart, monthEnd);

                // Kiểm tra đủ điều kiện nhận thưởng
                var requiredCash = World.CurrentCumulativeReward.GetMilestoneCash(milestoneNumber);
                if (totalCashSpent < requiredCash)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Player {CharacterName} not enough cash for milestone {milestoneNumber}: {totalCashSpent}/{requiredCash}");
                    return;
                }

                // Lấy rewards cho milestone này
                var rewards = World.CurrentCumulativeReward.GetMilestoneRewards(milestoneNumber);
                if (rewards.Count == 0)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"No rewards found for milestone {milestoneNumber}");
                    return;
                }

                // Kiểm tra inventory có đủ chỗ không
                var requiredSlots = rewards.Count;
                var availableSlots = GetParcelVacancyNumber();
                if (availableSlots < requiredSlots)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Player {CharacterName} inventory full: need {requiredSlots}, have {availableSlots}");
                    HeThongNhacNho("Hành trang không đủ chỗ trống để nhận thưởng", 10, "CumulativeReward");
                    return;
                }

                // Thêm items vào inventory
                var success = true;
                foreach (var reward in rewards)
                {
                    var item = World.CreateAnItem(reward.ItemId, reward.ItemAmount);
                    var emptySlot = GetParcelVacancy(this);

                    if (emptySlot != -1)
                    {
                        AddItems(item.ItemGlobal_ID, item.VatPham_ID, emptySlot,
                                item.VatPhamSoLuong, item.VatPham_ThuocTinh);

                        LogHelper.WriteLine(LogLevel.Info,
                            $"{CharacterName} received cumulative reward milestone {milestoneNumber}: {reward.ItemId} x{reward.ItemAmount}");
                    }
                    else
                    {
                        success = false;
                        break;
                    }
                }

                if (success)
                {
                    // Lưu log nhận thưởng
                    await GameDb.SaveCumulativeRewardLog(CharacterName, World.CurrentCumulativeReward.Id, milestoneNumber, totalCashSpent);

                    LogHelper.WriteLine(LogLevel.Info,
                        $"Player {CharacterName} successfully claimed cumulative reward milestone {milestoneNumber}");
                    SendCumulativeRewardResponse(true, milestoneNumber); // Send response packet
                    HeThongNhacNho($"Đã nhận thưởng tích lũy mốc {milestoneNumber} thành công!", 10, "CumulativeReward");
                }
                else
                {
                    SendCumulativeRewardResponse(false, milestoneNumber); // Send response packet
                    LogHelper.WriteLine(LogLevel.Error, $"Failed to add all rewards for {CharacterName} milestone {milestoneNumber}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to process cumulative reward request for {CharacterName}: {ex.Message}");
            }
        }
        public void SendCumulativeRewardResponse(bool success, int step = 0)
        {
            try
            {
                SendingClass w = new();
                w.Write4(step);
                w.Write4(success ? 1 : 0);
                Client?.SendPak(w, 0x4703, SessionID, true);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to send cumulative reward response to {CharacterName}: {ex.Message}");
            }
        }
        private async void VerifyCumulativeRewardAsync()
        {
            try
            {
                if (World.CurrentCumulativeReward == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"No active cumulative reward template for {CharacterName}");
                    return;
                }

                var success = await HeroYulgang.RxjhServer.CumulativeReward.CumulativeRewardEvent.SendCumulativeRewardPacket(this);

                if (success)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Sent cumulative reward data to {CharacterName}");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Failed to send cumulative reward data to {CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to check cumulative reward for {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Tính tổng cash đã sử dụng của player trong khoảng thời gian
        /// </summary>
        private async Task<int> GetPlayerCashSpentInMonth(string playerName, DateTime startDate, DateTime endDate)
        {
            try
            {
                var totalSpent = await BBGDb.GetPlayerCashSpentInPeriod(playerName, startDate, endDate);
                return (int)totalSpent;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to get cash spent for {playerName}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Lấy danh sách milestone đã nhận của player
        /// </summary>
        private async Task<List<int>> GetPlayerClaimedMilestones(string playerName, int templateId)
        {
            try
            {
                return await GameDb.GetPlayerClaimedCumulativeRewardMilestones(playerName, templateId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to get claimed milestones for {playerName}: {ex.Message}");
                return new List<int>();
            }
        }
    }
}