using System;

namespace HeroYulgang.Services
{
    public enum ServerState
    {
        Offline,
        Starting,
        Online,
        Stopping
    }

    public class ServerStatus
    {
        private static ServerStatus? _instance;
        private ServerState _state = ServerState.Offline;
        private DateTime _lastStateChange = DateTime.Now;
        private string _statusMessage = "M<PERSON>y chủ đang offline";
        private TimeSpan _uptime = TimeSpan.Zero;
        private float _averageUpdateTimeMs = 0;
        private int _connectionCount = 0;
        private string _detailsMessage = "Chưa có thông tin";
        private bool _loginServerConnected = false;

        public static ServerStatus Instance => _instance ??= new ServerStatus();

        public ServerState State
        {
            get => _state;
            private set
            {
                if (_state != value)
                {
                    _state = value;
                    LastStateChange = DateTime.Now;
                    UpdateStatusMessage();

                }
            }
        }

        public DateTime LastStateChange
        {
            get => _lastStateChange;
            private set
            {
                _lastStateChange = value;
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            private set
            {
                _statusMessage = value;
            }
        }

        public TimeSpan Uptime
        {
            get => _uptime;
            private set
            {
                _uptime = value;
                UpdateDetailsMessage();
            }
        }

        public float AverageUpdateTimeMs
        {
            get => _averageUpdateTimeMs;
            private set
            {
                _averageUpdateTimeMs = value;
                UpdateDetailsMessage();
            }
        }

        public int ConnectionCount
        {
            get => _connectionCount;
            private set
            {
                _connectionCount = value;
                UpdateDetailsMessage();
            }
        }

        public string DetailsMessage
        {
            get => _detailsMessage;
            private set
            {
                _detailsMessage = value;
            }
        }

        public bool LoginServerConnected
        {
            get => _loginServerConnected;
            set
            {
                _loginServerConnected = value;
                UpdateDetailsMessage();
            }
        }



        private ServerStatus()
        {
            // Private constructor for singleton pattern
        }

        public void SetOffline()
        {
            State = ServerState.Offline;
            Logger.Instance.Info("Máy chủ hiện đang offline");
        }

        public void SetStarting()
        {
            State = ServerState.Starting;
            Logger.Instance.Info("Máy chủ đang khởi động...");
        }

        public void SetOnline()
        {
            State = ServerState.Online;
            Logger.Instance.Info("Máy chủ hiện đang online");
        }

        public void SetStopping()
        {
            State = ServerState.Stopping;
            Logger.Instance.Info("Máy chủ đang dừng...");
        }

        private void UpdateStatusMessage()
        {
            StatusMessage = State switch
            {
                ServerState.Offline => "Máy chủ đang offline",
                ServerState.Starting => "Máy chủ đang khởi động...",
                ServerState.Online => "Máy chủ đang hoạt động",
                ServerState.Stopping => "Máy chủ đang dừng...",
                _ => "Trạng thái không xác định"
            };
        }

        private void UpdateDetailsMessage()
        {
            if (State == ServerState.Online)
            {
                string loginServerStatus = LoginServerConnected ? "Đã kết nối" : "Chưa kết nối";
                DetailsMessage = $"Uptime: {Uptime:hh\\:mm\\:ss}, Avg Update: {AverageUpdateTimeMs:F2}ms, Kết nối: {ConnectionCount}, LoginServer: {loginServerStatus}";
            }
            else
            {
                DetailsMessage = "Chưa có thông tin";
            }
        }

        public void UpdateServerStats(TimeSpan uptime, float avgUpdateTimeMs, int connectionCount, bool loginServerConnected = false)
        {
            UpdateStatsInternal(uptime, avgUpdateTimeMs, connectionCount, loginServerConnected);
        }

        private void UpdateStatsInternal(TimeSpan uptime, float avgUpdateTimeMs, int connectionCount, bool loginServerConnected)
        {
            Uptime = uptime;
            AverageUpdateTimeMs = avgUpdateTimeMs;
            ConnectionCount = connectionCount;
            LoginServerConnected = loginServerConnected;
        }


    }
}
