using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.GroupQuest;

namespace RxjhServer.Quest
{
    /// <summary>
    /// Manager class để quản lý quest system
    /// </summary>
    public static class QuestManager
    {
        /// <summary>
        /// Khởi tạo quest system khi World được khởi động
        /// </summary>
        public static void Initialize()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Đang khởi tạo Quest System...");
                
                // Load dữ liệu quest từ file JSON
                QuestClass.LoadQuestData("quest_data.json");
                
                LogHelper.WriteLine(LogLevel.Info, $"Quest System đã được khởi tạo thành công với {QuestClass.QuestDictionary.Count} quest");
                
                // Log thông tin metadata
                if (QuestClass.Metadata != null)
                {
                    LogHelper.WriteLine(LogLevel.Info, $"Quest Data Version: {QuestClass.Metadata.Version}");
                    LogHelper.WriteLine(LogLevel.Info, $"Export Date: {QuestClass.Metadata.ExportDate}");
                    LogHelper.WriteLine(LogLevel.Info, $"Total Quests: {QuestClass.Metadata.TotalQuests}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi khởi tạo Quest System: {ex.Message}");
            }
        }

        public static void ReloadQuestData()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Đang reload Quest Data...");
                QuestClass.LoadQuestData("quest_data.json");
                LogHelper.WriteLine(LogLevel.Info, "Quest Data reloaded successfully");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi reload Quest Data: {ex.Message}");
            }
        }

        public static List<int> GuildQuestKillMonster = new()
        {
            12001,
            12002,
            12003
        };
        public static List<int> GuildQuestKillPlayer = new()
        {
            12003,
            12004,
            12005
        };
        public static List<int> GuildQuestKillBoss = new()
        {
            12006,
            12007
        };

        /// <summary>
        /// Kiểm tra player có thể nhận quest không
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="questId">ID của quest</param>
        /// <returns>True nếu có thể nhận quest</returns>
        public static (bool, int) CanAcceptQuest(Players player, int questId)
        {
            var quest = QuestClass.GetQuest(questId);
            if (quest == null)
            {
                player.HeThongNhacNho($"Không tìm thấy quest: {questId}", 7, "DEBUG");
                return (false, 0);
            }
            // handle guild quest
            // respone từ chối 12, 0x79

        

            // Kiểm tra các yêu cầu để nhận quest
            int i = 1;
            foreach (var requirement in quest.AcceptRequirements)
            {
                i++;
                if (!CheckRequirement(player, requirement))
                {
                    player.HeThongNhacNho($"Không đáp ứng yêu cầu: {requirement.Description}", 7, "DEBUG");
                    return (false, i);
                }
            }

            if (player.HasQuest(questId))
            {
                player.HeThongNhacNho($"Đã nhận quest rồi: {questId}", 7, "DEBUG");
                return (false, 0);
            }

            player.HeThongNhacNho($"Có thể nhận quest: {questId}", 7, "DEBUG");

            return (true, 0);
        }

        /// <summary>
        /// Kiểm tra player có thể hoàn thành quest không
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="questId">ID của quest</param>
        /// <returns>True nếu có thể hoàn thành quest</returns>
        public static bool CanCompleteQuest(Players player, int questId)
        {
            var quest = QuestClass.GetQuest(questId);
            if (quest == null)
            {
                return false;
            }

        

            // Kiểm tra player có quest này không
            if (!player.PlayerQuests.ContainsKey(questId))
            {
                return false;
            }

            // Kiểm tra các yêu cầu để hoàn thành quest
            foreach (var requirement in quest.CompletionRequirements)
            {
                if (!CheckRequirement(player, requirement))
                {
                    return false;
                }
            }

            return true;
        }
        

        /// <summary>
        /// Kiểm tra một yêu cầu cụ thể
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="requirement">Yêu cầu cần kiểm tra</param>
        /// <returns>True nếu đáp ứng yêu cầu</returns>
        private static bool CheckRequirement(Players player, QuestRequirement requirement)
        {
            switch (requirement.Type?.ToLower())
            {
                case "level":
                    // Kiểm tra level của player
                    player.HeThongNhacNho($"Kiểm tra cấp độ yêu cầu: {requirement.Value} - Cấp độ hiện tại: {player.Player_Level}", 7, "DEBUG");
                    return player.Player_Level >= requirement.Value;

                case "item":
                    if (requirement.ItemId.HasValue && requirement.ItemAmount.HasValue)
                    {
                        switch ((int)requirement.ItemId)
                        {
                            case ItemDef.Item.Money:
                            case ItemDef.Item.Money2:
                                player.HeThongNhacNho($"Kiểm tra số lượng tiền yêu cầu: {requirement.ItemAmount} - Item ID: {requirement.ItemId} - Số lượng hiện tại: {player.Player_Money}", 7, "DEBUG");
                                return player.Player_Money >= requirement.ItemAmount;
                            default:
                                var itemInfo = World.ItemList[(int)requirement.ItemId];
                                if (itemInfo == null)
                                {
                                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy vật phẩm yêu cầu: {requirement.ItemId}");
                                    return false;
                                }
                                if (itemInfo.FLD_QUESTITEM == 1)
                                {
                                    player.HeThongNhacNho($"Kiểm tra số lượng item yêu cầu: {requirement.ItemAmount} - Item ID: {requirement.ItemId} - Số lượng hiện tại: {player.FindQuestItemByItemID((int)requirement.ItemId)?.VatPhamSoLuong ?? 0}", 7, "DEBUG");
                                    return HasEnoughItems(player, requirement.ItemId.Value, requirement.ItemAmount.Value);
                                }
                                else
                                {
                                    var item = player.FindItemByItemID((int)requirement.ItemId);
                                    player.HeThongNhacNho($"Kiểm tra số lượng item yêu cầu: {requirement.ItemAmount} - Item ID: {requirement.ItemId}", 7, "DEBUG");
                                    return item != null && item.GetVatPhamSoLuong >= requirement.ItemAmount;
                                }
                        }
                    }
                    break;
                case "ability":
                    player.HeThongNhacNho($"Kiểm tra sử dụng hết điểm kỹ năng: {requirement.Value} - Điểm kỹ năng hiện tại: {player.Player_Qigong_point}", 7, "DEBUG");
                    return player.Player_Qigong_point == requirement.Value;

                case "job":
                    // Kiểm tra nghề của player 
                    player.HeThongNhacNho($"Kiểm tra nghề yêu cầu: {requirement.Value} - Nghề hiện tại: {player.Player_Job}", 7, "DEBUG");
                    return requirement.Value == 0 || player.Player_Job == requirement.Value;

                case "job_level":
                    // Kiểm tra level nghề của player 
                    player.HeThongNhacNho($"Kiểm tra cấp độ nghề yêu cầu: {requirement.Value} - Cấp độ hiện tại: {player.Player_Job_level}", 7, "DEBUG");
                    return player.Player_Job_level == requirement.Value;
                case "guild":
                    player.HeThongNhacNho($"Kiểm tra bang phái yêu cầu: {requirement.Value} - Bang phái hiện tại: {player.GuildName}", 7, "DEBUG");
                    return requirement.Value == 0 || player.GangLevel >= requirement.Value; // 0 là không cần bang phái
                case "guildRole":
                    player.HeThongNhacNho($"Kiểm tra vai trò bang phái yêu cầu: {requirement.Value}", 7, "DEBUG");
                    return requirement.Value == 0 || !World.IsGuildMaster(player) && !World.IsGuildViceMaster(player); // 0 là không cần vai trò
                default:
                    player.HeThongNhacNho($"Loại requirement không được hỗ trợ: {requirement.Type}", 7, "DEBUG");
                    LogHelper.WriteLine(LogLevel.Warning, $"Loại requirement không được hỗ trợ: {requirement.Type}");
                    return true; // Mặc định cho phép nếu không hiểu requirement
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra player có đủ item không
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="itemId">ID của item</param>
        /// <param name="amount">Số lượng cần</param>
        /// <returns>True nếu có đủ item</returns>
        private static bool HasEnoughItems(Players player, long itemId, long amount)
        {
            var item = player.FindQuestItemByItemID((int)itemId);
            if (item == null)
            {
                return false;
            }
            return item.VatPhamSoLuong >= amount;
        }


    }
}
