#!/usr/bin/env python3
import os
import re

def fix_file(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix tuple access patterns
        content = re.sub(r'(\w+)\.x\b', r'\1.Item1', content)
        content = re.sub(r'(\w+)\.y\b', r'\1.Item2', content)
        
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed tuple access: {filepath}")
        else:
            print(f"No tuple fixes needed: {filepath}")
            
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")

def main():
    files = [
        "E:/YulgangDev/YulgangHero/yulgang-hero/AOT/RxjhServer/AOI/AOISystem.cs",
        "E:/YulgangDev/YulgangHero/yulgang-hero/AOT/RxjhServer/Players/Players.Moving.cs",
        "E:/YulgangDev/YulgangHero/yulgang-hero/AOT/RxjhServer/Players/Players.Npc.cs"
    ]
    
    for filepath in files:
        filepath = filepath.replace('/', os.sep)
        if os.path.exists(filepath):
            fix_file(filepath)
        else:
            print(f"File not found: {filepath}")

if __name__ == "__main__":
    main()