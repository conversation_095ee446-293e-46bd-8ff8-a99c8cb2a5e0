
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer;

namespace HeroYulgang.RxjhServer.CumulativeReward
{
    public class CumulativeRewardEvent
    {
        // Singleton instance
        private static CumulativeRewardEvent _instance;
        private static readonly object _lock = new object();

        public static CumulativeRewardEvent Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new CumulativeRewardEvent();
                    }
                }
                return _instance;
            }
        }

        // Private constructor để prevent external instantiation
        private CumulativeRewardEvent() { }
        private static async Task<SendingClass> BuildCumulativeRewardPacket(Players player)
        {
            var currentReward = World.CurrentCumulativeReward;
            SendingClass w = new();

            if (currentReward == null)
            {
                return w;
            }

            try
            {
                // Tính tổng cash đã sử dụng trong tháng hiện tại
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                var monthStart = new DateTime(currentYear, currentMonth, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                var totalCashSpent = await GetPlayerCashSpentInMonth(player.CharacterName, monthStart, monthEnd);

                // Lấy danh sách milestone đã nhận
                var claimedMilestones = await GetPlayerClaimedMilestones(player.CharacterName, currentReward.Id);


                // Write packet data
                w.Write4(0); // Status/Header
                w.Write8(totalCashSpent); // Total cash spent
                w.Write4(claimedMilestones.Count); // Current milestone

                // Milliseconds left to end of event
                var millisecondsLeft = (int)Math.Max(0, (currentReward.EndDate - DateTime.Now).TotalSeconds);
                w.Write4(millisecondsLeft*1000);
                w.Write4(0); // Additional data

                // Write data for all 10 milestones
                for (int i = 1; i <= 10; i++)
                {
                    var isRewarded = claimedMilestones.Contains(i);
                    var milestoneNeeded = currentReward.GetMilestoneCash(i);
                    var isClaimable = totalCashSpent >= milestoneNeeded && !isRewarded;
                    w.Write4(isRewarded ? 1 : isClaimable ? 0 : 2); // Is claimable flag
                    w.Write4(milestoneNeeded); // Cash needed for this milestone

                    // Get rewards for this milestone
                    var rewards = currentReward.GetMilestoneRewards(i);

                    // Write rewards (max 6 per milestone)
                    for (int j = 0; j < 6; j++)
                    {
                        if (j < rewards.Count)
                        {
                            var reward = rewards[j];
                            w.Write8(reward.ItemId); // Item ID
                        }
                        else
                        {
                            w.Write8(0); // Empty slot - Item ID
                        }
                    }
                }
                w.Write4(totalCashSpent);

                return w;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to build cumulative reward packet for {player.CharacterName}: {ex.Message}");
                return w;
            }
        }

        /// <summary>
        /// Helper method để tính cash đã sử dụng trong tháng
        /// </summary>
        private static async Task<int> GetPlayerCashSpentInMonth(string playerName, DateTime startDate, DateTime endDate)
        {
            try
            {
                var totalSpent = await BBGDb.GetPlayerCashSpentInPeriod(playerName, startDate, endDate);
                return (int)totalSpent;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to get cash spent for {playerName}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Helper method để lấy danh sách milestone đã nhận
        /// </summary>
        private static async Task<List<int>> GetPlayerClaimedMilestones(string playerName, int templateId)
        {
            try
            {
                return await GameDb.GetPlayerClaimedCumulativeRewardMilestones(playerName, templateId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to get claimed milestones for {playerName}: {ex.Message}");
                return new List<int>();
            }
        }


        // Static method để backward compatibility
        public static async Task<bool> SendCumulativeRewardPacket(Players player)
        {
            return await Instance.SendCumulativeRewardPacketAsync(player);
        }

        // Instance method
        public async Task<bool> SendCumulativeRewardPacketAsync(Players player)
        {
            try
            {
                var w = await BuildCumulativeRewardPacket(player);
                player.Client?.SendPak(w, 0x4503, player.SessionID, true);
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to send cumulative reward packet to {player.CharacterName}: {ex.Message}");
                return false;
            }
        }

        
    }
}