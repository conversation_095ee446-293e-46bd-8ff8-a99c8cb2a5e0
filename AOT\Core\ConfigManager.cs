using System;
using System.IO;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using HeroYulgang.Services;

namespace HeroYulgang.Core
{
    public class ServerConfig
    {
        public string ServerName { get; set; } = "HeroGSPorted";
        public int ServerId { get; set; } = 1;
        public int GameServerPort { get; set; } = 13000;
        public int GrpcPort { get; set; } = 7000;
        public int MaximumOnline { get; set; } = 1000;
        public int AutomaticConnectionTime { get; set; } = 60;
    }

    public class LoginServerConfig
    {
        public string LoginServerIP { get; set; } = "127.0.0.1";
        public int LoginServerGrpcPort { get; set; } = 6999;
        public int ClusterId { get; set; } = 1;
    }

    public class AppSettingsConfig
    {
        public bool ShowPacketData { get; set; } = true;
        public string Environment { get; set; } = "Development";
    }

    public class NetworkingSettingsConfig
    {
        public bool UseNetCoreServer { get; set; } = false;
        public bool UseAkkaNetworking { get; set; } = true;
    }

    public class DatabaseConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;
        public string? BBGDb { get; set; } = null;
    }

    public class PogresConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;
        public string BBGDb { get; set; } = string.Empty;
    }

    public class ConfigManager
    {
        private static ConfigManager? _instance;
        private IConfiguration _configuration = null!;

        public ServerConfig ServerSettings { get; private set; }
        public DatabaseConfig ConnectionStrings { get; private set; }

        public PogresConfig PogresSettings { get; private set; }
        public LoginServerConfig LoginServerSettings { get; private set; }
        public AppSettingsConfig AppSettings { get; private set; }
        public NetworkingSettingsConfig NetworkingSettings { get; private set; }

        public static ConfigManager Instance => _instance ??= new ConfigManager();

        private ConfigManager()
        {
            // Khởi tạo cấu hình mặc định
            ServerSettings = new ServerConfig();
            ConnectionStrings = new DatabaseConfig();
            PogresSettings = new PogresConfig();
            LoginServerSettings = new LoginServerConfig();
            AppSettings = new AppSettingsConfig();
            NetworkingSettings = new NetworkingSettingsConfig();

            try
            {
                // Đọc cấu hình từ appsettings.json
                _configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Load all configurations from appsettings
                LoadConfigurationFromAppSettings();

                Logger.Instance.Info("Đã tải cấu hình từ appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải cấu hình: {ex.Message}");
            }
        }

        public void SaveConfig()
        {
            try
            {
                var configData = new
                {
                    ConnectionStrings = ConnectionStrings,
                    ServerSettings = ServerSettings,
                    LoginServerSettings = LoginServerSettings,
                    AppSettings = AppSettings,
                    NetworkingSettings = NetworkingSettings
                };

                string json = JsonSerializer.Serialize(configData, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText("appsettings.json", json);
                Logger.Instance.Info("Đã lưu cấu hình vào appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu cấu hình: {ex.Message}");
            }
        }

        public void LoadCustomAppSettings(string appSettingsPath)
        {
            try
            {
                if (File.Exists(appSettingsPath))
                {
                    Logger.Instance.Info($"Loading custom appsettings from: {appSettingsPath}");

                    // Get the directory of the appsettings file
                    string appSettingsDirectory = Path.GetDirectoryName(appSettingsPath) ?? Directory.GetCurrentDirectory();

                    // Rebuild configuration with custom appsettings path
                    _configuration = new ConfigurationBuilder()
                        .SetBasePath(appSettingsDirectory)
                        .AddJsonFile(Path.GetFileName(appSettingsPath), optional: false, reloadOnChange: true)
                        .Build();

                    // Reload all configurations from the new appsettings
                    LoadConfigurationFromAppSettings();

                    Logger.Instance.Info("Custom appsettings loaded successfully");
                }
                else
                {
                    Logger.Instance.Warning($"Custom appsettings file not found: {appSettingsPath}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error loading custom appsettings: {ex.Message}");
            }
        }

        /// <summary>
        /// Public method to reload configuration from appsettings
        /// </summary>
        public void ReloadConfiguration()
        {
            LoadConfigurationFromAppSettings();
        }

        private void LoadConfigurationFromAppSettings()
        {
            try
            {
                // Đọc cấu hình kết nối cơ sở dữ liệu
                var connectionSection = _configuration.GetSection("ConnectionStrings");
                if (connectionSection.Exists())
                {
                    ConnectionStrings.AccountDb = connectionSection["AccountDb"] ?? ConnectionStrings.AccountDb;
                    ConnectionStrings.GameDb = connectionSection["GameDb"] ?? ConnectionStrings.GameDb;
                    ConnectionStrings.PublicDb = connectionSection["PublicDb"] ?? ConnectionStrings.PublicDb;
                    ConnectionStrings.BBGDb = connectionSection["BBGDb"];
                }

                var connectionPostgres = _configuration.GetSection("PostgresConnectionStrings");
                if (connectionPostgres.Exists())
                {
                    PogresSettings.AccountDb = connectionPostgres["AccountDb"] ?? PogresSettings.AccountDb;
                    PogresSettings.GameDb = connectionPostgres["GameDb"] ?? PogresSettings.GameDb;
                    PogresSettings.PublicDb = connectionPostgres["PublicDb"] ?? PogresSettings.PublicDb;
                    PogresSettings.BBGDb = connectionPostgres["BBGDb"] ?? PogresSettings.BBGDb;
                }

                // Đọc cấu hình server
                var serverSection = _configuration.GetSection("ServerSettings");
                if (serverSection.Exists())
                {
                    ServerSettings.ServerName = serverSection["ServerName"] ?? ServerSettings.ServerName;
                    ServerSettings.ServerId = int.TryParse(serverSection["ServerID"], out int serverId) ? serverId : ServerSettings.ServerId;
                    ServerSettings.GameServerPort = int.TryParse(serverSection["GameServerPort"], out int port) ? port : ServerSettings.GameServerPort;
                    ServerSettings.GrpcPort = int.TryParse(serverSection["GrpcPort"], out int grpcPort) ? grpcPort : ServerSettings.GrpcPort;
                    ServerSettings.MaximumOnline = int.TryParse(serverSection["MaximumOnline"], out int maxOnline) ? maxOnline : ServerSettings.MaximumOnline;
                    ServerSettings.AutomaticConnectionTime = int.TryParse(serverSection["AutomaticConnectionTime"], out int autoTime) ? autoTime : ServerSettings.AutomaticConnectionTime;
                }

                // Đọc cấu hình login server
                var loginServerSection = _configuration.GetSection("LoginServerSettings");
                if (loginServerSection.Exists())
                {
                    LoginServerSettings.LoginServerIP = loginServerSection["LoginServerIP"] ?? LoginServerSettings.LoginServerIP;
                    LoginServerSettings.LoginServerGrpcPort = int.TryParse(loginServerSection["LoginServerGrpcPort"], out int grpcPort) ? grpcPort : LoginServerSettings.LoginServerGrpcPort;
                    LoginServerSettings.ClusterId = int.TryParse(loginServerSection["ClusterId"], out int clusterId) ? clusterId : LoginServerSettings.ClusterId;
                }

                // Đọc cấu hình ứng dụng
                var appSettingsSection = _configuration.GetSection("AppSettings");
                if (appSettingsSection.Exists())
                {
                    AppSettings.ShowPacketData = bool.TryParse(appSettingsSection["ShowPacketData"], out bool showPacketData) ? showPacketData : AppSettings.ShowPacketData;
                    AppSettings.Environment = appSettingsSection["Environment"] ?? AppSettings.Environment;
                }

                // Đọc cấu hình networking
                var networkingSection = _configuration.GetSection("NetworkingSettings");
                if (networkingSection.Exists())
                {
                    NetworkingSettings.UseNetCoreServer = bool.TryParse(networkingSection["UseNetCoreServer"], out bool useNetCore) ? useNetCore : NetworkingSettings.UseNetCoreServer;
                    NetworkingSettings.UseAkkaNetworking = bool.TryParse(networkingSection["UseAkkaNetworking"], out bool useAkka) ? useAkka : NetworkingSettings.UseAkkaNetworking;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error loading configuration from appsettings: {ex.Message}");
            }
        }

        public void LoadCustomConfig(string configPath)
        {
            try
            {
                if (File.Exists(configPath))
                {
                    Logger.Instance.Info($"Loading custom config from: {configPath}");

                    // Read and parse the custom config
                    var jsonContent = File.ReadAllText(configPath);
                    var customConfig = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                    // Update server settings from custom config
                    if (customConfig.TryGetProperty("gameserver", out var gameServerConfig))
                    {
                        if (gameServerConfig.TryGetProperty("serverid", out var serverIdElement))
                        {
                            if (int.TryParse(serverIdElement.GetString(), out int serverId))
                            {
                                ServerSettings.ServerId = serverId;
                                Logger.Instance.Info($"Updated ServerId to: {serverId}");
                            }
                        }

                        if (gameServerConfig.TryGetProperty("gameserverport", out var portElement))
                        {
                            if (int.TryParse(portElement.GetString(), out int port))
                            {
                                ServerSettings.GameServerPort = port;
                                Logger.Instance.Info($"Updated GameServerPort to: {port}");
                            }
                        }

                        if (gameServerConfig.TryGetProperty("servername", out var nameElement))
                        {
                            var serverName = nameElement.GetString();
                            if (!string.IsNullOrEmpty(serverName))
                            {
                                ServerSettings.ServerName = serverName;
                                Logger.Instance.Info($"Updated ServerName to: {serverName}");
                            }
                        }

                        if (gameServerConfig.TryGetProperty("maximumonline", out var maxOnlineElement))
                        {
                            if (int.TryParse(maxOnlineElement.GetString(), out int maxOnline))
                            {
                                ServerSettings.MaximumOnline = maxOnline;
                                Logger.Instance.Info($"Updated MaximumOnline to: {maxOnline}");
                            }
                        }

                        if (gameServerConfig.TryGetProperty("accountverificationserverip", out var loginIpElement))
                        {
                            var loginIp = loginIpElement.GetString();
                            if (!string.IsNullOrEmpty(loginIp))
                            {
                                LoginServerSettings.LoginServerIP = loginIp;
                                Logger.Instance.Info($"Updated LoginServerIP to: {loginIp}");
                            }
                        }

                        if (gameServerConfig.TryGetProperty("accountverificationserverport", out var loginPortElement))
                        {
                            if (int.TryParse(loginPortElement.GetString(), out int loginPort))
                            {
                                LoginServerSettings.LoginServerGrpcPort = loginPort;
                                Logger.Instance.Info($"Updated LoginServerGrpcPort to: {loginPort}");
                            }
                        }
                    }

                    Logger.Instance.Info("Custom config loaded successfully");
                }
                else
                {
                    Logger.Instance.Warning($"Custom config file not found: {configPath}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error loading custom config: {ex.Message}");
            }
        }
    }
}
