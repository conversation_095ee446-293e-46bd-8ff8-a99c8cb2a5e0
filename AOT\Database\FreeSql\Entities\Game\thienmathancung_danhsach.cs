﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class thienmathancung_danhsach {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('thienmathancung_danhsach_id_seq1'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string bang_chiem_thanh { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string ngay_chiem_thanh { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string cong_thanh_cuonghoa_level { get; set; }

		[JsonProperty]
		public DateTime? thoigian_lammoi_congthanh { get; set; }

	}

}
