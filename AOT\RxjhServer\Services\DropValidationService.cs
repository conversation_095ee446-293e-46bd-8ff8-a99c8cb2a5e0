using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Public;

namespace RxjhServer.Services
{
    /// <summary>
    /// Service for validating drop system data and migration accuracy
    /// </summary>
    public static class DropValidationService
    {
        /// <summary>
        /// Perform comprehensive validation of the drop system
        /// </summary>
        /// <returns>Validation result</returns>
        public static async Task<ValidationResult> ValidateDropSystem()
        {
            var result = new ValidationResult();

            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Starting Drop System Validation ===");

                // Validate configuration
                await ValidateConfiguration(result);

                // Validate drop data integrity
                await ValidateDropDataIntegrity(result);

                // Validate drop rates
                await ValidateDropRates(result);

                // Validate migration accuracy
                await ValidateMigrationAccuracy(result);

                // Performance validation
                await ValidatePerformance(result);

                LogHelper.WriteLine(LogLevel.Info, "=== Validation Complete ===");
                LogHelper.WriteLine(LogLevel.Info, $"Total Checks: {result.TotalChecks}");
                LogHelper.WriteLine(LogLevel.Info, $"Passed: {result.PassedChecks}");
                LogHelper.WriteLine(LogLevel.Info, $"Failed: {result.FailedChecks}");
                LogHelper.WriteLine(LogLevel.Info, $"Warnings: {result.Warnings.Count}");

                if (result.Errors.Any())
                {
                    LogHelper.WriteLine(LogLevel.Error, "Validation Errors:");
                    foreach (var error in result.Errors.Take(10))
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"  - {error}");
                    }
                }

                if (result.Warnings.Any())
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Validation Warnings:");
                    foreach (var warning in result.Warnings.Take(10))
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"  - {warning}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Validation failed: {ex.Message}");
                result.Errors.Add($"Validation failed: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Validate drop system configuration
        /// </summary>
        private static async Task ValidateConfiguration(ValidationResult result)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Validating configuration...");

                // Check if configuration table exists and has required keys
                var configs = await PublicDb._freeSql.Select<tbl_drop_config>().ToListAsync();
                result.TotalChecks++;

                var requiredKeys = new[]
                {
                    "new_drop_system_enabled",
                    "global_drop_multiplier",
                    "debug_drop_logging",
                    "max_drops_per_kill",
                    "level_gap_penalty",
                    "team_drop_bonus"
                };

                var missingKeys = requiredKeys.Where(key => !configs.Any(c => c.config_key == key)).ToList();

                if (missingKeys.Any())
                {
                    result.FailedChecks++;
                    result.Errors.Add($"Missing configuration keys: {string.Join(", ", missingKeys)}");
                }
                else
                {
                    result.PassedChecks++;
                }

                // Validate configuration values
                foreach (var config in configs)
                {
                    result.TotalChecks++;

                    if (ValidateConfigValue(config))
                    {
                        result.PassedChecks++;
                    }
                    else
                    {
                        result.FailedChecks++;
                        result.Errors.Add($"Invalid configuration value: {config.config_key} = {config.config_value}");
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Configuration validation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate drop data integrity
        /// </summary>
        private static async Task ValidateDropDataIntegrity(ValidationResult result)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Validating drop data integrity...");

                var drops = await PublicDb._freeSql.Select<tbl_new_drops>().ToListAsync();

                foreach (var drop in drops)
                {
                    result.TotalChecks++;

                    var errors = ValidateDropItem(drop);
                    if (errors.Any())
                    {
                        result.FailedChecks++;
                        result.Errors.AddRange(errors.Select(e => $"Drop {drop.id}: {e}"));
                    }
                    else
                    {
                        result.PassedChecks++;
                    }
                }

                // Check for duplicate drops
                result.TotalChecks++;
                var duplicates = drops
                    .GroupBy(d => new { d.source_type, d.source_value, d.item_id })
                    .Where(g => g.Count() > 1)
                    .ToList();

                if (duplicates.Any())
                {
                    result.FailedChecks++;
                    result.Errors.Add($"Found {duplicates.Count} duplicate drop entries");
                }
                else
                {
                    result.PassedChecks++;
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Data integrity validation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate drop rates and probabilities
        /// </summary>
        private static async Task ValidateDropRates(ValidationResult result)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Validating drop rates...");

                var drops = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.is_active == true)
                    .ToListAsync();

                // Check for reasonable drop rate distribution
                result.TotalChecks++;
                var highRateDrops = drops.Where(d => d.drop_rate > 0.5m).ToList();
                var veryHighRateDrops = drops.Where(d => d.drop_rate > 0.9m).ToList();

                if (veryHighRateDrops.Count > drops.Count * 0.1) // More than 10% have >90% drop rate
                {
                    result.Warnings.Add($"High number of very high drop rates: {veryHighRateDrops.Count} items with >90% drop rate");
                }

                if (highRateDrops.Count > drops.Count * 0.2) // More than 20% have >50% drop rate
                {
                    result.Warnings.Add($"High number of high drop rates: {highRateDrops.Count} items with >50% drop rate");
                }

                result.PassedChecks++;

                // Check level range coverage
                result.TotalChecks++;
                var levelRangeDrops = drops.Where(d => d.source_type == "level_range").ToList();
                var levelCoverage = new Dictionary<int, int>();

                foreach (var drop in levelRangeDrops)
                {
                    if (TryParseLevelRange(drop.source_value, out int min, out int max))
                    {
                        for (int level = min; level <= max; level++)
                        {
                            levelCoverage[level] = levelCoverage.GetValueOrDefault(level, 0) + 1;
                        }
                    }
                }

                var uncoveredLevels = Enumerable.Range(1, 100)
                    .Where(level => !levelCoverage.ContainsKey(level))
                    .ToList();

                if (uncoveredLevels.Any())
                {
                    result.Warnings.Add($"Levels with no drops: {string.Join(", ", uncoveredLevels.Take(10))}");
                }

                result.PassedChecks++;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Drop rate validation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate migration accuracy by comparing old and new systems
        /// </summary>
        private static async Task ValidateMigrationAccuracy(ValidationResult result)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Validating migration accuracy...");

                // Compare total counts
                result.TotalChecks++;
                var oldNormalCount = await PublicDb._freeSql.Select<tbl_xwwl_drop>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .CountAsync();

                var oldBossCount = await PublicDb._freeSql.Select<tbl_xwwl_bossdrop>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .CountAsync();

                var newCount = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.source_type == "level_range")
                    .CountAsync();

                var expectedCount = oldNormalCount + oldBossCount;
                var countDifference = Math.Abs(newCount - expectedCount);

                if (countDifference > expectedCount * 0.1) // More than 10% difference
                {
                    result.FailedChecks++;
                    result.Errors.Add($"Migration count mismatch: Expected ~{expectedCount}, got {newCount}");
                }
                else
                {
                    result.PassedChecks++;
                }

                // Sample validation - compare a few specific items
                result.TotalChecks++;
                var sampleValidationPassed = await ValidateSampleMigration();
                if (sampleValidationPassed)
                {
                    result.PassedChecks++;
                }
                else
                {
                    result.FailedChecks++;
                    result.Errors.Add("Sample migration validation failed");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Migration accuracy validation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate system performance characteristics
        /// </summary>
        private static async Task ValidatePerformance(ValidationResult result)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Validating performance...");

                // Check total number of drop rules
                result.TotalChecks++;
                var totalDrops = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.is_active == true)
                    .CountAsync();

                if (totalDrops > 50000) // Arbitrary threshold
                {
                    result.Warnings.Add($"Large number of drop rules ({totalDrops}) may impact performance");
                }

                result.PassedChecks++;

                // Check for overly complex level ranges
                result.TotalChecks++;
                var complexRanges = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.source_type == "level_range")
                    .ToListAsync();

                var overlappingRanges = 0;
                foreach (var drop1 in complexRanges)
                {
                    foreach (var drop2 in complexRanges)
                    {
                        if (drop1.id != drop2.id && 
                            TryParseLevelRange(drop1.source_value, out int min1, out int max1) &&
                            TryParseLevelRange(drop2.source_value, out int min2, out int max2))
                        {
                            if (RangesOverlap(min1, max1, min2, max2))
                            {
                                overlappingRanges++;
                            }
                        }
                    }
                }

                if (overlappingRanges > totalDrops * 0.5) // More than 50% overlap
                {
                    result.Warnings.Add($"High number of overlapping level ranges may impact performance");
                }

                result.PassedChecks++;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Performance validation error: {ex.Message}");
            }
        }

        #region Helper Methods

        /// <summary>
        /// Validate a single configuration value
        /// </summary>
        private static bool ValidateConfigValue(tbl_drop_config config)
        {
            switch (config.config_key?.ToLower())
            {
                case "new_drop_system_enabled":
                case "debug_drop_logging":
                    return bool.TryParse(config.config_value, out _);

                case "global_drop_multiplier":
                case "level_gap_penalty":
                case "team_drop_bonus":
                    return decimal.TryParse(config.config_value, out var decimalVal) && 
                           decimalVal >= 0m && decimalVal <= 100m;

                case "max_drops_per_kill":
                    return int.TryParse(config.config_value, out var intVal) && 
                           intVal >= 1 && intVal <= 50;

                default:
                    return true; // Unknown keys are allowed
            }
        }

        /// <summary>
        /// Validate a single drop item
        /// </summary>
        private static List<string> ValidateDropItem(tbl_new_drops drop)
        {
            var errors = new List<string>();

            // Check required fields
            if (string.IsNullOrEmpty(drop.source_type))
                errors.Add("Missing source_type");

            if (string.IsNullOrEmpty(drop.source_value))
                errors.Add("Missing source_value");

            if (drop.item_id <= 0)
                errors.Add("Invalid item_id");

            // Check drop rate
            if (drop.drop_rate <= 0 || drop.drop_rate > 1)
                errors.Add($"Invalid drop_rate: {drop.drop_rate}");

            // Check quantities
            if (drop.quantity_min < 1)
                errors.Add("quantity_min must be >= 1");

            if (drop.quantity_max < drop.quantity_min)
                errors.Add("quantity_max must be >= quantity_min");

            // Check source type specific validation
            switch (drop.source_type)
            {
                case "level_range":
                    if (!TryParseLevelRange(drop.source_value, out int min, out int max))
                        errors.Add($"Invalid level range format: {drop.source_value}");
                    else if (min < 1 || max > 300 || min > max)
                        errors.Add($"Invalid level range values: {min}-{max}");
                    break;

                case "npc_specific":
                case "quest_based":
                    if (!int.TryParse(drop.source_value, out int id) || id <= 0)
                        errors.Add($"Invalid {drop.source_type} ID: {drop.source_value}");
                    break;

                default:
                    errors.Add($"Unknown source_type: {drop.source_type}");
                    break;
            }

            return errors;
        }

        /// <summary>
        /// Parse level range string "min-max"
        /// </summary>
        private static bool TryParseLevelRange(string rangeValue, out int min, out int max)
        {
            min = max = 0;

            if (string.IsNullOrEmpty(rangeValue))
                return false;

            var parts = rangeValue.Split('-');
            if (parts.Length != 2)
                return false;

            return int.TryParse(parts[0], out min) && int.TryParse(parts[1], out max);
        }

        /// <summary>
        /// Check if two ranges overlap
        /// </summary>
        private static bool RangesOverlap(int min1, int max1, int min2, int max2)
        {
            return min1 <= max2 && min2 <= max1;
        }

        /// <summary>
        /// Validate a sample of migrated items
        /// </summary>
        private static async Task<bool> ValidateSampleMigration()
        {
            try
            {
                // Take a few sample items from old system and verify they exist in new system
                var sampleOldDrops = await PublicDb._freeSql.Select<tbl_xwwl_drop>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .Take(10)
                    .ToListAsync();

                foreach (var oldDrop in sampleOldDrops)
                {
                    var expectedSourceValue = $"{oldDrop.fld_level1}-{oldDrop.fld_level2}";
                    var newDrop = await PublicDb._freeSql.Select<tbl_new_drops>()
                        .Where(d => d.source_type == "level_range" && 
                                   d.source_value == expectedSourceValue && 
                                   d.item_id == oldDrop.fld_pid)
                        .FirstAsync();

                    if (newDrop == null)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"Sample validation failed: Item {oldDrop.fld_pid} not found in new system");
                        return false;
                    }

                    // Check if drop rate conversion is approximately correct
                    var expectedRate = (decimal)oldDrop.fld_pp / 8000m;
                    var rateDifference = Math.Abs(newDrop.drop_rate - expectedRate);
                    
                    if (rateDifference > 0.001m) // Allow small floating point differences
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"Sample validation failed: Drop rate mismatch for item {oldDrop.fld_pid}");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Sample validation error: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// Validation result summary
    /// </summary>
    public class ValidationResult
    {
        public int TotalChecks { get; set; }
        public int PassedChecks { get; set; }
        public int FailedChecks { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        public bool IsValid => FailedChecks == 0;
        public double SuccessRate => TotalChecks > 0 ? (double)PassedChecks / TotalChecks : 0;
    }
}
