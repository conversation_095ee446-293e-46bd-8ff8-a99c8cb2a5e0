using System;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Than_Nu_Di_Thuong_Trang_Thai_Loai : IDisposable
{
	public System.Timers.Timer npcyd;

	public System.Timers.Timer yczt;

	public double ycztsl;

	public DateTime time;

	public Players Play;

	public Players Play_gayDame_Player;

	public NpcClass Npc;

	public int NpcPlayId;

	private int _FLD_PID;

	private double _FLD_NUM;

	public double TonThuong;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_NUM
	{
		get
		{
			return _FLD_NUM;
		}
		set
		{
			_FLD_NUM = value;
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-Dispose");
		}
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (yczt != null)
		{
			yczt.Enabled = false;
			yczt.Close();
			yczt.Dispose();
			yczt = null;
		}
		Play = null;
		Npc = null;
	}

	public X_Than_Nu_Di_Thuong_Trang_Thai_Loai(Players Play_, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong, double TonThuong)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-NEW");
		}
		FLD_PID = DiThuong_ID;
		FLD_NUM = DiThuong_SoLuong;
		this.TonThuong = TonThuong;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
	}

	public X_Than_Nu_Di_Thuong_Trang_Thai_Loai(NpcClass Npc_, int _NpcPlayId, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong, Players _Npc)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-NEW");
		}
		NpcPlayId = _NpcPlayId;
		FLD_PID = DiThuong_ID;
		FLD_NUM = DiThuong_SoLuong;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Npc = Npc_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		Play_gayDame_Player = _Npc;
		StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "ThoiGianKetThucSuKien1");
		}
		ThoiGianKetThucSuKien();
	}

	public void ThoiGianKetThucSuKien()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (Play != null && !Play.Client.TreoMay)
		{
			if (!Play.Exiting && Play.Client.Running)
			{
				try
				{
					switch (FLD_PID)
					{
					case 52:
						Play.FLD_ThanNu_ThemVao_CongKich -= (int)TonThuong;
						Play.FLD_ThanNu_ThemVao_PhongNgu -= (int)TonThuong;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 34:
						Play.FLD_ThanNu_ThemVao_CongKich += (int)TonThuong;
						Play.FLD_ThanNu_ThemVao_PhongNgu += (int)TonThuong;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 35:
						Play.FLD_ThanNu_ThemVao_CongKich += (int)TonThuong;
						Play.FLD_ThanNu_ThemVao_PhongNgu += (int)TonThuong;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 42:
						Play.ThuocTinh_PhongAn = 0;
						break;
					}
					Play.ThanNu_TrangThai_BatThuong.Remove(FLD_PID);
					TrangThaiHieuQua2(FLD_PID, 0, 0, 0);
					Dispose();
					return;
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "TrangThai_BatThuongClass ThoiGianKetThucSuKien  error：[" + FLD_PID + "]" + ex);
					return;
				}
				finally
				{
					Dispose();
				}
			}
			if (Play.ThanNu_TrangThai_BatThuong != null)
			{
				Play.ThanNu_TrangThai_BatThuong.Clear();
			}
			Dispose();
		}
		else
		{
			Dispose();
		}
	}

	public void TrangThaiHieuQua2(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-TrangThaiHieuQua");
		}
		var array = Converter.HexStringToByte("AA553E00250040153800000000002500000034000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
		if (Play != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.Send_Map_Data(array, array.Length);
			}
			Play.SendCurrentRangeBroadcastData(array, array.Length);
		}
	}

	public void StatusEffect(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-TrangThaiHieuQua");
		}
		var array = Converter.HexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 58, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 62, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(ThoiGian), 0, array, 38, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_SoLuong), 0, array, 42, 4);
		if (Play != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.SendMultiplePackage(array, array.Length);
			}
			Play.Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
		}
		else if (Npc != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.NPC_SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.NPC_SessionID), 0, array, 4, 2);
			Npc.QuangBaSoLieu(array, array.Length);
		}
	}
}
