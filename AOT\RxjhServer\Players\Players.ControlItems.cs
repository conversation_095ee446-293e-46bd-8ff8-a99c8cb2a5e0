﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
	public void PickUpItems(byte[] data, int length)
	{
		// Xử lý packet và lấy ID của item cần nhặt
		PacketModification(data, length);
		var targetItemGlobalId = BitConverter.ToInt64(data, 10);

		// Kiểm tra điều kiện cơ bản của player
		bool flowControl = HandlePickItem(targetItemGlobalId);
		if (!flowControl)
		{
			return;
		}

	}

	public bool HandlePickItem(long targetItemGlobalId)
	{
		if (NhanVat_HP <= 0 || PlayerTuVong || Exiting || GiaoDich.GiaoDichBenTrong || OpenWarehouse || InTheShop)
		{
			HeThongNhacNho("Trạng thái nhân vật không thể nhặt vật phẩm!!", 20, "Thiên cơ các");
			return false;
		}

		// Kiểm tra trạng thái cưỡi thần thú
		if (CharacterBeast != null && CharacterBeast.CuoiThu == 1)
		{
			HeThongNhacNho("Không thể nhặt đồ khi cưỡi thú!", 20, "Thiên cơ các");
			return false;
		}

		// Kiểm tra xem item đã có trong túi chưa
		for (int bagSlotIndex = 0; bagSlotIndex < 96; bagSlotIndex++)
		{
			var currentItemGlobalId = BitConverter.ToInt64(Item_In_Bag[bagSlotIndex].ItemGlobal_ID, 0);
			if (currentItemGlobalId != 0L && currentItemGlobalId == targetItemGlobalId)
			{
				HeThongNhacNho("Hành trang không đủ chỗ trống!!", 20, "Thiên cơ các");
				return false;
			}
		}

		// Xử lý logic nhặt item
		try
		{
			using (new Lock(World.locklist2, "Nhat_VP"))
			{
				var groundItem = GroundItem.GetItme(targetItemGlobalId);
				if (groundItem == null)
				{
					HeThongNhacNho("Vật phẩm đã bị người khác nhặt!!", 20, "Thiên cơ các");
					return false;
				}

				// Xác định player sẽ nhận item (có thể là bản thân hoặc team member)
				Players targetPlayer = this;
				bool canPickUpItem = false;

				// Kiểm tra quyền ưu tiên nhặt item
				if (groundItem.ItemPriority == null || groundItem.ItemPriority == this)
				{
					// Player có quyền nhặt trực tiếp
					canPickUpItem = true;
				}
				else if (MapID != 801 && World.BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu != 1)
				{
					// Kiểm tra quyền nhặt trong team
					bool isTeamMemberHasPriority = false;

					if (TeamID != 0)
					{
						if (World.WToDoi.TryGetValue(TeamID, out var teamData))
						{
							// Kiểm tra xem có team member nào có priority không
							foreach (var teamMember in teamData.PartyPlayers.Values)
							{
								if (teamMember == groundItem.ItemPriority)
								{
									isTeamMemberHasPriority = true;
									break;
								}
							}

							if (isTeamMemberHasPriority)
							{
								canPickUpItem = true;
							}
							else
							{
								PickUpItemReminder(5, targetItemGlobalId);
								return false;
							}
						}
						else
						{
							PickUpItemReminder(5, targetItemGlobalId);
							return false;
						}
					}
					else
					{
						PickUpItemReminder(5, targetItemGlobalId);
						return false;
					}
				}
				else
				{
					// Trường hợp đặc biệt: MapID == 801 hoặc BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu == 1
					canPickUpItem = true;
				}

				// Nếu có quyền nhặt, xử lý phân phối trong team
				if (canPickUpItem && TeamID != 0)
				{
					targetPlayer = ToDoi_PhanPhoi(this);
					if (targetPlayer == null && MapID != 801 && World.BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu != 1)
					{
						PickUpItemReminder(5, targetItemGlobalId);
						return false;
					}
					// Nếu targetPlayer vẫn null nhưng điều kiện đặc biệt được thỏa mãn, sử dụng player hiện tại
					if (targetPlayer == null)
					{
						targetPlayer = this;
					}
				}

				if (!canPickUpItem)
				{
					PickUpItemReminder(5, targetItemGlobalId);
					return false;
				}

				// Kiểm tra trọng lượng trước khi nhặt
				if (groundItem.Item.VatPham_TongTrongLuong + targetPlayer.CharacterCurrentWeight >= targetPlayer.TheTotalWeightOfTheCharacter)
				{
					targetPlayer.PickUpItemReminder(2, targetItemGlobalId);
					return false;
				}

				// Tìm slot trống trong túi
				var availableSlotIndex = GetParcelVacancy(targetPlayer);
				if (availableSlotIndex == -1)
				{
					PickUpItemReminder(7, targetItemGlobalId);
					return false;
				}

				// Lấy thông tin item từ database
				if (!World.ItemList.TryGetValue(BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), out var itemData))
				{
					return false;
				}

				// Xử lý logic stack item (nếu item có thể stack)
				var itemSide = itemData.FLD_SIDE;
				var itemQuantity = groundItem.Item.VatPhamSoLuong;

				if (itemSide != 0)
				{
					// Tìm item cùng loại đã có trong túi để stack
					if (itemData.FLD_TYPE == 205)
					{
						var existingStackableItem = targetPlayer.GetEventBagItemByItemId(BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), groundItem.Item.FLD_MAGIC0);
						if (existingStackableItem != null)
						{
							availableSlotIndex = existingStackableItem.VatPhamViTri;
							itemQuantity = BitConverter.GetBytes(BitConverter.ToInt32(groundItem.Item.VatPhamSoLuong, 0) + BitConverter.ToInt32(existingStackableItem.VatPhamSoLuong, 0));
						}
					}
					else
					{
						var existingStackableItem = targetPlayer.GetCharacterBagItemByItemId(BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), groundItem.Item.FLD_MAGIC0);
						if (existingStackableItem != null)
						{
							availableSlotIndex = existingStackableItem.VatPhamViTri;
							itemQuantity = BitConverter.GetBytes(BitConverter.ToInt32(groundItem.Item.VatPhamSoLuong, 0) + BitConverter.ToInt32(existingStackableItem.VatPhamSoLuong, 0));
						}
					}
				}

				// Kiểm tra item vẫn còn tồn tại trước khi xóa
				if (!World.GroundItemList.ContainsKey(targetItemGlobalId))
				{
					return false;
				}

				// Xóa item khỏi ground và thêm vào túi player
				World.GroundItemList.Remove(targetItemGlobalId);
				targetPlayer.PickUpGetItems(availableSlotIndex, itemQuantity, groundItem.Item.ItemGlobal_ID, groundItem.Item.VatPham_ID, groundItem.Item.VatPham_ThuocTinh, groundItem.ItemDropMapID);

				// Xử lý thông báo và logging cho Boss Drop items
				var bossDropClass = World.BossDrop.FindAll((DropClass x) => x.FLD_PID == BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0)).FirstOrDefault();
				if (bossDropClass != null && bossDropClass.CoMoThongBao > 0 && groundItem.ItemDropMapID == 0 && !targetPlayer.Client.TreoMay)
				{
					//var text = " [" + targetPlayer.UserName + "] nh泸t [" + bossDropClass.FLD_NAME + "] ta騣 [K阯h " + World.ServerID + "] ba襫 痿 [" + X_Toa_Do_Class.getmapname(targetPlayer.NhanVatToaDo_BanDo) + "]";
					//World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text);
					// var logText = "[" + AccountID + "][" + CharacterName + "] ON/OFF:[" + Offline_TreoMay_Mode_ON_OFF + "][" + bossDropClass.FLD_NAME + "] Code:[" + bossDropClass.FLD_PID + "] Global:[" + groundItem.Item.GetItemGlobal_ID + "] map [" + X_Toa_Do_Class.getmapname(MapID) + "]";
					// logo.Log_Drop_Nhat_Item_Hiem(logText);
				}
				// Xử lý thông báo và logging cho Drop items thường
				var normalDropClass = World.Drop.FindAll((DropClass x) => x.FLD_PID == BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0)).FirstOrDefault();
				if (normalDropClass != null)
				{
					if (World.CoHayKo_Drop_CoMoThongBao != 0 && normalDropClass.CoMoThongBao > 0 && groundItem.ItemDropMapID == 0 && !targetPlayer.Client.TreoMay)
					{
						//var text2 = " [" + targetPlayer.UserName + "] nh泸t [" + normalDropClass.FLD_NAME + "] ta騣 [K阯h " + World.ServerID + "] c忪p 痿 [" + normalDropClass.FLD_LEVEL1 + "~" + normalDropClass.FLD_LEVEL2 + "] ba襫 痿 [" + X_Toa_Do_Class.getmapname(targetPlayer.NhanVatToaDo_BanDo) + "]";
						//World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text2);
						// var logText2 = "[" + AccountID + "][" + CharacterName + "] ON/OFF:[" + Offline_TreoMay_Mode_ON_OFF + "][" + normalDropClass.FLD_NAME + "] Code:[" + normalDropClass.FLD_PID + "] Global:[" + groundItem.Item.GetItemGlobal_ID + "] map [" + X_Toa_Do_Class.getmapname(MapID) + "]";
						// logo.Log_Drop_Nhat_Item_Hiem(logText2);
					}

					if (groundItem.ItemDropMapID == 0)
					{
						GameDb.DropRecord(targetPlayer.AccountID, targetPlayer.CharacterName, BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), BitConverter.ToInt32(groundItem.Item.VatPhamSoLuong, 0), targetPlayer.MapID, (int)targetPlayer.PosX, (int)targetPlayer.PosY);
						//RxjhClass.DropRecord(targetPlayer.AccountID, targetPlayer.CharacterName, BitConverter.ToInt64(groundItem.Item.ItemGlobal_ID, 0), BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), groundItem.Item.GetItemName(), groundItem.Item.FLD_MAGIC0, groundItem.Item.FLD_MAGIC1, groundItem.Item.FLD_MAGIC2, groundItem.Item.FLD_MAGIC3, groundItem.Item.FLD_MAGIC4, targetPlayer.MapID, (int)targetPlayer.PosX, (int)targetPlayer.PosY, "掉落");

						// Thông báo cho team members
						if (TeamID != 0 && World.WToDoi.TryGetValue(TeamID, out var teamData))
						{
							foreach (var teamMember in teamData.PartyPlayers.Values)
							{
								if (teamMember.SessionID != targetPlayer.SessionID)
								{
									teamMember.HeThongNhacNho("Đại hiệp nhặt được [" + normalDropClass.FLD_NAME + "]", 2, targetPlayer.CharacterName ?? "");
								}
							}
						}
					}
				}
				// Xử lý thông báo và logging cho DCH Drop items (map đặc biệt 40101)
				var dchDropClass = World.DCH_Drop.FindAll((DropClass x) => x.FLD_PID == BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0)).FirstOrDefault();
				if (dchDropClass != null && targetPlayer.MapID == 40101)
				{
					if (World.CoHayKo_Drop_CoMoThongBao != 0 && dchDropClass.CoMoThongBao > 0 && groundItem.ItemDropMapID == 0 && !targetPlayer.Client.TreoMay)
					{
						//var text3 = " [" + targetPlayer.UserName + "] nh泸t [" + dchDropClass.FLD_NAME + "] ta騣 [K阯h " + World.ServerID + "] ba襫 痿 [" + X_Toa_Do_Class.getmapname(targetPlayer.NhanVatToaDo_BanDo) + "]";
						//World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text3);
						// var logText3 = "[" + AccountID + "][" + CharacterName + "] ON/OFF:[" + Offline_TreoMay_Mode_ON_OFF + "][" + dchDropClass.FLD_NAME + "] Code:[" + dchDropClass.FLD_PID + "] Global:[" + groundItem.Item.GetItemGlobal_ID + "] map [" + X_Toa_Do_Class.getmapname(MapID) + "]";
						// logo.Log_Drop_Nhat_Item_Hiem(logText3);
					}

					if (groundItem.ItemDropMapID == 0)
					{
						GameDb.DropRecord(targetPlayer.AccountID, targetPlayer.CharacterName, BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), BitConverter.ToInt32(groundItem.Item.VatPhamSoLuong, 0), targetPlayer.MapID, (int)targetPlayer.PosX, (int)targetPlayer.PosY);
						//RxjhClass.DropRecord(targetPlayer.AccountID, targetPlayer.CharacterName, BitConverter.ToInt64(groundItem.Item.ItemGlobal_ID, 0), BitConverter.ToInt32(groundItem.Item.VatPham_ID, 0), groundItem.Item.GetItemName(), groundItem.Item.FLD_MAGIC0, groundItem.Item.FLD_MAGIC1, groundItem.Item.FLD_MAGIC2, groundItem.Item.FLD_MAGIC3, groundItem.Item.FLD_MAGIC4, targetPlayer.MapID, (int)targetPlayer.PosX, (int)targetPlayer.PosY, "掉落");

						// Thông báo cho team members (lưu ý: sử dụng normalDropClass.FLD_NAME như trong code gốc)
						if (TeamID != 0 && World.WToDoi.TryGetValue(TeamID, out var teamData2))
						{
							foreach (var teamMember in teamData2.PartyPlayers.Values)
							{
								if (teamMember.SessionID != targetPlayer.SessionID)
								{
									teamMember.HeThongNhacNho("Đại hiệp nhặt được [" + normalDropClass.FLD_NAME + "]", 2, targetPlayer.CharacterName ?? "");
								}
							}
						}
					}
				}

				// Cleanup và cập nhật dữ liệu player
				groundItem.npcydtheout();
				targetPlayer.UpdateMoneyAndWeight();
				targetPlayer.CheckBackpackCopy(targetPlayer);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đón Vat Pham error [" + AccountID + "][" + CharacterName + "] ID[" + targetItemGlobalId + "] " + ex.Message);
		}

		return true;
	}
	
	public void ThrowItems(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			if (NhanVat_HP <= 0 || PlayerTuVong || Exiting || GiaoDich.GiaoDichBenTrong || OpenWarehouse || InTheShop || World.BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu == 1)
			{
				HeThongNhacNho("Không thể thực hiện vào thời điểm này!!", 10, "Thiên cơ các");
				return;
			}
			if (MapID == 101 || MapID == 801)
			{
				HeThongNhacNho("Bản đồ này không thể vứt bỏ bảo vật!!", 10, "Thiên cơ các");
				return;
			}
			int num = packetData[36];
			var num2 = BitConverter.ToInt32(packetData, 18);
			var num3 = BitConverter.ToInt32(packetData, 26);
			if (num3 <= 0 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0 || BitConverter.ToInt32(Item_In_Bag[num].VatPhamSoLuong, 0) == 0 || num2 == ********** || !World.ItemList.TryGetValue(num2, out var value) || value.FLD_QUESTITEM == 1)
			{
				return;
			}
			if (value.FLD_LOCK == 1)
			{
				HeThongNhacNho("Bảo vật bị khóa, không thể vứt bỏ!", 10, "Thiên cơ các");
				return;
			}
			if (Item_In_Bag[num].VatPham_KhoaLai)
			{
				return;
			}
			var array = new byte[World.Item_Db_Byte_Length];
			var array2 = new byte[8];
			Buffer.BlockCopy(Item_In_Bag[num].VatPham_byte, 0, array, 0, World.Item_Db_Byte_Length);
			Buffer.BlockCopy(array, 0, array2, 0, 8);
			if (Item_In_Bag[num].VatPhamLoaiHinh == 0)
			{
				ThrowingItemTips(num, BitConverter.ToInt32(Item_In_Bag[num].VatPhamSoLuong, 0));
				SubtractItem(num, BitConverter.ToInt32(Item_In_Bag[num].VatPhamSoLuong, 0));
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 12, 4);
				GroundItem xMatDatVatPhamLoai = new(array, PosX, PosY, PosZ, MapID, this, 1);
				if (!World.GroundItemList.TryGetValue(BitConverter.ToInt64(array2, 0), out var value2))
				{
					World.GroundItemList.Add(BitConverter.ToInt64(array2, 0), xMatDatVatPhamLoai);
					xMatDatVatPhamLoai.AddToAOI();

				}
				if (World.GroundItemList.TryGetValue(BitConverter.ToInt64(array2, 0), out value2))
				{
					xMatDatVatPhamLoai.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
				}
				// var txt = "[" + AccountID + "][" + CharacterName + "]--Code:[" + num2 + "]--Global:[" + value2.Item.GetItemGlobal_ID + "]--SL:[" + num3 + "]--TT1:[" + value.FLD_MAGIC0 + "]--TT2:[" + value.FLD_MAGIC1 + "]--TT3:[" + value.FLD_MAGIC2 + "]--TT4:[" + value.FLD_MAGIC3 + "]--TT5:[" + value.FLD_MAGIC4 + "]";
				// logo.Log_vut_item(txt, UserName);
			}
			else
			{
				if (BitConverter.ToInt32(Item_In_Bag[num].VatPhamSoLuong, 0) <= num3)
				{
					num3 = BitConverter.ToInt32(Item_In_Bag[num].VatPhamSoLuong, 0);
				}
				else
				{
					array2 = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					Buffer.BlockCopy(array2, 0, array, 0, 8);
				}
				try
				{
					ThrowingItemTips(num, num3);
					SubtractItem(num, num3);
					Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, array, 12, 4);
					GroundItem xMatDatVatPhamLoai2 = new(array, PosX, PosY, PosZ, MapID, this, 1);
					if (!World.GroundItemList.TryGetValue(BitConverter.ToInt64(array2, 0), out var value3))
					{
						World.GroundItemList.Add(BitConverter.ToInt64(array2, 0), xMatDatVatPhamLoai2);
						xMatDatVatPhamLoai2.AddToAOI();

					}
					if (World.GroundItemList.TryGetValue(BitConverter.ToInt64(array2, 0), out value3))
					{
						xMatDatVatPhamLoai2.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
					}
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Ném VatPham error 22 [" + AccountID + "][" + CharacterName + "]  " + ex.Message);
				}
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "Ném VatPham error 33 [" + AccountID + "][" + CharacterName + "]  " + ex2.Message);
		}
		UpdateMoneyAndWeight();
	}


	public void ThrowingItemTips(int position, int soLuong)
	{
		var array = Converter.HexStringToByte("AA551E0000000F00100001000000010000001100000001000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(position), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}


	public void SubtractItem(int position, int soLuong)
	{
		try
		{
			if (soLuong < 0)
			{
				return;
			}
			using (SendingClass sendingClass = new())
			{
				sendingClass.Write1(1);
				sendingClass.Write1(position);
				sendingClass.Write2(0);
				sendingClass.Write(Item_In_Bag[position].GetVatPham_ID);
				sendingClass.Write8(soLuong);
				sendingClass.Write8(0L);
				Client?.SendPak(sendingClass, 8704, SessionID);
			}
			if (BitConverter.ToInt32(Item_In_Bag[position].VatPhamSoLuong, 0) <= soLuong)
			{
				Item_In_Bag[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				return;
			}
			var bytes = BitConverter.GetBytes(BitConverter.ToInt32(Item_In_Bag[position].VatPhamSoLuong, 0) - soLuong);
			var fLdMagic = Item_In_Bag[position].FLD_MAGIC0;
			Item_In_Bag[position].VatPhamSoLuong = BitConverter.GetBytes(0);
			if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[position].VatPham_ID, 0), out var value))
			{
				var array = new byte[56];
				Buffer.BlockCopy(BitConverter.GetBytes(fLdMagic), 0, array, 0, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC1), 0, array, 4, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC2), 0, array, 8, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC3), 0, array, 12, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_MAGIC4), 0, array, 16, 4);
				AddItems(Item_In_Bag[position].ItemGlobal_ID, Item_In_Bag[position].VatPham_ID, Item_In_Bag[position].VatPhamViTri, bytes, array);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Xóa Vật Phẩm bị lỗi [" + AccountID + "][" + CharacterName + "] Position [" + position + "] SoLuong [" + soLuong + "] Vật Phẩm Tên [" + Item_In_Bag[position].GetItemName() + "] Số lượng [" + BitConverter.ToInt32(Item_In_Bag[position].VatPhamSoLuong, 0) + "]" + ex.Message);
		}
	}

	public void VatPham_GiamDi_SoLuong_DoBen(int position, int giamDiSoLuong)
	{
		try
		{
			if (giamDiSoLuong >= 0)
			{
				var array = new byte[4];
				var array2 = new byte[4];
				var array3 = new byte[8];
				var array4 = new byte[4];
				var array5 = new byte[56];
				Buffer.BlockCopy(Item_In_Bag[position].VatPham_byte, 12, array2, 0, 4);
				Buffer.BlockCopy(Item_In_Bag[position].VatPham_byte, 16, array, 0, 4);
				Buffer.BlockCopy(Item_In_Bag[position].VatPham_byte, 8, array4, 0, 4);
				Buffer.BlockCopy(Item_In_Bag[position].VatPham_byte, 0, array3, 0, 8);
				var num = BitConverter.ToInt32(array, 0) - giamDiSoLuong;
				if (num <= **********)
				{
					SubtractItem(position, 1);
					return;
				}
				SubtractItem(position, BitConverter.ToInt32(array2, 0));
				Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array5, 0, 4);
				AddItems(array3, array4, position, array2, array5);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "VatPham dấu trừ ThuocTinhSoLuong error [" + AccountID + "][" + CharacterName + "] Position[" + position + "] GiamDi_SoLuong[" + giamDiSoLuong + "]" + ex.Message);
		}
	}



	public void AddItems_4ViThan(byte[] itemGlobalId, byte[] vatPhamId, int viTri, byte[] soLuong, byte[] vatPhamThuocTinh, int fld4ViThanTuLinh)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA557200940223006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
				var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
				if (BitConverter.ToInt32(vatPhamId, 0) != 1008000044 && BitConverter.ToInt32(vatPhamId, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					itemGlobalId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					soLuong = BitConverter.GetBytes(BitConverter.ToInt32(soLuong, 0));
				}
			}
			else
			{
				soLuong = BitConverter.GetBytes(1);
			}
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(soLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, 55);
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array, 40, 2);
			Buffer.BlockCopy(array2, 0, array, 14, 12);
			Buffer.BlockCopy(array2, 12, array, 30, 4);
			Buffer.BlockCopy(array2, 16, array, 46, 55);
			if (fld4ViThanTuLinh != 0)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(fld4ViThanTuLinh), 0, array2, 71, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(fld4ViThanTuLinh), 0, array, 102, 4);
			}
			Item_In_Bag[viTri].VatPham_byte = array2;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Increase Item error 55 [" + AccountID + "][" + CharacterName + "] ViTri[" + viTri + "] SoLuong[" + BitConverter.ToInt32(soLuong, 0) + "]" + ex.Message);
		}
	}

	public void AddItems(byte[] itemGlobalId, byte[] vatPhamId, int viTri, byte[] soLuong, byte[] vatPhamThuocTinh)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA557200940223006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
				var characterItemType = new Item();
				if (value.FLD_TYPE == 205)
				{
					characterItemType = GetEventBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
				}

				if (BitConverter.ToInt32(vatPhamId, 0) != 1008000044 && BitConverter.ToInt32(vatPhamId, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					itemGlobalId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					soLuong = BitConverter.GetBytes(BitConverter.ToInt32(soLuong, 0));
				}
			}
			else
			{
				soLuong = BitConverter.GetBytes(1);
			}
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(soLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, 56);
			Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_TYPE == 205 ? 193 : 1), 0, array, 38, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array, 40, 2);
			Buffer.BlockCopy(array2, 0, array, 14, 12);
			Buffer.BlockCopy(array2, 12, array, 30, 4);
			Buffer.BlockCopy(array2, 16, array, 46, 55);
			Buffer.BlockCopy(array2, 71, array, 102, 2);
			if (value.FLD_TYPE == 205)
			{
				EventBag[viTri].VatPham_byte = array2;
			}
			else
			{
				Item_In_Bag[viTri].VatPham_byte = array2;
			}

			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "AddItems 55555 lỗi !! [" + AccountID + "][" + CharacterName + "] ViTri[" + viTri + "] SoLuong[" + BitConverter.ToInt32(soLuong, 0) + "]" + ex.Message);
		}
	}

	public void AddItems_Lock(byte[] itemGlobalId, byte[] vatPhamId, int viTri, byte[] soLuong, byte[] vatPhamThuocTinh, bool khoaLai, int soNgaySuDung)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA557200940223006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
				var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
				if (BitConverter.ToInt32(vatPhamId, 0) != 1008000044 && BitConverter.ToInt32(vatPhamId, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					itemGlobalId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					soLuong = BitConverter.GetBytes(BitConverter.ToInt32(soLuong, 0));
				}
			}
			else
			{
				soLuong = BitConverter.GetBytes(1);
			}
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(soLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, vatPhamThuocTinh.Length);
			if (soNgaySuDung > 0)
			{
				DateTime value2 = new(1970, 1, 1, 8, 0, 0);
				Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.Subtract(value2).TotalSeconds), 0, array2, 52, 4);
				Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddDays(soNgaySuDung).Subtract(value2).TotalSeconds), 0, array2, 56, 4);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array, 40, 2);
			Buffer.BlockCopy(array2, 0, array, 14, 12);
			Buffer.BlockCopy(array2, 12, array, 30, 4);
			Buffer.BlockCopy(array2, 16, array, 46, vatPhamThuocTinh.Length);
			if (khoaLai)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array2, 72, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(BitConverter.ToInt32(vatPhamId, 0) + 20000), 0, array, 22, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array2, 72, 1);
			}
			Item_In_Bag[viTri].VatPham_byte = array2;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Increase Item error 11 [" + AccountID + "][" + CharacterName + "]  ViTri[" + viTri + "]  SoLuong[" + BitConverter.ToInt32(soLuong, 0) + "]" + ex.Message);
		}
	}

	public void IncreaseItem4(byte[] itemGlobalId, byte[] vatPhamId, int viTri, byte[] soLuong, byte[] vatPhamThuocTinh)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA55720088040D006C0001000000AD20A92C252D410645CD9A3B000000000100000000000000010B0214000**********000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
				var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
				if (BitConverter.ToInt32(vatPhamId, 0) != 1008000044 && BitConverter.ToInt32(vatPhamId, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					itemGlobalId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					soLuong = BitConverter.GetBytes(BitConverter.ToInt32(soLuong, 0));
				}
			}
			else
			{
				soLuong = BitConverter.GetBytes(1);
			}
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(soLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, vatPhamThuocTinh.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array, 40, 2);
			Buffer.BlockCopy(array2, 0, array, 14, 12);
			Buffer.BlockCopy(array2, 12, array, 30, 4);
			Buffer.BlockCopy(array2, 16, array, 43, vatPhamThuocTinh.Length);
			Item_In_Bag[viTri].VatPham_byte = array2;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Increase Item error 22 [" + AccountID + "][" + CharacterName + "]  ViTri[" + viTri + "]  SoLuong[" + BitConverter.ToInt32(soLuong, 0) + "]" + ex.Message);
		}
	}

	public void IncreaseItem2(byte[] itemGlobalId, byte[] vatPhamId, int viTri, byte[] soLuong, byte[] vatPhamThuocTinh)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA55720094020D006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
				var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
				if (BitConverter.ToInt32(vatPhamId, 0) != 1008000044 && BitConverter.ToInt32(vatPhamId, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						itemGlobalId = characterItemType.ItemGlobal_ID;
						soLuong = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(soLuong, 0));
					}
				}
				else
				{
					itemGlobalId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					soLuong = BitConverter.GetBytes(BitConverter.ToInt32(soLuong, 0));
				}
			}
			else
			{
				soLuong = BitConverter.GetBytes(1);
			}
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(soLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, vatPhamThuocTinh.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array, 40, 2);
			Buffer.BlockCopy(array2, 0, array, 14, 12);
			Buffer.BlockCopy(array2, 12, array, 30, 4);
			Buffer.BlockCopy(array2, 16, array, 43, vatPhamThuocTinh.Length);
			Item_In_Bag[viTri].VatPham_byte = array2;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Increase Item error 222 [" + AccountID + "][" + CharacterName + "]  ViTri[" + viTri + "]  SoLuong[" + BitConverter.ToInt32(soLuong, 0) + "]" + ex.Message);
		}
	}

	public void AddItem_ThuocTinh_int(int vatPhamId, int viTri, int soLuong, int thuocTinh0, int thuocTinh1, int thuocTinh2, int thuocTinh3, int thuocTinh4, int soCapPhuHon, int trungCapPhuHon, int tienHoa, int khoaLai, int ngaySuDung)
	{
		try
		{
			if (!World.ItemList.TryGetValue(vatPhamId, out var value))
			{
				return;
			}
			var array = new byte[56];
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh0), 0, array, 0, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh1), 0, array, 4, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh2), 0, array, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh3), 0, array, 12, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh4), 0, array, 16, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(soCapPhuHon), 0, array, 46, 4);
			if (trungCapPhuHon > 0)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 22, 2);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(trungCapPhuHon), 0, array, 24, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(tienHoa), 0, array, 52, 4);
			var value2 = RxjhClass.CreateItemSeries();
			var array2 = Converter.HexStringToByte("AA55720000000D00640001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			var array3 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var characterItemType = GetCharacterBagItemByItemId(vatPhamId, thuocTinh0);
				if (vatPhamId != 1008000044 && vatPhamId != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						value2 = BitConverter.ToInt64(characterItemType.ItemGlobal_ID, 0);
						soLuong = BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + soLuong;
					}
				}
				else
				{
					value2 = RxjhClass.CreateItemSeries();
				}
			}
			else
			{
				soLuong = 1;
			}
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array3, 0, 8);
			Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array3, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array3, 12, 4);
			Buffer.BlockCopy(array, 0, array3, 16, array.Length);
			if (ngaySuDung > 0)
			{
				DateTime value3 = new(1970, 1, 1, 8, 0, 0);
				Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.Subtract(value3).TotalSeconds), 0, array3, 52, 4);
				Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddDays(ngaySuDung).Subtract(value3).TotalSeconds), 0, array3, 56, 4);
			}
			if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1000), 0, array3, 60, 2);
			}
			Item_In_Bag[viTri].VatPham_byte = array3;
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array2, 40, 2);
			Buffer.BlockCopy(array3, 0, array2, 14, 12);
			Buffer.BlockCopy(array3, 12, array2, 30, 4);
			Buffer.BlockCopy(array3, 16, array2, 46, array.Length);
			if (khoaLai > 0)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array3, 72, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId + 20000), 0, array2, 22, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array3, 72, 1);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			Client?.Send_Map_Data(array2, array2.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "AddItem-ThuocTinh-int vật phẩm phạm sai lầm 11 - ViTri - [" + AccountID + "][" + CharacterName + "]  ViTri[" + viTri + "]  SoLuong[" + soLuong + "]" + ex.Message);
		}
	}

	public void AddItem_int_Goi_Trai_Nghiem_16x(int vatPhamId, int viTri, int soLuong, int thuocTinh0, int thuocTinh1, int thuocTinh2, int thuocTinh3, int thuocTinh4, int soCapPhuHon, int trungCapPhuHon, int tienHoa, int khoaLai, int phutSuDung)
	{
		try
		{
			if (!World.ItemList.TryGetValue(vatPhamId, out var value))
			{
				return;
			}
			var array = new byte[56];
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh0), 0, array, 0, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh1), 0, array, 4, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh2), 0, array, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh3), 0, array, 12, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(thuocTinh4), 0, array, 16, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(soCapPhuHon), 0, array, 46, 4);
			if (trungCapPhuHon > 0)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 22, 2);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(trungCapPhuHon), 0, array, 24, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(tienHoa), 0, array, 52, 4);
			var value2 = RxjhClass.CreateItemSeries();
			var array2 = Converter.HexStringToByte("AA55720000000D00640001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			var array3 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var characterItemType = GetCharacterBagItemByItemId(vatPhamId, thuocTinh0);
				if (vatPhamId != 1008000044 && vatPhamId != 1008000045)
				{
					if (characterItemType != null)
					{
						viTri = characterItemType.VatPhamViTri;
						value2 = BitConverter.ToInt64(characterItemType.ItemGlobal_ID, 0);
						soLuong = BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + soLuong;
					}
				}
				else
				{
					value2 = RxjhClass.CreateItemSeries();
				}
			}
			else
			{
				soLuong = 1;
			}
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array3, 0, 8);
			Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array3, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array3, 12, 4);
			Buffer.BlockCopy(array, 0, array3, 16, array.Length);
			if (phutSuDung > 0)
			{
				DateTime value3 = new(1970, 1, 1, 8, 0, 0);
				Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.Subtract(value3).TotalSeconds), 0, array3, 52, 4);
				Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddSeconds(phutSuDung).Subtract(value3).TotalSeconds), 0, array3, 56, 4);
			}
			if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1000), 0, array3, 60, 2);
			}
			Item_In_Bag[viTri].VatPham_byte = array3;
			Buffer.BlockCopy(BitConverter.GetBytes(viTri), 0, array2, 40, 2);
			Buffer.BlockCopy(array3, 0, array2, 14, 12);
			Buffer.BlockCopy(array3, 12, array2, 30, 4);
			Buffer.BlockCopy(array3, 16, array2, 46, array.Length);
			if (khoaLai > 0)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array3, 72, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId + 20000), 0, array2, 22, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array3, 72, 1);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			Client?.Send_Map_Data(array2, array2.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "AddItem-ThuocTinh-int vật phẩm phạm sai lầm 11 - ViTri - [" + AccountID + "][" + CharacterName + "]  ViTri[" + viTri + "]  SoLuong[" + soLuong + "]" + ex.Message);
		}
	}
	
	public void ItemExchangeCheck(X_Trao_Doi_Vat_Pham_Loai vatPhamTraoDoi)
	{
		var num = 0;
		var array = vatPhamTraoDoi.CanVatPham.Split(';');
		for (var i = 0; i < array.Length; i++)
		{
			if (array[i] == null)
			{
				continue;
			}
			var array2 = array[i].Split(',');
			for (var j = 0; j < 66; j++)
			{
				if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == int.Parse(array2[0]) && BitConverter.ToInt32(Item_In_Bag[j].VatPhamSoLuong, 0) >= int.Parse(array2[1]))
				{
					num++;
					break;
				}
			}
		}
		if (num == array.Length)
		{
			for (var k = 0; k < num; k++)
			{
				var array3 = array[k].Split(',');
				for (var l = 0; l < 66; l++)
				{
					if (BitConverter.ToInt32(Item_In_Bag[l].VatPham_ID, 0) == int.Parse(array3[0]))
					{
						SubtractItem(l, int.Parse(array3[1]));
						break;
					}
				}
			}
			ExchangeItems(vatPhamTraoDoi);
		}
		else
		{
			HeThongNhacNho("Vật phẩm [" + vatPhamTraoDoi.CanVatPham + "] không đầy đủ, không đổi được.");
		}
	}

	public void ExchangeItems(X_Trao_Doi_Vat_Pham_Loai vatPhamTraoDoi)
	{
		if (vatPhamTraoDoi.VoHuan != 0)
		{
			// Player_WuXun += vatPhamTraoDoi.VoHuan;
			UpdateHonorPoint(vatPhamTraoDoi.VoHuan);
			// WuxunConsumptionTips(vatPhamTraoDoi.VoHuan);
			// HeThongNhacNho("Đạt được võ huân [" + vatPhamTraoDoi.VoHuan + "] Điểm. 33");
			UpdateMartialArtsAndStatus();
		}
		if (vatPhamTraoDoi.NguyenBao != 0)
		{
			KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
			KiemSoatNguyenBao_SoLuong(vatPhamTraoDoi.NguyenBao, 1);
			Save_NguyenBaoData();
		}
		if (vatPhamTraoDoi.Set != 0)
		{
			var parcelVacancyPosition = GetParcelVacancyPosition();
			if (parcelVacancyPosition == -1)
			{
				HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
				return;
			}
			AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(vatPhamTraoDoi.Set), parcelVacancyPosition, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 0);
			HeThongNhacNho("Chúc mừng đại hiệp, đã nhận được, hãy mở ra xem!", 10, "Thiên cơ các");
		}
		if (vatPhamTraoDoi.TienBac.Length != 0)
		{
			Player_Money += long.Parse(vatPhamTraoDoi.TienBac);
			HeThongNhacNho("Nhận được tiền tệ " + vatPhamTraoDoi.TienBac + " 2.");
			TipsForGettingMoney(uint.Parse(vatPhamTraoDoi.TienBac));
			UpdateMoneyAndWeight();
		}
		if (vatPhamTraoDoi.GoiVatPham.Length == 0)
		{
			return;
		}
		var array = vatPhamTraoDoi.GoiVatPham.Split(';');
		var num = 0;
		while (true)
		{
			if (num < Convert.ToInt32(array[1]))
			{
				var parcelVacancyPosition2 = GetParcelVacancyPosition();
				if (parcelVacancyPosition2 == -1)
				{
					break;
				}
				AddItem_ThuocTinh_int(Convert.ToInt32(array[0]), parcelVacancyPosition2, 1, Convert.ToInt32(array[2]), Convert.ToInt32(array[3]), Convert.ToInt32(array[4]), Convert.ToInt32(array[5]), Convert.ToInt32(array[6]), Convert.ToInt32(array[7]), Convert.ToInt32(array[8]), Convert.ToInt32(array[9]), Convert.ToInt32(array[10]), 0);
				num++;
				continue;
			}
			return;
		}
		HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
	}

}
