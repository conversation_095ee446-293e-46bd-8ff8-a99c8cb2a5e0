-- PostgreSQL functions for warehouse operations
-- Functions PostgreSQL cho các thao tác kho

-- Function to update personal warehouse
-- Function cập nhật kho cá nhân
CREATE OR REPLACE FUNCTION xwwl_update_user_warehouse(
    p_id VARCHAR(50),
    p_name VARCHAR(50),
    p_money VARCHAR(50),
    p_stritem BYTEA
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Update personal warehouse data
    -- Cập nhật dữ liệu kho cá nhân
    UPDATE tbl_xwwl_warehouse
    SET 
        fld_money = p_money,
        fld_item = p_stritem
    WHERE fld_name = p_name AND fld_id = p_id;
    
    -- Get the number of affected rows
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log the operation
    -- Ghi log thao tác
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Personal warehouse updated successfully for player: % (Account: %) - Rows affected: %', p_name, p_id, affected_rows;
    ELSE
        RAISE WARNING 'No personal warehouse found for player: % and account: %', p_name, p_id;
    END IF;
    
    -- Return the number of affected rows
    -- Trả về số dòng bị ảnh hưởng
    RETURN affected_rows;
    
END;
$$;

-- Function to update comprehensive/public warehouse
-- Function cập nhật kho tổng hợp/công cộng
CREATE OR REPLACE FUNCTION xwwl_update_id_warehouse(
    p_id VARCHAR(50),
    p_money VARCHAR(50),
    p_stritem BYTEA,
    p_stritime BYTEA,
    p_zbver INTEGER
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Update comprehensive warehouse data
    -- Cập nhật dữ liệu kho tổng hợp
    UPDATE tbl_xwwl_publicwarehouse
    SET 
        fld_money = p_money,
        fld_item = p_stritem,
        fld_itime = p_stritime,
        fld_zbver = p_zbver
    WHERE fld_id = p_id;
    
    -- Get the number of affected rows
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log the operation
    -- Ghi log thao tác
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Comprehensive warehouse updated successfully for account: % - Rows affected: %', p_id, affected_rows;
    ELSE
        RAISE WARNING 'No comprehensive warehouse found for account: %', p_id;
    END IF;
    
    -- Return the number of affected rows
    -- Trả về số dòng bị ảnh hưởng
    RETURN affected_rows;
    
END;
$$;

-- Function to update RX points (account data)
-- Function cập nhật điểm RX (dữ liệu tài khoản)
CREATE OR REPLACE FUNCTION xwwl_update_rxpiont(
    p_id VARCHAR(50),
    p_rxpiont INTEGER,
    p_rxpiontx INTEGER,
    p_coin INTEGER,
    p_safeword VARCHAR(50)
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Update account RX points data
    -- Cập nhật dữ liệu điểm RX tài khoản
    UPDATE account
    SET 
        fld_rxpiont = p_rxpiont,
        fld_rxpiontx = p_rxpiontx,
        fld_coin = p_coin,
        fld_safeword = p_safeword
    WHERE fld_id = p_id;
    
    -- Get the number of affected rows
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log the operation
    -- Ghi log thao tác
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Account RX points updated successfully for account: % - Rows affected: %', p_id, affected_rows;
    ELSE
        RAISE WARNING 'No account found for ID: %', p_id;
    END IF;
    
    -- Return the number of affected rows
    -- Trả về số dòng bị ảnh hưởng
    RETURN affected_rows;

END;
$$;

-- Function to update spirit beast/pet data
-- Function cập nhật dữ liệu linh thú/thú cưng
CREATE OR REPLACE FUNCTION xwwl_update_cw_data(
    p_id VARCHAR(20),
    p_name VARCHAR(20),
    p_level INTEGER,
    p_zcd INTEGER,
    p_job INTEGER,
    p_job_level INTEGER,
    p_exp VARCHAR(50),
    p_hp INTEGER,
    p_mp INTEGER,
    p_strwearitem BYTEA,
    p_stritem BYTEA,
    p_strkongfu BYTEA,
    p_bs INTEGER,
    p_magic1 INTEGER,
    p_magic2 INTEGER,
    p_magic3 INTEGER,
    p_magic4 INTEGER,
    p_magic5 INTEGER
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Update spirit beast/pet data
    -- Cập nhật dữ liệu linh thú/thú cưng
    UPDATE tbl_xwwl_cw
    SET
        fld_level = p_level,
        fld_zcd = p_zcd,
        fld_job = p_job,
        fld_job_level = p_job_level,
        fld_exp = p_exp,
        fld_hp = p_hp,
        fld_bs = p_bs,
        fld_mp = p_mp,
        fld_wearitem = p_strwearitem,
        fld_item = p_stritem,
        fld_kongfu = p_strkongfu,
        name = p_name,
        fld_magic1 = p_magic1,
        fld_magic2 = p_magic2,
        fld_magic3 = p_magic3,
        fld_magic4 = p_magic4,
        fld_magic5 = p_magic5
    WHERE itmeid = CAST(p_id AS INTEGER);

    -- Get the number of affected rows
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    -- Log the operation
    -- Ghi log thao tác
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Spirit beast data updated successfully for: % (ID: %) - Rows affected: %', p_name, p_id, affected_rows;
    ELSE
        RAISE WARNING 'No spirit beast found for ID: %', p_id;
    END IF;

    -- Return the number of affected rows
    -- Trả về số dòng bị ảnh hưởng
    RETURN affected_rows;

END;
$$;

-- Function to update mentoring/master-student data
-- Function cập nhật dữ liệu sư đồ/thầy trò
CREATE OR REPLACE FUNCTION update_st_data(
    p_name VARCHAR(50),
    p_level INTEGER,
    p_stlevel INTEGER,
    p_styhd INTEGER,
    p_stwg1 INTEGER,
    p_stwg2 INTEGER,
    p_stwg3 INTEGER
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Update mentoring/master-student data
    -- Cập nhật dữ liệu sư đồ/thầy trò
    UPDATE tbl_sudosolieu
    SET
        fld_tlevel = p_level,
        fld_stlevel = p_stlevel,
        fld_styhd = p_styhd,
        fld_stwg1 = p_stwg1,
        fld_stwg2 = p_stwg2,
        fld_stwg3 = p_stwg3
    WHERE fld_tname = p_name;

    -- Get the number of affected rows
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    -- Log the operation
    -- Ghi log thao tác
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Mentoring data updated successfully for: % - Rows affected: %', p_name, affected_rows;
    ELSE
        RAISE WARNING 'No mentoring data found for name: %', p_name;
    END IF;

    -- Return the number of affected rows
    -- Trả về số dòng bị ảnh hưởng
    RETURN affected_rows;

END;
$$;
