-- =====================================================
-- Migration Script: Old Drop System to New Drop System
-- =====================================================

-- This script migrates data from the old drop tables to the new unified drop system
-- Run this after creating the new drop tables

-- =====================================================
-- Step 1: Backup existing data (optional but recommended)
-- =====================================================

-- Create backup tables
CREATE TABLE IF NOT EXISTS tbl_xwwl_drop_backup AS SELECT * FROM tbl_xwwl_drop;
CREATE TABLE IF NOT EXISTS tbl_xwwl_bossdrop_backup AS SELECT * FROM tbl_xwwl_bossdrop;
CREATE TABLE IF NOT EXISTS tbl_xwwl_drop_dch_backup AS SELECT * FROM tbl_xwwl_drop_dch;
CREATE TABLE IF NOT EXISTS tbl_xwwl_drop_gs_backup AS SELECT * FROM tbl_xwwl_drop_gs;

-- =====================================================
-- Step 2: Clear existing new drop data (if re-running)
-- =====================================================

-- Uncomment the following line if you want to clear existing data
-- DELETE FROM tbl_new_drops WHERE source_type IN ('level_range', 'npc_specific');

-- =====================================================
-- Step 3: Migrate Normal Drops (tbl_xwwl_drop)
-- =====================================================

INSERT INTO tbl_new_drops (
    source_type, source_value, item_id, drop_rate,
    quantity_min, quantity_max,
    magic0, magic1, magic2, magic3, magic4,
    expire_days, is_active, priority,
    created_at, updated_at
)
SELECT
    'level_range' as source_type,
    CONCAT(
        COALESCE(fld_level1, 1), 
        '-', 
        COALESCE(fld_level2, 100)
    ) as source_value,
    fld_pid as item_id,
    CASE
        WHEN fld_pp <= 0 THEN 0.000001
        WHEN fld_pp >= 8000 THEN 1.000000
        ELSE CAST(fld_pp AS DECIMAL(8,6)) / 8000.0
    END as drop_rate,
    1 as quantity_min,
    1 as quantity_max,
    COALESCE(fld_magic0, 0) as magic0,
    COALESCE(fld_magic1, 0) as magic1,
    COALESCE(fld_magic2, 0) as magic2,
    COALESCE(fld_magic3, 0) as magic3,
    COALESCE(fld_magic4, 0) as magic4,
    COALESCE(fld_days, 0) as expire_days,
    true as is_active,
    100 as priority,
    NOW() as created_at,
    NOW() as updated_at
FROM tbl_xwwl_drop
WHERE fld_pid IS NOT NULL 
  AND fld_pid > 0 
  AND fld_pp IS NOT NULL 
  AND fld_pp > 0
  AND fld_level1 IS NOT NULL 
  AND fld_level2 IS NOT NULL
  AND fld_level1 >= 1 
  AND fld_level2 >= fld_level1
  AND fld_level2 <= 300
ON CONFLICT (source_type, source_value, item_id) DO NOTHING;

-- =====================================================
-- Step 4: Migrate Boss Drops (tbl_xwwl_bossdrop)
-- =====================================================

INSERT INTO tbl_new_drops (
    source_type, source_value, item_id, drop_rate,
    quantity_min, quantity_max,
    magic0, magic1, magic2, magic3, magic4,
    expire_days, is_active, priority,
    created_at, updated_at
)
SELECT
    'level_range' as source_type,  -- Could be 'npc_specific' if you have NPC mapping
    CONCAT(
        COALESCE(fld_level1, 1), 
        '-', 
        COALESCE(fld_level2, 100)
    ) as source_value,
    fld_pid as item_id,
    CASE
        WHEN fld_pp <= 0 THEN 0.000001
        WHEN fld_pp >= 8000 THEN 1.000000
        ELSE CAST(fld_pp AS DECIMAL(8,6)) / 8000.0
    END as drop_rate,
    1 as quantity_min,
    1 as quantity_max,
    COALESCE(fld_magic0, 0) as magic0,
    COALESCE(fld_magic1, 0) as magic1,
    COALESCE(fld_magic2, 0) as magic2,
    COALESCE(fld_magic3, 0) as magic3,
    COALESCE(fld_magic4, 0) as magic4,
    COALESCE(fld_days, 0) as expire_days,
    true as is_active,
    200 as priority,  -- Higher priority for boss drops
    NOW() as created_at,
    NOW() as updated_at
FROM tbl_xwwl_bossdrop
WHERE fld_pid IS NOT NULL 
  AND fld_pid > 0 
  AND fld_pp IS NOT NULL 
  AND fld_pp > 0
  AND fld_level1 IS NOT NULL 
  AND fld_level2 IS NOT NULL
  AND fld_level1 >= 1 
  AND fld_level2 >= fld_level1
  AND fld_level2 <= 300
ON CONFLICT (source_type, source_value, item_id) DO NOTHING;

-- =====================================================
-- Step 5: Migrate DCH Drops (tbl_xwwl_drop_dch)
-- =====================================================

INSERT INTO tbl_new_drops (
    source_type, source_value, item_id, drop_rate,
    quantity_min, quantity_max,
    magic0, magic1, magic2, magic3, magic4,
    expire_days, is_active, priority,
    created_at, updated_at
)
SELECT
    'level_range' as source_type,
    CONCAT(
        COALESCE(fld_level1, 1), 
        '-', 
        COALESCE(fld_level2, 100)
    ) as source_value,
    fld_pid as item_id,
    CASE
        WHEN fld_pp <= 0 THEN 0.000001
        WHEN fld_pp >= 8000 THEN 1.000000
        ELSE CAST(fld_pp AS DECIMAL(8,6)) / 8000.0
    END as drop_rate,
    1 as quantity_min,
    1 as quantity_max,
    COALESCE(fld_magic0, 0) as magic0,
    COALESCE(fld_magic1, 0) as magic1,
    COALESCE(fld_magic2, 0) as magic2,
    COALESCE(fld_magic3, 0) as magic3,
    COALESCE(fld_magic4, 0) as magic4,
    COALESCE(fld_days, 0) as expire_days,
    true as is_active,
    150 as priority,  -- Medium-high priority for DCH drops
    NOW() as created_at,
    NOW() as updated_at
FROM tbl_xwwl_drop_dch
WHERE fld_pid IS NOT NULL 
  AND fld_pid > 0 
  AND fld_pp IS NOT NULL 
  AND fld_pp > 0
  AND fld_level1 IS NOT NULL 
  AND fld_level2 IS NOT NULL
  AND fld_level1 >= 1 
  AND fld_level2 >= fld_level1
  AND fld_level2 <= 300
ON CONFLICT (source_type, source_value, item_id) DO NOTHING;

-- =====================================================
-- Step 6: Migrate GS Drops (tbl_xwwl_drop_gs)
-- =====================================================

INSERT INTO tbl_new_drops (
    source_type, source_value, item_id, drop_rate,
    quantity_min, quantity_max,
    magic0, magic1, magic2, magic3, magic4,
    expire_days, is_active, priority,
    created_at, updated_at
)
SELECT
    'level_range' as source_type,
    CONCAT(
        COALESCE(fld_level1, 1), 
        '-', 
        COALESCE(fld_level2, 100)
    ) as source_value,
    fld_pid as item_id,
    CASE
        WHEN fld_pp <= 0 THEN 0.000001
        WHEN fld_pp >= 8000 THEN 1.000000
        ELSE CAST(fld_pp AS DECIMAL(8,6)) / 8000.0
    END as drop_rate,
    1 as quantity_min,
    1 as quantity_max,
    COALESCE(fld_magic0, 0) as magic0,
    COALESCE(fld_magic1, 0) as magic1,
    COALESCE(fld_magic2, 0) as magic2,
    COALESCE(fld_magic3, 0) as magic3,
    COALESCE(fld_magic4, 0) as magic4,
    COALESCE(fld_days, 0) as expire_days,
    true as is_active,
    120 as priority,  -- Medium priority for GS drops
    NOW() as created_at,
    NOW() as updated_at
FROM tbl_xwwl_drop_gs
WHERE fld_pid IS NOT NULL 
  AND fld_pid > 0 
  AND fld_pp IS NOT NULL 
  AND fld_pp > 0
  AND fld_level1 IS NOT NULL 
  AND fld_level2 IS NOT NULL
  AND fld_level1 >= 1 
  AND fld_level2 >= fld_level1
  AND fld_level2 <= 300
ON CONFLICT (source_type, source_value, item_id) DO NOTHING;

-- =====================================================
-- Step 7: Migration Summary and Validation
-- =====================================================

-- Show migration summary
SELECT 
    'Migration Summary' as info,
    (SELECT COUNT(*) FROM tbl_xwwl_drop WHERE fld_pp > 0) as old_normal_drops,
    (SELECT COUNT(*) FROM tbl_xwwl_bossdrop WHERE fld_pp > 0) as old_boss_drops,
    (SELECT COUNT(*) FROM tbl_xwwl_drop_dch WHERE fld_pp > 0) as old_dch_drops,
    (SELECT COUNT(*) FROM tbl_xwwl_drop_gs WHERE fld_pp > 0) as old_gs_drops,
    (SELECT COUNT(*) FROM tbl_new_drops WHERE source_type = 'level_range') as new_level_drops,
    (SELECT COUNT(*) FROM tbl_new_drops WHERE source_type = 'npc_specific') as new_npc_drops,
    (SELECT COUNT(*) FROM tbl_new_drops WHERE source_type = 'quest_based') as new_quest_drops;

-- Show drop rate distribution
SELECT 
    'Drop Rate Distribution' as info,
    CASE 
        WHEN drop_rate >= 0.5 THEN '50%+'
        WHEN drop_rate >= 0.25 THEN '25-50%'
        WHEN drop_rate >= 0.1 THEN '10-25%'
        WHEN drop_rate >= 0.05 THEN '5-10%'
        WHEN drop_rate >= 0.01 THEN '1-5%'
        ELSE '<1%'
    END as rate_range,
    COUNT(*) as count
FROM tbl_new_drops
WHERE is_active = true
GROUP BY 
    CASE 
        WHEN drop_rate >= 0.5 THEN '50%+'
        WHEN drop_rate >= 0.25 THEN '25-50%'
        WHEN drop_rate >= 0.1 THEN '10-25%'
        WHEN drop_rate >= 0.05 THEN '5-10%'
        WHEN drop_rate >= 0.01 THEN '1-5%'
        ELSE '<1%'
    END
ORDER BY 
    CASE 
        WHEN drop_rate >= 0.5 THEN 1
        WHEN drop_rate >= 0.25 THEN 2
        WHEN drop_rate >= 0.1 THEN 3
        WHEN drop_rate >= 0.05 THEN 4
        WHEN drop_rate >= 0.01 THEN 5
        ELSE 6
    END;

-- Show priority distribution
SELECT 
    'Priority Distribution' as info,
    priority,
    COUNT(*) as count,
    AVG(drop_rate) as avg_drop_rate
FROM tbl_new_drops
WHERE is_active = true
GROUP BY priority
ORDER BY priority DESC;

-- =====================================================
-- Step 8: Optional - Create NPC-specific drops for known bosses
-- =====================================================

-- Example: Convert some level-range boss drops to NPC-specific drops
-- Uncomment and modify the following if you have specific NPC IDs for bosses

/*
-- Example boss mappings (modify according to your data)
INSERT INTO tbl_new_drops (
    source_type, source_value, item_id, drop_rate,
    quantity_min, quantity_max,
    magic0, magic1, magic2, magic3, magic4,
    expire_days, is_active, priority,
    created_at, updated_at
)
VALUES
-- Boss 1 (NPC ID: 15100)
('npc_specific', '15100', 500001, 0.500000, 1, 1, 300, 200, 0, 0, 0, 0, true, 250, NOW(), NOW()),
('npc_specific', '15100', 500002, 0.250000, 1, 1, 500, 300, 0, 0, 0, 0, true, 240, NOW(), NOW()),

-- Boss 2 (NPC ID: 15236)
('npc_specific', '15236', 600001, 0.400000, 1, 2, 400, 250, 0, 0, 0, 0, true, 250, NOW(), NOW()),
('npc_specific', '15236', 600002, 0.200000, 1, 1, 600, 400, 0, 0, 0, 0, true, 240, NOW(), NOW())

ON CONFLICT (source_type, source_value, item_id) DO NOTHING;
*/

-- =====================================================
-- Step 9: Final validation
-- =====================================================

-- Check for any invalid data
SELECT 
    'Validation Check' as info,
    COUNT(*) as invalid_drops
FROM tbl_new_drops
WHERE drop_rate <= 0 
   OR drop_rate > 1 
   OR item_id <= 0 
   OR quantity_min < 1 
   OR quantity_max < quantity_min;

-- Show sample of migrated data
SELECT 
    'Sample Migrated Data' as info,
    source_type,
    source_value,
    item_id,
    CONCAT(ROUND(drop_rate * 100, 4), '%') as drop_percentage,
    priority
FROM tbl_new_drops
WHERE is_active = true
ORDER BY priority DESC, drop_rate DESC
LIMIT 10;

-- =====================================================
-- Migration Complete
-- =====================================================

SELECT 'Migration completed successfully!' as status;
