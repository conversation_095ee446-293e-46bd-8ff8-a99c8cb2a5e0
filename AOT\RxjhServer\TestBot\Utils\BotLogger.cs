using System;
using System.IO;
using System.Text;
using HeroYulgang.Helpers;

namespace RxjhServer.TestBot.Utils
{
    /// <summary>
    /// Logger chuyên dụng cho Test Bot system
    /// </summary>
    public static class BotLogger
    {
        private static readonly string LogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "TestBot");
        private static readonly object _lockObject = new object();
        
        static BotLogger()
        {
            // Tạo thư mục log nếu chưa có
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }
        
        /// <summary>
        /// Log thông tin bot
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <param name="moduleName">Tên module</param>
        /// <param name="message">Message</param>
        /// <param name="level">Log level</param>
        public static void Log(string playerName, string moduleName, string message, LogLevel level = LogLevel.Info)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] [{playerName}] [{moduleName}] {message}";
                
                // Log to console/main log
                LogHelper.WriteLine(level, logMessage);
                
                // Log to file
                LogToFile(playerName, logMessage);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[BotLogger] Error logging: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Log to file
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <param name="message">Message</param>
        private static void LogToFile(string playerName, string message)
        {
            try
            {
                lock (_lockObject)
                {
                    var fileName = $"TestBot_{playerName}_{DateTime.Now:yyyy-MM-dd}.log";
                    var filePath = Path.Combine(LogDirectory, fileName);
                    
                    File.AppendAllText(filePath, message + Environment.NewLine, Encoding.UTF8);
                }
            }
            catch
            {
                // Ignore file logging errors
            }
        }
        
        /// <summary>
        /// Log debug message
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <param name="moduleName">Tên module</param>
        /// <param name="message">Message</param>
        public static void Debug(string playerName, string moduleName, string message)
        {
            Log(playerName, moduleName, message, LogLevel.Debug);
        }
        
        /// <summary>
        /// Log info message
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <param name="moduleName">Tên module</param>
        /// <param name="message">Message</param>
        public static void Info(string playerName, string moduleName, string message)
        {
            Log(playerName, moduleName, message, LogLevel.Info);
        }
        
        /// <summary>
        /// Log warning message
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <param name="moduleName">Tên module</param>
        /// <param name="message">Message</param>
        public static void Warning(string playerName, string moduleName, string message)
        {
            Log(playerName, moduleName, message, LogLevel.Warning);
        }
        
        /// <summary>
        /// Log error message
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <param name="moduleName">Tên module</param>
        /// <param name="message">Message</param>
        public static void Error(string playerName, string moduleName, string message)
        {
            Log(playerName, moduleName, message, LogLevel.Error);
        }
        
        /// <summary>
        /// Clean up old log files
        /// </summary>
        /// <param name="daysToKeep">Số ngày giữ lại</param>
        public static void CleanupOldLogs(int daysToKeep = 7)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var files = Directory.GetFiles(LogDirectory, "*.log");
                
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[BotLogger] Error cleaning up logs: {ex.Message}");
            }
        }
    }
}
