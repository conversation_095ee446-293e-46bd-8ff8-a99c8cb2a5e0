-- =====================================================
-- ATTENDANCE 14 DAY MIGRATION SCRIPT
-- Thêm hỗ trợ cho attendance 14 ngày (comeback attendance)
-- =====================================================

-- 1. Thêm trường attendance_type vào bảng tbl_attendance_templates
ALTER TABLE tbl_attendance_templates 
ADD COLUMN IF NOT EXISTS attendance_type VARCHAR(50) DEFAULT 'normal';

-- 2. Thêm trường eligible_after_days để xác định điều kiện đủ ngày không login
ALTER TABLE tbl_attendance_templates 
ADD COLUMN IF NOT EXISTS eligible_after_days INTEGER DEFAULT 0;

-- 3. Thê<PERSON> comment cho các trường mới
COMMENT ON COLUMN tbl_attendance_templates.attendance_type IS 'Loại attendance: normal, comeback_14day';
COMMENT ON COLUMN tbl_attendance_templates.eligible_after_days IS 'Số ngày không login để đủ điều kiện (0 = không yêu cầu)';

-- 4. Tạo index cho performance
CREATE INDEX IF NOT EXISTS idx_attendance_templates_type ON tbl_attendance_templates(attendance_type);
CREATE INDEX IF NOT EXISTS idx_attendance_templates_eligible_days ON tbl_attendance_templates(eligible_after_days);

-- 5. Cập nhật các attendance template hiện có thành loại 'normal'
UPDATE tbl_attendance_templates 
SET attendance_type = 'normal', eligible_after_days = 0 
WHERE attendance_type IS NULL OR attendance_type = '';

-- 6. Tạo function để kiểm tra player đã bao nhiều ngày không login
CREATE OR REPLACE FUNCTION get_days_since_last_login(p_player_name VARCHAR(255))
RETURNS INTEGER AS $$
DECLARE
    last_login_date TIMESTAMP;
    days_since_login INTEGER;
BEGIN
    -- Lấy thời gian login cuối cùng từ bảng loginrecord
    SELECT MAX(thoigian) INTO last_login_date
    FROM loginrecord
    WHERE username = p_player_name;
    
    -- Nếu không tìm thấy record login nào, trả về -1
    IF last_login_date IS NULL THEN
        RETURN -1;
    END IF;
    
    -- Tính số ngày từ lần login cuối đến hiện tại
    days_since_login := EXTRACT(DAY FROM (CURRENT_TIMESTAMP - last_login_date));
    
    RETURN days_since_login;
END;
$$ LANGUAGE plpgsql;

-- 7. Tạo function để kiểm tra player có đủ điều kiện attendance 14 ngày không
CREATE OR REPLACE FUNCTION is_eligible_for_comeback_attendance(
    p_player_name VARCHAR(255),
    p_required_days INTEGER DEFAULT 14
)
RETURNS BOOLEAN AS $$
DECLARE
    days_since_login INTEGER;
BEGIN
    -- Lấy số ngày từ lần login cuối
    days_since_login := get_days_since_last_login(p_player_name);
    
    -- Nếu không có record login, coi như đủ điều kiện
    IF days_since_login = -1 THEN
        RETURN TRUE;
    END IF;
    
    -- Kiểm tra có đủ số ngày yêu cầu không
    RETURN days_since_login >= p_required_days;
END;
$$ LANGUAGE plpgsql;

-- 8. Tạo function để lấy active attendance cho player (bao gồm cả comeback attendance)
CREATE OR REPLACE FUNCTION get_active_attendance_for_player(p_player_name VARCHAR(255))
RETURNS TABLE(
    id INTEGER,
    name VARCHAR(255),
    month INTEGER,
    year INTEGER,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    attendance_type VARCHAR(50),
    eligible_after_days INTEGER
) AS $$
BEGIN
    -- Trước tiên, kiểm tra attendance thường đang active
    RETURN QUERY
    SELECT t.id, t.name, t.month, t.year, t.start_date, t.end_date, t.attendance_type, t.eligible_after_days
    FROM tbl_attendance_templates t
    WHERE t.is_active = TRUE
    AND t.attendance_type = 'normal'
    AND CURRENT_DATE BETWEEN t.start_date::DATE AND t.end_date::DATE
    ORDER BY t.created_date DESC
    LIMIT 1;
    
    -- Nếu không có attendance thường, kiểm tra comeback attendance
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT t.id, t.name, t.month, t.year, t.start_date, t.end_date, t.attendance_type, t.eligible_after_days
        FROM tbl_attendance_templates t
        WHERE t.is_active = TRUE
        AND t.attendance_type = 'comeback_14day'
        AND CURRENT_DATE BETWEEN t.start_date::DATE AND t.end_date::DATE
        AND is_eligible_for_comeback_attendance(p_player_name, t.eligible_after_days) = TRUE
        ORDER BY t.created_date DESC
        LIMIT 1;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 9. Tạo sample attendance template 14 ngày
INSERT INTO tbl_attendance_templates (
    name, 
    month, 
    year, 
    is_active, 
    start_date, 
    end_date,
    attendance_type,
    eligible_after_days
) VALUES (
    'Điểm danh trở lại - 14 ngày',
    EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER,
    EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER,
    FALSE, -- Tạm thời chưa active, sẽ active sau khi setup xong
    DATE_TRUNC('month', CURRENT_DATE),
    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day',
    'comeback_14day',
    14
) ON CONFLICT (month, year) DO NOTHING;

-- 10. Lấy ID của attendance template vừa tạo để thêm rewards
DO $$
DECLARE
    template_id INTEGER;
BEGIN
    -- Lấy ID của template comeback vừa tạo
    SELECT id INTO template_id
    FROM tbl_attendance_templates
    WHERE attendance_type = 'comeback_14day'
    AND month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER
    AND year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER;
    
    -- Thêm rewards mẫu cho attendance 14 ngày (phần thưởng hấp dẫn hơn)
    IF template_id IS NOT NULL THEN
        -- Ngày 1: Vàng
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 1, 0x00000001, 100000) ON CONFLICT DO NOTHING;
        
        -- Ngày 2: Kinh nghiệm
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 2, 0x00000002, 50000) ON CONFLICT DO NOTHING;
        
        -- Ngày 3: Thuốc hồi máu lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 3, 0x00000003, 10) ON CONFLICT DO NOTHING;
        
        -- Ngày 7: Trang bị đặc biệt
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 7, 0x00000007, 1) ON CONFLICT DO NOTHING;
        
        -- Ngày 14: Phần thưởng lớn
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (template_id, 14, 0x00000014, 1) ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'Added sample rewards for comeback attendance template ID: %', template_id;
    END IF;
END $$;

-- 11. Verify the changes
SELECT 
    'Database migration completed successfully' as status,
    COUNT(*) as total_templates,
    COUNT(CASE WHEN attendance_type = 'normal' THEN 1 END) as normal_templates,
    COUNT(CASE WHEN attendance_type = 'comeback_14day' THEN 1 END) as comeback_templates
FROM tbl_attendance_templates;
