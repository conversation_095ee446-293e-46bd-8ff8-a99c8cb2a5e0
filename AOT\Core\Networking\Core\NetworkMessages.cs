using System;
using Akka.Actor;
using HeroYulgang.Helpers;
using RxjhServer;
using RxjhServer.Database;

namespace HeroYulgang.Core.Networking.Core
{
    /// <summary>
    /// Các loại gói tin trong hệ thống mạng
    /// </summary>
    public enum PacketType : ushort
    {
        // Các loại gói tin
        Login = 1,
        Valid1375 = 1375,
        CreateCharacter = 20,

        UpdateConfig = 22,

        CharacterList = 16,
        LogOut = 3,
        JoinWorld = 5,

        ServerTime = 143,
        CheckCharacterExist = 56,
        DeleteCharacter = 30,
        VerifyLogin = 836,

        ChangeChannel = 218,
        ChangeChannel2 = 211,
        VerifyVersion = 5638,
        VerifyVersion2 = 8212,
        Ping = 176
    }

    /// <summary>
    /// Đại diện cho một gói tin trong hệ thống
    /// </summary>
    public class Packet(PacketType type, byte[] data)
    {
        public PacketType Type { get; } = type;
        public byte[] Data { get; } = data;
        public int Length { get; } = data.Length;

        public static Packet? Parse(byte[] buffer)
        {
            return Parse(buffer, buffer.Length);
        }

        public static Packet? Parse(byte[] buffer, int length)
        {
            try
            {
                // Giả sử cấu trúc gói tin: [2 byte loại gói tin][2 byte độ dài][dữ liệu]
                if (length < 4)
                {
                    LogHelper.WriteLine(LogLevel.Error, " Parse packet Error length <4" );
                    return null;
                }

                ushort type = BitConverter.ToUInt16(buffer, 8);
                ushort dataLength = BitConverter.ToUInt16(buffer, 10);

                //if (length < 4 + dataLength)
                //{
                //    return null;
                //}

                // Keep the original buffer to comply with the old system
                return new Packet((PacketType)type, buffer);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, " Parse packet Error " + ex.Message);
                return null;
            }
        }

        public byte[] ToByteArray()
        {
            byte[] result = new byte[4 + Length];
            BitConverter.GetBytes((ushort)Type).CopyTo(result, 0);
            BitConverter.GetBytes((ushort)Length).CopyTo(result, 2);
            Data.CopyTo(result, 4);
            return result;
        }
    }

    // Actor Messages for networking system

    /// <summary>
    /// Message để gửi packet qua TCP
    /// </summary>
    public class SendPacket(IActorRef connection, byte[] data)
    {
        public IActorRef Connection { get; } = connection;
        public byte[] Data { get; } = data;
    }

    /// <summary>
    /// Message để đóng kết nối
    /// </summary>
    public class CloseConnection(IActorRef connection)
    {
        public IActorRef Connection { get; } = connection;
    }

    /// <summary>
    /// Message để thiết lập player reference
    /// </summary>
    public class SetPlayerReference(Players player)
    {
        public Players Player { get; } = player;
    }

    /// <summary>
    /// Message để thiết lập player reference và thực hiện login
    /// </summary>
    public class SetPlayerReferenceAndLogin(Players player, byte[] loginData, ClientSession session, string username)
    {
        public Players Player { get; } = player;
        public byte[] LoginData { get; } = loginData;
        public ClientSession Session { get; } = session;
        public string Username { get; } = username;
    }

    /// <summary>
    /// Message để khởi tạo kết nối TCP
    /// </summary>
    public class InitializeTcp(string host, int port)
    {
        public string Host { get; } = host;
        public int Port { get; } = port;
    }

    /// <summary>
    /// Message thông báo kết nối TCP đã sẵn sàng
    /// </summary>
    public class TcpReady(IActorRef tcpActor)
    {
        public IActorRef TcpActor { get; } = tcpActor;
    }

    /// <summary>
    /// Message để xử lý client mới kết nối
    /// </summary>
    public class NewClientConnection(IActorRef connection, System.Net.EndPoint remoteEndPoint)
    {
        public IActorRef Connection { get; } = connection;
        public System.Net.EndPoint RemoteEndPoint { get; } = remoteEndPoint;
    }

    /// <summary>
    /// Message để thông báo client đã ngắt kết nối
    /// </summary>
    public class ClientDisconnected(int sessionId)
    {
        public int SessionId { get; } = sessionId;
    }

    /// <summary>
    /// Message để broadcast packet đến tất cả client
    /// </summary>
    public class BroadcastPacket(byte[] data, Predicate<ClientSession>? filter = null)
    {
        public byte[] Data { get; } = data;
        public Predicate<ClientSession>? Filter { get; } = filter;
    }

    /// <summary>
    /// Message để broadcast packet đến một nhóm client cụ thể
    /// </summary>
    public class BroadcastToGroup(byte[] data, int[] sessionIds)
    {
        public byte[] Data { get; } = data;
        public int[] SessionIds { get; } = sessionIds;
    }

    /// <summary>
    /// Message để kiểm tra trạng thái kết nối
    /// </summary>
    public class CheckConnectionStatus(int sessionId)
    {
        public int SessionId { get; } = sessionId;
    }

    /// <summary>
    /// Message phản hồi trạng thái kết nối
    /// </summary>
    public class ConnectionStatusResponse(int sessionId, bool isConnected, DateTime lastActivity)
    {
        public int SessionId { get; } = sessionId;
        public bool IsConnected { get; } = isConnected;
        public DateTime LastActivity { get; } = lastActivity;
    }

    /// <summary>
    /// Message để cleanup tài nguyên
    /// </summary>
    public class CleanupResources(int sessionId)
    {
        public int SessionId { get; } = sessionId;
    }

    /// <summary>
    /// Message để thống kê hệ thống
    /// </summary>
    public class GetNetworkStats
    {
        public static readonly GetNetworkStats Instance = new();
    }

    /// <summary>
    /// Message phản hồi thống kê hệ thống
    /// </summary>
    public class NetworkStatsResponse(int activeConnections, long totalPacketsSent, long totalPacketsReceived, long totalBytesTransferred)
    {
        public int ActiveConnections { get; } = activeConnections;
        public long TotalPacketsSent { get; } = totalPacketsSent;
        public long TotalPacketsReceived { get; } = totalPacketsReceived;
        public long TotalBytesTransferred { get; } = totalBytesTransferred;
    }

    /// <summary>
    /// Message phản hồi số lượng session
    /// </summary>
    public class SessionCountResponse(int count)
    {
        public int Count() => count;
    }
}
