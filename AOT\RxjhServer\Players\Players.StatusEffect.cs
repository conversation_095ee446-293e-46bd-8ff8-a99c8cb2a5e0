﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
	public void TianyunyuePlasticSurgeryEffect(int vatPhamId)
	{
		var value = 0;
		switch (new Random().Next(1, 5))
		{
			case 1:
				value = 2001;
				break;
			case 2:
				value = 2002;
				break;
			case 3:
				value = 2003;
				break;
			case 4:
				value = 2004;
				break;
			case 5:
				value = 2005;
				break;
		}
		var array = Converter.HexStringToByte("AA5516007104300210000100000034E1143C000000000100D10755AA");
		Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 24, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		NewCharacterTemplate.KhuonMat = BitConverter.ToInt16(BitConverter.GetBytes(value), 0);
		LoadCharacterWearItem();
		UpdateMartialArtsAndStatus();
		UpdateBroadcastCharacterData();
		UpdateEquipmentEffects();
	}

	public void TianyunyueBeautyEffect(int vatPhamId)
	{
		var value = 0;
		switch (new Random().Next(1, 5))
		{
			case 1:
				value = 2001;
				break;
			case 2:
				value = 2002;
				break;
			case 3:
				value = 2003;
				break;
			case 4:
				value = 2004;
				break;
			case 5:
				value = 2005;
				break;
		}
		var array = Converter.HexStringToByte("AA5516007104300210000100000033E1143C000000000100D30755AA");
		Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 24, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		NewCharacterTemplate.KieuToc = BitConverter.ToInt16(BitConverter.GetBytes(value), 0);
		LoadCharacterWearItem();
		UpdateMartialArtsAndStatus();
		UpdateBroadcastCharacterData();
		UpdateEquipmentEffects();
	}

	public void EndAbnormalBlueStatusList()
	{
		if (TrangThai_XanhLam_BatThuong == null)
		{
			return;
		}
		var queue = Queue.Synchronized(new Queue());
		try
		{
			foreach (var value in TrangThai_XanhLam_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				if (World.jlMsg == 1)
				{
					LogHelper.WriteLine(0, "TrangThai_XanhLam_BatThuong Danh sách");
				}
				var xDiThuongRoiLamTrangThaiLoai = (X_Di_Thuong_Roi_Lam_Trang_Thai_Loai)queue.Dequeue();
				xDiThuongRoiLamTrangThaiLoai.ThoiGianKetThucSuKien();
				TrangThai_XanhLam_BatThuong?.Remove(xDiThuongRoiLamTrangThaiLoai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TrangThai_XanhLam_BatThuong Liệt kê danh sách error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void EndAbnormalBloodDropStatusList()
	{
		if (TrangThai_MatMau_BatThuong == null)
		{
			return;
		}
		var queue = Queue.Synchronized(new Queue());
		try
		{
			foreach (var value in TrangThai_MatMau_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				if (World.jlMsg == 1)
				{
					LogHelper.WriteLine(0, "TrangThai MatMau BatThuong Danh sách");
				}
				var xDiThuongMatMauTrangThaiLoai = (X_Di_Thuong_Mat_Mau_Trang_Thai_Loai)queue.Dequeue();
				xDiThuongMatMauTrangThaiLoai.ThoiGianKetThucSuKien();
				TrangThai_MatMau_BatThuong?.Remove(xDiThuongMatMauTrangThaiLoai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TrangThai_MatMau_BatThuong Liệt kê danh sách error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void EndAbnormalAttackStatusList()
	{
		if (TrangThai_TanCong_BatThuong == null)
		{
			return;
		}
		var queue = Queue.Synchronized(new Queue());
		try
		{
			foreach (var value in TrangThai_TanCong_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				if (World.jlMsg == 1)
				{
					LogHelper.WriteLine(0, "TrangThai TanCong BatThuong Danh sách");
				}
				var xDiThuongTrangThaiCongKichLoai = (X_Di_Thuong_Trang_Thai_Cong_Kich_Loai)queue.Dequeue();
				xDiThuongTrangThaiCongKichLoai.ThoiGianKetThucSuKien();
				TrangThai_TanCong_BatThuong?.Remove(xDiThuongTrangThaiCongKichLoai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TrangThai TanCong BatThuong Liệt kê danh sách error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void EndTheAbnormalDefenseStatusList()
	{
		if (TrangThai_PhongThu_BatThuong == null)
		{
			return;
		}
		var queue = Queue.Synchronized(new Queue());
		try
		{
			foreach (var value in TrangThai_PhongThu_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				if (World.jlMsg == 1)
				{
					LogHelper.WriteLine(0, "TrangThai_PhongThu_BatThuong Danh sách");
				}
				var xDiThuongTrangThaiPhongNguLoai = (X_Di_Thuong_Trang_Thai_Phong_Ngu_Loai)queue.Dequeue();
				xDiThuongTrangThaiPhongNguLoai.ThoiGianKetThucSuKien();
				TrangThai_PhongThu_BatThuong?.Remove(xDiThuongTrangThaiPhongNguLoai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TrangThai_PhongThu_BatThuong Liệt kê danh sách error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}
	
	public void Click_Chuot_Phai_Xoa_Buff_HoTro(byte[] data, int length)
	{
		var num = BitConverter.ToInt32(data, 10);
		try
		{
			var source = new int[11]
			{
				501301, 501302, 501303, 501401, 501402, 501403, 401301, 401302, 401201, 401202,
				401203
			};
			if (source.Contains(num) && AppendStatusList.ContainsKey(num))
			{
				AppendStatusList[num].ThoiGianKetThucSuKien();
			}
		}
		catch
		{
		}
	}
	
	public void WalkingState(byte[] packetData, int trangThaiId)
	{
		var array = Converter.HexStringToByte("AA55260060033D002000010000000100010000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(packetData, 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(trangThaiId), 0, array, 16, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		WalkingStatusId = trangThaiId;
		if (packetData[0] == 1 && packetData[1] == 0 && packetData[2] == 0 && packetData[3] == 0)
		{
			tracking_status_id1 = 1;
			if (Client != null)
			{
				BitConverter.ToInt16(array, 6);
				Client.Send_Map_Data(array, array.Length);
			}
			SendCurrentRangeBroadcastData(array, array.Length);
		}

		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void StateEffectCharacterBeast(byte[] wgId, int switchOnOffint, int sj)
	{
		SendingClass sendingClass = new();
		sendingClass.Write8(BitConverter.ToInt32(wgId, 0));
		sendingClass.Write4(0);
		sendingClass.Write4(switchOnOffint);
		sendingClass.Write4(sj);
		sendingClass.Write4(0);
		Client?.SendPak(sendingClass, 34560, CharacterBeastFullServiceID);
		SendCurrentRangeBroadcastData(sendingClass, 34560, CharacterBeastFullServiceID);
	}

	public void StateEffectsNew(int trangThaiId, int switchOnOff, int thoiGian, int soLuong, int soLuongLoaiHinh)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(trangThaiId);
		sendingClass.Write4(switchOnOff);
		sendingClass.Write4(thoiGian);
		sendingClass.Write4(0);
		sendingClass.Write4(soLuong);
		sendingClass.Write4(soLuongLoaiHinh);
		Client?.SendPak(sendingClass, 35328, SessionID);
	}

	public void StatusEffect(byte[] wgId, int switchOnOffint, int sj)
	{
		var num = BitConverter.ToInt32(wgId, 0);
		if (num != 242)
		{
			SendingClass sendingClass = new();
			sendingClass.Write8(num);
			sendingClass.Write4(0);
			sendingClass.Write4(switchOnOffint);
			sendingClass.Write4(sj);
			sendingClass.Write4(0);
			Client?.SendPak(sendingClass, 34560, SessionID);
			SendCurrentRangeBroadcastData(sendingClass, 34560, SessionID);
		}
	}
	
	public void NewStatusEffect(int wgId, int sj, int switchOnOff)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(wgId);
		sendingClass.Write4(0);
		sendingClass.Write2(92);
		sendingClass.Write2(1);
		sendingClass.Write(199);
		sendingClass.Write(49);
		sendingClass.Write(174);
		sendingClass.Write(95);
		sendingClass.Write4(switchOnOff);
		sendingClass.Write4(sj);
		Client?.SendPak(sendingClass, 17153, SessionID);
	}

	public void StatusEffect(int diThuongId, int switchOnOff, int diThuongSoLuong, int thoiGian)
	{
		var array = Converter.HexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(diThuongId), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(diThuongId), 0, array, 58, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(switchOnOff), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(switchOnOff), 0, array, 62, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(thoiGian), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(diThuongSoLuong), 0, array, 42, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}
	
	public void Ban_Phao_Hoa(int daoCuId, int castObject)
	{
		var array = Converter.HexStringToByte("AA5512000A007F000400CD99053C000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(daoCuId), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(castObject), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void HieuUng_ThangCap()
	{
		var array = Converter.HexStringToByte("AA55120014047F000400A0CB0000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}
	
	public void LinhThu_ChuyenChuc_NgheNghiep(int chinhTa, int chuyenChuc)
	{
		CharacterBeast.FLD_JOB_LEVEL = chuyenChuc;
		SendingClass sendingClass = new();
		sendingClass.Write(chuyenChuc);
		sendingClass.Write(chinhTa);
		Client?.SendPak(sendingClass, 32256, CharacterBeastFullServiceID);
		SendCurrentRangeBroadcastData(sendingClass, 32256, CharacterBeastFullServiceID);
	}

	public void CharacterToProfession(int nhanVatChinhTa, int chuyenChuc)
	{
		try
		{
			Player_Zx = nhanVatChinhTa;
			Player_Job_level = chuyenChuc;
			SendingClass sendingClass = new();
			sendingClass.Write(chuyenChuc);
			sendingClass.Write(nhanVatChinhTa);
			Client?.SendPak(sendingClass, 32256, SessionID);
			SendCurrentRangeBroadcastData(sendingClass, 32256, SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi hiệu ứng khi Thăng Chức hoặc Thăng Thiên !!!");
		}
	}

	public void EffectOfTakingMedicine(int vatPhamId)
	{
		SendingClass sendingClass = new();
		sendingClass.Write8(vatPhamId);
		Client?.SendPak(sendingClass, 32512, SessionID);
		SendCurrentRangeBroadcastData(sendingClass, 32512, SessionID);
	}

	public void TheEffectOfTakingMedicine(int vatPhamId)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(vatPhamId);
		Client?.SendPak(sendingClass, 32512, CharacterBeast.FullServiceID);
		SendCurrentRangeBroadcastData(sendingClass, 32512, CharacterBeast.FullServiceID);
	}
	
	public void ThanhTru_NoKhi()
	{
		Van_Cong = false;
		if (PrivateState(700014))
		{
			var xThemVaoTrangThaiLoai = AppendStatusList[700014];
			xThemVaoTrangThaiLoai.ThoiGianKetThucSuKien();
		}
		if (NhanVat_SP > 0)
		{
			NhanVat_SP = 0;
			CapNhat_HP_MP_SP();
		}
	}

	public void ThanhTru_KhinhCong(byte[] packetData, int packetSize)
	{
		var array = new byte[4];
		Buffer.BlockCopy(packetData, 10, array, 0, 4);
		var num = BitConverter.ToInt32(array, 0);
		if (GetAddState(601102))
		{
			AppendStatusList[601102].ThoiGianKetThucSuKien();
		}
		if (!GetAddState(601102))
		{
			StatusEffect value = new(this, 120000, 601102, 0);
			AppendStatusList.Add(601102, value);
			StatusEffect(BitConverter.GetBytes(601102), 1, 120000);
		}
		WalkingStatusId = 3;
		WalkingState(array, 3);
		UpdateMovementSpeed();
	}

	public void Xoa_TrangThai_Pill()
	{
		try
		{
			foreach (var key in AppendStatusList.Keys)
			{
				if (World.MagicList.TryGetValue(key, out var value) && value.FLD_VoCongLoaiHinh == 1)
				{
					AppendStatusList[key].ThoiGianKetThucSuKien();
				}
			}
		}
		catch
		{
		}
	}

}
