﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class kiemtrathietbi {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('kiemtrathietbi_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? vatphamloaihinh { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatcongkichgiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatphongngugiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhathpgiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatnoiconggiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhattrungdichgiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatnetranhgiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatcongkichvoconggiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatkhiconggiatri { get; set; }

		[JsonProperty]
		public int? vatphamcaonhatphuhongiatri { get; set; }

	}

}
