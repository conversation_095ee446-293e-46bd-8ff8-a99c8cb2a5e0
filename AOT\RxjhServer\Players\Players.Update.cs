﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    	public void CapNhat_HP_MP_SP()
	{
		try
		{
			if (TeamID == 0 && GetAddState(1000001506))
			{
				AppendStatusList[1000001506].ThoiGianKetThucSuKien();
			}
			if (NhanVat_SP > CharacterMax_SP)
			{
				NhanVat_SP = 0;
				if (Player_Job == 6)
				{
					NoKhi_SoLuong++;
					if (NoKhi_SoLuong > 5)
					{
						NoKhi_SoLuong = 5;
					}
				}
				else if (Player_Job == 7)
				{
					var num = 0;
					if (GetAddState(901301))
					{
						if (ThuongHaDieu_DemSo >= 3)
						{
							ThuongHaDieu_DemSo = 0;
						}
						switch (ThuongHaDieu_DemSo)
						{
							case 1:
							case 2:
								num = 900403;
								break;
							case 0:
								num = 900401;
								break;
						}
						if (num != 0 && !GetAddState(900401) && !GetAddState(900402) && !GetAddState(900403))
						{
							switch (num)
							{
								case 900401:
									CamSu_TrangThai = 16;
									break;
								case 900402:
									CamSu_TrangThai = 32;
									break;
								case 900403:
									CamSu_TrangThai = 64;
									break;
							}
							StatusEffect value = new(this, 60000, num, 0);
							AppendStatusList.Add(num, value);
							StatusEffect(BitConverter.GetBytes(num), 1, 60000);
							ThuongHaDieu_DemSo++;
						}
					}
					else if (GetAddState(901302))
					{
						if (LowerRiverMonitor >= 3)
						{
							LowerRiverMonitor = 0;
						}
						switch (LowerRiverMonitor)
						{
							case 1:
							case 2:
								num = 900402;
								break;
							case 0:
								num = 900401;
								break;
						}
						if (num != 0 && !GetAddState(900401) && !GetAddState(900402) && !GetAddState(900403))
						{
							switch (num)
							{
								case 900401:
									CamSu_TrangThai = 16;
									break;
								case 900402:
									CamSu_TrangThai = 32;
									break;
								case 900403:
									CamSu_TrangThai = 64;
									break;
							}
							StatusEffect value2 = new(this, 60000, num, 0);
							AppendStatusList.Add(num, value2);
							StatusEffect(BitConverter.GetBytes(num), 1, 60000);
							LowerRiverMonitor++;
						}
					}
					else if (GetAddState(901303) && NgocLienHoan != null)
					{
						try
						{
							if (NgocLienHoan_DemSo >= 6)
							{
								NgocLienHoan_DemSo = 0;
							}
							switch (NgocLienHoan_DemSo)
							{
								case 0:
									num = NgocLienHoan[0];
									break;
								case 1:
									num = NgocLienHoan[1];
									break;
								case 2:
									num = NgocLienHoan[2];
									break;
								case 3:
									num = NgocLienHoan[3];
									break;
								case 4:
									num = NgocLienHoan[4];
									break;
								case 5:
									num = NgocLienHoan[5];
									break;
							}
							if (num != 0 && !GetAddState(900401) && !GetAddState(900402) && !GetAddState(900403))
							{
								switch (num)
								{
									case 900401:
										CamSu_TrangThai = 16;
										break;
									case 900402:
										CamSu_TrangThai = 32;
										break;
									case 900403:
										CamSu_TrangThai = 64;
										break;
								}
								StatusEffect value3 = new(this, 60000, num, 0);
								AppendStatusList.Add(num, value3);
								StatusEffect(BitConverter.GetBytes(num), 1, 60000);
								NgocLienHoan_DemSo++;
							}
						}
						catch
						{
						}
					}
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
				}
				else if (!GetAddState(700014))
				{
					NoKhi = true;
					var num2 = 10000 + (int)CuongPhong_VanPha;
					StatusEffect value4;
					if (Player_Job == 3)
					{
						var num3 = 0.25 + 0.25 * THUONG_MatNhatCuongVu;
						value4 = new StatusEffect(this, num2, 700014, 0, num3);
						CPVP_ThemVao_Dame = num3;
						CPVP_ThemVao_Def = num3;
					}
					else if (Player_Job == 4)
					{
						value4 = new StatusEffect(this, num2, 700014, 0);
						CPVP_ThemVao_Dame = 0.15;
						CPVP_ThemVao_Def = 0.15;
					}
					else if (Player_Job == 5)
					{
						num2 = 10000 + (int)ThangThien_1_KhiCong_CuongPhongThienY * 3000 + 3000;
						value4 = new StatusEffect(this, num2, 700014, 0);
						CPVP_ThemVao_Dame = 0.15;
						CPVP_ThemVao_Def = 0.2;
					}
					else if (Player_Job == 10)
					{
						var num4 = 0.25 + 0.25 * QuyenSu_MatNhatCuongVu;
						value4 = new StatusEffect(this, num2, 700014, 0, num4);
						CPVP_ThemVao_Dame = num4;
						CPVP_ThemVao_Def = num4;
						QuyenSu_HoiTamNhatKich_UyLuc = 0.3;
					}
					else if (Player_Job == 11)
					{
						num2 = (int)MaiLieuChan_HuyenVuThanCong + 10000;
						double num5 = (int)(MaiLieuChan_ChuongLucVanDung * 1.2);
						value4 = new StatusEffect(this, num2, 700014, 0, num5);
						CPVP_ThemVao_Dame = 0.2;
						CharacterIsBasicallyTheLargest_Barrier = (int)(CharacterIsBasicallyTheLargest_Barrier * 1.2 + 0.5);
						if (MaiLieuChan_ChuongLucVanDung > 0.0)
						{
							MaiLieuChan_ChuongLucVanDung += num5;
						}
					}
					else
					{
						value4 = new StatusEffect(this, num2, 700014, 0);
						CPVP_ThemVao_Dame = 0.2;
						CPVP_ThemVao_Def = 0.2;
					}
					AppendStatusList.Remove(700014);
					AppendStatusList.Add(700014, value4);
					StatusEffect(BitConverter.GetBytes(700014), 1, num2);
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					UpdateMartialArtsAndStatus();
				}
			}
			if (NhanVat_HP > CharacterMax_HP)
			{
				NhanVat_HP = CharacterMax_HP;
			}
			if (NhanVat_MP > CharacterMax_MP)
			{
				NhanVat_MP = CharacterMax_MP;
			}
			if (NhanVat_AP > CharacterMax_AP)
			{
				NhanVat_AP = CharacterMax_AP;
			}
			if (NhanVat_HP < 0)
			{
				NhanVat_HP = CharacterMax_HP;
			}
			if (NhanVat_MP < 0)
			{
				NhanVat_MP = CharacterMax_MP;
			}
			if (NhanVat_AP < 0)
			{
				NhanVat_AP = CharacterMax_AP;
			}
			SendingClass w = new();
			// w.Write(1);
			// w.Write(3);
			// w.Write2(0);
			w.Write4(1);
			w.Write4(NhanVat_HP);
			w.Write4(NhanVat_MP);
			w.Write4(NhanVat_SP);
			w.Write4(CharacterMax_HP);
			w.Write4(CharacterMax_MP);
			w.Write4(CharacterMax_SP);
			w.Write4(NoKhi_SoLuong);
			if (Player_Job == 11)
			{
				w.Write4(CharacterMax_AP);
				w.Write4(NhanVat_AP);
				w.Write4((int)MaiLieuChan_ChuongLucVanDung + FLD_TrangBi_ThemVao_RecoveryMoney);
			}
			else
			{
				w.Write4(0);
				w.Write4(0);
				w.Write4(66);
			}
			w.Write(0);
			w.Write(NoKhi_Point);
			w.Write2(0);
			w.Write4(0);
			w.Write4(NhanVatCuongKhi);
			Client?.SendPak(w, 26880, SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Cập nhật HP MP SP lỗi - [" + AccountID + "] - [" + CharacterName + "] - Char:[" + Player_Job + "] - " + ex.Message);
		}
	}
	public void UpdateMartialArtsCoolDown()
	{
		try
		{
			List<X_Vo_Cong_Loai> VoCong_CoolDown_List = new();
			foreach (X_Vo_Cong_Loai value in World.MagicList.Values)
			{
				if (base.Player_Job == value.FLD_JOB || value.FLD_JOB == 0)
				{
					VoCong_CoolDown_List.Add(value);
				}
			}
			var array = new int[21]
			{
					**********, **********, **********, **********, **********, **********, **********, **********, **********, **********,
					**********, **********, **********, **********, **********, **********, **********, **********, **********, **********,
					**********
			};
			foreach (int key in array)
			{
				X_Vo_Cong_Loai x_Vo_Cong_Loai = new()
				{
					FLD_PID = key,
					FLD_JOB = 0,
					FLD_CDTIME = World.DelayBomMau_KhiPK
				};
				VoCong_CoolDown_List.Add(x_Vo_Cong_Loai);
			}
			if (VoCong_CoolDown_List.Count == 0)
			{
				return;
			}
			using SendingClass sendingClass = new();
			sendingClass.Write2(1);
			sendingClass.Write2(1);
			sendingClass.Write2(VoCong_CoolDown_List.Count);
			foreach (X_Vo_Cong_Loai voCong_CoolDown_ in VoCong_CoolDown_List)
			{
				sendingClass.Write8(voCong_CoolDown_.FLD_PID);
				sendingClass.Write8(0L);
				sendingClass.Write8(0L);
				sendingClass.Write8(0L);
				sendingClass.Write8(voCong_CoolDown_.FLD_CDTIME);
				sendingClass.Write8(0L);
				sendingClass.Write8(0L);
				sendingClass.Write4(0);
			}
			base.Client?.SendPak(sendingClass, 4610, base.SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "UpdateMartialArtsCool error" + SessionID + "|" + ToString() + "  " + ex.Message);
		}
	}

}
