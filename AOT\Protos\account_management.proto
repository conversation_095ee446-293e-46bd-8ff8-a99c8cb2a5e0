syntax = "proto3";

import "google/protobuf/timestamp.proto";

option csharp_namespace = "HeroYulgang.Services";

package account_management;

// Account Management Service Definition
service AccountManagement {
  // Get detailed information of all players
  rpc GetDetailedPlayers (GetDetailedPlayersRequest) returns (GetDetailedPlayersResponse);

  // Get detailed information of a specific player by name
  rpc GetPlayerDetail (GetPlayerDetailRequest) returns (GetPlayerDetailResponse);

  // Get account information with all characters
  rpc GetAccountInfo (GetAccountInfoRequest) returns (GetAccountInfoResponse);

  // Get party information
  rpc GetPartyInfo (GetPartyInfoRequest) returns (GetPartyInfoResponse);

  // Save all characters data
  rpc SaveAllCharacters (SaveAllCharactersRequest) returns (SaveAllCharactersResponse);

  // Stop game server
  rpc StopGameServer (StopGameServerRequest) returns (StopGameServerResponse);

  // Restart game server
  rpc RestartGameServer (RestartGameServerRequest) returns (RestartGameServerResponse);

  // Reload config and templates
  rpc ReloadConfig (ReloadConfigRequest) returns (ReloadConfigResponse);

  // Update server list
  rpc UpdateServerList (UpdateServerListRequest) returns (UpdateServerListResponse);
  rpc KickAccount (KickAccountRequest) returns (KickAccountResponse);

  rpc CheckOnline (CheckOnlineRequest) returns (CheckOnlineResponse);

  // Item Management Services
  rpc SendItemToPlayer (SendItemToPlayerRequest) returns (SendItemToPlayerResponse);
  rpc EditPlayerItem (EditPlayerItemRequest) returns (EditPlayerItemResponse);
  rpc UpdateGuildBuff (UpdateGuildBuffRequest) returns (UpdateGuildBuffResponse);
}

message UpdateGuildBuffRequest {
  int32 guild_id = 1;
  string guild_name= 2;
  int32 guild_level= 3;
  bool is_active = 4;
}

message UpdateGuildBuffResponse {
  bool success = 1;
  string message = 2;
}
message KickAccountRequest {
  string account_id = 1;
  string reason = 2;
}

message KickAccountResponse {
  bool success = 1;
  string message = 2;
}

message CheckOnlineRequest {
  string account_id = 1;
}

message CheckOnlineResponse {
  bool success = 1;
  string message = 2;
  bool is_online = 3;
}
// Request Messages
message GetDetailedPlayersRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search_term = 3;
  int32 server_id = 4;
  int32 cluster_id = 5;
  bool online_only = 6;
  string sort_by = 7; // characterName, level, job, mapId, lastActivity
  string sort_order = 8; // asc, desc
  bool include_party_info = 9;
}

message GetPlayerDetailRequest {
  string character_name = 1;
  int32 server_id = 2;
  int32 cluster_id = 3;
}

message GetAccountInfoRequest {
  string account_id = 1;
}

message GetPartyInfoRequest {
  int32 team_id = 1;
}

message SaveAllCharactersRequest {
  bool force_all = 1;
}

message StopGameServerRequest {
  bool graceful = 1;
  int32 timeout_seconds = 2;
}

message RestartGameServerRequest {
  bool graceful = 1;
  int32 timeout_seconds = 2;
}

// Response Messages
message GetDetailedPlayersResponse {
  bool success = 1;
  string message = 2;
  repeated DetailedPlayerInfo players = 3;
  int32 total_count = 4;
  int32 current_page = 5;
  int32 total_pages = 6;
  int32 online_count = 7;
  int32 party_count = 8;
}

message GetPlayerDetailResponse {
  bool success = 1;
  string message = 2;
  DetailedPlayerInfoFull player = 3;
}

message GetAccountInfoResponse {
  bool success = 1;
  string message = 2;
  AccountInfo account = 3;
}

message GetPartyInfoResponse {
  bool success = 1;
  string message = 2;
  PartyInfo party = 3;
}

message SaveAllCharactersResponse {
  bool success = 1;
  string message = 2;
  int32 saved_count = 3;
  int32 failed_count = 4;
  repeated string failed_characters = 5;
}

message StopGameServerResponse {
  bool success = 1;
  string message = 2;
  string server_state = 3;
  int32 connected_players = 4;
}

message RestartGameServerResponse {
  bool success = 1;
  string message = 2;
  string server_state = 3;
  int32 restart_time_seconds = 4;
}

// Data Messages
message DetailedPlayerInfo {
  string account_id = 1;
  string character_name = 2;
  int32 character_index = 3;
  int32 level = 4;
  int32 job = 5;
  string job_name = 6;
  int32 job_level = 7;
  float pos_x = 8;
  float pos_y = 9;
  float pos_z = 10;
  int32 map_id = 11;
  string map_name = 12;
  int64 money = 13;
  string experience = 14;
  int32 hp = 15;
  int32 mp = 16;
  int32 sp = 17;
  int32 max_hp = 18;
  int32 max_mp = 19;
  int32 max_sp = 20;
  bool is_dead = 21;
  int32 off_level = 22;
  int32 off_trade = 23;
  bool is_online = 24;
  int32 session_id = 25;
  string ip_address = 26;
  string login_time = 27;
  string last_activity = 28;
  int32 server_id = 29;
  int32 cluster_id = 30;
  PartyInfo party_info = 31;
  int32 wu_xun = 32;
  int32 qigong_point = 33;
  int32 fight_exp = 34;
  int32 gm_mode = 35;

}
message DetailedPlayerInfoFull {
  string account_id = 1;
  string character_name = 2;
  int32 character_index = 3;
  int32 level = 4;
  int32 job = 5;
  string job_name = 6;
  int32 job_level = 7;
  float pos_x = 8;
  float pos_y = 9;
  float pos_z = 10;
  int32 map_id = 11;
  string map_name = 12;
  int64 money = 13;
  string experience = 14;
  int32 hp = 15;
  int32 mp = 16;
  int32 sp = 17;
  int32 max_hp = 18;
  int32 max_mp = 19;
  int32 max_sp = 20;
  bool is_dead = 21;
  int32 off_level = 22;
  int32 off_trade = 23;
  bool is_online = 24;
  int32 session_id = 25;
  string ip_address = 26;
  string login_time = 27;
  string last_activity = 28;
  int32 server_id = 29;
  int32 cluster_id = 30;
  PartyInfo party_info = 31;
  int32 wu_xun = 32;
  int32 qigong_point = 33;
  int32 fight_exp = 34;
  int32 gm_mode = 35;

  // Extended player data for detailed view
  repeated ItemInfo wear_items = 36;
  repeated ItemInfo inventory_items = 37;
  repeated SkillInfo skills = 38;
  repeated AbilityInfo abilities = 39;
  repeated ItemInfo sub_wear_items = 40;
  repeated  ItemInfo third_wear_items = 41;
  repeated ItemInfo public_warehouse = 42;
  repeated ItemInfo personal_warehouse = 43;
  repeated DrugInfo medicines = 44;
  repeated DrugInfo title_drugs = 45;
  repeated DrugInfo time_medicines = 46;
  repeated AbilityInfo anti_abilities = 47;
  int32 anti_ability_point = 48;
  repeated AbilityInfo asc_abilities = 49;
  string couple = 50;
  string guild = 51;
  int32 than_nu_vo_cong_diem_so = 52;
  int32 atk = 53;
  int32 def = 54;
  int32 matk = 55;
  int32 mdef = 56;
  int32 eva = 57;
  int32 acc = 58;
  int32 monster_atk = 59;
  int32 monster_def = 60;
  int32 skill_eva = 61;
}

message ItemInfo {
  int64 global_id = 1;
  int32 item_id = 2;
  int32 quantity = 3;
  int32 item_option=4;
  int32 item_magic1=5;
  int32 item_magic2=6;
  int32 item_magic3=7;
  int32 item_magic4=8;
  int32 item_medium_soul =9;
  int32 item_day_1 = 10;
  int32 item_day_2 = 11;
  int32 item_low_soul = 12;
  int32 item_quality = 13;
  int32 item_beast = 14;
  
}

message AccountInfo {
  string account_id = 1;
  string email = 2;
  string created_at = 3;
  string last_login_at = 4;
  string last_login_ip = 5;
  bool is_online = 6;
  repeated DetailedPlayerInfo characters = 7;
  int32 total_characters = 8;
  int32 rx_point = 9;
  int32 rx_point_x = 10;
  int32 coin = 11;
  int32 vip = 12;
  string vip_time = 13;
}

message PartyInfo {
  int32 team_id = 1;
  string team_name = 2;
  string leader_name = 3;
  int32 member_count = 4;
  int32 max_members = 5;
  bool is_leader = 6;
  repeated PartyMember members = 7;
}

message PartyMember {
  int32 session_id = 1;
  string character_name = 2;
  int32 level = 3;
  int32 job = 4;
  bool is_online = 5;
  bool is_leader = 6;
}

message DrugInfo {
  int32 drug_id = 1;
  string drug_name = 2;
  int32 drug_time = 3;
}
// Skill information for detailed player data
message SkillInfo {
  int32 skill_id = 1;
  string skill_name = 2;
  int32 current_level = 3;
  int32 max_level = 4;
  int32 experience = 5;
  int32 skill_type = 6;
}

// Ability information for detailed player data
message AbilityInfo {
  int32 ability_id = 1;
  string ability_name = 2;
  int32 current_level = 3;
  int32 max_level = 4;
  int32 points = 5;
  int32 ability_type = 6;
}

message ReloadConfigRequest {
  string config_type = 1; // "all", "config", "template"
}

message ReloadConfigResponse {
  bool success = 1;
  string message = 2;
  repeated string reloaded_items = 3;
  google.protobuf.Timestamp reload_time = 4;
}

message UpdateServerListRequest {
  repeated ServerInfo servers = 1;
}

message UpdateServerListResponse {
  bool success = 1;
  string message = 2;
  int32 updated_count = 3;
}

message ServerInfo {
  int32 server_id = 1;
  string server_ip = 2;
  int32 server_port = 3;
  bool is_online = 4;
}

// Item Management Messages
message SendItemToPlayerRequest {
  string character_name = 1;
  int32 cluster_id = 2;
  int32 channel_id = 3;
  string delivery_method = 4; // "mail" or "direct"
  ItemCreationInfo item_info = 5;
}

message SendItemToPlayerResponse {
  bool success = 1;
  string message = 2;
  string delivery_method = 3;
  bool player_online = 4;
}

message EditPlayerItemRequest {
  string character_name = 1;
  int32 cluster_id = 2;
  int32 channel_id = 3;
  int32 bag_type = 4; // 0 = Item_Wear, 1 = Item_In_Bag
  int32 slot_position = 5;
  ItemCreationInfo item_info = 6;
}

message EditPlayerItemResponse {
  bool success = 1;
  string message = 2;
  bool player_online = 3;
}

message ItemCreationInfo {
  int32 item_id = 1;
  int32 quantity = 2;
  int32 magic0 = 3;
  int32 magic1 = 4;
  int32 magic2 = 5;
  int32 magic3 = 6;
  int32 magic4 = 7;
  int32 low_soul = 8;
  int32 medium_soul = 9;
  int32 tu_linh = 10;
  int32 day1 = 11;
  int32 day2 = 12;
  int32 tien_hoa = 13;
  bool create_new_series = 14;
}
