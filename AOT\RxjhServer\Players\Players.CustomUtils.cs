﻿using HeroYulgang.Helpers;
using RxjhServer.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    

	public void ChuyenDoi_Theluc_TrangBi()
	{
		try
		{
			var parcelVacancyPosition = GetParcelVacancyPosition();
			var itmeClass = World.ItemList[BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0)];
			if (parcelVacancyPosition == -1)
			{
				HeThong<PERSON>hacNho("Hành trang cần trống ít nhất 1 ô để thi triển!", 9, "Thiên cơ các");
			}
			else if (Player_Money < 1000)
			{
				HeThongNhacNho("Không đủ 1,000 ngân lượng để chuyển đổi thể lực trang bị!", 9, "Thiên cơ các");
			}
			else if (Item_In_Bag[0].VatPham_Khoa<PERSON>ai)
			{
				HeThong<PERSON>hacNho("Bảo vật này không thể chuyển đổi linh khí!", 9, "Thiên cơ các");
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == *********)
			{
				AddItem_ThuocTinh_int(*********, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == *********)
			{
				AddItem_ThuocTinh_int(*********, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301013)
			{
				AddItem_ThuocTinh_int(110302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302013)
			{
				AddItem_ThuocTinh_int(110301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301014)
			{
				AddItem_ThuocTinh_int(110302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302014)
			{
				AddItem_ThuocTinh_int(110301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301015)
			{
				AddItem_ThuocTinh_int(110302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302015)
			{
				AddItem_ThuocTinh_int(110301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301019)
			{
				AddItem_ThuocTinh_int(110302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302019)
			{
				AddItem_ThuocTinh_int(110301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301020)
			{
				AddItem_ThuocTinh_int(110302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302020)
			{
				AddItem_ThuocTinh_int(110301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301022)
			{
				AddItem_ThuocTinh_int(110302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302022)
			{
				AddItem_ThuocTinh_int(110301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301023)
			{
				AddItem_ThuocTinh_int(110302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302023)
			{
				AddItem_ThuocTinh_int(110301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110302025)
			{
				AddItem_ThuocTinh_int(110301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 110301025)
			{
				AddItem_ThuocTinh_int(110302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301012)
			{
				AddItem_ThuocTinh_int(120302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302012)
			{
				AddItem_ThuocTinh_int(120301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301013)
			{
				AddItem_ThuocTinh_int(120302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302013)
			{
				AddItem_ThuocTinh_int(120301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301014)
			{
				AddItem_ThuocTinh_int(120302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302014)
			{
				AddItem_ThuocTinh_int(120301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301015)
			{
				AddItem_ThuocTinh_int(120302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302015)
			{
				AddItem_ThuocTinh_int(120301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301019)
			{
				AddItem_ThuocTinh_int(120302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302019)
			{
				AddItem_ThuocTinh_int(120301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301020)
			{
				AddItem_ThuocTinh_int(120302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302020)
			{
				AddItem_ThuocTinh_int(120301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301022)
			{
				AddItem_ThuocTinh_int(120302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302022)
			{
				AddItem_ThuocTinh_int(120301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301023)
			{
				AddItem_ThuocTinh_int(120302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302023)
			{
				AddItem_ThuocTinh_int(120301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120302025)
			{
				AddItem_ThuocTinh_int(120301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 120301025)
			{
				AddItem_ThuocTinh_int(120302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301012)
			{
				AddItem_ThuocTinh_int(210302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302012)
			{
				AddItem_ThuocTinh_int(210301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301013)
			{
				AddItem_ThuocTinh_int(210302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302013)
			{
				AddItem_ThuocTinh_int(210301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301014)
			{
				AddItem_ThuocTinh_int(210302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302014)
			{
				AddItem_ThuocTinh_int(210301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301015)
			{
				AddItem_ThuocTinh_int(210302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302015)
			{
				AddItem_ThuocTinh_int(210301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301019)
			{
				AddItem_ThuocTinh_int(210302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302019)
			{
				AddItem_ThuocTinh_int(210301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301020)
			{
				AddItem_ThuocTinh_int(210302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302020)
			{
				AddItem_ThuocTinh_int(210301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301022)
			{
				AddItem_ThuocTinh_int(210302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302022)
			{
				AddItem_ThuocTinh_int(210301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301023)
			{
				AddItem_ThuocTinh_int(210302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302023)
			{
				AddItem_ThuocTinh_int(210301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210302025)
			{
				AddItem_ThuocTinh_int(210301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 210301025)
			{
				AddItem_ThuocTinh_int(210302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301012)
			{
				AddItem_ThuocTinh_int(220302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302012)
			{
				AddItem_ThuocTinh_int(220301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301013)
			{
				AddItem_ThuocTinh_int(220302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302013)
			{
				AddItem_ThuocTinh_int(220301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301014)
			{
				AddItem_ThuocTinh_int(220302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302014)
			{
				AddItem_ThuocTinh_int(220301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301015)
			{
				AddItem_ThuocTinh_int(220302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302015)
			{
				AddItem_ThuocTinh_int(220301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301019)
			{
				AddItem_ThuocTinh_int(220302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302019)
			{
				AddItem_ThuocTinh_int(220301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301020)
			{
				AddItem_ThuocTinh_int(220302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302020)
			{
				AddItem_ThuocTinh_int(220301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301022)
			{
				AddItem_ThuocTinh_int(220302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302022)
			{
				AddItem_ThuocTinh_int(220301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301023)
			{
				AddItem_ThuocTinh_int(220302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302023)
			{
				AddItem_ThuocTinh_int(220301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302025)
			{
				AddItem_ThuocTinh_int(220301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220301025)
			{
				AddItem_ThuocTinh_int(220302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301012)
			{
				AddItem_ThuocTinh_int(310302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302012)
			{
				AddItem_ThuocTinh_int(310301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301013)
			{
				AddItem_ThuocTinh_int(310302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302013)
			{
				AddItem_ThuocTinh_int(310301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301014)
			{
				AddItem_ThuocTinh_int(310302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302014)
			{
				AddItem_ThuocTinh_int(310301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301015)
			{
				AddItem_ThuocTinh_int(310302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302015)
			{
				AddItem_ThuocTinh_int(310301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301019)
			{
				AddItem_ThuocTinh_int(310302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302019)
			{
				AddItem_ThuocTinh_int(310301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301020)
			{
				AddItem_ThuocTinh_int(310302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302020)
			{
				AddItem_ThuocTinh_int(310301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301022)
			{
				AddItem_ThuocTinh_int(310302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302022)
			{
				AddItem_ThuocTinh_int(310301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301023)
			{
				AddItem_ThuocTinh_int(310302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302023)
			{
				AddItem_ThuocTinh_int(310301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310302025)
			{
				AddItem_ThuocTinh_int(310301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 310301025)
			{
				AddItem_ThuocTinh_int(310302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301012)
			{
				AddItem_ThuocTinh_int(320302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302012)
			{
				AddItem_ThuocTinh_int(320301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301013)
			{
				AddItem_ThuocTinh_int(320302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302013)
			{
				AddItem_ThuocTinh_int(320301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301014)
			{
				AddItem_ThuocTinh_int(320302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302014)
			{
				AddItem_ThuocTinh_int(320301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301015)
			{
				AddItem_ThuocTinh_int(320302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302015)
			{
				AddItem_ThuocTinh_int(320301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301019)
			{
				AddItem_ThuocTinh_int(320302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302019)
			{
				AddItem_ThuocTinh_int(320301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301020)
			{
				AddItem_ThuocTinh_int(320302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302020)
			{
				AddItem_ThuocTinh_int(320301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301022)
			{
				AddItem_ThuocTinh_int(320302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 220302022)
			{
				AddItem_ThuocTinh_int(220301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301023)
			{
				AddItem_ThuocTinh_int(320302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302023)
			{
				AddItem_ThuocTinh_int(320301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320302025)
			{
				AddItem_ThuocTinh_int(320301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 320301025)
			{
				AddItem_ThuocTinh_int(320302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301012)
			{
				AddItem_ThuocTinh_int(410302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302012)
			{
				AddItem_ThuocTinh_int(410301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301013)
			{
				AddItem_ThuocTinh_int(410302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302013)
			{
				AddItem_ThuocTinh_int(410301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301014)
			{
				AddItem_ThuocTinh_int(410302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302014)
			{
				AddItem_ThuocTinh_int(410301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301015)
			{
				AddItem_ThuocTinh_int(410302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302015)
			{
				AddItem_ThuocTinh_int(410301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301019)
			{
				AddItem_ThuocTinh_int(410302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302019)
			{
				AddItem_ThuocTinh_int(410301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301020)
			{
				AddItem_ThuocTinh_int(410302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302020)
			{
				AddItem_ThuocTinh_int(410301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301022)
			{
				AddItem_ThuocTinh_int(410302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302022)
			{
				AddItem_ThuocTinh_int(410301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301023)
			{
				AddItem_ThuocTinh_int(410302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302023)
			{
				AddItem_ThuocTinh_int(410301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410302025)
			{
				AddItem_ThuocTinh_int(410301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 410301025)
			{
				AddItem_ThuocTinh_int(410302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301012)
			{
				AddItem_ThuocTinh_int(520302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302012)
			{
				AddItem_ThuocTinh_int(420301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301013)
			{
				AddItem_ThuocTinh_int(420302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302013)
			{
				AddItem_ThuocTinh_int(420301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301014)
			{
				AddItem_ThuocTinh_int(420302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302014)
			{
				AddItem_ThuocTinh_int(420301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301015)
			{
				AddItem_ThuocTinh_int(420302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302015)
			{
				AddItem_ThuocTinh_int(420301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301019)
			{
				AddItem_ThuocTinh_int(420302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302019)
			{
				AddItem_ThuocTinh_int(420301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301020)
			{
				AddItem_ThuocTinh_int(420302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302020)
			{
				AddItem_ThuocTinh_int(420301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301022)
			{
				AddItem_ThuocTinh_int(420302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302022)
			{
				AddItem_ThuocTinh_int(420301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301023)
			{
				AddItem_ThuocTinh_int(420302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302023)
			{
				AddItem_ThuocTinh_int(420301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420302025)
			{
				AddItem_ThuocTinh_int(420301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 420301025)
			{
				AddItem_ThuocTinh_int(420302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301012)
			{
				AddItem_ThuocTinh_int(510302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302012)
			{
				AddItem_ThuocTinh_int(510301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301013)
			{
				AddItem_ThuocTinh_int(510302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302013)
			{
				AddItem_ThuocTinh_int(510301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301014)
			{
				AddItem_ThuocTinh_int(510302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302014)
			{
				AddItem_ThuocTinh_int(510301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301015)
			{
				AddItem_ThuocTinh_int(510302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302015)
			{
				AddItem_ThuocTinh_int(510301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301019)
			{
				AddItem_ThuocTinh_int(510302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302019)
			{
				AddItem_ThuocTinh_int(510301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301020)
			{
				AddItem_ThuocTinh_int(510302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302020)
			{
				AddItem_ThuocTinh_int(510301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301022)
			{
				AddItem_ThuocTinh_int(510302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302022)
			{
				AddItem_ThuocTinh_int(510301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301023)
			{
				AddItem_ThuocTinh_int(510302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302023)
			{
				AddItem_ThuocTinh_int(510301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510302025)
			{
				AddItem_ThuocTinh_int(510301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 510301025)
			{
				AddItem_ThuocTinh_int(510302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301012)
			{
				AddItem_ThuocTinh_int(520302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302012)
			{
				AddItem_ThuocTinh_int(520301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301013)
			{
				AddItem_ThuocTinh_int(520302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302013)
			{
				AddItem_ThuocTinh_int(520301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301014)
			{
				AddItem_ThuocTinh_int(520302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302014)
			{
				AddItem_ThuocTinh_int(520301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301015)
			{
				AddItem_ThuocTinh_int(520302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302015)
			{
				AddItem_ThuocTinh_int(520301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301019)
			{
				AddItem_ThuocTinh_int(520302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302019)
			{
				AddItem_ThuocTinh_int(520301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301020)
			{
				AddItem_ThuocTinh_int(520302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302020)
			{
				AddItem_ThuocTinh_int(520301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301022)
			{
				AddItem_ThuocTinh_int(520302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302022)
			{
				AddItem_ThuocTinh_int(520301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301023)
			{
				AddItem_ThuocTinh_int(520302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302023)
			{
				AddItem_ThuocTinh_int(520301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520302025)
			{
				AddItem_ThuocTinh_int(520301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 520301025)
			{
				AddItem_ThuocTinh_int(520302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301012)
			{
				AddItem_ThuocTinh_int(710302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302012)
			{
				AddItem_ThuocTinh_int(710301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301013)
			{
				AddItem_ThuocTinh_int(710302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302013)
			{
				AddItem_ThuocTinh_int(710301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301014)
			{
				AddItem_ThuocTinh_int(710302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302014)
			{
				AddItem_ThuocTinh_int(710301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301015)
			{
				AddItem_ThuocTinh_int(710302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302015)
			{
				AddItem_ThuocTinh_int(710301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301019)
			{
				AddItem_ThuocTinh_int(710302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302019)
			{
				AddItem_ThuocTinh_int(710301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301020)
			{
				AddItem_ThuocTinh_int(710302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302020)
			{
				AddItem_ThuocTinh_int(710301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301022)
			{
				AddItem_ThuocTinh_int(710302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302022)
			{
				AddItem_ThuocTinh_int(710301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301023)
			{
				AddItem_ThuocTinh_int(710302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302023)
			{
				AddItem_ThuocTinh_int(710301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710302025)
			{
				AddItem_ThuocTinh_int(710301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 710301025)
			{
				AddItem_ThuocTinh_int(710302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301012)
			{
				AddItem_ThuocTinh_int(720302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302012)
			{
				AddItem_ThuocTinh_int(720301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301013)
			{
				AddItem_ThuocTinh_int(720302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302013)
			{
				AddItem_ThuocTinh_int(720301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301014)
			{
				AddItem_ThuocTinh_int(720302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302014)
			{
				AddItem_ThuocTinh_int(720301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301015)
			{
				AddItem_ThuocTinh_int(720302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302015)
			{
				AddItem_ThuocTinh_int(720301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301019)
			{
				AddItem_ThuocTinh_int(720302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302019)
			{
				AddItem_ThuocTinh_int(720301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301020)
			{
				AddItem_ThuocTinh_int(720302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302020)
			{
				AddItem_ThuocTinh_int(720301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301022)
			{
				AddItem_ThuocTinh_int(720302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302022)
			{
				AddItem_ThuocTinh_int(720301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301023)
			{
				AddItem_ThuocTinh_int(720302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302023)
			{
				AddItem_ThuocTinh_int(720301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720302025)
			{
				AddItem_ThuocTinh_int(720301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 720301025)
			{
				AddItem_ThuocTinh_int(720302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301012)
			{
				AddItem_ThuocTinh_int(810302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302012)
			{
				AddItem_ThuocTinh_int(810301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301013)
			{
				AddItem_ThuocTinh_int(810302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302013)
			{
				AddItem_ThuocTinh_int(810301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301014)
			{
				AddItem_ThuocTinh_int(810302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302014)
			{
				AddItem_ThuocTinh_int(810301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301015)
			{
				AddItem_ThuocTinh_int(810302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302015)
			{
				AddItem_ThuocTinh_int(810301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301019)
			{
				AddItem_ThuocTinh_int(810302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302019)
			{
				AddItem_ThuocTinh_int(810301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301020)
			{
				AddItem_ThuocTinh_int(810302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302020)
			{
				AddItem_ThuocTinh_int(810301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301022)
			{
				AddItem_ThuocTinh_int(810302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302022)
			{
				AddItem_ThuocTinh_int(810301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301023)
			{
				AddItem_ThuocTinh_int(810302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302023)
			{
				AddItem_ThuocTinh_int(810301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810302025)
			{
				AddItem_ThuocTinh_int(810301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 810301025)
			{
				AddItem_ThuocTinh_int(810302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301012)
			{
				AddItem_ThuocTinh_int(820302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302012)
			{
				AddItem_ThuocTinh_int(820301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301013)
			{
				AddItem_ThuocTinh_int(820302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302013)
			{
				AddItem_ThuocTinh_int(820301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301014)
			{
				AddItem_ThuocTinh_int(820302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302014)
			{
				AddItem_ThuocTinh_int(820301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301015)
			{
				AddItem_ThuocTinh_int(820302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302015)
			{
				AddItem_ThuocTinh_int(820301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301019)
			{
				AddItem_ThuocTinh_int(820302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302019)
			{
				AddItem_ThuocTinh_int(820301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301020)
			{
				AddItem_ThuocTinh_int(820302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302020)
			{
				AddItem_ThuocTinh_int(820301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301022)
			{
				AddItem_ThuocTinh_int(820302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302022)
			{
				AddItem_ThuocTinh_int(820301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301023)
			{
				AddItem_ThuocTinh_int(820302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302023)
			{
				AddItem_ThuocTinh_int(820301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820302025)
			{
				AddItem_ThuocTinh_int(820301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 820301025)
			{
				AddItem_ThuocTinh_int(820302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301012)
			{
				AddItem_ThuocTinh_int(910302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302012)
			{
				AddItem_ThuocTinh_int(910301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301013)
			{
				AddItem_ThuocTinh_int(910302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302013)
			{
				AddItem_ThuocTinh_int(910301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301014)
			{
				AddItem_ThuocTinh_int(910302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302014)
			{
				AddItem_ThuocTinh_int(910301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301015)
			{
				AddItem_ThuocTinh_int(910302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302015)
			{
				AddItem_ThuocTinh_int(910301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301019)
			{
				AddItem_ThuocTinh_int(910302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302019)
			{
				AddItem_ThuocTinh_int(910301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301020)
			{
				AddItem_ThuocTinh_int(910302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302020)
			{
				AddItem_ThuocTinh_int(910301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301022)
			{
				AddItem_ThuocTinh_int(910302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302022)
			{
				AddItem_ThuocTinh_int(910301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301023)
			{
				AddItem_ThuocTinh_int(910302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302023)
			{
				AddItem_ThuocTinh_int(910301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910302025)
			{
				AddItem_ThuocTinh_int(910301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 910301025)
			{
				AddItem_ThuocTinh_int(910302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301012)
			{
				AddItem_ThuocTinh_int(920302012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302012)
			{
				AddItem_ThuocTinh_int(920301012, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301013)
			{
				AddItem_ThuocTinh_int(920302013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302013)
			{
				AddItem_ThuocTinh_int(920301013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301014)
			{
				AddItem_ThuocTinh_int(920302014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302014)
			{
				AddItem_ThuocTinh_int(920301014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301015)
			{
				AddItem_ThuocTinh_int(920302015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302015)
			{
				AddItem_ThuocTinh_int(920301015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301019)
			{
				AddItem_ThuocTinh_int(920302019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302019)
			{
				AddItem_ThuocTinh_int(920301019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301020)
			{
				AddItem_ThuocTinh_int(920302020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302020)
			{
				AddItem_ThuocTinh_int(920301020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301022)
			{
				AddItem_ThuocTinh_int(920302022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302022)
			{
				AddItem_ThuocTinh_int(920301022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301023)
			{
				AddItem_ThuocTinh_int(920302023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302023)
			{
				AddItem_ThuocTinh_int(920301023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920302025)
			{
				AddItem_ThuocTinh_int(920301025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 920301025)
			{
				AddItem_ThuocTinh_int(920302025, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501013)
			{
				AddItem_ThuocTinh_int(502013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502013)
			{
				AddItem_ThuocTinh_int(501013, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501014)
			{
				AddItem_ThuocTinh_int(502014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502014)
			{
				AddItem_ThuocTinh_int(501014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501015)
			{
				AddItem_ThuocTinh_int(502015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502015)
			{
				AddItem_ThuocTinh_int(501015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501016)
			{
				AddItem_ThuocTinh_int(502016, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502016)
			{
				AddItem_ThuocTinh_int(501016, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501017)
			{
				AddItem_ThuocTinh_int(502017, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502017)
			{
				AddItem_ThuocTinh_int(501017, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501018)
			{
				AddItem_ThuocTinh_int(502018, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502018)
			{
				AddItem_ThuocTinh_int(501018, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501019)
			{
				AddItem_ThuocTinh_int(502019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502019)
			{
				AddItem_ThuocTinh_int(501019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501020)
			{
				AddItem_ThuocTinh_int(502020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502020)
			{
				AddItem_ThuocTinh_int(501020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502022)
			{
				AddItem_ThuocTinh_int(501022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 501022)
			{
				AddItem_ThuocTinh_int(502022, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801014)
			{
				AddItem_ThuocTinh_int(802014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802014)
			{
				AddItem_ThuocTinh_int(801014, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801015)
			{
				AddItem_ThuocTinh_int(802015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802015)
			{
				AddItem_ThuocTinh_int(801015, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801016)
			{
				AddItem_ThuocTinh_int(802016, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802016)
			{
				AddItem_ThuocTinh_int(801016, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801017)
			{
				AddItem_ThuocTinh_int(802017, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802017)
			{
				AddItem_ThuocTinh_int(801017, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801018)
			{
				AddItem_ThuocTinh_int(802018, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802018)
			{
				AddItem_ThuocTinh_int(801018, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801019)
			{
				AddItem_ThuocTinh_int(802019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802019)
			{
				AddItem_ThuocTinh_int(801019, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801020)
			{
				AddItem_ThuocTinh_int(802020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802020)
			{
				AddItem_ThuocTinh_int(801020, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801021)
			{
				AddItem_ThuocTinh_int(802021, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802021)
			{
				AddItem_ThuocTinh_int(801021, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802023)
			{
				AddItem_ThuocTinh_int(801023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 801023)
			{
				AddItem_ThuocTinh_int(802023, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500113)
			{
				AddItem_ThuocTinh_int(502113, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502113)
			{
				AddItem_ThuocTinh_int(500113, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500114)
			{
				AddItem_ThuocTinh_int(502114, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502114)
			{
				AddItem_ThuocTinh_int(500114, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500115)
			{
				AddItem_ThuocTinh_int(502115, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502115)
			{
				AddItem_ThuocTinh_int(500115, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500116)
			{
				AddItem_ThuocTinh_int(502116, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502116)
			{
				AddItem_ThuocTinh_int(500116, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500117)
			{
				AddItem_ThuocTinh_int(502117, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502117)
			{
				AddItem_ThuocTinh_int(500117, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500118)
			{
				AddItem_ThuocTinh_int(502118, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502118)
			{
				AddItem_ThuocTinh_int(500118, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500119)
			{
				AddItem_ThuocTinh_int(502119, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502119)
			{
				AddItem_ThuocTinh_int(500119, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500120)
			{
				AddItem_ThuocTinh_int(502120, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502120)
			{
				AddItem_ThuocTinh_int(500120, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 502122)
			{
				AddItem_ThuocTinh_int(500122, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500122)
			{
				AddItem_ThuocTinh_int(502122, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800114)
			{
				AddItem_ThuocTinh_int(802114, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802114)
			{
				AddItem_ThuocTinh_int(800114, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800115)
			{
				AddItem_ThuocTinh_int(802115, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802115)
			{
				AddItem_ThuocTinh_int(800115, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800116)
			{
				AddItem_ThuocTinh_int(802116, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802116)
			{
				AddItem_ThuocTinh_int(800116, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800117)
			{
				AddItem_ThuocTinh_int(802117, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802117)
			{
				AddItem_ThuocTinh_int(800117, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800118)
			{
				AddItem_ThuocTinh_int(802118, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802118)
			{
				AddItem_ThuocTinh_int(800118, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800119)
			{
				AddItem_ThuocTinh_int(802119, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802119)
			{
				AddItem_ThuocTinh_int(800119, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800120)
			{
				AddItem_ThuocTinh_int(802120, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802120)
			{
				AddItem_ThuocTinh_int(800120, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800121)
			{
				AddItem_ThuocTinh_int(802121, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802121)
			{
				AddItem_ThuocTinh_int(800121, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 802123)
			{
				AddItem_ThuocTinh_int(800123, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800123)
			{
				AddItem_ThuocTinh_int(802123, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900105)
			{
				AddItem_ThuocTinh_int(900107, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900106)
			{
				AddItem_ThuocTinh_int(900108, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900109)
			{
				AddItem_ThuocTinh_int(900111, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900110)
			{
				AddItem_ThuocTinh_int(900112, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900107)
			{
				AddItem_ThuocTinh_int(900105, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900108)
			{
				AddItem_ThuocTinh_int(900106, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900111)
			{
				AddItem_ThuocTinh_int(900109, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900112)
			{
				AddItem_ThuocTinh_int(900110, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				Player_Money -= 1000L;
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
			}
			else
			{
				HeThongNhacNho("Trang bị này chưa được hỗ trợ trong giang hồ!!!", 9, "Thiên cơ các");
			}
			UpdateMoneyAndWeight();
			Init_Item_In_Bag();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Info, "Lỗi chuyển đổi trang bị chính tà - " + ex.Message);
		}
	}

	public void ChuyenDoi_Theluc_VuKhi()
	{
		try
		{
			var parcelVacancyPosition = GetParcelVacancyPosition();
			var itmeClass = World.ItemList[BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0)];
			if (parcelVacancyPosition == -1)
			{
				HeThongNhacNho("Hành trang cần trống ít nhất 1 ô để thi triển!", 9, "Thiên cơ các");
			}
			else if (Player_Money < 1000)
			{
				HeThongNhacNho("Không đủ 1,000 ngân lượng để chuyển đổi thể lực thần binh!", 9, "Thiên cơ các");
			}
			else if (Item_In_Bag[0].VatPham_KhoaLai)
			{
				HeThongNhacNho("Bảo vật này không thể chuyển đổi linh khí!", 9, "Thiên cơ các");
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == *********)
			{
				AddItem_ThuocTinh_int(*********, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == *********)
			{
				AddItem_ThuocTinh_int(*********, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201250)
			{
				AddItem_ThuocTinh_int(100202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201251)
			{
				AddItem_ThuocTinh_int(100202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201253)
			{
				AddItem_ThuocTinh_int(100202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201254)
			{
				AddItem_ThuocTinh_int(100202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201261)
			{
				AddItem_ThuocTinh_int(100202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201262)
			{
				AddItem_ThuocTinh_int(100202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == *********)
			{
				AddItem_ThuocTinh_int(*********, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == *********)
			{
				AddItem_ThuocTinh_int(*********, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202260)
			{
				AddItem_ThuocTinh_int(100201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202261)
			{
				AddItem_ThuocTinh_int(100201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202263)
			{
				AddItem_ThuocTinh_int(100201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202264)
			{
				AddItem_ThuocTinh_int(100201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202271)
			{
				AddItem_ThuocTinh_int(100201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202272)
			{
				AddItem_ThuocTinh_int(100201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100202274)
			{
				AddItem_ThuocTinh_int(100201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 100201264)
			{
				AddItem_ThuocTinh_int(100202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201037)
			{
				AddItem_ThuocTinh_int(200202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201039)
			{
				AddItem_ThuocTinh_int(200202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201250)
			{
				AddItem_ThuocTinh_int(200202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201251)
			{
				AddItem_ThuocTinh_int(200202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201253)
			{
				AddItem_ThuocTinh_int(200202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201254)
			{
				AddItem_ThuocTinh_int(200202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201261)
			{
				AddItem_ThuocTinh_int(200202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201262)
			{
				AddItem_ThuocTinh_int(200202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202038)
			{
				AddItem_ThuocTinh_int(200201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202040)
			{
				AddItem_ThuocTinh_int(200201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202260)
			{
				AddItem_ThuocTinh_int(200201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202261)
			{
				AddItem_ThuocTinh_int(200201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202263)
			{
				AddItem_ThuocTinh_int(200201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202264)
			{
				AddItem_ThuocTinh_int(200201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202271)
			{
				AddItem_ThuocTinh_int(200201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202272)
			{
				AddItem_ThuocTinh_int(200201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200202274)
			{
				AddItem_ThuocTinh_int(200201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 200201264)
			{
				AddItem_ThuocTinh_int(200202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201037)
			{
				AddItem_ThuocTinh_int(300202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201039)
			{
				AddItem_ThuocTinh_int(300202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201250)
			{
				AddItem_ThuocTinh_int(300202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201251)
			{
				AddItem_ThuocTinh_int(300202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201253)
			{
				AddItem_ThuocTinh_int(300202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201254)
			{
				AddItem_ThuocTinh_int(300202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201261)
			{
				AddItem_ThuocTinh_int(300202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201262)
			{
				AddItem_ThuocTinh_int(300202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202038)
			{
				AddItem_ThuocTinh_int(300201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202040)
			{
				AddItem_ThuocTinh_int(300201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202260)
			{
				AddItem_ThuocTinh_int(300201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202261)
			{
				AddItem_ThuocTinh_int(300201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202263)
			{
				AddItem_ThuocTinh_int(300201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202264)
			{
				AddItem_ThuocTinh_int(300201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202271)
			{
				AddItem_ThuocTinh_int(300201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202272)
			{
				AddItem_ThuocTinh_int(300201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300202274)
			{
				AddItem_ThuocTinh_int(300201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 300201264)
			{
				AddItem_ThuocTinh_int(300202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201037)
			{
				AddItem_ThuocTinh_int(400202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201039)
			{
				AddItem_ThuocTinh_int(400202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201250)
			{
				AddItem_ThuocTinh_int(400202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201251)
			{
				AddItem_ThuocTinh_int(400202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201253)
			{
				AddItem_ThuocTinh_int(400202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201254)
			{
				AddItem_ThuocTinh_int(400202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201261)
			{
				AddItem_ThuocTinh_int(400202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201262)
			{
				AddItem_ThuocTinh_int(400202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202038)
			{
				AddItem_ThuocTinh_int(400201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202040)
			{
				AddItem_ThuocTinh_int(400201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202260)
			{
				AddItem_ThuocTinh_int(400201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202261)
			{
				AddItem_ThuocTinh_int(400201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202263)
			{
				AddItem_ThuocTinh_int(400201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202264)
			{
				AddItem_ThuocTinh_int(400201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202271)
			{
				AddItem_ThuocTinh_int(400201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202272)
			{
				AddItem_ThuocTinh_int(400201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400202274)
			{
				AddItem_ThuocTinh_int(400201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 400201264)
			{
				AddItem_ThuocTinh_int(400202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201037)
			{
				AddItem_ThuocTinh_int(500202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201039)
			{
				AddItem_ThuocTinh_int(500202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201250)
			{
				AddItem_ThuocTinh_int(500202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201251)
			{
				AddItem_ThuocTinh_int(500202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201253)
			{
				AddItem_ThuocTinh_int(500202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201254)
			{
				AddItem_ThuocTinh_int(500202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201261)
			{
				AddItem_ThuocTinh_int(500202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201262)
			{
				AddItem_ThuocTinh_int(500202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202038)
			{
				AddItem_ThuocTinh_int(500201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202040)
			{
				AddItem_ThuocTinh_int(500201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202260)
			{
				AddItem_ThuocTinh_int(500201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202261)
			{
				AddItem_ThuocTinh_int(500201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202263)
			{
				AddItem_ThuocTinh_int(500201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202264)
			{
				AddItem_ThuocTinh_int(500201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202271)
			{
				AddItem_ThuocTinh_int(500201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202272)
			{
				AddItem_ThuocTinh_int(500201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500202274)
			{
				AddItem_ThuocTinh_int(500201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 500201264)
			{
				AddItem_ThuocTinh_int(500202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201037)
			{
				AddItem_ThuocTinh_int(700202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201039)
			{
				AddItem_ThuocTinh_int(700202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201250)
			{
				AddItem_ThuocTinh_int(700202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201251)
			{
				AddItem_ThuocTinh_int(600202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201253)
			{
				AddItem_ThuocTinh_int(700202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201254)
			{
				AddItem_ThuocTinh_int(700202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201261)
			{
				AddItem_ThuocTinh_int(700202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201262)
			{
				AddItem_ThuocTinh_int(700202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202038)
			{
				AddItem_ThuocTinh_int(700201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202040)
			{
				AddItem_ThuocTinh_int(700201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202260)
			{
				AddItem_ThuocTinh_int(700201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202261)
			{
				AddItem_ThuocTinh_int(700201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202263)
			{
				AddItem_ThuocTinh_int(700201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202264)
			{
				AddItem_ThuocTinh_int(700201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202271)
			{
				AddItem_ThuocTinh_int(700201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202272)
			{
				AddItem_ThuocTinh_int(700201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700202274)
			{
				AddItem_ThuocTinh_int(700201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 700201264)
			{
				AddItem_ThuocTinh_int(700202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201037)
			{
				AddItem_ThuocTinh_int(800202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201039)
			{
				AddItem_ThuocTinh_int(800202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201250)
			{
				AddItem_ThuocTinh_int(800202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201251)
			{
				AddItem_ThuocTinh_int(800202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201253)
			{
				AddItem_ThuocTinh_int(800202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201254)
			{
				AddItem_ThuocTinh_int(800202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201261)
			{
				AddItem_ThuocTinh_int(800202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201262)
			{
				AddItem_ThuocTinh_int(800202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202038)
			{
				AddItem_ThuocTinh_int(800201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202040)
			{
				AddItem_ThuocTinh_int(800201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202260)
			{
				AddItem_ThuocTinh_int(800201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202261)
			{
				AddItem_ThuocTinh_int(800201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202263)
			{
				AddItem_ThuocTinh_int(800201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202264)
			{
				AddItem_ThuocTinh_int(800201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202271)
			{
				AddItem_ThuocTinh_int(800201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202272)
			{
				AddItem_ThuocTinh_int(800201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800202274)
			{
				AddItem_ThuocTinh_int(800201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 800201264)
			{
				AddItem_ThuocTinh_int(800202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201037)
			{
				AddItem_ThuocTinh_int(900202038, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201039)
			{
				AddItem_ThuocTinh_int(900202040, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201250)
			{
				AddItem_ThuocTinh_int(900202260, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201251)
			{
				AddItem_ThuocTinh_int(900202261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201253)
			{
				AddItem_ThuocTinh_int(900202263, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201254)
			{
				AddItem_ThuocTinh_int(900202264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201261)
			{
				AddItem_ThuocTinh_int(900202271, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201262)
			{
				AddItem_ThuocTinh_int(900202272, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202038)
			{
				AddItem_ThuocTinh_int(900201037, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202040)
			{
				AddItem_ThuocTinh_int(900201039, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202260)
			{
				AddItem_ThuocTinh_int(900201250, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202261)
			{
				AddItem_ThuocTinh_int(900201251, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202263)
			{
				AddItem_ThuocTinh_int(900201253, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202264)
			{
				AddItem_ThuocTinh_int(900201254, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202271)
			{
				AddItem_ThuocTinh_int(900201261, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202272)
			{
				AddItem_ThuocTinh_int(900201262, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900202274)
			{
				AddItem_ThuocTinh_int(900201264, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) == 900201264)
			{
				AddItem_ThuocTinh_int(900202274, parcelVacancyPosition, 1, Item_In_Bag[0].FLD_MAGIC0, Item_In_Bag[0].FLD_MAGIC1, Item_In_Bag[0].FLD_MAGIC2, Item_In_Bag[0].FLD_MAGIC3, Item_In_Bag[0].FLD_MAGIC4, Item_In_Bag[0].FLD_FJ_LowSoul, Item_In_Bag[0].FLD_FJ_TrungCapPhuHon, Item_In_Bag[0].FLD_FJ_TienHoa, 0, 0);
				Item_In_Bag[0].VatPham_byte = new byte[(World.Newversion >= 14) ? 76 : 73];
				SubtractItem(0, 1);
				LoadCharacterWearItem();
				UpdateEquipmentEffects();
				Player_Money -= 1000L;
				UpdateMoneyAndWeight();
				Init_Item_In_Bag();
			}
			else
			{
				HeThongNhacNho("Thần binh này chưa được hỗ trợ chuyển đổi!!!", 21, "Thiên cơ các");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Info, "Lỗi chuyển đổi vũ khí chính tà - " + ex.Message);
		}
	}
	
	public void TrungSinh_Level_165()
	{
		var num = *********0L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (numberOfRebirths < 0 || playerLevel < 165 || Player_Job_level < 11)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 165 và thăng thiên lục cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (numberOfRebirths >= 1)
			{
				HeThongNhacNho("Đại hiệp đã trùng sinh lần [" + numberOfRebirths + "], hãy trùng sinh lần [" + (numberOfRebirths + 1) + "] tại cấp 165!!", 10, "Thiên cơ các");
				return;
			}
			if (playerMoney <= 0 || playerMoney < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện trùng sinh!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 20, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 141;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 9);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 100;
			BanThuong_ThemVao_PhongThu += 100;
			BanThuong_ThemVao_CLVC += 10;
			BanThuong_ThemVao_PTVC += 250;
			BanThuong_ThemVao_SinhMenh += 500;
			BanThuong_ThemVao_NoiCong += 250;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "[" + AccountID + "]-[" + CharacterName + "]-DT truoc:[" + numberOfRebirths + "]-DT sau:[" + NumberOfRebirths + "]-Lv truoc:[" + playerLevel + "]-Lv sau:[" + Player_Level + "]-G truoc:[" + playerMoney + "]-G sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			World.GuiThongBao("Chúc mừng [" + CharacterName + "] đầu thai lần [" + NumberOfRebirths + "] thành công.");
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Xuất hiện lỗi, đại hiệp hãy chờ vài khắc rồi thử lại!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_154()
	{
		var num = *********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 1 || Player_Level < 154 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 154 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 2)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 156 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 150;
			BanThuong_ThemVao_PhongThu += 130;
			BanThuong_ThemVao_CLVC += 5;
			BanThuong_ThemVao_PTVC += 100;
			BanThuong_ThemVao_SinhMenh += 1000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_156()
	{
		var num = *********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 2 || Player_Level < 156 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 156 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 3)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 157 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 150;
			BanThuong_ThemVao_PhongThu += 130;
			BanThuong_ThemVao_CLVC += 5;
			BanThuong_ThemVao_PTVC += 100;
			BanThuong_ThemVao_SinhMenh += 1000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_157()
	{
		var num = *********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 3 || Player_Level < 157 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 157 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 4)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 158 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 150;
			BanThuong_ThemVao_PhongThu += 130;
			BanThuong_ThemVao_CLVC += 5;
			BanThuong_ThemVao_PTVC += 100;
			BanThuong_ThemVao_SinhMenh += 1000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_158()
	{
		var num = *********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 4 || Player_Level < 158 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 158 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 5)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 159 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 150;
			BanThuong_ThemVao_PhongThu += 130;
			BanThuong_ThemVao_CLVC += 5;
			BanThuong_ThemVao_PTVC += 100;
			BanThuong_ThemVao_SinhMenh += 1000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_159()
	{
		var num = **********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 5 || Player_Level < 159 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 159 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 6)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 160 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 200;
			BanThuong_ThemVao_PhongThu += 180;
			BanThuong_ThemVao_CLVC += 7;
			BanThuong_ThemVao_PTVC += 200;
			BanThuong_ThemVao_SinhMenh += 1500;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_160()
	{
		var num = **********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 6 || Player_Level < 160 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 160 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 7)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 161 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 200;
			BanThuong_ThemVao_PhongThu += 180;
			BanThuong_ThemVao_CLVC += 7;
			BanThuong_ThemVao_PTVC += 200;
			BanThuong_ThemVao_SinhMenh += 1500;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_161()
	{
		var num = **********;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 7 || Player_Level < 161 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 161 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 8)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 162 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 200;
			BanThuong_ThemVao_PhongThu += 180;
			BanThuong_ThemVao_CLVC += 7;
			BanThuong_ThemVao_PTVC += 200;
			BanThuong_ThemVao_SinhMenh += 1500;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_162()
	{
		var num = 3000000000L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 8 || Player_Level < 162 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 162 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 9)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 163 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 200;
			BanThuong_ThemVao_PhongThu += 180;
			BanThuong_ThemVao_CLVC += 7;
			BanThuong_ThemVao_PTVC += 200;
			BanThuong_ThemVao_SinhMenh += 1500;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_163()
	{
		var num = 3000000000L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 9 || Player_Level < 163 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 163 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 10)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 163 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 200;
			BanThuong_ThemVao_PhongThu += 180;
			BanThuong_ThemVao_CLVC += 7;
			BanThuong_ThemVao_PTVC += 200;
			BanThuong_ThemVao_SinhMenh += 1500;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_164()
	{
		var num = *********0L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < 10 || Player_Level < 164 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp 164 và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= 11)
			{
				HeThongNhacNho("Bạn đã đầu thai lần [" + NumberOfRebirths + "] vui lòng đầu thai lần [" + (NumberOfRebirths + 1) + "] level 164 !!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 300;
			BanThuong_ThemVao_PhongThu += 280;
			BanThuong_ThemVao_CLVC += 10;
			BanThuong_ThemVao_PTVC += 250;
			BanThuong_ThemVao_SinhMenh += 2000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_165()
	{
		var num = *********0L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		var num2 = 165;
		var num3 = 11;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < num3 || Player_Level < num2 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp [" + num2 + "], đầu thai [" + (num3 - 1) + "] và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= num3)
			{
				HeThongNhacNho("Đại hiệp đã đầu thai lần [" + num3 + "], hãy đầu thai lần [" + (num3 + 1) + "] tại cấp [" + num2 + "]!!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 300;
			BanThuong_ThemVao_PhongThu += 280;
			BanThuong_ThemVao_CLVC += 10;
			BanThuong_ThemVao_PTVC += 250;
			BanThuong_ThemVao_SinhMenh += 2000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_166()
	{
		var num = *********0L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		var num2 = 166;
		var num3 = 12;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < num3 || Player_Level < num2 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp [" + num2 + "], đầu thai [" + (num3 - 1) + "] và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= num3)
			{
				HeThongNhacNho("Đại hiệp đã đầu thai lần [" + num3 + "], hãy đầu thai lần [" + (num3 + 1) + "] tại cấp [" + num2 + "]!!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += 300;
			BanThuong_ThemVao_PhongThu += 280;
			BanThuong_ThemVao_CLVC += 10;
			BanThuong_ThemVao_PTVC += 250;
			BanThuong_ThemVao_SinhMenh += 2000;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_167()
	{
		var num = *********0L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		var num2 = 167;
		var num3 = 13;
		var num4 = 300;
		var num5 = 280;
		var num6 = 10;
		var num7 = 250;
		var num8 = 2000;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < num3 || Player_Level < num2 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp [" + num2 + "], đầu thai [" + (num3 - 1) + "] và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= num3)
			{
				HeThongNhacNho("Đại hiệp đã đầu thai lần [" + num3 + "], hãy đầu thai lần [" + (num3 + 1) + "] tại cấp [" + num2 + "]!!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += num4;
			BanThuong_ThemVao_PhongThu += num5;
			BanThuong_ThemVao_CLVC += num6;
			BanThuong_ThemVao_PTVC += num7;
			BanThuong_ThemVao_SinhMenh += num8;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}

	public void Dauthai_level_168()
	{
		var num = *********0L;
		var playerMoney = Player_Money;
		var numberOfRebirths = NumberOfRebirths;
		var playerLevel = Player_Level;
		var num2 = 168;
		var num3 = 14;
		var num4 = 300;
		var num5 = 280;
		var num6 = 20;
		var num7 = 100;
		var num8 = 2000;
		try
		{
			if (BitConverter.ToInt32(Item_Wear[0].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[1].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[3].VatPham_ID, 0) != 0L || BitConverter.ToInt32(Item_Wear[4].VatPham_ID, 0) != 0L || (long)BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 0)
			{
				HeThongNhacNho("Đại hiệp hãy tháo toàn bộ bảo vật trên người để tránh lỗi lạc!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths < num3 || Player_Level < num2 || Player_Job_level < 10)
			{
				HeThongNhacNho("Đại hiệp cần đạt cấp [" + num2 + "], đầu thai [" + (num3 - 1) + "] và thăng thiên ngũ cảnh!", 10, "Thiên cơ các");
				return;
			}
			if (NumberOfRebirths >= num3)
			{
				HeThongNhacNho("Đại hiệp đã đầu thai lần [" + num3 + "], hãy đầu thai lần [" + (num3 + 1) + "] tại cấp [" + num2 + "]!!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Money <= 0 || Player_Money < num)
			{
				HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để thực hiện đầu thai!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job == 8)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai = Item_In_Bag[0];
				var parcelVacancy = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), xVatPhamLoai.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 9)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai2 = Item_In_Bag[0];
				var parcelVacancy2 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy2, BitConverter.GetBytes(1), xVatPhamLoai2.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 11)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai3 = Item_In_Bag[0];
				var parcelVacancy3 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy3, BitConverter.GetBytes(1), xVatPhamLoai3.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 12)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai4 = Item_In_Bag[0];
				var parcelVacancy4 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy4, BitConverter.GetBytes(1), xVatPhamLoai4.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			else if (Player_Job == 13)
			{
				if (BitConverter.ToInt32(Item_In_Bag[0].VatPham_ID, 0) != *********)
				{
					HeThongNhacNho("Hãy đặt thần binh Phong Ấn vào ô đầu tiên trong hành trang!", 10, "Thiên cơ các");
					return;
				}
				var xVatPhamLoai5 = Item_In_Bag[0];
				var parcelVacancy5 = GetParcelVacancy(this);
				AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy5, BitConverter.GetBytes(1), xVatPhamLoai5.VatPham_ThuocTinh);
				SubtractItem(0, 1);
			}
			Player_Money -= num;
			NumberOfRebirths++;
			Player_Level = 101;
			CharacterExperience = 0L;
			CharacterToProfession(Player_Zx, 5);
			SaveCharacterData();
			BanThuong_ThemVao_TanCong += num4;
			BanThuong_ThemVao_PhongThu += num5;
			BanThuong_ThemVao_CLVC += num6;
			BanThuong_ThemVao_PTVC += num7;
			BanThuong_ThemVao_SinhMenh += num8;
			BanThuong_ThemVao_NoiCong += 500;
			UpdateMoneyAndWeight();
			CapNhat_HP_MP_SP();
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			LevelUpNoti(1);
			HeThongNhacNho("Đại hiệp hãy chờ một lát, thông tin đang được lưu lại!", 10, "Truyền Âm Các");
			var text = "- [" + AccountID + "] - [" + CharacterName + "] - DT truoc:[" + numberOfRebirths + "]DT sau:[" + NumberOfRebirths + "] - Level truoc:[" + playerLevel + "]Level sau:[" + Player_Level + "] - Gold truoc:[" + playerMoney + "]Gold sau:[" + Player_Money + "]";
			// logo.Log_dau_thai_tru_point(text, UserName);
			QuayLaiChonNhanVat(null, 0);
			var now = DateTime.Now;
			while ((DateTime.Now - now).TotalMilliseconds < 1500.0)
			{
			}
			DangXuat(null, 0);
		}
		catch
		{
			HeThongNhacNho("Phát hiện lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ hoặc thử lại sau vài khắc!!!", 10, "Thiên cơ các");
		}
	}
}
