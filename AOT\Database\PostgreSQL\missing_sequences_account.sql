-- Complete script to add auto-increment primary keys for Account database tables
-- <PERSON><PERSON><PERSON> hoàn chỉnh để thêm auto-increment primary key cho các bảng Account database
-- Generated automatically from entity analysis
-- <PERSON><PERSON><PERSON><PERSON> tạo tự động từ phân tích entity

-- ========================================
-- SECTION 1: CREATE SEQUENCES
-- PHẦN 1: TẠO CÁC SEQUENCE
-- ========================================

CREATE SEQUENCE IF NOT EXISTS "account_id_seq";
CREATE SEQUENCE IF NOT EXISTS "addcash_id_seq";
CREATE SEQUENCE IF NOT EXISTS "banned_id_seq";
CREATE SEQUENCE IF NOT EXISTS "doanhthu_id_seq";
CREATE SEQUENCE IF NOT EXISTS "ipcheck_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_more_run_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_online_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_trucash_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_updatelog_id_seq";
CREATE SEQUENCE IF NOT EXISTS "trucash_id_seq";

-- ========================================
-- SECTION 2: SET SEQUENCE VALUES AND ATTACH TO ID COLUMNS
-- PHẦN 2: THIẾT LẬP GIÁ TRỊ SEQUENCE VÀ GẮN VÀO CÁC CỘT ID
-- ========================================

-- Table: account
SELECT setval('account_id_seq', COALESCE((SELECT MAX("id") FROM "account"), 0) + 1, false);
ALTER TABLE "account"
   ALTER COLUMN "id" SET DEFAULT nextval('account_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "account_id_seq" OWNED BY "account"."id";
SELECT 'Setup completed for account' as status;

-- Table: addcash
SELECT setval('addcash_id_seq', COALESCE((SELECT MAX("id") FROM "addcash"), 0) + 1, false);
ALTER TABLE "addcash"
   ALTER COLUMN "id" SET DEFAULT nextval('addcash_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "addcash_id_seq" OWNED BY "addcash"."id";
SELECT 'Setup completed for addcash' as status;

-- Table: banned
SELECT setval('banned_id_seq', COALESCE((SELECT MAX("id") FROM "banned"), 0) + 1, false);
ALTER TABLE "banned"
   ALTER COLUMN "id" SET DEFAULT nextval('banned_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "banned_id_seq" OWNED BY "banned"."id";
SELECT 'Setup completed for banned' as status;

-- Table: doanhthu
SELECT setval('doanhthu_id_seq', COALESCE((SELECT MAX("id") FROM "doanhthu"), 0) + 1, false);
ALTER TABLE "doanhthu"
   ALTER COLUMN "id" SET DEFAULT nextval('doanhthu_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "doanhthu_id_seq" OWNED BY "doanhthu"."id";
SELECT 'Setup completed for doanhthu' as status;

-- Table: ipcheck
SELECT setval('ipcheck_id_seq', COALESCE((SELECT MAX("id") FROM "ipcheck"), 0) + 1, false);
ALTER TABLE "ipcheck"
   ALTER COLUMN "id" SET DEFAULT nextval('ipcheck_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "ipcheck_id_seq" OWNED BY "ipcheck"."id";
SELECT 'Setup completed for ipcheck' as status;

-- Table: tbl_more_run
SELECT setval('tbl_more_run_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_more_run"), 0) + 1, false);
ALTER TABLE "tbl_more_run"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_more_run_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_more_run_id_seq" OWNED BY "tbl_more_run"."id";
SELECT 'Setup completed for tbl_more_run' as status;

-- Table: tbl_online
SELECT setval('tbl_online_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_online"), 0) + 1, false);
ALTER TABLE "tbl_online"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_online_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_online_id_seq" OWNED BY "tbl_online"."id";
SELECT 'Setup completed for tbl_online' as status;

-- Table: tbl_trucash
SELECT setval('tbl_trucash_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_trucash"), 0) + 1, false);
ALTER TABLE "tbl_trucash"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_trucash_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_trucash_id_seq" OWNED BY "tbl_trucash"."id";
SELECT 'Setup completed for tbl_trucash' as status;

-- Table: tbl_updatelog
SELECT setval('tbl_updatelog_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_updatelog"), 0) + 1, false);
ALTER TABLE "tbl_updatelog"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_updatelog_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_updatelog_id_seq" OWNED BY "tbl_updatelog"."id";
SELECT 'Setup completed for tbl_updatelog' as status;

-- Table: trucash
SELECT setval('trucash_id_seq', COALESCE((SELECT MAX("id") FROM "trucash"), 0) + 1, false);
ALTER TABLE "trucash"
   ALTER COLUMN "id" SET DEFAULT nextval('trucash_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "trucash_id_seq" OWNED BY "trucash"."id";
SELECT 'Setup completed for trucash' as status;

-- ========================================
-- VERIFICATION QUERIES
-- CÁC TRUY VẤN XÁC MINH
-- ========================================

-- Check all sequences created
SELECT schemaname, sequencename, last_value, start_value, increment_by
FROM pg_sequences 
WHERE sequencename LIKE '%_id_seq'
ORDER BY sequencename;

-- Check column defaults for Account tables
SELECT 
    table_name,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN (
    'account', 'addcash', 'banned', 'doanhthu', 'ipcheck',
    'tbl_more_run', 'tbl_online', 'tbl_trucash', 'tbl_updatelog', 'trucash'
) AND column_name = 'id'
ORDER BY table_name;
