# Script to automatically update rd.xml with all FreeSql entity classes
param(
    [string]$ProjectRoot = ".",
    [string]$RdXmlPath = "rd.xml"
)

Write-Host "🔍 Scanning for FreeSql entity classes..." -ForegroundColor Green

# Find all entity classes
$entityFiles = Get-ChildItem -Path "$ProjectRoot\Database\FreeSql\Entities" -Recurse -Filter "*.cs" | 
    Where-Object { $_.Name -notlike "*__*" -and $_.Name -notlike "*.Designer.cs" }

$entityClasses = @()

foreach ($file in $entityFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Extract namespace and class name
    if ($content -match 'namespace\s+([^\s{]+)' -and $content -match 'public\s+partial\s+class\s+(\w+)') {
        $namespace = $matches[1]
        $className = $matches[1] # From second match
        
        # Re-match for class name
        if ($content -match 'public\s+partial\s+class\s+(\w+)') {
            $className = $matches[1]
            $fullClassName = "$namespace.$className"
            $entityClasses += $fullClassName
            Write-Host "  ✓ Found: $fullClassName" -ForegroundColor Cyan
        }
    }
}

Write-Host "📝 Found $($entityClasses.Count) entity classes" -ForegroundColor Green

# Read current rd.xml
$rdXmlContent = Get-Content $RdXmlPath -Raw

# Find the FreeSql Entity Classes section
$startMarker = "<!-- FreeSql Entity Classes -->"
$endMarker = "<!-- JSON Source Generation -->"

if ($rdXmlContent -match "($startMarker.*?)$endMarker") {
    $oldSection = $matches[1]
    
    # Generate new entity entries
    $newEntries = @()
    $newEntries += "      <!-- FreeSql Entity Classes -->"
    
    # Add existing manual entries first
    $newEntries += "      <Type Name=`"HeroYulgang.Database.FreeSql.Entities.Public.ItemTemplate`" Dynamic=`"Required All`" />"
    $newEntries += "      <Type Name=`"HeroYulgang.Database.FreeSql.Entities.Public.MonsterTemplate`" Dynamic=`"Required All`" />"
    
    # Add all discovered entities
    foreach ($entityClass in ($entityClasses | Sort-Object)) {
        $newEntries += "      <Type Name=`"$entityClass`" Dynamic=`"Required All`" />"
    }
    
    $newEntries += ""
    
    $newSection = $newEntries -join "`r`n"
    
    # Replace the section
    $newRdXmlContent = $rdXmlContent -replace [regex]::Escape($oldSection), $newSection
    
    # Write back to file
    Set-Content -Path $RdXmlPath -Value $newRdXmlContent -Encoding UTF8
    
    Write-Host "✅ Updated rd.xml with $($entityClasses.Count) entity classes" -ForegroundColor Green
} else {
    Write-Host "❌ Could not find FreeSql Entity Classes section in rd.xml" -ForegroundColor Red
}

Write-Host "🎉 AOT entities update completed!" -ForegroundColor Green
