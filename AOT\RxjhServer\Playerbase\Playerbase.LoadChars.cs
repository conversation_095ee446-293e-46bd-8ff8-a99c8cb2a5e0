﻿using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Database.FreeSql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;


namespace RxjhServer;

public partial class PlayersBes : X_Khi_Cong_Thuoc_Tinh
{
    
	public void ReadCharacterData()
	{
		var num = 0;
		try
        {
            Clear();
            var character = GameDb.FindCharacter(CharacterName, AccountID).GetAwaiter().GetResult();
            if (character == null && Client != null)
            {
                Client.Dispose();
                LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 62]");
            }
            num = SetData(character);
        }
        catch (Exception ex16)
		{
			LogHelper.WriteLine(LogLevel.Error, "Read Character Data Lỗi nghiêm trọng !! num: [" + num + "] [" + AccountID + "][" + CharacterName + "] - " + ex16.Message +ex16.StackTrace);
			Client.Dispose();
			LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 67]");
		}

	}

    public int SetData(tbl_xwwl_char dbRow)
    {
        int num = SetBaseStat(dbRow);
        num = 113;
        LoadMasterAppearance().Wait();
        num = 114;
        var num3 = 0;
        num3 = LoadAscentionAbility(dbRow, num3);
       
        LoadAbility(dbRow, num3);
        LoadMagic(dbRow);
        LoadAscentionMagic(dbRow);
        LoadQuestBag(dbRow);
        num = LoadItemInBag(dbRow);
        LoadNtcItem(dbRow);
        LoadCostumeBag(dbRow);
        LoadWearItem(dbRow);
		LoadPlayerQuests();
        // LoadPlayerQuest(dbRow);
		// LoadPlayerCompletedQuest(dbRow);
		LoadBirdMail();
        LoadCharacterWareHouse().Wait();
        LoadPublicWareHouse(out num, out var src);
        LoadStime(dbRow);
        LoadChTime(dbRow);
        LoadCTime(dbRow, src);
		LoadThoLinhPhu(dbRow);
        ReadGangData();
        LoadPinkBag(dbRow);
        TinhToan_NhanVatCoBan_DuLieu();
        EquipmentDataVersion = 1;
        ComprehensiveWarehouseEquipmentDataVersion = 1;
		CreatedAt = (DateTime)dbRow.created_at;
        return num;
    }

  
	private async Task LoadMasterAppearance()
	{
		MasterData.TID = -1;
		for (var i = 0; i < 3; i++)
		{
			ApprenticeData[i] = new();
			ApprenticeData[i].TID = -1;
		}

		var master = await GameDb.LoadTeacherList(CharacterName);
		if (master != null)
		{
			MasterData.TID = (int)master.fld_index;
			MasterData.STNAME = master.fld_tname;
			MasterData.TLEVEL = (int)master.fld_tlevel;
			MasterData.STLEVEL = (int)master.fld_stlevel;
			MasterData.STYHD = (int)master.fld_styhd;
			MasterData.STWG1 = (int)master.fld_stwg1;
			MasterData.STWG2 = (int)master.fld_stwg2;
			MasterData.STWG3 = (int)master.fld_stwg3;
		}
		var apprentices = await GameDb.LoadStudentList(CharacterName);
		if (apprentices != null)
		{
			foreach (var apprentice in apprentices)
			{
				var index = (int)apprentice.fld_index;
				if (index <= 2)
				{
					ApprenticeData[index].TID = index;
					ApprenticeData[index].STNAME = apprentice.fld_sname;
					ApprenticeData[index].TLEVEL = (int)apprentice.fld_tlevel;
					ApprenticeData[index].STLEVEL = (int)apprentice.fld_stlevel;
					ApprenticeData[index].STYHD = (int)apprentice.fld_styhd;
					ApprenticeData[index].STWG1 = (int)apprentice.fld_stwg1;
					ApprenticeData[index].STWG2 = (int)apprentice.fld_stwg2;
					ApprenticeData[index].STWG3 = (int)apprentice.fld_stwg3;
				}
			}
		}

		// var dataTable = RxjhClass.DatDuocMasterData(CharacterName);
		// if (dataTable != null)
		// {
		// 	MasterData.TID = (int)dataTable.Rows[0]["FLD_INDEX"];
		// 	MasterData.STNAME = dataTable.Rows[0]["FLD_SNAME"].ToString();
		// 	MasterData.TLEVEL = (int)dataTable.Rows[0]["FLD_TLEVEL"];
		// 	MasterData.STLEVEL = (int)dataTable.Rows[0]["FLD_STLEVEL"];
		// 	MasterData.STYHD = (int)dataTable.Rows[0]["FLD_STYHD"];
		// 	MasterData.STWG1 = (int)dataTable.Rows[0]["FLD_STWG1"];
		// 	MasterData.STWG2 = (int)dataTable.Rows[0]["FLD_STWG2"];
		// 	MasterData.STWG3 = (int)dataTable.Rows[0]["FLD_STWG3"];
		// 	dataTable.Dispose();
		// 	LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 64]");
		// }
		// else
		// {
		// 	var dataTable2 = RxjhClass.DatDuocApprenticeData(CharacterName);
		// 	if (dataTable2 != null)
		// 	{
		// 		for (var j = 0; j < dataTable2.Rows.Count; j++)
		// 		{
		// 			var num2 = (int)dataTable2.Rows[j]["FLD_INDEX"];
		// 			if (num2 <= 2)
		// 			{
		// 				ApprenticeData[num2].TID = (int)dataTable2.Rows[j]["FLD_INDEX"];
		// 				ApprenticeData[num2].STNAME = dataTable2.Rows[j]["FLD_TNAME"].ToString();
		// 				ApprenticeData[num2].TLEVEL = (int)dataTable2.Rows[j]["FLD_TLEVEL"];
		// 				ApprenticeData[num2].STLEVEL = (int)dataTable2.Rows[j]["FLD_STLEVEL"];
		// 				ApprenticeData[num2].STYHD = (int)dataTable2.Rows[j]["FLD_STYHD"];
		// 				ApprenticeData[num2].STWG1 = (int)dataTable2.Rows[j]["FLD_STWG1"];
		// 				ApprenticeData[num2].STWG2 = (int)dataTable2.Rows[j]["FLD_STWG2"];
		// 				ApprenticeData[num2].STWG3 = (int)dataTable2.Rows[j]["FLD_STWG3"];
		// 			}
		// 		}
		// 		dataTable2.Dispose();
		// 		LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 65]");
		// 	}
		// }
	}

	public void CalculateMartialArtsAttackPowerOfHusbandAndWifeData()
	{
		if (VoCongMoi[2, 16] == null)
		{
			return;
		}
		List<int> list = new();
		List<int> list2 = new();
		for (var i = 0; i < 32; i++)
		{
			if (VoCongMoi[3, i] != null)
			{
				var num = VoCongMoi[3, i].FLD_AT + (VoCongMoi[3, i].VoCong_DangCap - 1) * VoCongMoi[3, i].FLD_MoiCapThemNguyHai;
				if (num > 0)
				{
					ThangThienKhiCong_CapDoVoCong = VoCongMoi[3, i].VoCong_DangCap;
					list.Add(num);
					var item = VoCongMoi[3, i].FLD_MP + (VoCongMoi[3, i].VoCong_DangCap - 1) * VoCongMoi[3, i].FLD_MoiCapThemMP;
					list2.Add(item);
				}
			}
		}
		for (var j = 0; j < 32; j++)
		{
			if (VoCongMoi[0, j] != null)
			{
				var num2 = VoCongMoi[0, j].FLD_AT + (VoCongMoi[0, j].VoCong_DangCap - 1) * VoCongMoi[0, j].FLD_MoiCapThemNguyHai;
				if (num2 > 0)
				{
					list.Add(num2);
					list2.Add(VoCongMoi[0, j].FLD_MP);
				}
			}
		}
		var array = list.ToArray();
		var array2 = list2.ToArray();
		Array.Sort(array, array2);
		if (array.Length != 0)
		{
			MartialArtsAttackPowerOfHusbandAndWife = array[array.Length - 1] * 10 / 100 + array[array.Length - 1];
			MartialArtsAttackPowerOfHusbandAndWifeMP = array2[array2.Length - 1];
		}
	}

	/// <summary>
	/// Optimized version using GameDb
	/// Phiên bản tối ưu sử dụng GameDb
	/// </summary>
	public async void ReadGangData()
	{
		try
		{
			// Use optimized GameDb function
			var guildData = await GameDb.GetPlayerGuildDataOptimized(CharacterName);

			if (guildData != null && guildData.HasGuild)
			{
				GuildId = guildData.GuildId;
				GuildName = guildData.GuildName;
				GangCharacterLevel = guildData.MemberLevel;
				GangLevel = guildData.GuildLevel;
				GangServiceWords = guildData.MonPhucWord;
				GangDoorClothesColor = guildData.MonPhucMauSac;
				GangBadge = guildData.MonHuy;
			}
			else
			{
				ResetGuildData();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Lỗi ReadGangData cho {CharacterName}: {ex.Message}");
			ResetGuildData();
		}
	}


	private void ResetGuildData()
	{
		GuildId = 0;
		GuildName = string.Empty;
		GangLevel = 0;
		GangCharacterLevel = 0;
		GangDoorClothesColor = 0;
		GangServiceWords = 0;
		GangBadge = null;
		MonPhai_LienMinh_MinhChu = string.Empty;
		ThongBao_CongThanh = 0;
	}
	
	public void KhoiTaoIconTui()
	{
		try
		{
			StringBuilder stringBuilder = new();
			stringBuilder.Append("AA554B02130571004102C01E0078D192ADD4187CE6143C000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000043BB0B1880E93817016A1800000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000058B8DC5E9CC15419E3E1143C000000000100010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			var array = Converter.HexStringToByte(stringBuilder.ToString());
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại KhoiTaoIconTui !!!");
		}
	}

	public void KhoiTaoTuiNgungThanChau_BaoBoc()
	{
		try
		{
			var array = Converter.HexStringToByte("AA5522022C0171001402ABFB010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "KhoiTaoTuiNgungThanChau_BaoBoc !!!" + ex.Message);
		}
	}

	public void KhoiTaoAoChoang_HanhLy()
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write1(169);
			sendingClass.Write4(0);
			for (var i = 0; i < 66; i++)
			{
				if (BitConverter.ToInt32(AoChang_HanhLy[i].VatPhamSoLuong, 0) == 0)
				{
					AoChang_HanhLy[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					sendingClass.Write(new byte[World.Item_Byte_Length_92], 0, World.Item_Byte_Length_92);
				}
				else
				{
					sendingClass.Write(AoChang_HanhLy[i].GetByte(), 0, World.Item_Byte_Length_92);
				}
			}
			if (Client != null)
			{
				Client.SendPak(sendingClass, 40448, SessionID);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Khởi tạo túi áo choàng !!");
		}
	}

	public void INITIALIZE_PINK_BAG()
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write1(193);
			sendingClass.Write4(0);
			for (var i = 0; i < 24; i++)
			{
				if (BitConverter.ToInt32(EventBag[i].VatPhamSoLuong, 0) == 0)
				{
					EventBag[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					sendingClass.Write(new byte[World.Item_Byte_Length_92], 0, World.Item_Byte_Length_92);
				}
				else
				{
					sendingClass.Write(EventBag[i].GetByte(), 0, World.Item_Byte_Length_92);
				}
			}
			if (Client != null)
			{
				Client.SendPak(sendingClass, 15621, SessionID);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Check túi Pink V23 !!!");
		}
	}

	public void Init_Item_In_Bag()
	{
		try
		{
			// Kiểm tra Item_In_Bag đã được khởi tạo chưa
			if (Item_In_Bag == null)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Init_Item_In_Bag Error: Item_In_Bag là null cho player {AccountID}-{CharacterName}");
				return;
			}

			// Đối với offline player, có thể không cần gửi packet
			if (!IsOfflinePlayer() && Client == null)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Init_Item_In_Bag Error: Client là null cho online player {AccountID}-{CharacterName}");
				return;
			}

			//HeThongNhacNho("Tải lại hành trang!", 10, "Thiên cơ các");
			var num = 0;
			SendingClass sendingClass = new();
			sendingClass.Write1(1);
			sendingClass.Write4(2);
			//var text = Converter.ToString(sendingClass.ToArray3());
			List<long> list = new();

			for (var i = 0; i < 96; i++)
			{
				try
				{
					// Kiểm tra Item_In_Bag[i] không null
					if (Item_In_Bag[i] == null)
					{
						Item_In_Bag[i] = new Item();
						Item_In_Bag[i].VatPham_byte = new byte[76];
						continue;
					}

					if (Item_In_Bag[i].GetVatPham_ID != 0)
					{
						if (!list.Contains(Item_In_Bag[i].GetItemGlobal_ID))
						{
							list.Add(Item_In_Bag[i].GetItemGlobal_ID);
						}
						else
						{
							Item_In_Bag[i].VatPham_byte = new byte[76];
							num = 1;
							var getItemGlobal_ID = Item_In_Bag[i].GetItemGlobal_ID;
						}
					}
					if (BitConverter.ToInt32(Item_In_Bag[i].VatPhamSoLuong, 0) == 0)
					{
						Item_In_Bag[i].VatPham_byte = new byte[76];
					}
					else
					{
						KiemTraVatPhamHeThong("EquipmentBarPackage", ref Item_In_Bag[i]);
					}
					if (Item_In_Bag[i].FLD_FJ_TrungCapPhuHon <= 22 && Item_In_Bag[i].FLD_FJ_TrungCapPhuHon >= 21 && Item_In_Bag[i].FLD_FJ_LowSoul > 0)
					{
						Item_In_Bag[i].Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = Item_In_Bag[i].FLD_FJ_TrungCapPhuHon - 20;
					}
					sendingClass.Write(Item_In_Bag[i].GetByte(), 0, World.Item_Byte_Length_92);
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, $"Init_Item_In_Bag Error tại slot {i} cho player {AccountID}-{CharacterName}: {ex.Message}");
					// Khởi tạo lại item slot bị lỗi
					Item_In_Bag[i] = new Item();
					Item_In_Bag[i].VatPham_byte = new byte[76];
					sendingClass.Write(Item_In_Bag[i].GetByte(), 0, World.Item_Byte_Length_92);
				}
			}
			//var text2 = Converter.ToString(sendingClass.ToArray3());
			// Chỉ gửi packet cho online player
			if (CanSendPacket())
			{
				Client.SendPak(sendingClass, 28928, SessionID);
			}
			list.Clear();
			if (num == 1)
			{
				BanAccount(6789, AccountID, "");
				var noiDungLog = "[" + AccountID + "][" + CharacterName + "] Phat hien item trung seri] [" + list?.ToString() + "]";
				// logo.Log_Check_Item_Trung_Seri(noiDungLog);
				if (Client != null)
				{
					Client.Dispose();
					LogHelper.WriteLine(LogLevel.Error, "Phat hien item trung seri [" + AccountID + "]-[" + CharacterName + "][Mã dis 68]");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Khởi tạo túi đồ bị lỗi cho player {AccountID}-{CharacterName}: {ex.Message}");
			LogHelper.WriteLine(LogLevel.Error, $"Stack trace: {ex.StackTrace}");
		}
	}
	private int[] itemIndices = { 3, 5, 4, 1, 2, 0 };

	public void LoadCharacterWearItem()
	{
		LoadCharacterWearItem_New();
	}
	 public void LoadCharacterWearItem_New()
	{
		
		SendingClass w = new();
		w.Write4(1); // C->F
		w.Write4(SessionID); // 10-13
		w.WriteString(CharacterName, 16); //14-23
		w.Write4(GuildId != 0 ? GuildId : 0); //24 - 27
		w.WriteName(GuildName ?? ""); //28 ->35
		w.Write1(GuildId != 0 ? GangCharacterLevel : 0); //36
		w.Write2(GangBadge != null ? World.ServerGroupID : 0); // 37-39
		w.Write2(Player_Zx); //3a
		w.Write2(Player_Level); // 3c
		w.Write2(Player_Job_level); //3e
		w.Write1(Player_Job); //40
		w.Write1(Player_Sex); //41
		w.Write2(NewCharacterTemplate.MauToc); //42
		w.Write2(NewCharacterTemplate.KieuToc); //44
		w.Write2(NewCharacterTemplate.KhuonMat); // 46
		w.Write1(NewCharacterTemplate.AmThanh); // 48
		w.Write1(NewCharacterTemplate.GioiTinh); //49
		w.Write4(0);
		w.Write(1);
		w.Write(2);
		w.Write(PosX); //50
		w.Write(PosZ); //54
		w.Write(PosY); //58
		w.Write4(MapID); //5c - 5f
		w.Write(new byte[12]); // 60 -6f
		w.Write(CharacterNameTemplate, 0, 48);
		for (int i = 0; i < 15; i++)
		{
			if (BitConverter.ToInt32(Item_Wear[i].VatPhamSoLuong, 0) == 0)
			{
				Item_Wear[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
			if (Item_Wear[i].FLD_FJ_TrungCapPhuHon <= 22 &&
				Item_Wear[i].FLD_FJ_TrungCapPhuHon >= 21 &&
				Item_Wear[i].FLD_FJ_LowSoul > 0)
			{
				Item_Wear[i].Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = Item_Wear[i].FLD_FJ_TrungCapPhuHon - 20;
			}
			w.Write(Item_Wear[i].GetByte());
		}
		// Sub bag
		for (int j = 0; j < 14; j++)
		{
			if (BitConverter.ToInt32(Sub_Wear[j].VatPhamSoLuong, 0) == 0)
			{
				Sub_Wear[j].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
			w.Write(Sub_Wear[j].GetByte());
		}
		w.Write(new byte[World.Item_Byte_Length_92]);
		w.Write(BitConverter.ToInt32(Item_Wear[15].VatPhamSoLuong, 0) != 0 ? Item_Wear[15].GetByte() : new byte[World.Item_Byte_Length_92]);
		w.Write(new byte[World.Item_Byte_Length_92]);
		w.Write(BitConverter.ToInt32(Item_Wear[16].VatPhamSoLuong, 0) != 0 ? Item_Wear[16].GetByte() : new byte[World.Item_Byte_Length_92]);
		w.Write(new byte[World.Item_Byte_Length_92]);
	

		for (int k = 0; k < itemIndices.Length; k++)
		{
			int index = itemIndices[k];
			var item = ThietBiTab3[index];
			if (item == null || BitConverter.ToInt32(item.VatPhamSoLuong, 0) == 0)
			{
				item.VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
			//packet.Write(BitConverter.ToInt32(Item_Wear[0].VatPhamSoLuong, 0) != 0 ? Item_Wear[0].GetByte() : new byte[World.Item_Byte_Length]);
			w.Write(item.GetByte());
		}
			w.Write(BitConverter.ToInt32(Item_Wear[17].VatPhamSoLuong, 0) != 0 ? Item_Wear[17].GetByte() : new byte[World.Item_Byte_Length_92]);
		w.Write(new byte[World.Item_Byte_Length_92]);
		Client?.SendPak(w, 40960, SessionID);
	}
	
	public void TinhToanNhanVat_VoHuanGiaDoan_VinhDu()
	{
		try
		{
			if (HonorID_ == 0)
			{
				return;
			}
			UpdateHonor();
			if (HonorID_ != 0)
			{
				if (AppendStatusList != null && !GetAddState(HonorID_))
				{
					var characterData = GetCharacterData(SessionID);
					if (characterData != null)
					{
						StatusEffect x_Them_Vao_Trang_Thai_Loai = new(characterData, 604800000, HonorID_, 0);
						characterData.AppendStatusList.Add(x_Them_Vao_Trang_Thai_Loai.FLD_PID, x_Them_Vao_Trang_Thai_Loai);
						characterData.StatusEffect(BitConverter.GetBytes(HonorID_), 1, 604800000);
					}
				}
			}
			else
			{
				var num = CurrentHonorTitle();
				if (num != 0)
				{
					AppendStatusList[num].ThoiGianKetThucSuKien();
					HonorID_ = 0;
					FLD_HonorID = 0;
				}
			}
		}
		catch
		{
		}
	}

	public int CurrentHonorTitle()
	{
		try
		{
			if (AppendStatusList != null && AppendStatusList.Count != 0)
			{
				if (GetAddState(1008001300))
				{
					return 1008001300;
				}
				if (GetAddState(1008001301))
				{
					return 1008001301;
				}
				if (GetAddState(1008001302))
				{
					return 1008001302;
				}
				if (GetAddState(1008001303))
				{
					return 1008001303;
				}
				if (GetAddState(1008001304))
				{
					return 1008001304;
				}
				if (GetAddState(1008001305))
				{
					return 1008001305;
				}
				if (GetAddState(1008001306))
				{
					return 1008001306;
				}
				if (GetAddState(1008001307))
				{
					return 1008001307;
				}
				if (GetAddState(1008001308))
				{
					return 1008001308;
				}
				if (GetAddState(1008001309))
				{
					return 1008001309;
				}
				if (GetAddState(1008001310))
				{
					return 1008001310;
				}
				if (GetAddState(1008001311))
				{
					return 1008001311;
				}
				if (GetAddState(1008001312))
				{
					return 1008001312;
				}
				if (GetAddState(1008001313))
				{
					return 1008001313;
				}
				if (GetAddState(1008001314))
				{
					return 1008001314;
				}
				if (GetAddState(1008001315))
				{
					return 1008001315;
				}
				if (GetAddState(1008001316))
				{
					return 1008001316;
				}
				if (GetAddState(1008001317))
				{
					return 1008001317;
				}
				if (GetAddState(1008001318))
				{
					return 1008001318;
				}
				if (GetAddState(1008001319))
				{
					return 1008001319;
				}
				if (GetAddState(1008001200))
				{
					return 1008001200;
				}
				if (GetAddState(1008001201))
				{
					return 1008001201;
				}
				if (GetAddState(1008001202))
				{
					return 1008001202;
				}
				if (GetAddState(1008001203))
				{
					return 1008001203;
				}
				if (GetAddState(1008001204))
				{
					return 1008001204;
				}
				if (GetAddState(1008001205))
				{
					return 1008001205;
				}
				if (GetAddState(1008001206))
				{
					return 1008001206;
				}
				if (GetAddState(1008001207))
				{
					return 1008001207;
				}
				if (GetAddState(1008001208))
				{
					return 1008001208;
				}
				if (GetAddState(1008001209))
				{
					return 1008001209;
				}
				if (GetAddState(1008001210))
				{
					return 1008001210;
				}
				if (GetAddState(1008001211))
				{
					return 1008001211;
				}
				if (GetAddState(1008001212))
				{
					return 1008001212;
				}
				if (GetAddState(1008001213))
				{
					return 1008001213;
				}
				if (GetAddState(1008001214))
				{
					return 1008001214;
				}
				if (GetAddState(1008001215))
				{
					return 1008001215;
				}
				if (GetAddState(1008001216))
				{
					return 1008001216;
				}
				if (GetAddState(1008001217))
				{
					return 1008001217;
				}
				if (GetAddState(1008001218))
				{
					return 1008001218;
				}
				if (GetAddState(1008001219))
				{
					return 1008001219;
				}
				if (GetAddState(1008001220))
				{
					return 1008001220;
				}
				if (GetAddState(1008001221))
				{
					return 1008001221;
				}
				if (GetAddState(1008001222))
				{
					return 1008001222;
				}
				if (GetAddState(1008001223))
				{
					return 1008001223;
				}
				if (GetAddState(1008001224))
				{
					return 1008001224;
				}
				if (GetAddState(1008001225))
				{
					return 1008001225;
				}
				if (GetAddState(1008001226))
				{
					return 1008001226;
				}
				if (GetAddState(1008001227))
				{
					return 1008001227;
				}
				if (GetAddState(1008001228))
				{
					return 1008001228;
				}
				if (GetAddState(1008001229))
				{
					return 1008001229;
				}
				if (GetAddState(1008001230))
				{
					return 1008001230;
				}
				if (GetAddState(1008001231))
				{
					return 1008001231;
				}
				if (GetAddState(1008001232))
				{
					return 1008001232;
				}
				if (GetAddState(1008001233))
				{
					return 1008001233;
				}
				if (GetAddState(1008001234))
				{
					return 1008001234;
				}
				if (GetAddState(1008001235))
				{
					return 1008001235;
				}
				if (GetAddState(1008001236))
				{
					return 1008001236;
				}
				if (GetAddState(1008001237))
				{
					return 1008001237;
				}
				if (GetAddState(1008001238))
				{
					return 1008001238;
				}
				if (GetAddState(1008001239))
				{
					return 1008001239;
				}
				if (GetAddState(1008001240))
				{
					return 1008001240;
				}
				if (GetAddState(1008001241))
				{
					return 1008001241;
				}
				if (GetAddState(1008001242))
				{
					return 1008001242;
				}
				if (GetAddState(1008001243))
				{
					return 1008001243;
				}
				if (GetAddState(1008001244))
				{
					return 1008001244;
				}
				if (GetAddState(1008001245))
				{
					return 1008001245;
				}
				if (GetAddState(1008001246))
				{
					return 1008001246;
				}
				if (GetAddState(1008001247))
				{
					return 1008001247;
				}
				if (GetAddState(1008001248))
				{
					return 1008001248;
				}
				if (GetAddState(1008001249))
				{
					return 1008001249;
				}
				if (GetAddState(1008001250))
				{
					return 1008001250;
				}
				if (GetAddState(1008001251))
				{
					return 1008001251;
				}
				if (GetAddState(1008001252))
				{
					return 1008001252;
				}
				if (GetAddState(1008001253))
				{
					return 1008001253;
				}
				if (GetAddState(1008001254))
				{
					return 1008001254;
				}
				if (GetAddState(1008001255))
				{
					return 1008001255;
				}
				if (GetAddState(1008001256))
				{
					return 1008001256;
				}
				if (GetAddState(1008001257))
				{
					return 1008001257;
				}
				if (GetAddState(1008001258))
				{
					return 1008001258;
				}
				if (GetAddState(1008001259))
				{
					return 1008001259;
				}
				if (GetAddState(1008001260))
				{
					return 1008001260;
				}
				if (GetAddState(1008001261))
				{
					return 1008001261;
				}
				if (GetAddState(1008001262))
				{
					return 1008001262;
				}
				if (GetAddState(1008001263))
				{
					return 1008001263;
				}
				if (GetAddState(1008001264))
				{
					return 1008001264;
				}
				if (GetAddState(1008001265))
				{
					return 1008001265;
				}
				if (GetAddState(1008001266))
				{
					return 1008001266;
				}
				if (GetAddState(1008001267))
				{
					return 1008001267;
				}
				if (GetAddState(1008001268))
				{
					return 1008001268;
				}
				if (GetAddState(1008001269))
				{
					return 1008001269;
				}
				if (GetAddState(1008001270))
				{
					return 1008001270;
				}
				if (GetAddState(1008001271))
				{
					return 1008001271;
				}
				if (GetAddState(1008001272))
				{
					return 1008001272;
				}
				if (GetAddState(1008001273))
				{
					return 1008001273;
				}
				if (GetAddState(1008001274))
				{
					return 1008001274;
				}
				if (GetAddState(1008001275))
				{
					return 1008001275;
				}
				if (GetAddState(1008001276))
				{
					return 1008001276;
				}
				if (GetAddState(1008001277))
				{
					return 1008001277;
				}
				if (GetAddState(1008001278))
				{
					return 1008001278;
				}
				if (GetAddState(1008001279))
				{
					return 1008001279;
				}
				if (GetAddState(1008001280))
				{
					return 1008001280;
				}
				if (GetAddState(1008001281))
				{
					return 1008001281;
				}
				if (GetAddState(1008001282))
				{
					return 1008001282;
				}
				if (GetAddState(1008001283))
				{
					return 1008001283;
				}
				if (GetAddState(1008001284))
				{
					return 1008001284;
				}
				if (GetAddState(1008001285))
				{
					return 1008001285;
				}
				if (GetAddState(1008001286))
				{
					return 1008001286;
				}
				if (GetAddState(1008001287))
				{
					return 1008001287;
				}
				if (GetAddState(1008001288))
				{
					return 1008001288;
				}
				if (GetAddState(1008001289))
				{
					return 1008001289;
				}
				if (GetAddState(1008001290))
				{
					return 1008001290;
				}
				if (GetAddState(1008001291))
				{
					return 1008001291;
				}
				if (GetAddState(1008001292))
				{
					return 1008001292;
				}
				if (GetAddState(1008001293))
				{
					return 1008001293;
				}
				if (GetAddState(1008001294))
				{
					return 1008001294;
				}
				if (GetAddState(1008001295))
				{
					return 1008001295;
				}
				if (GetAddState(1008001296))
				{
					return 1008001296;
				}
				if (GetAddState(1008001297))
				{
					return 1008001297;
				}
				return GetAddState(1008001298) ? 1008001298 : (GetAddState(1008001299) ? 1008001299 : 0);
			}
			return 0;
		}
		catch
		{
			return 0;
		}
	}

	public bool TopTenHonors()
	{
		try
		{
			return AppendStatusList != null && AppendStatusList.Count != 0 && (GetAddState(1008001300) || GetAddState(1008001301) || GetAddState(1008001302) || GetAddState(1008001303) || GetAddState(1008001304) || GetAddState(1008001305) || GetAddState(1008001306) || GetAddState(1008001307) || GetAddState(1008001308) || GetAddState(1008001309) || GetAddState(1008001310) || GetAddState(1008001311) || GetAddState(1008001312) || GetAddState(1008001313) || GetAddState(1008001314) || GetAddState(1008001315) || GetAddState(1008001316) || GetAddState(1008001317) || GetAddState(1008001318) || GetAddState(1008001319));
		}
		catch
		{
			return false;
		}
	}

	public void UpdateHonor()
	{
		var num = get_HonorID(CharacterName, 3);
		if (num == 0)
		{
			num = get_HonorID(CharacterName, 2);
			if (num == 0)
			{
				num = get_HonorID(CharacterName, 1);
			}
		}
		if (num == 0)
		{
			FLD_HonorID = 0;
			HonorID_ = 0;
			return;
		}
		HonorID_ = num;
		if (num < 1008001250)
		{
			FLD_HonorID = num - 1008001099;
		}
		else
		{
			FLD_HonorID = num - 1008001049;
		}
	}

	
	public void GuiDi_NhiemVu_VatPham_List()
    {
        try
        {
            using SendingClass sendingClass = new();
            sendingClass.Write4(36);
            for(int i = 0; i < 36; i++)
            {
                sendingClass.Write4(i);
                sendingClass.Write4(0);
                sendingClass.Write4(NhiemVu_VatPham[i].VatPham_ID);
                sendingClass.Write4(0);
                sendingClass.Write4(NhiemVu_VatPham[i].VatPhamSoLuong);
            }
            Client?.SendPak(sendingClass, 33024, SessionID);
        } catch(Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "GuiDi_NhiemVu_VatPham_Listerror![" + AccountID + "]-[" + CharacterName + "]" + ex);
        }
    }
	public void GuiDi_NhiemVu_VatPham_List_Old()
	{
		try
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(36);
			foreach (var value in NhiemVu_VatPham)
			{
				sendingClass.Write4(value.VatPham_ID);
				sendingClass.Write4(value.VatPhamSoLuong);
			}
			Client?.SendPak(sendingClass, 33024, SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GuiDi_NhiemVu_VatPham_Listerror![" + AccountID + "]-[" + CharacterName + "]" + ex);
		}
	}

	public void HopThanh_HeThong_HuyBo()
	{
		for (var i = 0; i < 96; i++)
		{
			Item_In_Bag[i].Lock_Move = false;
		}
	}

	public void UpdateProductionSystem()
	{
		try
		{
			var cheTaoVatPhamDanhSachClass = X_Che_Tac_Vat_Pham_Loai.GetCheTaoVatPhamDanhSachClass(FLD_LoaiSanXuat, FLD_LevelSanXuat);
			if (cheTaoVatPhamDanhSachClass == null || cheTaoVatPhamDanhSachClass.Count <= 0 || cheTaoVatPhamDanhSachClass == null || cheTaoVatPhamDanhSachClass.Count <= 0)
			{
				return;
			}
			SendingClass sendingClass = new();
			sendingClass.Write2(cheTaoVatPhamDanhSachClass.Count);
			foreach (var item in cheTaoVatPhamDanhSachClass)
			{
				sendingClass.Write8(item);
				sendingClass.Write4(10000);
				sendingClass.Write2(0);
				sendingClass.Write2(1);
				sendingClass.Write2(1);
				if (FLD_LoaiSanXuat == 3)
				{
					sendingClass.Write2(FLD_LoaiSanXuat);
					sendingClass.Write2(16);
				}
				else
				{
					sendingClass.Write2(FLD_LoaiSanXuat);
				}
			}
			if (Client != null)
			{
				Client.SendPak(sendingClass, 13079, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Update Production System error[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void UpdateCoupleSystem(int int_109, string string_11, string string_12, int int_110, DateTime dateTime_3)
	{
		try
		{
			var array = Converter.HexStringToByte("AA558E00A6007C17800001000000714C7E0000000000C4687E0000000000BDD600000000000000000000000000B7709C000000000000AC89014D00000000AC89014D040000002330000002D0A9B77406000000000000A59D0300CB1C0A4D00000000010000000000000000000000000000000000000000000000000000000000000000000000001F86A3000000000000568055AA");
			DateTime value = new(1970, 1, 1, 8, 0, 0);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 10, 2);
			var bytes = Encoding.Default.GetBytes(string_11);
			System.Buffer.BlockCopy(bytes, 0, array, 30, bytes.Length);
			if (int_110 == 0)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 58, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 62, 4);
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 58, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddMinutes(int_110 - 4320 - 5760).Subtract(value).TotalSeconds), 0, array, 62, 4);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_loveDegreeLevel), 0, array, 66, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_Couple_Love), 0, array, 70, 4);
			if (WhetherMarried == 1)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 74, 1);
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 74, 1);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes((int)FLD_NgayKiNiemKetHon.Subtract(value).TotalSeconds), 0, array, 90, 4);
			if (string_12.Length != 0)
			{
				var bytes2 = Encoding.Default.GetBytes(string_12);
				System.Buffer.BlockCopy(bytes2, 0, array, 102, bytes2.Length);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.SendMultiplePackage(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Update Couple System error[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void KiemSoatNguyenBao_SoLuong(int int_109, int int_110)
	{
		if (int_109 <= 0)
		{
			return;
		}
		if (World.KiemTra_NguyenBao == 1)
		{
			//if (int_109 >= World.SoNguyenBao_ToiDa_TrongMotGiaoDich)
			//{
			//	LogHelper.WriteLine(LogLevel.Error, "ID vượt số lượng [" + Userid + "]-[" + UserName + "] [Số Lượng：" + int_109 + "] [ Giới hạn GD：" + World.SoNguyenBao_ToiDa_TrongMotGiaoDich + "]");
			//	switch (World.HoatDong_PhatHienPhoi)
			//	{
			//	case 1:
			//		return;
			//	case 2:
			//		Title(63, Userid, "PhatHienPhoi2-1");
			//		break;
			//	}
			//}
			// if (FLD_RXPIONT >= World.GioiHan_TongSoNguyenBao_1TaiKhoan)
			// {
			// 	var array = new string[9] { "Point vượt quá Thiên cơ các cho phép ID:[", Userid, "]-[", UserName, "] [NguyenBao：", null, null, null, null };
			// 	array[5] = FLD_RXPIONT.ToString();
			// 	array[6] = "] [系统允许 Cao Nhat So Luong：";
			// 	array[7] = World.GioiHan_TongSoNguyenBao_1TaiKhoan.ToString();
			// 	array[8] = "]";
			// 	LogHelper.WriteLine(LogLevel.Error, string.Concat(array));
			// 	switch (World.HoatDong_PhatHienPhoi)
			// 	{
			// 	case 2:
			// 		Title(67, Userid, "HoatDong_PhatHienPhoi2-2");
			// 		break;
			// 	case 1:
			// 		FLD_RXPIONT = 0;
			// 		Save_NguyenBaoData();
			// 		return;
			// 	}
			// }
		}
		if (int_110 == 0)
		{
			FLD_RXPIONT -= int_109;
			HeThongNhacNho("Tiêu [" + int_109 + "] Nguyên Bảo [" + DateTime.Now.ToString() + "]", 22, "Truyền Âm Các");
			Save_NguyenBaoData();
		}
		else
		{
			FLD_RXPIONT += int_109;
			HeThongNhacNho("Nhận [" + int_109 + "] Nguyên Bảo [" + DateTime.Now.ToString() + "]", 22, "Truyền Âm Các");
			Save_NguyenBaoData();
		}
	}

	public void CheckTheIngotPointData(int int_109, int int_110)
	{
		if (int_109 > 0)
		{
			if (int_110 == 0)
			{
				FLD_RXPIONTX -= int_109;
			}
			else
			{
				FLD_RXPIONTX += int_109;
			}
		}
	}

	public void CheckTheDataOfHuaXiaCoin(int int_109, int int_110)
	{
		if (int_109 > 0)
		{
			if (int_110 == 0)
			{
				FLD_Coin -= int_109;
				HeThongNhacNho("Tiêu phí " + int_109 + " Nguyên Bảo tặng phẩm [" + DateTime.Now.ToString() + "]!", 22, "Truyền Âm Các");
			}
			else
			{
				FLD_Coin += int_109;
				HeThongNhacNho("Thu hoạch được " + int_109 + " Nguyên Bảo tặng phẩm [" + DateTime.Now.ToString() + "]!", 22, "Truyền Âm Các");
			}
		}
	}

}

