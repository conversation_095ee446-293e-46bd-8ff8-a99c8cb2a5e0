using System;
using HeroYulgang.Services;
using HeroYulgang.Core.Networking.Protocol;
using System.IO;
using HeroYulgang.Database.FreeSql;
using RxjhServer;
using System.Threading.Tasks;
using HeroYulgang.Core.Networking.Utils;
using HeroYulgang.Core.Networking.Network;
using System.Net;
using HeroYulgang.Core.Networking.Core;
using RxjhServer.HelperTools;
using System.Linq;

namespace HeroYulgang.Core.NetCore
{
    /// <summary>
    /// Simplified packet processor that delegates to existing Akka packet handling logic
    /// Uses PacketBuffer from Akka networking to maintain compatibility
    /// </summary>
    public class PacketProcessor : IDisposable
    {
        #region Fields

        private readonly PacketBuffer _packetBuffer;
        private volatile bool _disposed = false;

        #endregion

        #region Constructor

        public PacketProcessor()
        {
            _packetBuffer = new PacketBuffer();
            Logger.Instance.Info("PacketProcessor initialized with NetCoreServer packet handling");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Process incoming raw data from client using Akka's PacketBuffer
        /// </summary>
        public void ProcessRawData(GameSession session, byte[] rawData)
        {
            if (session == null || rawData == null || rawData.Length == 0 || _disposed)
                return;

            try
            {
                // Add raw data to packet buffer (same as Akka networking)
                _packetBuffer.AddData(rawData);

                // Extract complete packets from buffer
                var packets = _packetBuffer.ExtractCompletePackets();

                foreach (var packetData in packets)
                {
                    ProcessSinglePacket(session, packetData);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error processing raw data from session {session.Id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Process a single complete packet (similar to Akka's ClientConnection.HandleReceived)
        /// </summary>
        private void ProcessSinglePacket(GameSession session, byte[] packetData)
        {
            try
            {
                // Parse packet using Akka's Packet.Parse method
                var packet = Packet.Parse(packetData);
                if (packet == null)
                {
                    Logger.Instance.Warning($"Failed to parse packet from session {session.Id}");
                    return;
                }

                // Handle packet based on login status (same logic as Akka)
                if (!session.Login)
                {
                    Logger.Instance.Debug($"[{packet.Type}]-[Login] from client {session.SessionId}");
                    switch (packet.Type)
                    {
                        case PacketType.Login:
                            HandleLoginPacket(session, packet);
                            break;
                        case PacketType.Valid1375:
                            Handle1375(session,packet);
                            break;
                        default:
                            Logger.Instance.Warning($"Unexpected packet type {packet.Type} before login from session {session.Id}");
                            break;
                    }
                    // HandleLoginPacket(session, packet);
                }
                else
                {
                    Logger.Instance.Debug($"[{packet.Type}]-[{session.AccountId}] from client {session.SessionId}");
                    // Delegate to player's packet handling (same as Akka)
                    if (session.Player != null)
                    {
                        var dataX = new byte[packet.Data.Length - 2];
                        Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                        Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                        _ = session.Player.ManagePacket(dataX, dataX.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error processing single packet from session {session.Id}: {ex.Message}");
            }
        }
        private static void Handle1375(GameSession session, Networking.Core.Packet packet)
        {
            try
            {
                // Validation packet 1375
                if (!ValidatePacket1375(packet.Data, packet.Data.Length))
                {
                    Logger.Instance.Warning($"Packet 1375 không hợp lệ từ ConnectionID {session.ConnectionId} - ngắt kết nối");
                    session.Disconnect();
                    return;
                }

                // Đánh dấu đã validate packet 1375
                session.HasValidatedPacket1375 = true;
                Logger.Instance.Debug($"ConnectionID {session.ConnectionId} đã validate packet 1375 thành công");

                // Gửi response packet 1375 với ConnectionID
                var array = Converter.HexStringToByte("aa550000d504600527010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000200010055aa");
                Buffer.BlockCopy(BitConverter.GetBytes(session.ConnectionId), 0, array, 4, 2);
                session.Send_Map_Data(array, array.Length);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi xử lý packet 1375 cho ConnectionID {session.ConnectionId}: {ex.Message}");
                session.Disconnect();
            }
        }

        /// <summary>
        /// Validate packet 1375 format
        /// </summary>
        private static bool ValidatePacket1375(byte[] data, int length)
        {
            try
            {
                // Kiểm tra độ dài tối thiểu
                if (length < 8)
                {
                    Logger.Instance.Warning($"Packet 1375 quá ngắn: {length} bytes");
                    return false;
                }

                // Kiểm tra header aa55
                if (data[0] != 0xaa || data[1] != 0x55)
                {
                    Logger.Instance.Warning($"Packet 1375 thiếu header aa55: {data[0]:X2}{data[1]:X2}");
                    return false;
                }

                // Kiểm tra footer 55aa (2 bytes cuối)
                if (data[length - 2] != 0x55 || data[length - 1] != 0xaa)
                {
                    Logger.Instance.Warning($"Packet 1375 thiếu footer 55aa: {data[length - 2]:X2}{data[length - 1]:X2}");
                    return false;
                }

                // Kiểm tra opcode 1375 (0x0557) tại offset 6-7
                if (length >= 8)
                {
                    ushort opcode = BitConverter.ToUInt16(data, 6);
                    if (opcode != 1375)
                    {
                        Logger.Instance.Warning($"Packet opcode không phải 1375: {opcode}");
                        return false;
                    }
                }

                Logger.Instance.Debug($"Packet 1375 hợp lệ");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi validate packet 1375: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Handle login packet - improved based on ClientConnection.HandleLoginSync
        /// </summary>
        private void HandleLoginPacket(GameSession session, Networking.Core.Packet packet)
        {
            try
            {
                Logger.Instance.Debug($"Processing login packet for session {session.SessionId}");

                // Parse username from packet (same as Akka implementation)
                var reader = new BinaryReader(new MemoryStream(packet.Data));
                reader.BaseStream.Seek(12, SeekOrigin.Begin);
                byte[] userNameByte = reader.ReadBytes(14);
                string username = System.Text.Encoding.Default.GetString(userNameByte).Trim().Replace("\0", String.Empty);

                if (username.Length == 0)
                {
                    Logger.Instance.Warning($"Invalid username length from session {session.SessionId}");
                    session.Disconnect();
                    return;
                }

                // Find account (same as Akka implementation)
                var account = AccountDb.FindAccount(username).Result;
                if (account == null)
                {
                    Logger.Instance.Warning($"Account not found: {username}");
                    session.Disconnect();
                    return;
                }

                Logger.Instance.Debug($"Login request from {username}");

                // Kiểm tra session đã validate packet 1375 chưa
                if (!session.HasValidatedPacket1375)
                {
                    Logger.Instance.Warning($"Client {username} chưa validate packet 1375 - ngắt kết nối");
                    session.Disconnect();
                    return;
                }

                var existingPlayer = World.allConnectedChars.Values.FirstOrDefault(p => p.AccountID == username);
                if (existingPlayer != null)
                {
                    existingPlayer.Client.Dispose();
                    session.Disconnect();
                    Logger.Instance.Debug($"Disconnected old connection for player {username}");
                    return;
                }

                // XÁC THỰC VỚI LOGINSERVER TRƯỚC KHI TẠO PLAYER
                Logger.Instance.Info($"Bắt đầu xác thực với LoginServer cho {username}");

                // Chuẩn bị thông tin xác thực
                var clientString = session.Id.ToString();
                var clientPort = 0; // TODO: Lấy port thực tế từ session
                var lanIp = username; // Tạm thời sử dụng username

                // Gọi ValidateAccountLoginAsync
                Task.Run(async () =>
                {
                    try
                    {
                        var res = await World.Instance.loginServerClient.ValidateAccountLoginAsync(
                            username,
                            "",
                            clientString,
                            clientPort,
                            lanIp,
                            clientPort
                        );

                        if (res.IsValid)
                        {
                            Logger.Instance.Info($"Xác thực thành công cho {username} - tạo Player");

                            // Cấp PlayerSessionID SAU KHI xác thực thành công
                            int playerSessionId = session.AllocatePlayerSessionId();
                            if (playerSessionId == -1)
                            {
                                Logger.Instance.Error($"Không thể cấp PlayerSessionID cho {username}");
                                session.Disconnect();
                                return;
                            }

                            // Tạo player SAU KHI xác thực thành công
                            var player = new Players
                            {
                                AccountID = username,
                                LanIp = lanIp,
                                SessionID = playerSessionId // Sử dụng PlayerSessionID
                            };

                            // Set session properties
                            session.IsAuthenticated = true;
                            NetCoreServerWrapper.Instance.UpdateSessionAccount(session, username);
                            session.Player = player;

                            // Create UnifiedNetState for NetCoreServer
                            var netState = new UnifiedNetState(session);
                            player.Client = netState;
                            netState.Player = player;
                            Logger.Instance.Info($"Setup Player account netcore {player.AccountID} với PlayerSessionID {playerSessionId}");
                            World.allConnectedChars.TryAdd(player.SessionID, player);

                            // Thêm mapping từ ConnectionID đến PlayerSessionID
                            World.AddConnectionMapping(session.ConnectionId, playerSessionId);

                            // Prepare login data
                            var dataX = new byte[packet.Data.Length - 2];
                            Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                            Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);

                            // Process login
                            try
                            {
                                Task.Run(() => player?.KetNoi_DangNhap(dataX, dataX.Length));
                                Logger.Instance.Debug($"User {username} called KetNoi_DangNhap successfully");
                                session.Login = true;
                            }
                            catch (Exception ex)
                            {
                                Logger.Instance.Error($"Error processing login for {username}: {ex.Message}");
                                session.Disconnect();
                            }
                        }
                        else
                        {
                            Logger.Instance.Warning($"Xác thực thất bại cho {username}: {res.ErrorMessage}");
                            session.Disconnect();
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Lỗi xác thực với LoginServer cho {username}: {ex.Message}");
                        session.Disconnect();
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error handling login packet for session {session.SessionId}: {ex.Message}");

                // Send failure response (same as Akka implementation)
                try
                {
                    byte[] responseData = BitConverter.GetBytes(0); // 0 = failure
                    session.Send(responseData);
                }
                catch (Exception sendEx)
                {
                    Logger.Instance.Error($"Error sending failure response: {sendEx.Message}");
                }

                session.Disconnect();
            }
        }



        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                _disposed = true;
                Logger.Instance.Info("PacketProcessor disposed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing PacketProcessor: {ex.Message}");
            }

            GC.SuppressFinalize(this);
        }

        #endregion
    }
}
