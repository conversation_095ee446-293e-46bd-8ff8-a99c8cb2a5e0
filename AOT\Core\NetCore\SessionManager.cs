using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Services;
using HeroYulgang.Core.Managers;

namespace HeroYulgang.Core.NetCore
{
    /// <summary>
    /// Manages active game sessions
    /// Provides session lookup, cleanup, and statistics
    /// Thread-safe for high concurrency scenarios
    /// SessionIDs are allocated from SessionIdManager for client compatibility
    /// </summary>
    public class SessionManager : IDisposable
    {
        #region Fields

        private readonly ConcurrentDictionary<Guid, GameSession> _sessions;
        private readonly ConcurrentDictionary<string, GameSession> _sessionsByAccount;
        private readonly ConcurrentDictionary<int, GameSession> _sessionsBySessionId;
        private readonly Timer _cleanupTimer;
        private volatile bool _disposed = false;
        
        // Configuration
        private readonly TimeSpan _inactiveTimeout = TimeSpan.FromMinutes(30); // 30 minutes timeout
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);  // Cleanup every 5 minutes

        #endregion

        #region Properties

        public int ActiveSessionCount => _sessions.Count;
        public int AuthenticatedSessionCount => _sessions.Values.Count(s => s.IsAuthenticated);

        #endregion

        #region Constructor

        public SessionManager()
        {
            _sessions = new ConcurrentDictionary<Guid, GameSession>();
            _sessionsByAccount = new ConcurrentDictionary<string, GameSession>();
            _sessionsBySessionId = new ConcurrentDictionary<int, GameSession>();
            
            // Start cleanup timer
            _cleanupTimer = new Timer(CleanupInactiveSessions, null, _cleanupInterval, _cleanupInterval);
            
            Logger.Instance.Info("SessionManager initialized");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Register a new session
        /// SessionID is already allocated from SessionIdManager in GameSession constructor
        /// </summary>
        public bool RegisterSession(GameSession session)
        {
            if (session == null || _disposed)
                return false;

            try
            {
                bool added = _sessions.TryAdd(session.Id, session);
                if (added)
                {
                    // Also add to SessionId lookup
                    _sessionsBySessionId.TryAdd(session.ConnectionId, session);
                    
                    Logger.Instance.Debug($"Registered session {session.Id} (SessionId: {session.ConnectionId})");
                }
                return added;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error registering session {session.Id}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unregister a session
        /// SessionID will be released back to SessionIdManager in GameSession.Dispose()
        /// </summary>
        public bool UnregisterSession(Guid sessionId)
        {
            if (_disposed)
                return false;

            try
            {
                if (_sessions.TryRemove(sessionId, out GameSession session))
                {
                    // Remove from other lookups
                    if (!string.IsNullOrEmpty(session.Account))
                    {
                        _sessionsByAccount.TryRemove(session.Account, out _);
                    }
                    _sessionsBySessionId.TryRemove(session.ConnectionId, out _);
                    
                    Logger.Instance.Debug($"Unregistered session {sessionId} (Account: {session.Account})");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error unregistering session {sessionId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get session by ID
        /// </summary>
        public GameSession GetSession(Guid sessionId)
        {
            _sessions.TryGetValue(sessionId, out GameSession session);
            return session;
        }

        /// <summary>
        /// Get session by SessionId (uint)
        /// </summary>
        public GameSession GetSessionBySessionId(int sessionId)
        {
            _sessionsBySessionId.TryGetValue(sessionId, out GameSession session);
            return session;
        }

        /// <summary>
        /// Get session by account name
        /// </summary>
        public GameSession GetSessionByAccount(string account)
        {
            if (string.IsNullOrEmpty(account))
                return null;
                
            _sessionsByAccount.TryGetValue(account, out GameSession session);
            return session;
        }

        /// <summary>
        /// Update session account mapping (when user authenticates)
        /// </summary>
        public void UpdateSessionAccount(GameSession session, string account)
        {
           
            if (session == null || string.IsNullOrEmpty(account) || _disposed)
                return;

            try
            {
                // Remove old account mapping if exists
                session.AccountId = account;
                if (!string.IsNullOrEmpty(session.Account))
                {
                    _sessionsByAccount.TryRemove(session.Account, out _);
                }
                
                // Add new account mapping
                _sessionsByAccount.TryAdd(account, session);
                
                Logger.Instance.Debug($"Updated session {session.Id} account mapping: {account}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error updating session account mapping: {ex.Message}");
            }
        }

        /// <summary>
        /// Get all active sessions
        /// </summary>
        public GameSession[] GetAllSessions()
        {
            try
            {
                return _sessions.Values.ToArray();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error getting all sessions: {ex.Message}");
                return new GameSession[0];
            }
        }

        /// <summary>
        /// Get all authenticated sessions
        /// </summary>
        public GameSession[] GetAuthenticatedSessions()
        {
            try
            {
                return _sessions.Values.Where(s => s.IsAuthenticated).ToArray();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error getting authenticated sessions: {ex.Message}");
                return new GameSession[0];
            }
        }

        /// <summary>
        /// Check if account is already online
        /// </summary>
        public bool IsAccountOnline(string account)
        {
            if (string.IsNullOrEmpty(account))
                return false;
                
            return _sessionsByAccount.ContainsKey(account);
        }

        /// <summary>
        /// Disconnect session by account (for duplicate login handling)
        /// </summary>
        public bool DisconnectAccount(string account)
        {
            if (string.IsNullOrEmpty(account))
                return false;

            try
            {
                var session = GetSessionByAccount(account);
                if (session != null)
                {
                    session.Disconnect();
                    Logger.Instance.Info($"Disconnected duplicate login for account: {account}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disconnecting account {account}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get session statistics
        /// </summary>
        public SessionStatistics GetStatistics()
        {
            try
            {
                var sessions = _sessions.Values.ToArray();
                return new SessionStatistics
                {
                    TotalSessions = sessions.Length,
                    AuthenticatedSessions = sessions.Count(s => s.IsAuthenticated),
                    TotalPacketsReceived = sessions.Sum(s => s.PacketsReceived),
                    TotalPacketsSent = sessions.Sum(s => s.PacketsSent),
                    TotalBytesReceived = sessions.Sum(s => s.BytesReceived),
                    TotalBytesSent = sessions.Sum(s => s.BytesSent)
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error getting session statistics: {ex.Message}");
                return new SessionStatistics();
            }
        }

        #endregion

        #region Private Methods

        private void CleanupInactiveSessions(object state)
        {
            if (_disposed)
                return;

            try
            {
                var inactiveSessions = _sessions.Values
                    .Where(s => s.IsInactive(_inactiveTimeout))
                    .ToArray();

                foreach (var session in inactiveSessions)
                {
                    try
                    {
                        Logger.Instance.Info($"Disconnecting inactive session {session.Id} (Account: {session.Account})");
                        session.Disconnect();
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Error disconnecting inactive session {session.Id}: {ex.Message}");
                    }
                }

                if (inactiveSessions.Length > 0)
                {
                    Logger.Instance.Info($"Cleaned up {inactiveSessions.Length} inactive sessions");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error during session cleanup: {ex.Message}");
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                _disposed = true;
                
                // Stop cleanup timer
                _cleanupTimer?.Dispose();
                
                // Disconnect all sessions
                var sessions = _sessions.Values.ToArray();
                foreach (var session in sessions)
                {
                    try
                    {
                        session.Disconnect();
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Error disconnecting session {session.Id} during dispose: {ex.Message}");
                    }
                }
                
                // Clear collections
                _sessions.Clear();
                _sessionsByAccount.Clear();
                _sessionsBySessionId.Clear();
                
                Logger.Instance.Info("SessionManager disposed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing SessionManager: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// Session statistics data structure
    /// </summary>
    public struct SessionStatistics
    {
        public int TotalSessions { get; set; }
        public int AuthenticatedSessions { get; set; }
        public long TotalPacketsReceived { get; set; }
        public long TotalPacketsSent { get; set; }
        public long TotalBytesReceived { get; set; }
        public long TotalBytesSent { get; set; }
    }
}
