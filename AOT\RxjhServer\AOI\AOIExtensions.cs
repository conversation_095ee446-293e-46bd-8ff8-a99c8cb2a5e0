using HeroYulgang.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RxjhServer.AOI
{
    public static class AOIExtensions
    {
        #region Player Extensions
        public static void UpdateAOI(this Players player)
        {
            try
            {
                if (player?.Client?.Running == true)
                    AOISystem.Instance.UpdatePlayerAOI(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI update failed for {player?.CharacterName}: {ex.Message}");
                try
                {
                    player?.GetTheReviewRangePlayers();
                    player?.GetReviewScopeNpc();
                    player?.ScanGroundItems();
                }
                catch { /* Ignore fallback errors */ }
            }
        }

        /// <summary>
        /// Grid-based packet broadcasting - thay thế NearbyPlayers cache
        /// </summary>
        public static void SendToNearbyPlayers(this Players player, byte[] data, int length, bool skipSelf = true)
        {
            try
            {
                if (player?.Client?.Running != true || data == null || length <= 0)
                    return;

                var grids = AOISystem.Instance.GetNearbyGrids(player.MapID, player.PosX, player.PosY);
                foreach (var grid in grids)
                {
                    grid.ForEachPlayer(nearbyPlayer =>
                    {
                        if (skipSelf && nearbyPlayer.SessionID == player.SessionID)
                            return;

                        if (nearbyPlayer?.Client?.Running == true &&
                            !nearbyPlayer.Client.TreoMay &&
                            nearbyPlayer.IsJoinWorld &&
                            AOISystem.Instance.IsWithinAOIRange(player.PosX, player.PosY, nearbyPlayer.PosX, nearbyPlayer.PosY))
                        {
                            nearbyPlayer.Client.Send_Map_Data(data, length);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendToNearbyPlayers error for {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Grid-based packet broadcasting với SendingClass
        /// </summary>
        public static void SendToNearbyPlayers(this Players player, HeroYulgang.Utils.SendingClass pak, int id, int wordid, bool skipSelf = true)
        {
            try
            {
                if (player?.Client?.Running != true || pak == null)
                    return;

                var grids = AOISystem.Instance.GetNearbyGrids(player.MapID, player.PosX, player.PosY);
                foreach (var grid in grids)
                {
                    grid.ForEachPlayer(nearbyPlayer =>
                    {
                        if (skipSelf && nearbyPlayer.SessionID == player.SessionID)
                            return;

                        if (nearbyPlayer?.Client?.Running == true &&
                            !nearbyPlayer.Client.TreoMay &&
                            nearbyPlayer.IsJoinWorld &&
                            AOISystem.Instance.IsWithinAOIRange(player.PosX, player.PosY, nearbyPlayer.PosX, nearbyPlayer.PosY))
                        {
                            nearbyPlayer.Client.SendPak(pak, id, wordid);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendToNearbyPlayers (SendingClass) error for {player?.CharacterName}: {ex.Message}");
            }
        }

        public static void AddToAOI(this Players player) => AOISystem.Instance.AddPlayer(player);
        public static void RemoveFromAOI(this Players player) => AOISystem.Instance.RemovePlayer(player.SessionID);
        public static void UpdateAOIPosition(this Players player, float newX, float newY, int map) => AOISystem.Instance.MovePlayer(player, newX, newY, map);
        
        // Legacy compatibility methods
        public static void ForceUpdateAOI(this Players player) => player.UpdateAOI();
        public static void HandlePlayerDeath(this Players player) => player.UpdateAOI();
        public static void HandlePlayerRespawn(this Players player) => player.UpdateAOI();
        public static void UpdateMovementAOI(this Players player) => player.UpdateAOI();

        /// <summary>
        /// Cập nhật NearbyPlayers cache chỉ cho events (spawn/despawn)
        /// </summary>
        public static void UpdateNearbyPlayersCache(this Players player)
        {
            try
            {
                if (player?.Client?.Running != true || player.NearbyPlayers == null)
                    return;

                var grids = AOISystem.Instance.GetNearbyGrids(player.MapID, player.PosX, player.PosY);
                var currentVisible = new HashSet<int>();

                // Tìm players hiện tại trong tầm nhìn
                foreach (var grid in grids)
                {
                    foreach (var playerID in grid.Players.Keys)
                    {
                        if (playerID != player.SessionID &&
                            World.allConnectedChars.TryGetValue(playerID, out var otherPlayer) &&
                            AOISystem.Instance.IsWithinAOIRange(player.PosX, player.PosY, otherPlayer.PosX, otherPlayer.PosY))
                        {
                            currentVisible.Add(playerID);
                        }
                    }
                }

                // Kiểm tra players cần despawn (không còn trong tầm nhìn)
                var toRemove = new List<(int, int)>();
                foreach (var kvp in player.NearbyPlayers.ToList())
                {
                    var (serverID, sessionID) = kvp.Key;
                    if (!currentVisible.Contains(sessionID))
                    {
                        toRemove.Add((serverID, sessionID));
                        // Trigger despawn event
                        var despawnPlayer = kvp.Value;
                        if (despawnPlayer?.Client?.Running == true)
                        {
                            // Xóa là 2 chiều
                            player.NotifyPlayerExit(player, despawnPlayer);
                            player.NearbyPlayers.TryRemove((serverID, sessionID), out _);
                            despawnPlayer.NotifyPlayerExit(despawnPlayer, player);
                            
                            despawnPlayer.NearbyPlayers.TryRemove((World.ServerID, player.SessionID), out _);
                        }
                    }
                }
                // Kiểm tra players cần spawn (mới vào tầm nhìn)
                foreach (var playerID in currentVisible)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var newPlayer) &&
                        !player.NearbyPlayers.ContainsKey((World.ServerID, playerID)))
                    {
                        player.NearbyPlayers.TryAdd((World.ServerID, playerID), newPlayer);
                        // Trigger spawn event
                        player.UpdateCharacterData(newPlayer);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"UpdateNearbyPlayersCache error for {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật cache NearbyNpcs chỉ cho events (spawn/despawn)
        /// </summary>
        public static void UpdateNearbyNpcsCache(this Players player)
        {
            try
            {
                if (player?.Client?.Running != true || player.NearbyNpcs == null)
                    return;

                var grids = AOISystem.Instance.GetNearbyGrids(player.MapID, player.PosX, player.PosY);
                var currentVisible = new HashSet<int>();

                // Tìm NPCs hiện tại trong tầm nhìn
                foreach (var grid in grids)
                {
                    foreach (var npcID in grid.NPCs.Keys)
                    {
                        if (World.MapList.TryGetValue(player.MapID, out var mapClass))
                        {
                            var npc = mapClass.GetNpcBySessionId(npcID);
                            if (npc != null &&
                                AOISystem.Instance.IsWithinAOIRange(player.PosX, player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                            {
                                currentVisible.Add(npcID);
                            }
                        }
                    }
                }

                // Kiểm tra NPCs cần despawn (không còn trong tầm nhìn)
                var npcsToDespawn = new Dictionary<int, NpcClass>();
                var toRemove = new List<int>();
                foreach (var kvp in player.NearbyNpcs.ToList())
                {
                    var npcID = kvp.Key;
                    if (!currentVisible.Contains(npcID))
                    {
                        toRemove.Add(npcID);
                        npcsToDespawn.Add(npcID, kvp.Value);
                    }
                }

                // Remove despawned NPCs from cache
                foreach (var npcID in toRemove)
                {
                    player.NearbyNpcs.TryRemove(npcID, out _);
                }

                // Send despawn packets if needed
                if (npcsToDespawn.Count > 0)
                {
                    NpcClass.UpdateNPC_Despawn(npcsToDespawn, player);
                }

                // Kiểm tra NPCs cần spawn (mới vào tầm nhìn)
                var npcsToSpawn = new Dictionary<int, NpcClass>();
                foreach (var npcID in currentVisible)
                {
                    if (World.MapList.TryGetValue(player.MapID, out var mapClass) &&
                        !player.NearbyNpcs.ContainsKey(npcID))
                    {
                        var npc = mapClass.GetNpcBySessionId(npcID);
                        if (npc != null)
                        {
                            player.NearbyNpcs.TryAdd(npcID, npc);
                            npcsToSpawn.Add(npcID, npc);
                        }
                    }
                }

                // Send spawn packets for new NPCs
                if (npcsToSpawn.Count > 0)
                {
                    NpcClass.UpdateNPC_Spawn(npcsToSpawn, player);
                    LogHelper.WriteLine(LogLevel.Debug, $"Spawned {npcsToSpawn.Count} NPCs for player {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"UpdateNearbyNpcsCache error for {player?.CharacterName}: {ex.Message}");
            }
        }
        #endregion

        #region NPC Extensions
        public static void AddToAOI(this NpcClass npc) => AOISystem.Instance.AddNPC(npc);
        public static void RemoveFromAOI(this NpcClass npc) => AOISystem.Instance.RemoveNPC(npc.NPC_SessionID, npc.Rxjh_Map);

        /// <summary>
        /// Force broadcast NPC spawn to nearby players
        /// </summary>
        public static void BroadcastSpawnToNearbyPlayers(this NpcClass npc)
        {
            try
            {
                var grids = AOISystem.Instance.GetNearbyGrids(npc.Rxjh_Map, npc.Rxjh_X, npc.Rxjh_Y);
                var playersToUpdate = new List<Players>();

                foreach (var grid in grids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (player?.Client?.Running == true && player.MapID == npc.Rxjh_Map &&
                            AOISystem.Instance.IsWithinAOIRange(player.PosX, player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                        {
                            playersToUpdate.Add(player);
                        }
                    }
                }

                // Update all nearby players' NPC cache
                foreach (var player in playersToUpdate)
                {
                    // Add NPC to player's cache if not already there
                    if (!player.NearbyNpcs.ContainsKey(npc.NPC_SessionID))
                    {
                        player.NearbyNpcs.TryAdd(npc.NPC_SessionID, npc);

                        // Send spawn packet for this specific NPC
                        var spawnDict = new Dictionary<int, NpcClass> { { npc.NPC_SessionID, npc } };
                        NpcClass.UpdateNPC_Spawn(spawnDict, player);

                        LogHelper.WriteLine(LogLevel.Debug, $"Broadcasted NPC {npc.Name} spawn to player {player.CharacterName}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting NPC spawn: {ex.Message}");
            }
        }
        #endregion
        
        #region Ground Item Extensions
        public static void AddToAOI(this GroundItem item) => AOISystem.Instance.AddGroundItem(item);
        public static void RemoveFromAOI(this GroundItem item) => AOISystem.Instance.RemoveGroundItem(item.id, item.MapID);
        #endregion
        
        #region Batch Operations
        public static void BatchUpdateAOI(this IEnumerable<Players> players)
        {
            var playerList = players?.Where(p => p?.Client?.Running == true).ToList();
            if (playerList?.Count > 0)
                AOISystem.Instance.BatchUpdate(playerList);
        }
        #endregion
        
        #region Utility
        public static (int x, int y) GetGridCoords(float posX, float posY, int mapID) => AOISystem.Instance.GetGridCoords(posX, posY, mapID);
        public static int GetEntityCount(int mapID) => AOISystem.Instance.GetEntityCount(mapID);
        #endregion
        /// <summary>
        /// CRITICAL FIX: Sync NearbyPlayers with AOI data
        /// This helps bridge the gap between legacy systems during transition
        /// </summary>
        public static void SyncNearbyPlayersWithAOI(this Players player)
        {
            try
            {
                if (player?.Client?.Running != true) return;

                // Get current AOI visible players using direct grid queries
                var grids = AOISystem.Instance.GetNearbyGrids(player.MapID, player.PosX, player.PosY);
                var aoiVisiblePlayers = new List<Players>();
                
                foreach (var grid in grids)
                {
                    foreach (var visiblePlayer in grid.GetPlayers())
                    {
                        if (visiblePlayer.SessionID != player.SessionID &&
                            AOISystem.Instance.IsWithinAOIRange(player.PosX, player.PosY, visiblePlayer.PosX, visiblePlayer.PosY))
                        {
                            aoiVisiblePlayers.Add(visiblePlayer);
                        }
                    }
                }
                
                // Clear and rebuild NearbyPlayers to match AOI
                if (player.NearbyPlayers != null)
                {
                    player.NearbyPlayers.Clear();
                    foreach (var visiblePlayer in aoiVisiblePlayers)
                    {
                        player.NearbyPlayers.TryAdd((visiblePlayer.OriginalServerID, visiblePlayer.SessionID), visiblePlayer);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error syncing NearbyPlayers with AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Enhanced UpdateAOI that also syncs legacy systems
        /// </summary>
        public static void UpdateAOIComplete(this Players player)
        {
            player.UpdateAOI();
            player.SyncNearbyPlayersWithAOI();
        }
    }
}