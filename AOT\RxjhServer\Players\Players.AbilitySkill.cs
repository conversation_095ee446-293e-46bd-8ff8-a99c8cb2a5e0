﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
	public void KhiCongPoint(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		if (Player_Qigong_point < 1)
		{
			return;
		}
		try
		{
			var num = 0;
			var num2 = 0;
			int abilityID = BitConverter.ToUInt16(packetData, 118);
			int num4 = BitConverter.ToUInt16(packetData, 130);
			var khiCongLoaiHinh = 0;
			
			if (World.KhiCongTangThem.TryGetValue(abilityID, out var ability))
			{
				// Ki<PERSON>m tra điều kiện nâng kỹ năng
				if (ability.Job_level > Player_Job_level || ability.Player_Level > Player_Level || (ability.FLD_JOB != 0 && ability.FLD_JOB != Player_Job))
				{
					HeThongNhacNho("Đại hiệp không đủ điều kiện để sử dụng kỹ năng này!", 10, "Thiên cơ các");
					return;
				}
				for (var i = 0; i < 12; i++)
				{
					var array3 = new byte[2];
					Buffer.BlockCopy(packetData, 10 + i * 4, array3, 0, 2);
					if (BitConverter.ToInt16(array3, 0) != abilityID)
					{
						continue;
					}
					if (BitConverter.ToInt16(KhiCong[i].KhiCong_byte, 0) >= 20)
					{
						return;
					}
					if (num4 != 1)
					{
						num = BitConverter.ToInt16(KhiCong[i].KhiCong_byte, 0) + 1;
						Player_Qigong_point--;
					}
					else
					{
						var num5 = 20 - BitConverter.ToInt16(KhiCong[i].KhiCong_byte, 0);
						if (num5 >= Player_Qigong_point)
						{
							num5 = Player_Qigong_point;
						}
						num = BitConverter.ToInt16(KhiCong[i].KhiCong_byte, 0) + num5;
						Player_Qigong_point -= num5;
					}
					if (num > 20)
					{
						num = 20;
					}
					BitConverter.GetBytes(num);
					KhiCong[i].KhiCong_SoLuong = num;
				}
			}
			else if (ThangThienKhiCong.TryGetValue(abilityID, out var value) && value.KhiCong_SoLuong < 20)
			{
				khiCongLoaiHinh = 1;
				if (value.KhiCong_SoLuong >= 20)
				{
					return;
				}
				if (num4 == 1)
				{
					var num6 = 20 - value.KhiCong_SoLuong;
					if (num6 >= Player_Qigong_point)
					{
						num6 = Player_Qigong_point;
					}
					num2 = value.KhiCong_SoLuong + num6;
					Player_Qigong_point -= num6;
				}
				else
				{
					num2 = value.KhiCong_SoLuong + 1;
					Player_Qigong_point--;
				}
				if (num2 > 20)
				{
					num2 = 20;
				}
				value.KhiCong_SoLuong = num2;
			}
			else if (PhanKhiCong.TryGetValue(abilityID, out value) && value.KhiCong_SoLuong < 20)
			{
				if (value.KhiCong_SoLuong >= 20)
				{
					return;
				}
				if (num4 == 1)
				{
					var num7 = 20 - value.KhiCong_SoLuong;
					if (Player_Anti_Qigong_point >= num7)
					{
						Player_Anti_Qigong_point -= num7;
						value.KhiCong_SoLuong = 20;
					}
					else
					{
						value.KhiCong_SoLuong += Player_Anti_Qigong_point;
						Player_Anti_Qigong_point = 0;
					}
				}
				else
				{
					var num8 = value.KhiCong_SoLuong + 1;
					Player_Anti_Qigong_point--;
					if (num8 > 20)
					{
						num8 = 20;
					}
					value.KhiCong_SoLuong = num8;
				}
			}
			if (num < 20)
			{
				LonNhat_KhiCong_PhatDong(abilityID, khiCongLoaiHinh);
			}
			UpdateKhiCong();
			UpdateMartialArtsAndStatus();
			CapNhat_HP_MP_SP();
		}
		catch (Exception)
		{
			HeThongNhacNho("Mời đại hiệp sử dụng quan phục mới nhất từ hộ khách!", 20, "Thiên cơ các");
		}
	}
	
	public void Goi_KyNang_HocTap(byte[] packetData, int packetSize)
	{
		PacketReader packetReader = new(packetData, packetSize, bool_0: false);
		packetReader.Seek(4, SeekOrigin.Begin);
		var num = packetReader.ReadInt16();
		packetReader.Seek(10, SeekOrigin.Begin);
		var num2 = packetReader.ReadInt32();
		X_Vo_Cong_Loai value2;
		if (num == SessionID)
		{
			if (!World.MagicList.TryGetValue(num2, out var value))
			{
				return;
			}
			if (Player_Level < value.FLD_LEVEL)
			{
				HeThongNhacNho("Nhân vật cần đạt cấp độ [" + value.FLD_LEVEL + "] mới có thể tu luyện!", 10, "Thiên cơ các");
				return;
			}
			if (Player_Job_level < value.FLD_JOBLEVEL)
			{
				HeThongNhacNho("Nhân vật cần đạt [" + value.FLD_JOBLEVEL + "] thăng chức mới có thể tu luyện!", 10, "Thiên cơ các");
				return;
			}
			if (SkillExperience < value.FLD_NEEDEXP)
			{
				HeThongNhacNho("Lịch luyện cần đạt [" + value.FLD_NEEDEXP + "] mới có thể tu luyện!", 10, "Thiên cơ các");
				return;
			}
			X_Vo_Cong_Loai xVoCongLoai = new(num2);
			if (Player_Job != 8 && Player_Job != 9 && Player_Job != 13)
			{
				VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
				SkillExperience -= value.FLD_NEEDEXP;
			}
			else if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] != null && VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap != 0)
			{
				if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap >= value.FLD_VoCongToiCaoDangCap)
				{
					VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap = value.FLD_VoCongToiCaoDangCap;
					return;
				}
				var num3 = xVoCongLoai.FLD_LEVEL;
				if (xVoCongLoai.FLD_LEVEL <= 1)
				{
					num3 = 0;
				}
				var num4 = xVoCongLoai.FLD_NEEDEXP + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				int num5;
				if (Player_Job == 9)
				{
					num5 = num3 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * (value.FLD_MoiCapThemTuLuyenDangCap / 2) + 20;
				}
				else
				{
					num5 = num3 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemTuLuyenDangCap;
					if (num5 > Player_Level)
					{
						HeThongNhacNho("Đại hiệp chưa đủ cấp độ để lĩnh hội võ công này!!", 10, "Thiên cơ các");
						return;
					}
				}
				var num6 = VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapVoCongDiemSo;
				if (xVoCongLoai.FLD_PID == 1000501)
				{
					if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 2)
					{
						num4 = 250000;
					}
				}
				else if (xVoCongLoai.FLD_PID == 1000402)
				{
					if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 2)
					{
						num4 = 5000;
					}
					else if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 3)
					{
						num4 = 50000;
					}
				}
				else if (xVoCongLoai.FLD_PID == 1000401)
				{
					if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 1)
					{
						num4 = 200;
					}
					else if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 2)
					{
						num4 = 1000;
					}
				}
				else if (xVoCongLoai.FLD_PID == 1000109)
				{
					if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 1)
					{
						num4 = 0;
					}
				}
				else if (xVoCongLoai.FLD_PID == 1000106)
				{
					num4 -= 50;
					if (num4 < 0)
					{
						num4 = 0;
					}
				}
				else if (xVoCongLoai.FLD_PID == 2030101)
				{
					if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 1)
					{
						num5 = 115;
					}
					else if (VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap == 2)
					{
						num5 = 119;
					}
					num4 = 100000 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				}
				else if (xVoCongLoai.FLD_PID == 2030102)
				{
					num4 = 130000 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				}
				else if (xVoCongLoai.FLD_PID == 2030103)
				{
					num4 = 160000 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				}
				else if (xVoCongLoai.FLD_PID == 2030201)
				{
					num4 = 100000 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				}
				else if (xVoCongLoai.FLD_PID == 2030202)
				{
					num4 = 130000 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				}
				else if (xVoCongLoai.FLD_PID == 2030203)
				{
					num4 = 160000 + VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapThemLichLuyen;
				}
				if (Player_Level < num5)
				{
					HeThongNhacNho("Nhân vật cần đạt cấp độ [" + num5 + "] mới có thể tu luyện!", 10, "Thiên cơ các");
					return;
				}
				if (SkillExperience < num4)
				{
					HeThongNhacNho("Nhân vật lịch luyện đạt tới [" + xVoCongLoai.FLD_NEEDEXP + "] mới có thể tu luyện.");
					return;
				}
				if (ThangThienVoCong_DiemSo < num6)
				{
					HeThongNhacNho("Điểm võ công thăng thiên không đủ, không thể tu luyện!", 10, "Thiên cơ các");
					return;
				}
				SkillExperience -= num4;
				ThangThienVoCong_DiemSo -= num6;
				VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX].VoCong_DangCap++;
			}
			else
			{
				if (xVoCongLoai.FLD_PID == 2009101)
				{
					for (var i = 0; i < 3; i++)
					{
						xVoCongLoai = new X_Vo_Cong_Loai(num2 + i);
						xVoCongLoai.VoCong_DangCap = 1;
						VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
					}
				}
				else if (xVoCongLoai.FLD_PID == 2009104)
				{
					for (var j = 0; j < 4; j++)
					{
						xVoCongLoai = new X_Vo_Cong_Loai(num2 + j);
						xVoCongLoai.VoCong_DangCap = 1;
						VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
					}
				}
				else if (xVoCongLoai.FLD_PID == 2009108)
				{
					for (var k = 0; k < 5; k++)
					{
						xVoCongLoai = new X_Vo_Cong_Loai(num2 + k);
						xVoCongLoai.VoCong_DangCap = 1;
						VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
					}
				}
				else if (xVoCongLoai.FLD_PID == 6000201)
				{
					for (var l = 0; l < 3; l++)
					{
						xVoCongLoai = new X_Vo_Cong_Loai(num2 + l);
						xVoCongLoai.VoCong_DangCap = 1;
						VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
					}
				}
				else if (xVoCongLoai.FLD_PID == 6000202)
				{
					for (var m = 0; m < 4; m++)
					{
						xVoCongLoai = new X_Vo_Cong_Loai(num2 + m);
						xVoCongLoai.VoCong_DangCap = 1;
						VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
					}
				}
				else if (xVoCongLoai.FLD_PID == 6000203)
				{
					for (var n = 0; n < 5; n++)
					{
						xVoCongLoai = new X_Vo_Cong_Loai(num2 + n);
						xVoCongLoai.VoCong_DangCap = 1;
						VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
					}
				}
				else if (xVoCongLoai.FLD_PID == 2000402 && VoCongMoi[0, 23] == null)
				{
					return;
				}
				xVoCongLoai.VoCong_DangCap = 1;
				VoCongMoi[xVoCongLoai.FLD_VoCongLoaiHinh, xVoCongLoai.FLD_INDEX] = xVoCongLoai;
				SkillExperience -= value.FLD_NEEDEXP;
			}
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
			LearningSkillsTips();
		}
		else if (num == CharacterBeastFullServiceID && World.MagicList.TryGetValue(num2, out value2))
		{
			X_Vo_Cong_Loai xVoCongLoai2 = new(num2);
			if (SkillExperience < xVoCongLoai2.FLD_NEEDEXP)
			{
				HeThongNhacNho("Kỹ năng không đủ, không thể tu luyện!", 10, "Thiên cơ các");
				return;
			}
			CharacterBeast.VoCongMoi[xVoCongLoai2.FLD_VoCongLoaiHinh, xVoCongLoai2.FLD_INDEX] = xVoCongLoai2;
			SkillExperience -= xVoCongLoai2.FLD_NEEDEXP;
			UpdateSpiritBeastMartialArtsAndStatus();
			LearningSkillsTips();
			UpdateKinhNghiemVaTraiNghiem();
		}
		else
		{
			HeThongNhacNho("Chưa đạt yêu cầu, không thể tu luyện!", 10, "Thiên cơ các");
		}
	}

	public void LearningSkills(int fldVoCongLoaiHinh, int fldIndex, int skillLevel = 0)
	{
		var wg = X_Vo_Cong_Loai.GetWg(Player_Zx, Player_Job, fldVoCongLoaiHinh, fldIndex);
		if (wg != null)
		{
			if (wg.FLD_NEEDEXP > SkillExperience)
			{
				HeThongNhacNho("Kinh nghiệm võ công cần đạt [" + wg.FLD_NEEDEXP + "] mới có thể tu luyện!", 10, "Thiên cơ các");
				LogHelper.WriteLine(LogLevel.Debug, $"Kinh nghiệm võ công không đủ {wg.FLD_PID} !![" + wg.FLD_NAME + "] [" + AccountID + "][" + CharacterName + "] Cần: " + wg.FLD_NEEDEXP + " Có: " + SkillExperience);

				return;
			}
			VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX] = new X_Vo_Cong_Loai(wg.FLD_PID);
			if (skillLevel > 0)
			{
				VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX].VoCong_DangCap = skillLevel;
			}
			SkillExperience -= wg.FLD_NEEDEXP;
			LearningSkillsTips();

		}
	}
	

	public void LearningSkillsTips()
	{
		var array = Converter.HexStringToByte("AA554A01000046003C01000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000AC260000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SkillExperience), 0, array, 322, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void KyNangLienHoan(byte[] data, int length)
	{
		try
		{
			if (Player_Job == 7)
			{
				return;
			}
			int num = data[14];
			var array = Converter.HexStringToByte("AA555200B801C8004C000100000001000000A9360300853C0300863C0300873C030000000000A9360300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			Buffer.BlockCopy(data, 38, array, 38, 16);
			NewMartialArtsCombos.Clear();
			for (var i = 0; i < num; i++)
			{
				List<X_Vo_Cong_Loai> list = new();
				var array2 = new byte[4];
				try
				{
					for (var j = 0; j < 3; j++)
					{
						Buffer.BlockCopy(data, j * 4 + 38, array2, 0, 4);
						if (BitConverter.ToInt32(array2, 0) != 0)
						{
							list.Add(new X_Vo_Cong_Loai(BitConverter.ToInt32(array2, 0)));
						}
					}
				}
				catch
				{
				}
			}
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}

	public void KyNangLienHoan2(byte[] data, int length)
	{
		var array = Converter.HexStringToByte("AA555200AF05C3004C00010000000000000000000000853C0300863C0300873C030000000000A9360300AB3603000D37030000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(data, 38, array, 38, 16);
		NewMartialArtsCombos.Clear();
		for (var i = 0; i < 3; i++)
		{
			new List<X_Vo_Cong_Loai>();
			var array2 = new byte[4];
			Buffer.BlockCopy(data, i * 4 + 38, array2, 0, 4);
			NewMartialArtsCombos.Add(new X_Vo_Cong_Loai(BitConverter.ToInt32(array2, 0)));
			HeThongNhacNho("Võ công Liên Hoàn đăng ký thành công!", 10, "Thiên cơ các");
		}
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void PutInTheShortcutBar(byte[] packetData, int packetSize)
	{
		var array = Converter.HexStringToByte("AA551F002C01181010002CDC143C00000000030002000300000000000000000000000055AA");
		var array2 = new byte[4];
		var array3 = new byte[4];
		var array4 = new byte[4];
		var array5 = new byte[4];
		Buffer.BlockCopy(packetData, 10, array2, 0, 4);
		Buffer.BlockCopy(packetData, 18, array3, 0, 2);
		Buffer.BlockCopy(packetData, 20, array4, 0, 2);
		Buffer.BlockCopy(packetData, 22, array5, 0, 2);
		Buffer.BlockCopy(array2, 0, array, 10, 4);
		var itmeid = Converter.getItmeid(Converter.ToString(array2));
		Buffer.BlockCopy(BitConverter.GetBytes(itmeid), 0, array, 10, 4);
		if (BitConverter.ToInt32(array4, 0) == 2)
		{
			if (itmeid == 1008000003 && ShortcutBar.Contains(1008000003))
			{
				if (NhanVat_HP > CharacterMax_HP)
				{
					NhanVat_HP = CharacterMax_HP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000006 && ShortcutBar.Contains(1008000006))
			{
				if (NhanVat_MP > CharacterMax_MP)
				{
					NhanVat_MP = CharacterMax_MP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000044 && ShortcutBar.Contains(1008000044))
			{
				CharactersToAddMax_HP -= 200;
				if (NhanVat_HP > CharacterMax_HP)
				{
					NhanVat_HP = CharacterMax_HP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000045 && ShortcutBar.Contains(1008000045))
			{
				CharactersToAddMax_MP -= 200;
				if (NhanVat_MP > CharacterMax_MP)
				{
					NhanVat_MP = CharacterMax_MP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000077 && ShortcutBar.Contains(1008000077))
			{
				if (NhanVat_HP > CharacterMax_HP)
				{
					NhanVat_HP = CharacterMax_HP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000078 && ShortcutBar.Contains(1008000078))
			{
				if (NhanVat_MP > CharacterMax_MP)
				{
					NhanVat_MP = CharacterMax_MP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000079 && ShortcutBar.Contains(1008000079))
			{
				if (NhanVat_HP > CharacterMax_HP)
				{
					NhanVat_HP = CharacterMax_HP;
				}
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000080 && ShortcutBar.Contains(1008000080))
			{
				if (NhanVat_MP > CharacterMax_MP)
				{
					NhanVat_MP = CharacterMax_MP;
				}
				ShortcutBar.Remove(itmeid);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 18, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 20, 2);
		}
		else
		{
			if (itmeid == 1008000003 && ShortcutBar.Contains(1008000003))
			{
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000006 && ShortcutBar.Contains(1008000006))
			{
				ShortcutBar.Remove(itmeid);
			}
			if (itmeid == 1008000044 && !ShortcutBar.Contains(1008000044))
			{
				CharactersToAddMax_HP += 200;
				ShortcutBar.Add(1008000044);
			}
			if (itmeid == 1008000045 && !ShortcutBar.Contains(1008000045))
			{
				CharactersToAddMax_MP += 200;
				ShortcutBar.Add(1008000045);
			}
			if (itmeid == 1008000077 && !ShortcutBar.Contains(1008000077))
			{
				ShortcutBar.Add(1008000077);
			}
			if (itmeid == 1008000078 && !ShortcutBar.Contains(1008000078))
			{
				ShortcutBar.Add(1008000078);
			}
			if (itmeid == 1008000079 && !ShortcutBar.Contains(1008000079))
			{
				ShortcutBar.Add(1008000079);
			}
			if (itmeid == 1008000080 && !ShortcutBar.Contains(1008000080))
			{
				ShortcutBar.Add(1008000080);
			}
			Buffer.BlockCopy(array3, 0, array, 18, 2);
			Buffer.BlockCopy(array4, 0, array, 20, 2);
		}
		Buffer.BlockCopy(array5, 0, array, 22, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		CapNhat_HP_MP_SP();
	}

	public void ActionExpression(byte[] data, int length)
	{
		ActionExpression(data[10]);
	}

	public void ActionExpression(int dongTac)
	{
		try
		{
			if (GMMode == 0 && MapID == 101 && World.Event_Tet_GiapThin_Progress != 0 && (GetAddState(601101) || GetAddState(601102) || GetAddState(601103) || GetAddState(1001101) || GetAddState(1001102) || GetAddState(1001201) || GetAddState(1001202)))
			{
				HeThongNhacNho("Không thể dùng khinh công để công phá Dưa Hấu!!", 20, "Thiên cơ các");
				return;
			}
			switch (dongTac)
			{
				case 100:
					if (DateTime.Now.Subtract(Time_Bieu_Cam).TotalMilliseconds > 500.0)
					{
						Time_Bieu_Cam = DateTime.Now;
						var array2 = Converter.HexStringToByte("AA550700EE04240001000255AA");
						Buffer.BlockCopy(BitConverter.GetBytes(dongTac), 0, array2, 10, 1);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
						Client?.Send_Map_Data(array2, array2.Length);
						SendCurrentRangeBroadcastData(array2, array2.Length);
					}
					break;
				case 101:
					{
						if (!(DateTime.Now.Subtract(Time_Bieu_Cam).TotalMilliseconds > 500.0))
						{
							break;
						}
						Time_Bieu_Cam = DateTime.Now;
						if (CharacterPKMode != 0 && MapID != 801 && MapID != 40101)
						{
							HeThongNhacNho("Trong trạng thái giao chiến, không thể sử dụng chức năng này!!", 10, "Thiên cơ các");
							break;
						}
						var array3 = Converter.HexStringToByte("AA550700EE04240001000255AA");
						Buffer.BlockCopy(BitConverter.GetBytes(dongTac), 0, array3, 10, 1);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
						Client?.Send_Map_Data(array3, array3.Length);
						SendCurrentRangeBroadcastData(array3, array3.Length);
						break;
					}
				default:
					{
						var array = Converter.HexStringToByte("AA550700EE04240001000255AA");
						Buffer.BlockCopy(BitConverter.GetBytes(dongTac), 0, array, 10, 1);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
						Client?.Send_Map_Data(array, array.Length);
						SendCurrentRangeBroadcastData(array, array.Length);
						break;
					}
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Động tác lỗi đã Try !!!");
		}
	}
	
	private void Hoc_Skill_ThanNu(byte[] data, int length)
	{
		var num = BitConverter.ToInt32(data, 10);
		if (!World.MagicList.TryGetValue(num, out var value) || value.FLD_VoCongLoaiHinh != 1)
		{
			return;
		}
		try
		{
			if (VoCongMoi[1, value.FLD_INDEX] == null)
			{
				VoCongMoi[1, value.FLD_INDEX] = new X_Vo_Cong_Loai(num);
			}
			var num2 = ((VoCongMoi[1, value.FLD_INDEX].VoCong_DangCap == 0) ? 1 : (VoCongMoi[1, value.FLD_INDEX].VoCong_DangCap + value.FLD_MoiCapVoCongDiemSo));
			if (ThanNuVoCongDiemSo < num2)
			{
				HeThongNhacNho("Điểm thần lực không đủ, không thể lĩnh hội võ công này!", 10, "Thiên cơ các");
				return;
			}
			if (VoCongMoi[1, value.FLD_INDEX].VoCong_DangCap >= value.FLD_VoCongToiCaoDangCap)
			{
				VoCongMoi[1, value.FLD_INDEX].VoCong_DangCap = value.FLD_VoCongToiCaoDangCap;
				return;
			}
			ThanNuVoCongDiemSo -= num2;
			VoCongMoi[1, value.FLD_INDEX].VoCong_DangCap++;
			UpdateMartialArtsAndStatus();
		}
		catch
		{
		}
	}

	private void ThemThangThienVoCongPoint(byte[] data, int length)
	{
		var num = BitConverter.ToInt32(data, 10);
		if (!World.MagicList.TryGetValue(num, out var value) || value.FLD_VoCongLoaiHinh != 3)
		{
			return;
		}
		try
		{
			if ((value.FLD_ZX != 0 && value.FLD_ZX != Player_Zx) || value.FLD_JOB != Player_Job || value.FLD_JOBLEVEL > Player_Job_level || value.FLD_LEVEL > Player_Level)
			{
				return;
			}
			if (VoCongMoi[3, value.FLD_INDEX] != null && VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap >= value.FLD_VoCongToiCaoDangCap)
			{
				VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap = value.FLD_VoCongToiCaoDangCap;
				HeThongNhacNho("Giới hạn nâng cấp võ công là [" + value.FLD_VoCongToiCaoDangCap + "] để có thể tu luyện!!", 10, "Thiên cơ các");
				return;
			}
			if (VoCongMoi[3, value.FLD_INDEX] == null)
			{
				VoCongMoi[3, value.FLD_INDEX] = new X_Vo_Cong_Loai(num);
			}
			if (Player_Job == 8)
			{
				int num2;
				int num3;
				if (VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap <= 0)
				{
					num2 = Player_Level;
					num3 = 0;
				}
				else
				{
					num2 = value.FLD_MoiCapThemTuLuyenDangCap + (VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap - 1) * value.FLD_MoiCapThemTuLuyenDangCap;
					num3 = VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapVoCongDiemSo;
				}
				if (Player_Level < num2)
				{
					HeThongNhacNho("Nhân vật cần đạt cấp độ [" + num2 + "] mới có thể tu luyện!", 10, "Thiên cơ các");
					return;
				}
				if (ThangThienVoCong_DiemSo < num3)
				{
					HeThongNhacNho("Điểm võ công thăng thiên không đủ, không thể tu luyện!", 10, "Thiên cơ các");
					return;
				}
				ThangThienVoCong_DiemSo -= num3;
				VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap++;
				UpdateKinhNghiemVaTraiNghiem();
				UpdateMartialArtsAndStatus();
				return;
			}
			int num4;
			int num5;
			if (VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap <= 0)
			{
				num4 = value.FLD_NEEDEXP;
				num5 = 0;
			}
			else
			{
				num4 = value.FLD_NEEDEXP + (VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap - 1) * value.FLD_MoiCapThemLichLuyen;
				num5 = VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap * value.FLD_MoiCapVoCongDiemSo;
			}
			if (SkillExperience < num4)
			{
				HeThongNhacNho("Nhân vật cần đạt [" + num4 + "] điểm kỹ năng mới có thể tu luyện!", 10, "Thiên cơ các");
				return;
			}
			if (ThangThienVoCong_DiemSo < num5)
			{
				HeThongNhacNho("Điểm võ công thăng thiên không đủ, không thể tu luyện!", 10, "Thiên cơ các");
				return;
			}
			SkillExperience -= num4;
			ThangThienVoCong_DiemSo -= num5;
			VoCongMoi[3, value.FLD_INDEX].VoCong_DangCap++;
			UpdateKinhNghiemVaTraiNghiem();
			UpdateMartialArtsAndStatus();
		}
		catch
		{
		}
	}
		public void LonNhat_KhiCong_PhatDong(int khiCongidd, int khiCongLoaiHinh)
	{
		var array = Converter.HexStringToByte("AA554E00A202440058000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		if (khiCongLoaiHinh == 0)
		{
			for (var i = 0; i < 12; i++)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(GetKhiCong_ID(i, Player_Job)), 0, array, 10 + i * 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(KhiCong[i].KhiCongID), 0, array, 12 + i * 4, 2);
			}
		}
		else
		{
			var num = 0;
			foreach (var value in ThangThienKhiCong.Values)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(value.KhiCongID), 0, array, 10 + num * 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value.KhiCong_SoLuong), 0, array, 12 + num * 4, 2);
				num++;
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(khiCongidd), 0, array, 82, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Player_Qigong_point), 0, array, 84, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(10), 0, array, 86, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void InitializeCareerSkills()
	{
		var num = 0;
		try
		{
			List<X_Vo_Cong_Loai> list = new();
			foreach (var value in World.MagicList.Values)
			{
				if (value.FLD_PID != 710100 && value.FLD_PID != 710101 && value.FLD_PID != 710102 && value.FLD_PID != 710103 && value.FLD_JOB == Player_Job)
				{
					list.Add(value);
				}
			}
			StringBuilder stringBuilder = new();
			stringBuilder.Append("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");
			var array = Converter.HexStringToByte(stringBuilder.ToString());
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			for (var i = 0; i < list.Count; i++)
			{
				if (list[i].FLD_INDEX <= 31 && list[i].FLD_INDEX != -1)
				{
					var index = i % 200;
					var coolDown = list[i].FLD_CDTIME;
					Buffer.BlockCopy(BitConverter.GetBytes(list[i].FLD_PID), 0, array, 16 + index * 56, 4);
					if (Player_Job == 8 && coolDown > 1000 && HanBaoQuan_ThangThien_3_KhiCong_NoiTucHanhTam != 0.0)
					{
						coolDown = (int)(coolDown * (1.0 - HanBaoQuan_ThangThien_3_KhiCong_NoiTucHanhTam));
					}
					Buffer.BlockCopy(BitConverter.GetBytes(coolDown), 0, array, 16 + index * 56 + 32, 4);
					var magicType = list[i].FLD_VoCongLoaiHinh;
					num = list[i].FLD_PID;
					if ((magicType == 0 || magicType == 3) && VoCongMoi[list[i].FLD_VoCongLoaiHinh, list[i].FLD_INDEX] != null)
					{
						VoCongMoi[list[i].FLD_VoCongLoaiHinh, list[i].FLD_INDEX].FLD_CDTIME = coolDown;
					}
				}
			}

			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khởi tạo Kỹ năng nghề nghiệp () [" + SessionID + "] | ID kỹ năng : [" + num + "] - [" + ex.Message);
		}
	}

	public void ViewBiography(byte[] data, int length)
	{
		try
		{
			if (DanhSach_TruyenThu == null)
			{
				return;
			}
			var array = Converter.HexStringToByte("AA55D1010000B20023020000");
			var array2 = Converter.HexStringToByte("00000000000000000055AA");
			var array3 = new byte[array.Length + array2.Length + DanhSach_TruyenThu.Count * 36];
			Buffer.BlockCopy(array, 0, array3, 0, array.Length);
			Buffer.BlockCopy(array2, 0, array3, array3.Length - array2.Length, array2.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(DanhSach_TruyenThu.Count * 36 + 17), 0, array3, 2, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(DanhSach_TruyenThu.Count * 36 + 9), 0, array3, 8, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(DanhSach_TruyenThu.Count), 0, array3, 11, 2);
			var num = 0;
			foreach (var value in DanhSach_TruyenThu.Values)
			{
				var array4 = new byte[32];
				Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuID), 0, array4, 0, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.CoPhaiLaNPC), 0, array4, 4, 1);
				var bytes = Encoding.Default.GetBytes(value.TruyenThuNguoiGui);
				Buffer.BlockCopy(bytes, 0, array4, 5, bytes.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Year - 2000), 0, array4, 25, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Month), 0, array4, 26, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Day), 0, array4, 27, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Hour), 0, array4, 28, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(value.TruyenThuThoiGian.Minute), 0, array4, 29, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(value.DaXemHayChua), 0, array4, 30, 2);
				Buffer.BlockCopy(array4, 0, array3, num * 36 + 13, array4.Length);
				num++;
			}
			Client.Send_Map_Data(array3, array3.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kiểm tra TruyenThu error() " + SessionID + "|      " + ex.Message);
		}
	}
	
	public void ReadingBiography(byte[] packetData, int packetSize)
	{
		var num = BitConverter.ToInt32(packetData, 10);
		try
		{
			if (DanhSach_TruyenThu == null)
			{
				return;
			}
			var array = Converter.HexStringToByte("AA553F000000B600310000");
			var array2 = Converter.HexStringToByte("000000000000000055AA");
			foreach (var value in DanhSach_TruyenThu.Values)
			{
				if (value.TruyenThuID == num)
				{
					if (value.DaXemHayChua == 0)
					{
						value.DaXemHayChua = 1;
						GameDb.UpdateBirdMail(num, 1);
						GetAllMails();
					}
					var bytes = Encoding.Default.GetBytes(value.TruyenThuNoiDung);
					var array3 = new byte[array.Length + array2.Length + bytes.Length + 6];
					Buffer.BlockCopy(array, 0, array3, 0, array.Length);
					Buffer.BlockCopy(array2, 0, array3, array3.Length - array2.Length, array2.Length);
					Buffer.BlockCopy(BitConverter.GetBytes(array3.Length - 6), 0, array3, 2, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(bytes.Length + 7), 0, array3, 8, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array3, 11, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(bytes.Length), 0, array3, 15, 2);
					Buffer.BlockCopy(bytes, 0, array3, 17, bytes.Length);
					Client.Send_Map_Data(array3, array3.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Reading Biography() error" + SessionID + "|  " + ex.Message);
		}
	}
		public async Task CreateBiography(Players nguoiGui, string doiPhuongTen, int isNpc, string noiDungTinNhan, int tinNhanType, int soLuong)
	{
		try
		{
			if (Player_Money < 10000)
			{
				HeThongNhacNho("Ngân lượng không đủ!", 10, "Thiên cơ các");
				return;
			}
			Player_Money -= 10000L;
			UpdateMoneyAndWeight();
			if (await GameDb.CheckCharacterExists(doiPhuongTen))
			{
				var characterData = GetCharacterData(doiPhuongTen);
				if (characterData != null)
				{
					X_Nguoi_Truyen_Thu_Loai xNguoiTruyenThuLoai = new();
					xNguoiTruyenThuLoai.TruyenThuID = (int)RxjhClass.CreateItemSeries();
					xNguoiTruyenThuLoai.TruyenThuNguoiGui = CharacterName;
					xNguoiTruyenThuLoai.TruyenThuNoiDung = noiDungTinNhan;
					xNguoiTruyenThuLoai.TruyenThuThoiGian = DateTime.Now;
					xNguoiTruyenThuLoai.CoPhaiLaNPC = 0;
					xNguoiTruyenThuLoai.DaXemHayChua = 0;
					characterData.DanhSach_TruyenThu.Add(xNguoiTruyenThuLoai.TruyenThuID, xNguoiTruyenThuLoai);
					characterData.GetAllMails();
					characterData.NewMailNotification(tinNhanType, soLuong);
				}
				var array = Converter.HexStringToByte("AA551000010000B4000100000000000000005DA355AA");
				Client.Send_Map_Data(array, array.Length);
				GameDb.CreateBirdMail(doiPhuongTen, CharacterName, 0, noiDungTinNhan, tinNhanType);
				//RxjhClass.CreateBiography(CharacterName, doiPhuongTen, 0, noiDungTinNhan, tinNhanType);
			}
			else
			{
				HeThongNhacNho("Không thể tìm thấy đối phương!", 10, "Thiên cơ các");
			}
		}
		catch
		{
		}
	}

}
