
using System;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer;

public partial class Players
{
	public bool isChangingEquipment = false;
	public void ChangeEquipment(byte[] packetData, int packetSize)
	{
		try
		{
			if (isChangingEquipment)
			{
				return;
			}
			isChangingEquipment = true;
			if (Exiting)
			{
				LogHelper.WriteLine(LogLevel.Debug, "Thoát Change_Equipment BUG [" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  ");
				return;
			}
			if (!OpenWarehouse && (CuaHangCaNhan == null || !CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa))
			{
				int fromType = packetData[10];
				int fromIndex = packetData[12];
				int toType = packetData[14];
				int toIndex = packetData[16];
				var quantity = BitConverter.ToInt32(packetData, 18);
				if (quantity <= 0 || quantity > 9999)
				{
					return;
				}
				HeThong<PERSON>hacNho($"fromType: {fromType}[{fromIndex}] toType: {toType}[{toIndex} qt: {quantity} ]", 7, "DEBUG");
				var vatPhamByte = new byte[World.Item_Db_Byte_Length];
				switch (fromType)
				{
					case 193:
						//THAO_TAC_PINKBAG_VAT_PHAM(fromType, fromIndex, toType, toIndex, quantity);
						break;
					case 59:
						if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0) == 0)
						{
							break;
						}
						switch (toType)
						{
							case 60:
								if (toIndex >= 16)
								{
									break;
								}
								if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0) == 0)
								{
									if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0) != 0)
									{
										CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_byte;
										CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_byte = vatPhamByte;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
									}
								}
								else
								{
									if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0) == 0)
									{
										break;
									}
									var itmeClass = World.ItemList[BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0)];
									if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0) != 0 && !CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].Lock_Move)
									{
										var itmeClass2 = World.ItemList[BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0)];
										if (itmeClass.FLD_RESIDE2 == itmeClass2.FLD_RESIDE2 && itmeClass2.FLD_RESIDE2 >= 1 && itmeClass2.FLD_RESIDE2 <= 16)
										{
											var vatPhamByte4 = CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte;
											CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_byte;
											Item_Wear[fromIndex].VatPham_byte = vatPhamByte4;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
										}
									}
								}
								break;
							case 59:
								if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_ID, 0) == 0)
								{
									CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
									CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = vatPhamByte;
									ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[toIndex].VatPhamSoLuong, 0));
								}
								else
								{
									var vatPhamByte5 = CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte;
									CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
									CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = vatPhamByte5;
									ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[toIndex].VatPhamSoLuong, 0));
								}
								break;
							case 1:
								if (!CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_KhoaLai)
								{
									if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
									{
										Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
										CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = vatPhamByte;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
									}
									else
									{
										var vatPhamByte3 = Item_In_Bag[toIndex].VatPham_byte;
										Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
										CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = vatPhamByte3;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
									}
									UpdateMoneyAndWeight();
								}
								break;
						}
						break;
					case 1:
						{
							var xVatPhamLoai4 = Item_In_Bag[fromIndex];
							if (BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) == 0)
							{
								break;
							}
							switch (toType)
							{
								case 193:
									if (TitleDrug.ContainsKey(1008002684) || true)
									{
										if (!Item_In_Bag[fromIndex].Lock_Move && World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0), out var _) && BitConverter.ToInt32(EventBag[toIndex].VatPham_ID, 0) == 0)
										{
											EventBag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
											Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, EventBag[toIndex].VatPham_byte, BitConverter.ToInt32(EventBag[toIndex].VatPhamSoLuong, 0));
										}
									}
									else
									{
										HeThongNhacNho("Cần bảo vật [Túi 1] mới có thể sử dụng!!", 10, "Thiên cơ các");
									}
									break;
								case 1:
									{
										if (Item_In_Bag[fromIndex].Lock_Move || quantity != Item_In_Bag[fromIndex].GetVatPhamSoLuong || BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0)
										{
											break;
										}
										var num7 = 0;
										try
										{
											num7 = 1;
											var itmeClass20 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
											num7 = 2;
											if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
											{
												num7 = 3;
												Item_In_Bag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
												Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
												ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
												break;
											}
											num7 = 4;
											var itmeClass21 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
											num7 = 5;
											if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) != BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) || (itmeClass21.FLD_SIDE != 1 && itmeClass20.FLD_SIDE != 1))
											{
												num7 = 6;
												var vatPhamByte16 = Item_In_Bag[toIndex].VatPham_byte;
												Item_In_Bag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
												Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte16;
												ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
											}
											break;
										}
										catch
										{
											LogHelper.WriteLine(LogLevel.Error, "Vật phẩm chưa có trong TBL_XWWL_ITEM - num:[" + num7 + "]");
											break;
										}
									}
								case 0:
									{
										(bool flowControl, toIndex) = ChangeFromBagToWear(fromType, fromIndex, toType, toIndex, quantity, vatPhamByte);
										if (!flowControl)
										{
											break;
										}
										break;
									}
								case 169:
									{
										if (!Item_In_Bag[fromIndex].Lock_Move && World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0), out var value) && value.FLD_RESIDE2 == 12 && BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0) == 0)
										{
											AoChang_HanhLy[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
											Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
										}
										break;
									}
								case 123:
									{
										if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0 || Item_In_Bag[fromIndex].Lock_Move)
										{
											break;
										}
										var itmeClass18 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
										if (itmeClass18.FLD_LEVEL > Player_Level || (itmeClass18.FLD_ZX != 0 && itmeClass18.FLD_ZX != Player_Zx))
										{
											break;
										}
										if (World.GuildList.TryGetValue(CharacterName, out var value2))
										{
											var num6 = BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0);
											if (value2.Role != 6 && (num6 == 900105 || num6 == 900106 || num6 == 900107 || num6 == 900108))
											{
												HeThongNhacNho("Chỉ có bang chủ mới có thể sử dụng áo choàng Môn Chủ!!", 10, "Thiên cơ các");
												break;
											}
										}
										if (itmeClass18.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass18.FLD_RESIDE1 != Player_Job)
												{
													break;
												}
											}
											else if (itmeClass18.FLD_RESIDE1 != Player_Job && itmeClass18.FLD_RESIDE1 != 9)
											{
												break;
											}
										}
										if ((itmeClass18.FLD_JOB_LEVEL != 0 && itmeClass18.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass18.FLD_SEX != 0 && itmeClass18.FLD_SEX != Player_Sex) || itmeClass18.FLD_RESIDE2 < 1 || itmeClass18.FLD_RESIDE2 > 16 || (itmeClass18.FLD_XWJD >= 1 && itmeClass18.FLD_XWJD > VoHuanGiaiDoan) || (toIndex == 14 && CharacterBeast != null))
										{
											break;
										}
										if (BitConverter.ToInt32(Sub_Wear[toIndex].VatPham_ID, 0) == 0)
										{
											if ((Player_Job == 11 && itmeClass18.FLD_RESIDE1 == 0 && itmeClass18.FLD_RESIDE2 == 2) || (itmeClass18.FLD_RESIDE2 != toIndex + 1 && ((itmeClass18.FLD_RESIDE2 != 2 && itmeClass18.FLD_RESIDE2 != 3 && itmeClass18.FLD_RESIDE2 != 8 && itmeClass18.FLD_RESIDE2 != 9 && itmeClass18.FLD_RESIDE2 != 10 && itmeClass18.FLD_RESIDE2 != 11) || itmeClass18.FLD_RESIDE2 != toIndex)))
											{
												break;
											}
											Sub_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
											Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Wear[toIndex].VatPhamSoLuong, 0));
										}
										else
										{
											if (BitConverter.ToInt32(Sub_Wear[toIndex].VatPham_ID, 0) == 0)
											{
												break;
											}
											var itmeClass19 = World.ItemList[BitConverter.ToInt32(Sub_Wear[toIndex].VatPham_ID, 0)];
											if (itmeClass18.FLD_RESIDE2 != itmeClass19.FLD_RESIDE2)
											{
												break;
											}
											var vatPhamByte15 = Sub_Wear[toIndex].VatPham_byte;
											Sub_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
											Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte15;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Wear[toIndex].VatPhamSoLuong, 0));
										}
										UpdateCharacterData(this);
										UpdateBroadcastCharacterData();
										UpdateEquipmentEffects();
										CalculateCharacterEquipmentData();
										UpdateMartialArtsAndStatus();
										UpdateMoneyAndWeight();
										CapNhat_HP_MP_SP();
										break;
									}
								case 188:
									{
										if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0 || Item_In_Bag[fromIndex].Lock_Move || !World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0), out var value3) || value3.FLD_LEVEL > Player_Level || (value3.FLD_ZX != 0 && value3.FLD_ZX != Player_Zx))
										{
											break;
										}
										if (!ItemDef.VatPhamNgheNghiep_CoTrungKhopKhong(Player_Job, value3.FLD_RESIDE1, value3.FLD_RESIDE2, value3.FLD_PID))
										{
											HeThongNhacNho("Trang bị không phù hợp với nghề nghiệp của đại hiệp!", 10, "Thiên cơ các");
										}
										else
										{
											if (value3.FLD_RESIDE2 == 6 || value3.FLD_RESIDE2 == 7 || value3.FLD_RESIDE2 == 8 || value3.FLD_RESIDE2 == 9 || value3.FLD_RESIDE2 == 20 || value3.FLD_RESIDE2 == 21 || value3.FLD_RESIDE2 == 30 || value3.FLD_RESIDE2 == 30 || (value3.FLD_JOB_LEVEL != 0 && value3.FLD_JOB_LEVEL > Player_Job_level) || (value3.FLD_SEX != 0 && value3.FLD_SEX != Player_Sex) || (value3.FLD_XWJD >= 1 && value3.FLD_XWJD > VoHuanGiaiDoan) || (toIndex == 14 && CharacterBeast != null))
											{
												break;
											}
											ItmeClass value4;
											if (BitConverter.ToInt32(ThietBiTab3[toIndex].VatPham_ID, 0) == 0)
											{
												if (ItemDef.TrangBi_Position_CoChinhXacKhong(value3.FLD_RESIDE2, toIndex))
												{
													ThietBiTab3[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
													Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
													ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, ThietBiTab3[toIndex].VatPham_byte, BitConverter.ToInt32(ThietBiTab3[toIndex].VatPhamSoLuong, 0));
												}
											}
											else if (World.ItemList.TryGetValue(BitConverter.ToInt32(ThietBiTab3[toIndex].VatPham_ID, 0), out value4))
											{
												if (value3.FLD_RESIDE2 != value4.FLD_RESIDE2)
												{
													break;
												}
												var vatPhamByte17 = ThietBiTab3[toIndex].VatPham_byte;
												ThietBiTab3[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
												Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte17;
												ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, ThietBiTab3[toIndex].VatPham_byte, BitConverter.ToInt32(ThietBiTab3[toIndex].VatPhamSoLuong, 0));
											}
											UpdateCharacterData(this);
											UpdateBroadcastCharacterData();
											UpdateEquipmentEffects();
											CalculateCharacterEquipmentData();
											UpdateMartialArtsAndStatus();
											UpdateMoneyAndWeight();
											CapNhat_HP_MP_SP();
										}
										break;
									}
								case 60:
									if (Item_In_Bag[fromIndex].VatPham_KhoaLai || (BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100001 && BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100002 && BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100003 && BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100004 && BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100005 && BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100006 && BitConverter.ToInt32(xVatPhamLoai4.VatPham_ID, 0) != 601100007))
									{
										break;
									}
									if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0) == 0)
									{
										if (CharacterBeast.FLD_JOB_LEVEL == 1)
										{
											if (toIndex > 8)
											{
												break;
											}
										}
										else if (CharacterBeast.FLD_JOB_LEVEL == 2)
										{
											if (toIndex > 12)
											{
												break;
											}
										}
										else if (CharacterBeast.FLD_JOB_LEVEL == 3)
										{
											if (toIndex > 16)
											{
												break;
											}
										}
										else if (toIndex > 4)
										{
											break;
										}
										CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
										Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
									}
									else
									{
										var vatPhamByte14 = CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte;
										CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
										Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte14;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
									}
									UpdateTheWeightOfTheBeast();
									break;
								case 171:
									switch (fromType)
									{
										case 1:
											if (BitConverter.ToInt32(Item_NTC[toIndex].VatPham_ID, 0) == 0)
											{
												Item_NTC[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
												Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
												ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
											}
											break;
										case 171:
											if (BitConverter.ToInt32(Item_NTC[toIndex].VatPham_ID, 0) == 0)
											{
												Item_NTC[toIndex].VatPham_byte = Item_NTC[fromIndex].VatPham_byte;
												Item_NTC[fromIndex].VatPham_byte = vatPhamByte;
												ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_NTC[toIndex].VatPham_byte, BitConverter.ToInt32(Item_NTC[toIndex].VatPhamSoLuong, 0));
											}
											else
											{
												var vatPhamByte13 = Item_NTC[toIndex].VatPham_byte;
												Item_NTC[toIndex].VatPham_byte = Item_NTC[fromIndex].VatPham_byte;
												Item_NTC[fromType].VatPham_byte = vatPhamByte13;
												ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_NTC[toIndex].VatPham_byte, BitConverter.ToInt32(Item_NTC[toIndex].VatPhamSoLuong, 0));
											}
											break;
									}
									UpdateNTC_Bag();
									Init_Item_In_Bag();
									break;
							}
							break;
						}
					case 0:
						{
							var xVatPhamLoai = Item_Wear[fromIndex];
							if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) == 0 || xVatPhamLoai.Lock_Move)
							{
								break;
							}
							switch (toType)
							{
								case 169:
									{
										if (Item_Wear[fromIndex].Lock_Move || BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0) == 0 || Item_Wear[fromIndex].Lock_Move)
										{
											return;
										}
										var itmeClass8 = World.ItemList[BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0)];
										if ((itmeClass8.FLD_SEX != 0 && itmeClass8.FLD_SEX != Player_Sex) || itmeClass8.FLD_RESIDE2 != 12)
										{
											return;
										}
										if (BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0) == 0)
										{
											AoChang_HanhLy[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
											Item_Wear[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
											break;
										}
										var itmeClass9 = World.ItemList[BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0)];
										if (itmeClass8.FLD_RESIDE2 != itmeClass9.FLD_RESIDE2)
										{
											return;
										}
										var vatPhamByte8 = AoChang_HanhLy[toIndex].VatPham_byte;
										AoChang_HanhLy[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
										Item_Wear[fromIndex].VatPham_byte = vatPhamByte8;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
										break;
									}
								case 1:
									{
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) > 0)
										{
											var itmeClass5 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
											if (itmeClass5.FLD_NJ > 0 && Item_In_Bag[fromIndex].FLD_FJ_NJ <= 0)
											{
												HeThongNhacNho("Độ bền bảo vật bằng 0, không thể trang bị!", 10, "Thiên cơ các");
												break;
											}
										}
										HeThongNhacNho($"Real Quantity {Item_Wear[fromIndex].GetVatPhamSoLuong}");
										if (toIndex >= World.SoLuongTrangBi_ToiDa || (fromIndex == 14 && CharacterBeast != null) || quantity != Item_Wear[fromIndex].GetVatPhamSoLuong)
										{
											return;
										}
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
										{
											if (BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0) == 0)
											{
												return;
											}
											Item_In_Bag[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
											Item_Wear[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
											break;
										}
										if (BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0) == 0)
										{
											return;
										}
										var itmeClass6 = World.ItemList[BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0)];
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0 || Item_In_Bag[toIndex].Lock_Move)
										{
											return;
										}
										var itmeClass7 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
										if (itmeClass6.FLD_RESIDE2 != itmeClass7.FLD_RESIDE2 || itmeClass7.FLD_LEVEL > Player_Level || (itmeClass7.FLD_ZX != 0 && itmeClass7.FLD_ZX != Player_Zx))
										{
											return;
										}
										if (itmeClass6.FLD_RESIDE1 != itmeClass7.FLD_RESIDE1 && Player_Job == 11)
										{
											HeThongNhacNho("Bảo vật không dành cho Diệu Yến!", 10, "Thiên cơ các");
											return;
										}
										if (itmeClass6.FLD_RESIDE2 == 13 && Player_Job == 4)
										{
											HeThongNhacNho("Bảo vật không dành cho Cung!", 10, "Thiên cơ các");
											return;
										}
										if (itmeClass7.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass7.FLD_RESIDE1 != Player_Job)
												{
													return;
												}
											}
											else if (itmeClass7.FLD_RESIDE1 != Player_Job && itmeClass7.FLD_RESIDE1 != 10)
											{
												return;
											}
										}
										if ((itmeClass7.FLD_JOB_LEVEL != 0 && itmeClass7.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass7.FLD_SEX != 0 && itmeClass7.FLD_SEX != Player_Sex) || (itmeClass7.FLD_XWJD >= 1 && itmeClass7.FLD_XWJD > VoHuanGiaiDoan) || itmeClass7.FLD_RESIDE2 < 1 || itmeClass7.FLD_RESIDE2 >= 16)
										{
											return;
										}
										var vatPhamByte7 = Item_In_Bag[toIndex].VatPham_byte;
										Item_In_Bag[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
										Item_Wear[fromIndex].VatPham_byte = vatPhamByte7;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
										break;
									}
								case 0:
									{
										if (fromIndex == 14 && CharacterBeast != null)
										{
											return;
										}
										var itmeClass3 = World.ItemList[BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0)];
										if (itmeClass3.FLD_LEVEL > Player_Level || (itmeClass3.FLD_ZX != 0 && itmeClass3.FLD_ZX != Player_Zx))
										{
											return;
										}
										if (itmeClass3.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass3.FLD_RESIDE1 != Player_Job)
												{
													return;
												}
											}
											else if (itmeClass3.FLD_RESIDE1 != Player_Job && itmeClass3.FLD_RESIDE1 != 10)
											{
												return;
											}
										}
										if ((itmeClass3.FLD_JOB_LEVEL != 0 && itmeClass3.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass3.FLD_SEX != 0 && itmeClass3.FLD_SEX != Player_Sex) || (itmeClass3.FLD_XWJD >= 1 && itmeClass3.FLD_XWJD > VoHuanGiaiDoan) || itmeClass3.FLD_RESIDE2 < 1 || itmeClass3.FLD_RESIDE2 > 16)
										{
											return;
										}
										if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
										{
											if (itmeClass3.FLD_RESIDE2 != toIndex + 1 && ((itmeClass3.FLD_RESIDE2 != 2 && itmeClass3.FLD_RESIDE2 != 3 && itmeClass3.FLD_RESIDE2 != 8 && itmeClass3.FLD_RESIDE2 != 9 && itmeClass3.FLD_RESIDE2 != 10 && itmeClass3.FLD_RESIDE2 != 11) || itmeClass3.FLD_RESIDE2 != toIndex))
											{
												return;
											}
											Item_Wear[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
											Item_Wear[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
											break;
										}
										if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
										{
											return;
										}
										var itmeClass4 = World.ItemList[BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0)];
										if (itmeClass3.FLD_RESIDE2 != itmeClass4.FLD_RESIDE2)
										{
											return;
										}
										var vatPhamByte6 = Item_Wear[toIndex].VatPham_byte;
										Item_Wear[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
										Item_Wear[fromIndex].VatPham_byte = vatPhamByte6;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
										break;
									}
							}
							UpdateCharacterData(this);
							UpdateBroadcastCharacterData();
							UpdateEquipmentEffects();
							CalculateCharacterEquipmentData();
							UpdateMartialArtsAndStatus();
							UpdateMoneyAndWeight();
							CapNhat_HP_MP_SP();
							break;
						}
					case 169:
						if (AoChang_HanhLy[fromIndex].Lock_Move || BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0) == 0)
						{
							break;
						}
						switch (toType)
						{
							case 169:
								if (!AoChang_HanhLy[fromIndex].Lock_Move)
								{
									if (BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0) == 0)
									{
										AoChang_HanhLy[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
										AoChang_HanhLy[fromIndex].VatPham_byte = vatPhamByte;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
									}
									else
									{
										var vatPhamByte20 = AoChang_HanhLy[toIndex].VatPham_byte;
										AoChang_HanhLy[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
										AoChang_HanhLy[fromIndex].VatPham_byte = vatPhamByte20;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
									}
								}
								break;
							case 1:
								if (Player_Money < 10000000)
								{
									HeThongNhacNho("Đại hiệp thật nghèo, ngay cả 10,000,000 lượng cũng không có!", 10, "Thiên cơ các");
									break;
								}
								Player_Money -= 10000000L;
								if (BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0) != 0 && !AoChang_HanhLy[fromIndex].Lock_Move && BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
								{
									Item_In_Bag[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
									AoChang_HanhLy[fromIndex].VatPham_byte = vatPhamByte;
									ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
								}
								UpdateMoneyAndWeight();
								break;
							case 0:
								{
									if (BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0) == 0 || AoChang_HanhLy[fromIndex].Lock_Move)
									{
										break;
									}
									var itmeClass24 = World.ItemList[BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0)];
									if ((itmeClass24.FLD_SEX != 0 && itmeClass24.FLD_SEX != Player_Sex) || itmeClass24.FLD_RESIDE2 != 12)
									{
										break;
									}
									if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
									{
										if (Player_Money < 1000000)
										{
											HeThongNhacNho("Đại hiệp thật nghèo, ngay cả 1 triệu lượng cũng không có!", 10, "Thiên cơ các");
											break;
										}
										Player_Money -= 1000000L;
										if (itmeClass24.FLD_RESIDE2 != toIndex + 1 && ((itmeClass24.FLD_RESIDE2 != 2 && itmeClass24.FLD_RESIDE2 != 3 && itmeClass24.FLD_RESIDE2 != 8 && itmeClass24.FLD_RESIDE2 != 9 && itmeClass24.FLD_RESIDE2 != 10 && itmeClass24.FLD_RESIDE2 != 11) || itmeClass24.FLD_RESIDE2 != toIndex))
										{
											break;
										}
										Item_Wear[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
										AoChang_HanhLy[fromIndex].VatPham_byte = vatPhamByte;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
									}
									else
									{
										if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
										{
											break;
										}
										var itmeClass25 = World.ItemList[BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0)];
										if (itmeClass24.FLD_RESIDE2 != itmeClass25.FLD_RESIDE2)
										{
											break;
										}
										var vatPhamByte19 = Item_Wear[toIndex].VatPham_byte;
										Item_Wear[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
										AoChang_HanhLy[fromIndex].VatPham_byte = vatPhamByte19;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
									}
									UpdateCharacterData(this);
									UpdateEquipmentEffects();
									CalculateCharacterEquipmentData();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
									break;
								}
						}
						break;
					case 188:
						{
							var xVatPhamLoai2 = ThietBiTab3[fromIndex];
							if (BitConverter.ToInt32(xVatPhamLoai2.VatPham_ID, 0) == 0 || xVatPhamLoai2.Lock_Move)
							{
								break;
							}
							switch (toType)
							{
								case 1:
									{
										if (toIndex >= World.SoLuongTrangBi_ToiDa || (fromIndex == 14 && CharacterBeast != null))
										{
											return;
										}
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
										{
											if (BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0) == 0)
											{
												return;
											}
											Item_In_Bag[toIndex].VatPham_byte = ThietBiTab3[fromIndex].VatPham_byte;
											ThietBiTab3[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
											break;
										}
										if (BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0) == 0)
										{
											return;
										}
										var itmeClass12 = World.ItemList[BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0)];
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0 || Item_In_Bag[toIndex].Lock_Move)
										{
											return;
										}
										var itmeClass13 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
										if (itmeClass12.FLD_RESIDE2 != itmeClass13.FLD_RESIDE2 || itmeClass13.FLD_LEVEL > Player_Level || (itmeClass13.FLD_ZX != 0 && itmeClass13.FLD_ZX != Player_Zx))
										{
											return;
										}
										if (itmeClass13.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass13.FLD_RESIDE1 != Player_Job)
												{
													return;
												}
											}
											else if (itmeClass13.FLD_RESIDE1 != Player_Job && itmeClass13.FLD_RESIDE1 != 10)
											{
												return;
											}
										}
										if ((itmeClass13.FLD_JOB_LEVEL != 0 && itmeClass13.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass13.FLD_SEX != 0 && itmeClass13.FLD_SEX != Player_Sex) || (itmeClass13.FLD_XWJD >= 1 && itmeClass13.FLD_XWJD > VoHuanGiaiDoan) || itmeClass13.FLD_RESIDE2 < 1 || itmeClass13.FLD_RESIDE2 > 16)
										{
											return;
										}
										var vatPhamByte10 = Item_In_Bag[toIndex].VatPham_byte;
										Item_In_Bag[toIndex].VatPham_byte = ThietBiTab3[fromIndex].VatPham_byte;
										ThietBiTab3[fromIndex].VatPham_byte = vatPhamByte10;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
										break;
									}
								case 0:
									{
										if (fromIndex == 14 && CharacterBeast != null)
										{
											return;
										}
										var itmeClass10 = World.ItemList[BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0)];
										if (itmeClass10.FLD_LEVEL > Player_Level || (itmeClass10.FLD_ZX != 0 && itmeClass10.FLD_ZX != Player_Zx))
										{
											return;
										}
										if (itmeClass10.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass10.FLD_RESIDE1 != Player_Job)
												{
													return;
												}
											}
											else if (itmeClass10.FLD_RESIDE1 != Player_Job && itmeClass10.FLD_RESIDE1 != 10)
											{
												return;
											}
										}
										if ((itmeClass10.FLD_JOB_LEVEL != 0 && itmeClass10.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass10.FLD_SEX != 0 && itmeClass10.FLD_SEX != Player_Sex) || (itmeClass10.FLD_XWJD >= 1 && itmeClass10.FLD_XWJD > VoHuanGiaiDoan) || itmeClass10.FLD_RESIDE2 < 1 || itmeClass10.FLD_RESIDE2 > 16)
										{
											return;
										}
										if (BitConverter.ToInt32(ThietBiTab3[toIndex].VatPham_ID, 0) == 0)
										{
											if (itmeClass10.FLD_RESIDE2 != toIndex + 1 && ((itmeClass10.FLD_RESIDE2 != 2 && itmeClass10.FLD_RESIDE2 != 3 && itmeClass10.FLD_RESIDE2 != 8 && itmeClass10.FLD_RESIDE2 != 9 && itmeClass10.FLD_RESIDE2 != 10 && itmeClass10.FLD_RESIDE2 != 11) || itmeClass10.FLD_RESIDE2 != toIndex))
											{
												return;
											}
											ThietBiTab3[toIndex].VatPham_byte = ThietBiTab3[fromIndex].VatPham_byte;
											ThietBiTab3[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, ThietBiTab3[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Wear[toIndex].VatPhamSoLuong, 0));
											break;
										}
										if (BitConverter.ToInt32(ThietBiTab3[toIndex].VatPham_ID, 0) == 0)
										{
											return;
										}
										var itmeClass11 = World.ItemList[BitConverter.ToInt32(ThietBiTab3[toIndex].VatPham_ID, 0)];
										if (itmeClass10.FLD_RESIDE2 != itmeClass11.FLD_RESIDE2)
										{
											return;
										}
										var vatPhamByte9 = ThietBiTab3[toIndex].VatPham_byte;
										ThietBiTab3[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
										ThietBiTab3[fromIndex].VatPham_byte = vatPhamByte9;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, ThietBiTab3[toIndex].VatPham_byte, BitConverter.ToInt32(ThietBiTab3[toIndex].VatPhamSoLuong, 0));
										break;
									}
							}
							UpdateCharacterData(this);
							UpdateBroadcastCharacterData();
							UpdateEquipmentEffects();
							CalculateCharacterEquipmentData();
							UpdateMartialArtsAndStatus();
							UpdateMoneyAndWeight();
							CapNhat_HP_MP_SP();
							break;
						}
					case 123:
						{
							var xVatPhamLoai3 = Sub_Wear[fromIndex];
							if (BitConverter.ToInt32(xVatPhamLoai3.VatPham_ID, 0) == 0 || xVatPhamLoai3.Lock_Move)
							{
								break;
							}
							switch (toType)
							{
								case 1:
									{
										if (toIndex >= World.SoLuongTrangBi_ToiDa || (fromIndex == 14 && CharacterBeast != null))
										{
											return;
										}
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
										{
											if (BitConverter.ToInt32(Sub_Wear[fromIndex].VatPham_ID, 0) == 0)
											{
												return;
											}
											Item_In_Bag[toIndex].VatPham_byte = Sub_Wear[fromIndex].VatPham_byte;
											Sub_Wear[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
											break;
										}
										if (BitConverter.ToInt32(Sub_Wear[fromIndex].VatPham_ID, 0) == 0)
										{
											return;
										}
										var itmeClass16 = World.ItemList[BitConverter.ToInt32(Sub_Wear[fromIndex].VatPham_ID, 0)];
										if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0 || Item_In_Bag[toIndex].Lock_Move)
										{
											return;
										}
										var itmeClass17 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
										if (itmeClass16.FLD_RESIDE2 != itmeClass17.FLD_RESIDE2 || itmeClass17.FLD_LEVEL > Player_Level || (itmeClass17.FLD_ZX != 0 && itmeClass17.FLD_ZX != Player_Zx))
										{
											return;
										}
										if (itmeClass17.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass17.FLD_RESIDE1 != Player_Job)
												{
													return;
												}
											}
											else if (itmeClass17.FLD_RESIDE1 != Player_Job && itmeClass17.FLD_RESIDE1 != 10)
											{
												return;
											}
										}
										if ((itmeClass17.FLD_JOB_LEVEL != 0 && itmeClass17.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass17.FLD_SEX != 0 && itmeClass17.FLD_SEX != Player_Sex) || (itmeClass17.FLD_XWJD >= 1 && itmeClass17.FLD_XWJD > VoHuanGiaiDoan) || itmeClass17.FLD_RESIDE2 < 1 || itmeClass17.FLD_RESIDE2 > 16)
										{
											return;
										}
										var vatPhamByte12 = Item_In_Bag[toIndex].VatPham_byte;
										Item_In_Bag[toIndex].VatPham_byte = Sub_Wear[fromIndex].VatPham_byte;
										Sub_Wear[fromIndex].VatPham_byte = vatPhamByte12;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
										break;
									}
								case 0:
									{
										if (fromIndex == 14 && CharacterBeast != null)
										{
											return;
										}
										var itmeClass14 = World.ItemList[BitConverter.ToInt32(Sub_Wear[fromIndex].VatPham_ID, 0)];
										if (itmeClass14.FLD_LEVEL > Player_Level || (itmeClass14.FLD_ZX != 0 && itmeClass14.FLD_ZX != Player_Zx))
										{
											return;
										}
										if (itmeClass14.FLD_RESIDE1 != 0)
										{
											if (Player_Job != 1 && Player_Job != 8)
											{
												if (itmeClass14.FLD_RESIDE1 != Player_Job)
												{
													return;
												}
											}
											else if (itmeClass14.FLD_RESIDE1 != Player_Job && itmeClass14.FLD_RESIDE1 != 10)
											{
												return;
											}
										}
										if ((itmeClass14.FLD_JOB_LEVEL != 0 && itmeClass14.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass14.FLD_SEX != 0 && itmeClass14.FLD_SEX != Player_Sex) || (itmeClass14.FLD_XWJD >= 1 && itmeClass14.FLD_XWJD > VoHuanGiaiDoan) || itmeClass14.FLD_RESIDE2 < 1 || itmeClass14.FLD_RESIDE2 > 16)
										{
											return;
										}
										if (BitConverter.ToInt32(Sub_Wear[toIndex].VatPham_ID, 0) == 0)
										{
											if (itmeClass14.FLD_RESIDE2 != toIndex + 1 && ((itmeClass14.FLD_RESIDE2 != 2 && itmeClass14.FLD_RESIDE2 != 3 && itmeClass14.FLD_RESIDE2 != 8 && itmeClass14.FLD_RESIDE2 != 9 && itmeClass14.FLD_RESIDE2 != 10 && itmeClass14.FLD_RESIDE2 != 11) || itmeClass14.FLD_RESIDE2 != toIndex))
											{
												return;
											}
											Sub_Wear[toIndex].VatPham_byte = Sub_Wear[fromIndex].VatPham_byte;
											Sub_Wear[fromIndex].VatPham_byte = vatPhamByte;
											ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Wear[toIndex].VatPhamSoLuong, 0));
											break;
										}
										if (BitConverter.ToInt32(Sub_Wear[toIndex].VatPham_ID, 0) == 0)
										{
											return;
										}
										var itmeClass15 = World.ItemList[BitConverter.ToInt32(Sub_Wear[toIndex].VatPham_ID, 0)];
										if (itmeClass14.FLD_RESIDE2 != itmeClass15.FLD_RESIDE2)
										{
											return;
										}
										var vatPhamByte11 = Sub_Wear[toIndex].VatPham_byte;
										Sub_Wear[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
										Sub_Wear[fromIndex].VatPham_byte = vatPhamByte11;
										ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Wear[toIndex].VatPhamSoLuong, 0));
										break;
									}
							}
							UpdateCharacterData(this);
							UpdateBroadcastCharacterData();
							UpdateEquipmentEffects();
							CalculateCharacterEquipmentData();
							UpdateMartialArtsAndStatus();
							UpdateMoneyAndWeight();
							CapNhat_HP_MP_SP();
							break;
						}
					case 60:
						if (!CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_KhoaLai)
						{
							if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
							{
								Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
								CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = vatPhamByte;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							}
							else
							{
								var vatPhamByte2 = Item_In_Bag[toIndex].VatPham_byte;
								Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
								CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = vatPhamByte2;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							}
							UpdateTheWeightOfTheBeast();
						}
						break;
				}
				return;
			}
			int num8 = packetData[10];
			int num9 = packetData[12];
			int num10 = packetData[14];
			int num11 = packetData[16];
			var num12 = BitConverter.ToInt32(packetData, 18);
			try
			{
				switch (num10)
				{
					case 190:
						if (!_hopThanhVatPhamTable.ContainsKey(1) && new int[6] { 1000001011, 1000001382, 1000001383, 1000001384, 1000001385, 1000002006 }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							if (Item_In_Bag[num9].FLD_MAGIC1 != 0 || Item_In_Bag[num9].FLD_MAGIC2 != 0 || Item_In_Bag[num9].FLD_MAGIC3 != 0 || Item_In_Bag[num9].FLD_MAGIC4 != 0)
							{
								ItmeClass value7;
								if (Item_In_Bag[num9].Lock_Move)
								{
									Item_In_Bag[num9].Lock_Move = false;
									SynthesisHint(num9, 2, 0L, Item_In_Bag[num9]);
								}
								else if (World.ItemList.TryGetValue((int)Item_In_Bag[num9].GetVatPham_ID, out value7))
								{
									HcItimesClass hcItimesClass3 = new();
									hcItimesClass3.Position = num9;
									hcItimesClass3.VatPham = Item_In_Bag[num9].VatPham_byte;
									Item_In_Bag[num9].Lock_Move = true;
									_hopThanhVatPhamTable.Add(1, hcItimesClass3);
									SynthesisHint(411, 0, 10000000L, Item_In_Bag[num9]);
								}
								else
								{
									SynthesisHint(411, 5, 0L, Item_In_Bag[num9]);
								}
							}
							else
							{
								HeThongNhacNho("Thần thú chưa có thuộc tính, không thể hợp thành!", 20, "Thiên cơ các");
							}
							break;
						}
						if (_hopThanhVatPhamTable.ContainsKey(1) && !_hopThanhVatPhamTable.ContainsKey(2) && BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0) == 1000000779)
						{
							if (Item_In_Bag[num9].FLD_MAGIC1 == 0 && Item_In_Bag[num9].FLD_MAGIC2 == 0 && Item_In_Bag[num9].FLD_MAGIC3 == 0 && Item_In_Bag[num9].FLD_MAGIC4 == 0)
							{
								if (_hopThanhVatPhamTable.Count == 0)
								{
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								HcItimesClass hcItimesClass4 = null;
								if (_hopThanhVatPhamTable.ContainsKey(1))
								{
									hcItimesClass4 = _hopThanhVatPhamTable[1];
								}
								hcItimesClass4.DatDuocThuocTinh();
								hcItimesClass4.CuongHoaThuocTinhGiaiDoan();
								if (!_hopThanhVatPhamTable.ContainsKey(2))
								{
									if (Item_In_Bag[num9].Lock_Move)
									{
										Item_In_Bag[num9].Lock_Move = false;
										SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
										break;
									}
									HcItimesClass hcItimesClass5 = new();
									hcItimesClass5.Position = num9;
									hcItimesClass5.VatPham = Item_In_Bag[num9].VatPham_byte;
									Item_In_Bag[num9].Lock_Move = true;
									_hopThanhVatPhamTable.Add(2, hcItimesClass5);
									SynthesisHint(412, 0, 0L, Item_In_Bag[num9]);
								}
							}
							else
							{
								HeThongNhacNho("Phôi Sói phải là phôi trắng, không có thuộc tính!!", 20, "Thiên cơ các");
							}
							break;
						}
						if (_hopThanhVatPhamTable.ContainsKey(1) && _hopThanhVatPhamTable.ContainsKey(2) && !_hopThanhVatPhamTable.ContainsKey(3) && new int[6] { *********, *********, *********, *********, *********, ********* }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)) && _hopThanhVatPhamTable.Count >= 2 && !_hopThanhVatPhamTable.ContainsKey(3))
						{
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(413, 2, 0L, Item_In_Bag[num9]);
							}
							else
							{
								HcItimesClass hcItimesClass6 = new();
								hcItimesClass6.Position = num9;
								hcItimesClass6.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(3, hcItimesClass6);
								SynthesisHint(413, 0, 0L, Item_In_Bag[num9]);
							}
						}
						if (!_hopThanhVatPhamTable.ContainsKey(1) && new int[14]
						{
						1900001, 1900002, 1900003, 1900004, 1900005, 1900006, 1900007, 1900008, 1900009, 1900010,
						1900011, 1900012, 1900013, 1900014
						}.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							ItmeClass value8;
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(num9, 2, 0L, Item_In_Bag[num9]);
							}
							else if (World.ItemList.TryGetValue((int)Item_In_Bag[num9].GetVatPham_ID, out value8))
							{
								HcItimesClass hcItimesClass7 = new();
								hcItimesClass7.Position = num9;
								hcItimesClass7.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(1, hcItimesClass7);
								SynthesisHint(411, 0, 10000000L, Item_In_Bag[num9]);
							}
							else
							{
								SynthesisHint(411, 5, 0L, Item_In_Bag[num9]);
							}
						}
						else if (_hopThanhVatPhamTable.ContainsKey(1) && !_hopThanhVatPhamTable.ContainsKey(2) && new int[14]
						{
						1900001, 1900002, 1900003, 1900004, 1900005, 1900006, 1900007, 1900008, 1900009, 1900010,
						1900011, 1900012, 1900013, 1900014
						}.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							if (_hopThanhVatPhamTable.Count == 0)
							{
								SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
								break;
							}
							HcItimesClass hcItimesClass8 = null;
							if (_hopThanhVatPhamTable.ContainsKey(1))
							{
								hcItimesClass8 = _hopThanhVatPhamTable[1];
							}
							hcItimesClass8.DatDuocThuocTinh();
							hcItimesClass8.CuongHoaThuocTinhGiaiDoan();
							if (!_hopThanhVatPhamTable.ContainsKey(2))
							{
								if (Item_In_Bag[num9].GetVatPham_ID != BitConverter.ToInt32(hcItimesClass8.VatPham_id, 0))
								{
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								if (Item_In_Bag[num9].Lock_Move)
								{
									Item_In_Bag[num9].Lock_Move = false;
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								HcItimesClass hcItimesClass9 = new();
								hcItimesClass9.Position = num9;
								hcItimesClass9.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(2, hcItimesClass9);
								SynthesisHint(412, 0, 0L, Item_In_Bag[num9]);
							}
						}
						else if (_hopThanhVatPhamTable.ContainsKey(1) && _hopThanhVatPhamTable.ContainsKey(2) && !_hopThanhVatPhamTable.ContainsKey(3) && new int[6] { *********, *********, *********, *********, *********, ********* }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)) && _hopThanhVatPhamTable.Count >= 2 && !_hopThanhVatPhamTable.ContainsKey(3))
						{
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(413, 2, 0L, Item_In_Bag[num9]);
							}
							else
							{
								HcItimesClass hcItimesClass10 = new();
								hcItimesClass10.Position = num9;
								hcItimesClass10.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(3, hcItimesClass10);
								SynthesisHint(413, 0, 0L, Item_In_Bag[num9]);
							}
						}
						if (!_hopThanhVatPhamTable.ContainsKey(1) && new int[40]
						{
						1000002051, 1000002052, 1000002053, 1000002054, 1000002055, 1000002056, 1000002057, 1000002058, 1000002059, 1000002060,
						1000002061, 1000002062, 1000002063, 1000002064, 1000002065, 1000002066, 1000002067, 1000002068, 1000002069, 1000002070,
						1000002071, 1000002072, 1000002073, 1000002074, 1000002075, 1000002076, 1000002077, 1000002078, 1000002079, 1000002080,
						1000002081, 1000002082, 1000002083, 1000002084, 1000002085, 1000002086, 1000002087, 1000002088, 1000002089, 1000002090
						}.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							ItmeClass value9;
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(num9, 2, 0L, Item_In_Bag[num9]);
							}
							else if (World.ItemList.TryGetValue((int)Item_In_Bag[num9].GetVatPham_ID, out value9))
							{
								HcItimesClass hcItimesClass11 = new();
								hcItimesClass11.Position = num9;
								hcItimesClass11.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(1, hcItimesClass11);
								SynthesisHint(411, 0, 10000000L, Item_In_Bag[num9]);
							}
							else
							{
								SynthesisHint(411, 5, 0L, Item_In_Bag[num9]);
							}
						}
						else if (_hopThanhVatPhamTable.ContainsKey(1) && !_hopThanhVatPhamTable.ContainsKey(2) && new int[40]
						{
						1000002051, 1000002052, 1000002053, 1000002054, 1000002055, 1000002056, 1000002057, 1000002058, 1000002059, 1000002060,
						1000002061, 1000002062, 1000002063, 1000002064, 1000002065, 1000002066, 1000002067, 1000002068, 1000002069, 1000002070,
						1000002071, 1000002072, 1000002073, 1000002074, 1000002075, 1000002076, 1000002077, 1000002078, 1000002079, 1000002080,
						1000002081, 1000002082, 1000002083, 1000002084, 1000002085, 1000002086, 1000002087, 1000002088, 1000002089, 1000002090
						}.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							if (_hopThanhVatPhamTable.Count == 0)
							{
								SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
								break;
							}
							HcItimesClass hcItimesClass12 = null;
							if (_hopThanhVatPhamTable.ContainsKey(1))
							{
								hcItimesClass12 = _hopThanhVatPhamTable[1];
							}
							hcItimesClass12.DatDuocThuocTinh();
							hcItimesClass12.CuongHoaThuocTinhGiaiDoan();
							if (!_hopThanhVatPhamTable.ContainsKey(2))
							{
								if (Item_In_Bag[num9].GetVatPham_ID != BitConverter.ToInt32(hcItimesClass12.VatPham_id, 0))
								{
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								if (Item_In_Bag[num9].Lock_Move)
								{
									Item_In_Bag[num9].Lock_Move = false;
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								HcItimesClass hcItimesClass13 = new();
								hcItimesClass13.Position = num9;
								hcItimesClass13.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(2, hcItimesClass13);
								SynthesisHint(412, 0, 0L, Item_In_Bag[num9]);
							}
						}
						else if (_hopThanhVatPhamTable.ContainsKey(1) && _hopThanhVatPhamTable.ContainsKey(2) && !_hopThanhVatPhamTable.ContainsKey(3) && new int[6] { *********, *********, *********, *********, *********, ********* }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)) && _hopThanhVatPhamTable.Count >= 2 && !_hopThanhVatPhamTable.ContainsKey(3))
						{
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(413, 2, 0L, Item_In_Bag[num9]);
							}
							else
							{
								HcItimesClass hcItimesClass14 = new();
								hcItimesClass14.Position = num9;
								hcItimesClass14.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(3, hcItimesClass14);
								SynthesisHint(413, 0, 0L, Item_In_Bag[num9]);
							}
						}
						if (!_hopThanhVatPhamTable.ContainsKey(1) && new int[3] { 1000001301, 1000001302, 1000001185 }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							ItmeClass value10;
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(num9, 2, 0L, Item_In_Bag[num9]);
							}
							else if (World.ItemList.TryGetValue((int)Item_In_Bag[num9].GetVatPham_ID, out value10))
							{
								HcItimesClass hcItimesClass15 = new();
								hcItimesClass15.Position = num9;
								hcItimesClass15.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(1, hcItimesClass15);
								SynthesisHint(411, 0, 10000000L, Item_In_Bag[num9]);
							}
							else
							{
								SynthesisHint(411, 5, 0L, Item_In_Bag[num9]);
							}
						}
						else if (_hopThanhVatPhamTable.ContainsKey(1) && !_hopThanhVatPhamTable.ContainsKey(2) && new int[3] { 1000001301, 1000001302, 1000001185 }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)))
						{
							if (_hopThanhVatPhamTable.Count == 0)
							{
								SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
								break;
							}
							HcItimesClass hcItimesClass16 = null;
							if (_hopThanhVatPhamTable.ContainsKey(1))
							{
								hcItimesClass16 = _hopThanhVatPhamTable[1];
							}
							hcItimesClass16.DatDuocThuocTinh();
							hcItimesClass16.CuongHoaThuocTinhGiaiDoan();
							if (!_hopThanhVatPhamTable.ContainsKey(2))
							{
								if (Item_In_Bag[num9].GetVatPham_ID != BitConverter.ToInt32(hcItimesClass16.VatPham_id, 0))
								{
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								if (Item_In_Bag[num9].Lock_Move)
								{
									Item_In_Bag[num9].Lock_Move = false;
									SynthesisHint(412, 2, 0L, Item_In_Bag[num9]);
									break;
								}
								HcItimesClass hcItimesClass17 = new();
								hcItimesClass17.Position = num9;
								hcItimesClass17.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(2, hcItimesClass17);
								SynthesisHint(412, 0, 0L, Item_In_Bag[num9]);
							}
						}
						else if (_hopThanhVatPhamTable.ContainsKey(1) && _hopThanhVatPhamTable.ContainsKey(2) && !_hopThanhVatPhamTable.ContainsKey(3) && new int[6] { *********, *********, *********, *********, *********, ********* }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)) && _hopThanhVatPhamTable.Count >= 2 && !_hopThanhVatPhamTable.ContainsKey(3))
						{
							if (Item_In_Bag[num9].Lock_Move)
							{
								Item_In_Bag[num9].Lock_Move = false;
								SynthesisHint(413, 2, 0L, Item_In_Bag[num9]);
								break;
							}
							HcItimesClass hcItimesClass18 = new();
							hcItimesClass18.Position = num9;
							hcItimesClass18.VatPham = Item_In_Bag[num9].VatPham_byte;
							Item_In_Bag[num9].Lock_Move = true;
							_hopThanhVatPhamTable.Add(3, hcItimesClass18);
							SynthesisHint(413, 0, 0L, Item_In_Bag[num9]);
						}
						break;
					case 21:
						try
						{
							if (_hopThanhVatPhamTable.ContainsKey(1) || !new int[4] { 1000001390, 1000001395, 1000001400, 1000001405 }.Contains(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0)) || CurrentOperationType != 6)
							{
								break;
							}
							if (Item_In_Bag[num9].Lock_Move)
							{
								SynthesisHint(21, 3, 0L, Item_In_Bag[num9]);
							}
							else if (!Item_In_Bag[num9].VatPham_KhoaLai && Item_In_Bag[num9].FLD_CuongHoaSoLuong <= 0)
							{
								ItmeClass value6;
								if (Item_In_Bag[num9].FLD_MAGIC1 != 0 && Item_In_Bag[num9].FLD_MAGIC2 != 0 && Item_In_Bag[num9].FLD_MAGIC3 != 0 && Item_In_Bag[num9].FLD_MAGIC4 != 0)
								{
									SynthesisHint(21, 3, 0L, Item_In_Bag[num9]);
								}
								else if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0), out value6))
								{
									if (value6.FLD_RESIDE2 != 32)
									{
										SynthesisHint(21, 5, 0L, Item_In_Bag[num9]);
									}
									else if (!_hopThanhVatPhamTable.ContainsKey(1))
									{
										var num13 = (_phiHopThanh = CalculateSyntheticEnhancementCost(value6, num9, 21));
										HcItimesClass hcItimesClass2 = new();
										hcItimesClass2.Position = num9;
										hcItimesClass2.VatPham = Item_In_Bag[num9].VatPham_byte;
										Item_In_Bag[num9].Lock_Move = true;
										_hopThanhVatPhamTable.Add(1, hcItimesClass2);
										SynthesisHint(21, 1, num13, Item_In_Bag[num9]);
									}
								}
								else
								{
									SynthesisHint(21, 5, 0L, Item_In_Bag[num9]);
								}
							}
							else
							{
								SynthesisHint(21, 3, 0L, Item_In_Bag[num9]);
							}
							break;
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, "Thông báo ThuocTinh Tổng hợp 22 error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
							break;
						}
					case 23:
						try
						{
							if (!_hopThanhVatPhamTable.ContainsKey(1) || _hopThanhVatPhamTable.ContainsKey(2) || BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0) != ********* || CurrentOperationType != 6)
							{
								break;
							}
							if (_hopThanhVatPhamTable != null && _hopThanhVatPhamTable.Count != 0 && _hopThanhVatPhamTable.ContainsKey(1))
							{
								if (!_hopThanhVatPhamTable.ContainsKey(3))
								{
									_hopThanhVatPhamTable.Add(3, new HcItimesClass
									{
										Position = num9,
										VatPham = Item_In_Bag[num9].VatPham_byte
									});
									Item_In_Bag[num9].Lock_Move = true;
									SynthesisHint(23, 1, 0L, Item_In_Bag[num9]);
								}
							}
							else
							{
								SynthesisHint(23, 5, 0L, Item_In_Bag[num9]);
							}
							break;
						}
						catch (Exception ex2)
						{
							LogHelper.WriteLine(LogLevel.Error, "Thông báo ThuocTinh Tổng hợp 33 error![" + AccountID + "]-[" + CharacterName + "]" + ex2.Message);
							break;
						}
					case 11:
					case 24:
					case 241:
						try
						{
							if (_hopThanhVatPhamTable.ContainsKey(1) || (CurrentOperationType != 8 && CurrentOperationType != 209 && CurrentOperationType != 323 && CurrentOperationType != 324))
							{
								break;
							}
							ItmeClass value13;
							if (Item_In_Bag[num9].VatPham_KhoaLai)
							{
								SynthesisHint(11, 2, 0L, Item_In_Bag[num9]);
							}
							else if (World.ItemList.TryGetValue((int)Item_In_Bag[num9].GetVatPham_ID, out value13))
							{
								if (value13.FLD_RESIDE2 != 12 && value13.FLD_RESIDE2 != 0 && (value13.FLD_RESIDE2 < 16 || value13.FLD_RESIDE2 > 19) && Item_In_Bag[num9].FLD_CuongHoaSoLuong < World.CuongHoa_GioiHan_Item_NPC_DKT)
								{
									var num16 = 0L;
									if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 15)
									{
										num16 = 1000000000L;
									}
									else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 16)
									{
										num16 = 1500000000L;
									}
									else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 17)
									{
										num16 = 2000000000L;
									}
									else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 18)
									{
										num16 = 2500000000L;
									}
									long num17 = (_phiHopThanh = CalculateSyntheticEnhancementCost(value13, num9, 11));
									HcItimesClass hcItimesClass21 = new();
									hcItimesClass21.Position = num9;
									hcItimesClass21.VatPham = Item_In_Bag[num9].VatPham_byte;
									Item_In_Bag[num9].Lock_Move = true;
									_hopThanhVatPhamTable.Add(1, hcItimesClass21);
									SynthesisHint(11, 1, num17 + num16, Item_In_Bag[num9]);
								}
							}
							else
							{
								HeThongNhacNho("Đã đạt giới hạn cường hóa [" + World.CuongHoa_GioiHan_Item_NPC_DKT + "]!!", 10, "Thiên cơ các");
							}
							break;
						}
						catch (Exception ex5)
						{
							LogHelper.WriteLine(LogLevel.Error, "Thông báo CuongHoa lỗi 12345  [" + AccountID + "]-[" + CharacterName + "]" + ex5.Message);
							break;
						}
					case 55:
						try
						{
							if (CurrentOperationType != 19 || Item_In_Bag[num9].VatPham_KhoaLai || Item_In_Bag[num9].FLDThuocTinhSoLuong < 10)
							{
								break;
							}
							if (_hopThanhVatPhamTable.ContainsKey(1))
							{
								SynthesisHint(61, 5, 0L, Item_In_Bag[num9]);
							}
							else if (!Item_In_Bag[num9].VatPham_KhoaLai && Item_In_Bag[num9].FLDThuocTinhSoLuong < 10)
							{
								if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[num9].VatPham_ID, 0), out var value12) && value12.FLD_RESIDE2 != 1 && value12.FLD_RESIDE2 != 4)
								{
									SynthesisHint(61, 5, 0L, Item_In_Bag[num9]);
									break;
								}
								HcItimesClass hcItimesClass20 = new();
								hcItimesClass20.Position = num9;
								hcItimesClass20.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(1, hcItimesClass20);
								SynthesisHint(61, 1, 0L, Item_In_Bag[num9]);
							}
							else
							{
								SynthesisHint(61, 3, 0L, Item_In_Bag[num9]);
							}
							break;
						}
						catch (Exception ex4)
						{
							LogHelper.WriteLine(LogLevel.Error, "Thông báo ThuocTinhGiaiDoan sự tổng hợp 11 error![" + AccountID + "]-[" + CharacterName + "]" + ex4.Message);
							break;
						}
					case 111:
						try
						{
							break;
						}
						catch (Exception ex6)
						{
							LogHelper.WriteLine(LogLevel.Error, "sự tổng hợp Thông báo TrungCapPhuHon 11 error! [" + AccountID + "]-[" + CharacterName + "]" + ex6.Message);
							break;
						}
					case 49:
						try
						{
							if ((WorkingItemIndex > -1 && ((Item_In_Bag[num9].FLD_CuongHoaSoLuong < 10 && Item_In_Bag[WorkingItemIndex].GetVatPham_ID == **********) || Item_In_Bag[WorkingItemIndex].GetVatPham_ID == **********)) || _hopThanhVatPhamTable.ContainsKey(1) || (CurrentOperationType != 8 && CurrentOperationType != 209 && CurrentOperationType != 323 && CurrentOperationType != 324))
							{
								break;
							}
							ItmeClass value11;
							if (Item_In_Bag[num9].VatPham_KhoaLai)
							{
								SynthesisHint(41, 2, 0L, Item_In_Bag[num9]);
							}
							else if (World.ItemList.TryGetValue((int)Item_In_Bag[num9].GetVatPham_ID, out value11))
							{
								if (value11.FLD_RESIDE2 == 12)
								{
									SynthesisHint(41, 5, 0L, Item_In_Bag[num9]);
								}
								else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong < World.CuongHoa_GioiHan_Item_NPC_DKT)
								{
									var num14 = 0L;
									if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 15)
									{
										num14 = 1000000000L;
									}
									else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 16)
									{
										num14 = 1500000000L;
									}
									else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 17)
									{
										num14 = 2000000000L;
									}
									else if (Item_In_Bag[num9].FLD_CuongHoaSoLuong == 18)
									{
										num14 = 2500000000L;
									}
									long num15 = (_phiHopThanh = CalculateSyntheticEnhancementCost(value11, num9, 41));
									HcItimesClass hcItimesClass19 = new();
									hcItimesClass19.Position = num9;
									hcItimesClass19.VatPham = Item_In_Bag[num9].VatPham_byte;
									Item_In_Bag[num9].Lock_Move = true;
									_hopThanhVatPhamTable.Add(1, hcItimesClass19);
									SynthesisHint(41, 1, num15 + num14, Item_In_Bag[num9]);
								}
							}
							else
							{
								HeThongNhacNho("Đã đạt giới hạn cường hóa [" + World.CuongHoa_GioiHan_Item_NPC_DKT + "]!!", 10, "Thiên cơ các");
							}
							break;
						}
						catch (Exception ex3)
						{
							LogHelper.WriteLine(LogLevel.Error, "Thông báo CuongHoa lỗi 12345  [" + AccountID + "]-[" + CharacterName + "]" + ex3.Message);
							break;
						}
					case 50:
						if (CurrentOperationType == 101 || CurrentOperationType == 324)
						{
							if (_hopThanhVatPhamTable.Count == 0)
							{
								SynthesisHint(43, 5, 0L, Item_In_Bag[num9]);
							}
							else if ((!_hopThanhVatPhamTable.ContainsKey(5) && Item_In_Bag[num9].GetVatPham_ID >= ********* && Item_In_Bag[num9].GetVatPham_ID <= *********) || Item_In_Bag[num9].GetVatPham_ID == ********** || Item_In_Bag[num9].GetVatPham_ID == ********** || Item_In_Bag[num9].GetVatPham_ID == ********** || Item_In_Bag[num9].GetVatPham_ID == **********)
							{
								HcItimesClass hcItimesClass = new();
								hcItimesClass.Position = num9;
								hcItimesClass.VatPham = Item_In_Bag[num9].VatPham_byte;
								Item_In_Bag[num9].Lock_Move = true;
								_hopThanhVatPhamTable.Add(5, hcItimesClass);
								SynthesisHint(43, 1, 0L, Item_In_Bag[num9]);
							}
						}
						break;
				}
			}
			catch (Exception ex7)
			{
				LogHelper.WriteLine(LogLevel.Error, "Hợp thành [Sói] xảy ra lỗi - Lỗi 1![" + AccountID + "]-[" + CharacterName + "]" + ex7.Message);
			}
		}
		catch (Exception ex8)
		{
			LogHelper.WriteLine(LogLevel.Error, "Change_Equipment error 22 [" + AccountID + "][" + CharacterName + "]  " + ex8.Message);
		}
	}

    private (bool flowControl, int value) ChangeFromBagToWear(int fromType, int fromIndex, int toType, int toIndex, int quantity, byte[] vatPhamByte)
    {
        if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0 || quantity != Item_In_Bag[fromIndex].GetVatPhamSoLuong || Item_In_Bag[fromIndex].Lock_Move)
        {
            return (flowControl: false, value: default);
        }
        var itmeClass22 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
        if (itmeClass22.FLD_LEVEL > Player_Level || (Player_Job == 11 && (itmeClass22.FLD_RESIDE2 == 7 || itmeClass22.FLD_RESIDE2 == 8 || itmeClass22.FLD_RESIDE2 == 10) && Player_Job != itmeClass22.FLD_RESIDE1) || (itmeClass22.FLD_ZX != 0 && itmeClass22.FLD_ZX != Player_Zx))
        {
            return (flowControl: false, value: default);
        }
        if (itmeClass22.FLD_NJ > 0 && Item_In_Bag[fromIndex].FLD_FJ_NJ <= 0)
        {
            HeThongNhacNho("Độ bền bảo vật bằng 0, không thể trang bị!", 10, "Thiên cơ các");
            return (flowControl: false, value: default);
        }
        if (itmeClass22.FLD_RESIDE1 != 0)
        {
            if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 1000000148)
            {
                if (Player_Job != 11 && Player_Job != 4)
                {
                    return (flowControl: false, value: default);
                }
            }
            else if (itmeClass22.FLD_RESIDE1 != Player_Job)
            {
                return (flowControl: false, value: default);
            }
        }
        if (Player_Job == 11 && itmeClass22.FLD_RESIDE1 != 11 && (itmeClass22.FLD_RESIDE2 == 2 || itmeClass22.FLD_RESIDE2 == 5 || itmeClass22.FLD_RESIDE2 == 6 || itmeClass22.FLD_RESIDE2 == 7 || itmeClass22.FLD_RESIDE2 == 8 || itmeClass22.FLD_RESIDE2 == 10))
        {
            HeThongNhacNho("Bảo vật không dành cho Diệu Yến!", 10, "Thiên cơ các");
        }
        else
        {
            if ((itmeClass22.FLD_JOB_LEVEL != 0 && itmeClass22.FLD_JOB_LEVEL > Player_Job_level) || (itmeClass22.FLD_SEX != 0 && itmeClass22.FLD_SEX != Player_Sex) || itmeClass22.FLD_RESIDE2 < 1 || itmeClass22.FLD_RESIDE2 > 16 || (itmeClass22.FLD_XWJD >= 1 && itmeClass22.FLD_XWJD > VoHuanGiaiDoan) || (toIndex == 14 && CharacterBeast != null))
            {
                if (itmeClass22.FLD_RESIDE2 != 37)
                {
                    return (flowControl: false, value: default);
                }
            }
			 if (itmeClass22.FLD_RESIDE2 == 37)
                    toIndex = 17;
            if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
            {
                if (itmeClass22.FLD_RESIDE2 != 37 && itmeClass22.FLD_RESIDE2 != toIndex + 1 && ((itmeClass22.FLD_RESIDE2 != 2 && itmeClass22.FLD_RESIDE2 != 3 && itmeClass22.FLD_RESIDE2 != 8 && itmeClass22.FLD_RESIDE2 != 9 && itmeClass22.FLD_RESIDE2 != 10 && itmeClass22.FLD_RESIDE2 != 11) || itmeClass22.FLD_RESIDE2 != toIndex))
                {
                    return (flowControl: false, value: default);
                }
                Item_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
                Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte;
                ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
            }
            else
            {
                if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
                {
                    return (flowControl: false, value: default);
                }
                var itmeClass23 = World.ItemList[BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0)];
                if (itmeClass22.FLD_RESIDE2 != itmeClass23.FLD_RESIDE2)
                {
                    return (flowControl: false, value: default);
                }
                var vatPhamByte18 = Item_Wear[toIndex].VatPham_byte;
                Item_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
                Item_In_Bag[fromIndex].VatPham_byte = vatPhamByte18;
                ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
            }
            UpdateCharacterData(this);
            UpdateBroadcastCharacterData();
            UpdateEquipmentEffects();
            CalculateCharacterEquipmentData();
            UpdateMartialArtsAndStatus();
            UpdateMoneyAndWeight();
            CapNhat_HP_MP_SP();
        }

        return (flowControl: true, value: default);
    }

}