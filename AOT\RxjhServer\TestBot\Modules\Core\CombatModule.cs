using System;
using System.Collections.Generic;
using System.Linq;
using RxjhServer.TestBot.Core;
using RxjhServer.HelperTools;

namespace RxjhServer.TestBot.Modules.Core
{
    /// <summary>
    /// Module quản lý combat và tấn công
    /// </summary>
    public class CombatModule : BaseBotModule
    {
        public override string ModuleName => "CombatModule";
        public override int Priority => 5;

        protected override int UpdateInterval => 2000; // Attack every 1.5 seconds

        private DateTime _lastAttackTime = DateTime.MinValue;
        private int _currentTargetId = 0;

        protected override bool OnCanExecute()
        {
            return Config.CombatEnabled && base.OnCanExecute();
        }

        protected override void OnUpdate()
        {
            // Tìm và tấn công target
            HandleCombat();
        }

        /// <summary>
        /// Xử lý combat logic
        /// </summary>
        private void HandleCombat()
        {
            try
            {
                // Kiểm tra target hiện tại
                var currentTarget = GetCurrentTarget();

                if (currentTarget == null || currentTarget.NPCDeath || currentTarget.Rxjh_HP <= 0)
                {
                    // Tìm target mới
                    _currentTargetId = FindNewTarget();
                    if (_currentTargetId <= 10000)
                    {
                        // LogDebug("No valid target found");
                        // return;
                        // Di chuyển tới target
                        var closestTarget2 = Player.NearbyNpcs.Values
                        .OrderBy(npc => CalculateDistance(Player.PosX, Player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                        .FirstOrDefault();
                        MoveWithPacket(closestTarget2.Rxjh_X, closestTarget2.Rxjh_Y, CalculateDistance(Player.PosX, Player.PosY, closestTarget2.Rxjh_X, closestTarget2.Rxjh_Y));
                        // Player.Mobile(closestTarget2.Rxjh_X, closestTarget2.Rxjh_Y, closestTarget2.Rxjh_Z, closestTarget2.Rxjh_Map, 2);
                        LogDebug($"No valid target found, moving to closest NPC {closestTarget2.NPC_SessionID}");
                        return;
                    }
                }

                // Kiểm tra khoảng cách đến target
                currentTarget = GetCurrentTarget();
                if (currentTarget != null)
                {
                    var distance = CalculateDistance(Player.PosX, Player.PosY, currentTarget.Rxjh_X, currentTarget.Rxjh_Y);

                    if (distance > 30) // Too far to attack
                    {
                        LogDebug($"Target too far ({distance:F1}), finding new target");
                        _currentTargetId = FindNewTarget();
                        if (_currentTargetId <= 10000)
                            return;
                    }

                    // Thực hiện tấn công
                    if (IsTimeToAct(_lastAttackTime, UpdateInterval))
                    {
                        PerformAttack();
                        _lastAttackTime = DateTime.Now;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in combat: {ex.Message}");
            }
        }
        private void MoveWithPacket(float targetX, float targetY, float distance)
        {
            try
            {
                var packetHex = "AA552E002E010700280002000000F91DC7426177ACC3978E3C44F91DC74200007041978E3C4401050000000000005C28000055AA";
                var packet = Converter.HexStringToByte(packetHex);

                // Set session ID
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.SessionID), 0, packet, 4, 2);

                // Set target position
                System.Buffer.BlockCopy(BitConverter.GetBytes(targetX), 0, packet, 14, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, packet, 18, 4); // Z offset
                System.Buffer.BlockCopy(BitConverter.GetBytes(targetY), 0, packet, 22, 4);

                // Set current position
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosX), 0, packet, 26, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosZ), 0, packet, 30, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosY), 0, packet, 34, 4);

                // Set movement speed
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.WalkingStatusId), 0, packet, 39, 1);
                System.Buffer.BlockCopy(BitConverter.GetBytes(distance), 0, packet, 42, 4);

                Player.CharacterMove(packet, packet.Length);

                LogDebug($"Sent movement packet to ({targetX}, {targetY})");
            }
            catch (Exception ex)
            {
                LogError($"Error sending movement packet: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy target hiện tại
        /// </summary>
        /// <returns>NPC target hoặc null</returns>
        private NpcClass GetCurrentTarget()
        {
            if (_currentTargetId <= 10000)
                return null;

            return MapClass.GetNpc(Player.MapID, _currentTargetId);
        }

        /// <summary>
        /// Tìm target mới (tương tự int_AutomaticAttackPhamViNpc)
        /// </summary>
        /// <returns>NPC Session ID</returns>
        private int FindNewTarget()
        {
            try
            {
                if (Player.NearbyNpcs == null)
                    return 0;

                var validTargets = new List<NpcClass>();

                foreach (var npc in Player.NearbyNpcs.Values)
                {
                    if (IsValidTarget(npc))
                    {
                        validTargets.Add(npc);
                    }
                }
                // LogInfo($"{Player.CharacterName} NpcList {Player.NpcList.Count} validTargets {validTargets.Count}");
                if (validTargets.Count == 0)
                {
                    // Find Nearby Monster in Active Range and run to attack range
                    //var closestTarget2 = Player.NpcList.Values
                    //.OrderBy(npc => CalculateDistance(Player.PosX, Player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                    //.FirstOrDefault();
                    //Player.Mobile(closestTarget2.Rxjh_X, closestTarget2.Rxjh_Y, closestTarget2.Rxjh_Z, closestTarget2.Rxjh_Map, 2);

                    return 0;
                }

                // Tìm target gần nhất
                var closestTarget = validTargets
                    .OrderBy(npc => CalculateDistance(Player.PosX, Player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                    .FirstOrDefault();

                if (closestTarget != null)
                {
                    LogDebug($"Found new target: {closestTarget.NPC_SessionID} at distance {CalculateDistance(Player.PosX, Player.PosY, closestTarget.Rxjh_X, closestTarget.Rxjh_Y):F1}");
                    return closestTarget.NPC_SessionID;
                }

                return 0;
            }
            catch (Exception ex)
            {
                LogError($"Error finding new target: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Kiểm tra xem NPC có phải target hợp lệ không
        /// </summary>
        /// <param name="npc">NPC để kiểm tra</param>
        /// <returns>True nếu hợp lệ</returns>
        private bool IsValidTarget(NpcClass npc)
        {
            try
            {
                // Kiểm tra cơ bản
                if (npc.NPCDeath || npc.IsNpc == 1 || npc.Rxjh_HP <= 1 || npc.FLD_PID < 10000)
                {
                    return false;
                }

                // Kiểm tra map
                if (npc.Rxjh_Map != Player.MapID)
                {
                    return false;
                }

                // Kiểm tra khoảng cách
                var distance = CalculateDistance(Player.PosX, Player.PosY, npc.Rxjh_X, npc.Rxjh_Y);
                var maxRange = Config.CombatRange;

                // Special case cho map 7101
                if (Player.MapID == 7101)
                    maxRange = 1000;

                if (distance > maxRange)
                    return false;

                // Kiểm tra khoảng cách từ home position
                var distanceFromHome = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, Config.HomeX, Config.HomeY);
                if (distanceFromHome > maxRange)
                {
                    LogDebug($"Npc Far {npc.NPC_SessionID} {distanceFromHome}");
                    return false;
                }


                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Thực hiện tấn công (tương tự OfflineTrainLevel)
        /// </summary>
        private void PerformAttack()
        {
            try
            {
                var target = GetCurrentTarget();
                if (target == null)
                    return;

                // var packetHex = "AA551E002E01090018005828B3C300000000AE705F4300007041B82D35441B2700000100000055AA";
                // var packet = Converter.HexStringToByte(packetHex);

                // Set session ID
                // System.Buffer.BlockCopy(BitConverter.GetBytes(Player.SessionID), 0, packet, 4, 2);

                // // Set target ID
                // System.Buffer.BlockCopy(BitConverter.GetBytes(_currentTargetId), 0, packet, 10, 2);

                // // Set skill ID
                var skillId = GetAttackSkillId();
                // System.Buffer.BlockCopy(BitConverter.GetBytes(skillId), 0, packet, 14, 4);

                // // Set position
                // System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosX), 0, packet, 18, 4);
                // System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosZ), 0, packet, 22, 4);
                // System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosY), 0, packet, 26, 4);

                // Player.Attack(packet, packet.Length);
                Player.HandleAttack((short)Player.SessionID, skillId, _currentTargetId, Player.PosX,Player.PosY);

                LogDebug($"Attacked target {_currentTargetId} with skill {skillId}");
            }
            catch (Exception ex)
            {
                LogError($"Error performing attack: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy skill ID để tấn công
        /// </summary>
        /// <returns>Skill ID</returns>
        private int GetAttackSkillId()
        {
            try
            {
                // Ưu tiên skill từ config
                if (Config.PreferredSkillId > 0)
                    return Config.PreferredSkillId;

                // Sử dụng skill từ player
                if (Player.OfflineTreoMaySkill_ID > 0)
                    return Player.OfflineTreoMaySkill_ID;

                // Default skill cho job cụ thể
                // TODO skill cụ thể cho player
                switch (Player.Player_Job)
                {
                    case 4: // Thích khách
                        return 400001;
                    case 5:
                        return 0; // Thích khách
                    default:
                        return Player.FindHighestLevelVoCong()?.FLD_PID ?? 0; ;
                }
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Set target cụ thể
        /// </summary>
        /// <param name="targetId">Target NPC Session ID</param>
        public void SetTarget(int targetId)
        {
            _currentTargetId = targetId;
            LogInfo($"Target set to: {targetId}");
        }

        /// <summary>
        /// Clear target hiện tại
        /// </summary>
        public void ClearTarget()
        {
            _currentTargetId = 0;
            LogInfo("Target cleared");
        }

        /// <summary>
        /// Lấy thông tin target hiện tại
        /// </summary>
        /// <returns>Target info string</returns>
        public string GetTargetInfo()
        {
            try
            {
                var target = GetCurrentTarget();
                if (target == null)
                    return "No target";

                var distance = CalculateDistance(Player.PosX, Player.PosY, target.Rxjh_X, target.Rxjh_Y);
                return $"Target: {target.NPC_SessionID} | HP: {target.Rxjh_HP} | Distance: {distance:F1}";
            }
            catch
            {
                return "Target info: Unknown";
            }
        }

        /// <summary>
        /// Lấy số lượng target có thể tấn công
        /// </summary>
        /// <returns>Số lượng target</returns>
        public int GetAvailableTargetCount()
        {
            try
            {
                if (Player.NearbyNpcs == null)
                    return 0;

                return Player.NearbyNpcs.Values.Count(IsValidTarget);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Force attack target cụ thể
        /// </summary>
        /// <param name="targetId">Target ID</param>
        public void ForceAttack(int targetId)
        {
            try
            {
                var oldTarget = _currentTargetId;
                _currentTargetId = targetId;

                PerformAttack();

                _currentTargetId = oldTarget;
                LogInfo($"Force attacked target: {targetId}");
            }
            catch (Exception ex)
            {
                LogError($"Error force attacking target {targetId}: {ex.Message}");
            }
        }
    }
}
