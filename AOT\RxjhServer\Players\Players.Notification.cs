﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public void CharacterGainsExperience(double kinhNghiem)
	{
		if (kinhNghiem <= 0.0)
		{
			return;
		}
		if (CharacterBeast != null)
		{
			if (CharacterBeast.FLD_ZCD > 2000)
			{
				CharacterBeast.FLD_ZCD = 2000;
			}
			else if (CharacterBeast.FLD_ZCD < 0)
			{
				CharacterBeast.FLD_ZCD = 0;
			}
			kinhNghiem *= 0.8 + 0.4 * CharacterBeast.FLD_ZCD / 2000.0;
			if (CharacterBeast.FLD_LEVEL < 100)
			{
				if (Player_Level < World.GioiHan_Level_CaoNhat)
				{
					CharacterExperience += (long)(kinhNghiem * (100 - Config.PetKinhNghiem) / 100.0);
				}
				CharacterBeast.FLD_EXP += (long)(kinhNghiem * Config.PetKinhNghiem / 200.0);
				CharacterBeast.CalculateBasicData();
				UpdateSpiritBeastHP_MP_SP();
				UpdateTheSpiritBeastExperienceAndTrainExperience();
				UpdateSpiritBeastMartialArtsAndStatus();
			}
			else if (Player_Level < World.GioiHan_Level_CaoNhat)
			{
				CharacterExperience += (long)kinhNghiem;
			}
		}
		else if (Player_Level < World.GioiHan_Level_CaoNhat)
		{
			CharacterExperience += (long)kinhNghiem;
		}
	}

	public void CharactersGetMoney(double gold, int loaiHinh)
	{
		if (!(gold < 0.0) && gold <= 2000000000.0)
		{
			if (loaiHinh == 1)
			{
				Player_Money += (uint)gold;
				TipsForGettingMoney((uint)gold);
			}
			else
			{
				Player_Money -= (uint)gold;
			}
			UpdateMoneyAndWeight();
		}
	}
		public void TipsAfterTheUpgradeOfTheSpiritBeast()
	{
		SendingClass sendingClass = new();
		sendingClass.Write1(CharacterBeast.FLD_LEVEL);
		sendingClass.Write1(1);
		Client?.SendPak(sendingClass, 30464, CharacterBeastFullServiceID);
		SendCurrentRangeBroadcastData(sendingClass, 30464, CharacterBeastFullServiceID);
	}

	public void UpdateEquipmentEffectSpiritBeast(Players thisPlayer)
	{
		var array = Converter.HexStringToByte("AA5567000000640058000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeastFullServiceID), 0, array, 14, 2);
		var bytes = Encoding.Default.GetBytes(thisPlayer.CharacterBeast.Name);
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.FLD_LEVEL), 0, array, 38, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.FLD_JOB_LEVEL), 0, array, 39, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.FLD_JOB), 0, array, 40, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.Bs), 0, array, 42, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.NhanVatToaDo_X), 0, array, 46, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.NhanVatToaDo_Z), 0, array, 50, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.NhanVatToaDo_Y), 0, array, 54, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.NhanVatToaDo_MAP), 0, array, 58, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.NhanVatToaDo_X), 0, array, 82, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeast.NhanVatToaDo_Y), 0, array, 90, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(thisPlayer.CharacterBeastFullServiceID), 0, array, 4, 2);
		for (var i = 0; i < 4; i++)
		{
			byte[] src;
			try
			{
				src = CharacterBeast.ThuCungVaTrangBi[i].VatPham_ID;
			}
			catch
			{
				src = new byte[4];
			}
			Buffer.BlockCopy(src, 0, array, 62 + i * 4, 4);
		}
		thisPlayer.Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
	}

	public void CancelTheGangWar(byte[] packetData, int packetSize)
	{
		var key = BitConverter.ToInt32(packetData, 10);
		if (World.HelpList.TryGetValue(key, out var value))
		{
			value.DanhSachUngVien.Clear();
			World.HelpList.Remove(key);
			KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
			KiemSoatNguyenBao_SoLuong(50, 1);
			GameDb.GuildWar_MoneyBet_Delete(AccountID, CharacterName, key, 0);
			Save_NguyenBaoData();
			LogHelper.WriteLine(LogLevel.Debug, "Huỷ bỏ bang chiến trả về DangKy_BangPhai ID: " + key + " GangName:" + GuildName + " Người giúp đỡ :" + CharacterName);
			ApplicationForCancellationOfHelpTips(1);
		}
		else
		{
			ApplicationForCancellationOfHelpTips(0);
		}
	}

	public bool DidYouParticipateInTheWarThatDay(string name)
	{
		try
		{
			if (World.HelpNameList == null || World.HelpNameList.Count <= 0)
			{
				return false;
			}
			foreach (var value in World.HelpNameList.Values)
			{
				using (new Lock(value.DanhSachUngVien, "BangChienDanhSachUngVien"))
				{
					foreach (var value2 in value.DanhSachUngVien.Values)
					{
						if (value2.CharacterName == name)
						{
							return true;
						}
					}
				}
			}
		}
		catch
		{
			return false;
		}
		return false;
	}

	public void ApplyForHelp(byte[] packetData, int packetSize)
	{
		try
		{
			if (World.CoMoRa_HeThong_MonChien == 0)
			{
				HeThongNhacNho("Bang phái đang đóng cửa, thời gian khai mở: [" + World.HeThong_MonChien_Gio + "] giờ [" + World.HeThong_MonChien_Phut + "] khắc [" + World.HeThong_MonChien_Giay + "] giây!");
				return;
			}
			if (World.MonChien_ProgressNew >= 2)
			{
				HeThongNhacNho("Bang phái hỗn chiến đang diễn ra bên trong, đại hiệp hãy quay lại sau!");
				return;
			}
			if (GuildName.Length == 0)
			{
				HeThongNhacNho("Đại hiệp hãy lập bang phái mới để chinh chiến giang hồ!");
				ApplyForHelpTips(5);
				return;
			}
			if (World.HelpList.Count >= 4)
			{
				HeThongNhacNho("Số bang đăng ký Bang Chiến đã đạt tối đa 4, đại hiệp hãy quay lại sau!");
				ApplyForHelpTips(5);
				return;
			}
			if (GangCharacterLevel != 6)
			{
				ApplyForHelpTips(3);
				return;
			}
			BitConverter.ToInt16(packetData, 14);
			var num = BitConverter.ToInt32(packetData, 2118);
			var num2 = BitConverter.ToInt32(packetData, 10);
			if (num != 7301)
			{
				HeThongNhacNho("Hiện tại chỉ mở bản đồ [Đấu Thần Quán] cho Bang Chiến!");
				ApplyForHelpTips(5);
				return;
			}
			if (World.HelpList.ContainsKey(num2))
			{
				HeThongNhacNho("Bang phái đã ghi danh, không cần lặp lại lần nữa!");
				return;
			}
			if (World.BangChienThang_ID == GuildId)
			{
				HeThongNhacNho("Bang phái của đại hiệp đã thắng trận trước, hôm nay không thể tái chiến!");
				return;
			}
			Dictionary<int, Players> dictionary = new();
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == 1201 && value.GuildName == GuildName && value.Player_Level >= 100 && value.NhanVat_HP > 0 && !value.PlayerTuVong && !value.Exiting)
				{
					if (!DidYouParticipateInTheWarThatDay(value.CharacterName))
					{
						dictionary.Add(value.SessionID, value);
					}
					else
					{
						value.HeThongNhacNho("Đại hiệp đã thắng trận Bang Chiến trước, hôm nay không thể tham gia!");
					}
				}
			}
			if (dictionary.Count < World.SoNguoiDangKyCongThanhChien)
			{
				ApplyForHelpTips(4);
				HeThongNhacNho("Số hiệp khách cần để ghi danh Bang Chiến là: [" + World.SoNguoiDangKyCongThanhChien + "]", 20, "Thiên cơ các");
				dictionary.Clear();
				return;
			}
			KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
			if (FLD_RXPIONT >= World.HeThong_MonChien_CanNguyenBao)
			{
				X_Bang_Chien_Class xBangChienClass = new();
				xBangChienClass.DangKy_BangPhaiID = num2;
				xBangChienClass.YeuCau_BanDo = num;
				xBangChienClass.DangKyTenBangPhai = GuildName;
				xBangChienClass.BangPhaiMonChu = CharacterName;
				xBangChienClass.DangCap = GangLevel;
				xBangChienClass.DanhSachUngVien = dictionary;
				xBangChienClass.MonChu_TuVong_SoLan = 0;
				xBangChienClass.DiemSoHienTai = 0;
				World.HelpList.Add(num2, xBangChienClass);
				World.HelpNameList.Add(GuildId, xBangChienClass);
				ApplyForHelpTips(0);
				LogHelper.WriteLine(LogLevel.Debug, "Đăng Ký Bang Phái ID: [" + num2 + "] Tên môn phái: [" + GuildName + "] Bang chủ: [" + CharacterName + "] Nhân số: [" + xBangChienClass.DanhSachUngVien.Count + "]");
				HeThongNhacNho("Đăng ký Bang Chiến thành công | Môn phái: [" + GuildName + "] Bang chủ: [" + CharacterName + "] Nhân số: [" + xBangChienClass.DanhSachUngVien.Count + "]", 10, "Thiên cơ các");
				KiemSoatNguyenBao_SoLuong(World.HeThong_MonChien_CanNguyenBao, 0);
				GameDb.GuildWar_MoneyBet(AccountID, CharacterName, num2, World.HeThong_MonChien_CanNguyenBao);
				foreach (var value2 in xBangChienClass.DanhSachUngVien.Values)
				{
					value2.SafeMode = 1;
				}
				Save_NguyenBaoData();
			}
			else
			{
				HeThongNhacNho("Điểm không đủ, xin Bang Chiến cần tiêu hao [" + World.HeThong_MonChien_CanNguyenBao + "] điểm!");
				ApplyForHelpTips(2);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Áp dụng BangChien error：" + ex.ToString());
		}
	}

	public void ApplyForHelpTips(int id)
	{
		var array = Converter.HexStringToByte("AA5512003F003D10040004000000000000000000BE9A55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void ApplicationForCancellationOfHelpTips(int id)
	{
		var array = Converter.HexStringToByte("AA5512003F003B10040001000000000000000000BE9A55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void AnnouncementOfSuccessfulMatchmaking(int id)
	{
		var array = Converter.HexStringToByte("AA55AA000F2766009C000830000000000000000000000000000000000000000002307C000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000690000640A000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void HelpPrepareAnnouncementPrompt(string sj)
	{
		var array = Converter.HexStringToByte("AA55AA000F3766009C000830000000000000000000000000000000000000000003317C330000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000690000650A000000000000000055AA");
		Buffer.BlockCopy(Encoding.Default.GetBytes(sj), 0, array, 35, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void HelpStartPrompt(int id, int id2)
	{
		var array = Converter.HexStringToByte("AA551600E001371008000000000000000000000000000000ED3155AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(id2), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void SystemCountdown(int id, int id2)
	{
		var array = Converter.HexStringToByte("AA551600E001371008000000000000000000000000000000ED3155AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(id2), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void HelpUpdateScore(int zfs, int kfs)
	{
		var array = Converter.HexStringToByte("AA5513000F2739100800FFFFFFFF000000000000000000003C5B55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(zfs), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(kfs), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}
	
	public void CoupleSystem(byte[] data, int length)
	{
		var num = 0;
		try
		{
			var array = new byte[14];
			var array2 = new byte[14];
			Buffer.BlockCopy(data, 33, array, 0, 14);
			var text = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
			Buffer.BlockCopy(data, 18, array2, 0, 14);
			var text2 = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
			int num2 = data[10];
			int num3 = data[14];
			switch (num2)
			{
				case 2:
				case 6:
				case 9:
				case 10:
				case 11:
				case 16:
				case 18:
				case 19:
					break;
				case 1:
					{
						if (FLD_Couple.Length != 0)
						{
							CoupleTips(16, text2, text);
							break;
						}
						num = 3;
						var players2 = World.KiemTra_Ten_NguoiChoi(text);
						if (players2 != null)
						{
							num = 4;
							if (players2.CuaHangCaNhan == null && !players2.InTheShop && !players2.OpenWarehouse)
							{
								if (players2.FLD_Couple.Length != 0)
								{
									CoupleTips(16, text2, text);
									break;
								}
								if (players2.Config.ToTinh_SwitchOnOff == 0)
								{
									CoupleTips(21, text2, text);
									break;
								}
								// Cho phép kết hôn đồng giới
								//if (players2.Player_Sex == Player_Sex)
								//{
								//	CoupleTips(24, text2, text);
								//	break;
								//}
								CoupleTips(6, text2, text);
								players2.CoupleRequest(1, text2, text);
							}
						}
						else
						{
							CoupleTips(18, text2, text);
						}
						break;
					}
				case 3:
					{
						num = 5;
						var array4 = new byte[4];
						Buffer.BlockCopy(data, 54, array4, 0, 4);
						var hoaHongId = BitConverter.ToInt32(array4, 0);
						var players3 = World.KiemTra_Ten_NguoiChoi(text);
						num = 6;
						if (players3 != null)
						{
							num = 7;
							if (FLD_Couple == players3.CharacterName)
							{
								num = 8;
								if (!players3.AppendStatusList.ContainsKey(1000000891) && !players3.AppendStatusList.ContainsKey(1000000892) && !players3.AppendStatusList.ContainsKey(1000000893))
								{
									num = 9;
									players3.CoupleFlowerRequest(3, text2, text, hoaHongId);
								}
								else
								{
									HeThongNhacNho("Đối phương đã mang trạng thái bí pháp, không thể tái thi triển!");
								}
							}
						}
						else
						{
							CoupleTips(18, text2, text);
						}
						break;
					}
				case 4:
					{
						var players = World.KiemTra_Ten_NguoiChoi(text);
						if (players != null)
						{
							if (WhetherMarried == 1)
							{
								var flag = false;
								var flag2 = false;
								var position = 0;
								var position2 = 0;
								for (var i = 0; i < 96; i++)
								{
									if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000415)
									{
										position = i;
										flag = true;
										break;
									}
								}
								for (var j = 0; j < 96; j++)
								{
									if (BitConverter.ToInt32(players.Item_In_Bag[j].VatPham_ID, 0) == 1000000415)
									{
										position2 = j;
										flag2 = true;
										break;
									}
								}
								if (flag && flag2)
								{
									FLD_Couple = "";
									FLD_Couple_Love = 0;
									WhetherMarried = 0;
									NhanCuoiKhacChu = string.Empty;
									GiaiTruQuanHe_Countdown = 0;
									players.FLD_Couple = "";
									players.FLD_Couple_Love = 0;
									players.WhetherMarried = 0;
									VoCongMoi[2, 16] = null;
									VoCongMoi[2, 17] = null;
									players.NhanCuoiKhacChu = string.Empty;
									players.GiaiTruQuanHe_Countdown = 0;
									players.VoCongMoi[2, 16] = null;
									players.VoCongMoi[2, 17] = null;
									SubtractItem(position, 1);
									players.SubtractItem(position2, 1);
									HeThongNhacNho("Ly hôn đã hoàn tất, đại hiệp hãy tái nhập giang hồ!");
									players.HeThongNhacNho("Ly hôn đã hoàn tất, đại hiệp hãy tái nhập giang hồ!");
								}
								else
								{
									HeThongNhacNho("Song phương ly hôn cần mang theo bảo vật " + ItmeClass.DatDuocVatPhamTen_XungHao(1000000415), 10, "Thiên cơ các");
								}
							}
							else
							{
								FLD_Couple = "";
								FLD_Couple_Love = 0;
								WhetherMarried = 0;
								NhanCuoiKhacChu = string.Empty;
								GiaiTruQuanHe_Countdown = 0;
								players.FLD_Couple = "";
								players.FLD_Couple_Love = 0;
								players.WhetherMarried = 0;
								VoCongMoi[2, 16] = null;
								VoCongMoi[2, 17] = null;
								players.NhanCuoiKhacChu = string.Empty;
								players.GiaiTruQuanHe_Countdown = 0;
								players.VoCongMoi[2, 16] = null;
								players.VoCongMoi[2, 17] = null;
								HeThongNhacNho("Ly hôn đã hoàn tất, đại hiệp hãy rời giang hồ rồi trở lại!");
								players.HeThongNhacNho("Ly hôn đã hoàn tất, đại hiệp hãy rời giang hồ rồi trở lại!");
							}
						}
						else
						{
							CoupleTips(18, text2, text);
						}
						break;
					}
				case 5:
					GiaiTruQuanHe_Countdown = 4320;
					CoupleTips(11, CharacterName, text);
					UpdateCoupleSystem(1, text, NhanCuoiKhacChu, GiaiTruQuanHe_Countdown, DateTime.Now);
					GameDb.UpdateMaritalStatus(text, GiaiTruQuanHe_Countdown);
					break;
				case 7:
					{
						var players4 = World.KiemTra_Ten_NguoiChoi(text2);
						int num4 = data[14];
						var parcelVacancy = GetParcelVacancy(this);
						var parcelVacancy2 = players4.GetParcelVacancy(players4);
						if (players4 == null || parcelVacancy == -1 || parcelVacancy2 == -1)
						{
							break;
						}
						if (num4 == 3)
						{
							FLD_Couple = players4.CharacterName;
							players4.FLD_Couple = CharacterName;
							GiaiTruQuanHe_Countdown = 0;
							players4.GiaiTruQuanHe_Countdown = 0;
							FLD_Couple_Love = 0;
							players4.FLD_Couple_Love = 0;
							FLD_loveDegreeLevel = 10;
							players4.FLD_loveDegreeLevel = 10;
							players4.WhetherMarried = 1;
							WhetherMarried = 1;
							UpdateCoupleSystem(2, players4.CharacterName, NhanCuoiKhacChu, GiaiTruQuanHe_Countdown, DateTime.Now);
							players4.UpdateCoupleSystem(2, CharacterName, players4.NhanCuoiKhacChu, players4.GiaiTruQuanHe_Countdown, DateTime.Now);
							CoupleTips(num4, text, text2);
							players4.CoupleTips(num4, text2, text);
							players4.ShowBigPrint(SessionID, 403);
							ShowBigPrint(players4.SessionID, 403);
							if (!players4.Client.TreoMay)
							{
								var text4 = "Nhân vật [" + text2 + "] đã chính thức kết hôn cùng [" + text + "] trong một lễ hợp duyên long trọng tại [Kênh " + World.ServerID + "]. Giang hồ chứng giám, đôi uyên ương này từ nay chung bước hành tẩu thiên hạ!";
								World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text4);
							}
							var flag3 = false;
							var flag4 = false;
							for (var k = 1; k < 96; k++)
							{
								if (BitConverter.ToInt32(Item_In_Bag[k].VatPham_ID, 0) == 1000000415)
								{
									flag3 = true;
									break;
								}
							}
							if (!flag3)
							{
								IncreaseItem4(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000415), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
							}
							for (var l = 1; l < 96; l++)
							{
								if (BitConverter.ToInt32(players4.Item_In_Bag[l].VatPham_ID, 0) == 1000000415)
								{
									flag4 = true;
									break;
								}
							}
							if (!flag4)
							{
								var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
								players4.IncreaseItem4(bytes, BitConverter.GetBytes(1000000415), parcelVacancy2, BitConverter.GetBytes(1), new byte[56]);
							}
						}
						else
						{
							CoupleTips(num4, text2, text);
							players4.CoupleTips(num4, text2, text);
						}
						break;
					}
				case 8:
					{
						num = 22;
						var array5 = new byte[4];
						Buffer.BlockCopy(data, 54, array5, 0, 4);
						var num11 = BitConverter.ToInt32(array5, 0);
						num = 23;
						var players5 = World.KiemTra_Ten_NguoiChoi(text2);
						switch (num3)
						{
							case 8:
								if (players5 != null)
								{
									if (FLD_Couple == players5.CharacterName)
									{
										players5.CoupleTips(8, text2, text);
									}
								}
								else
								{
									CoupleTips(18, text2, text);
								}
								break;
							case 7:
								{
									num = 24;
									var flag5 = false;
									if (AppendStatusList == null)
									{
										break;
									}
									num = 2410;
									if (AppendStatusList.ContainsKey(1000000891) || AppendStatusList.ContainsKey(1000000892) || AppendStatusList.ContainsKey(1000000893))
									{
										break;
									}
									for (var m = 0; m < 96; m++)
									{
										num = 241;
										if (FLD_Couple == players5.CharacterName)
										{
											num = 24111;
											if (BitConverter.ToInt32(players5.Item_In_Bag[m].VatPham_ID, 0) == num11)
											{
												flag5 = true;
												num = 242;
												players5.SubtractItem(m, 1);
												break;
											}
											continue;
										}
										HeThongNhacNho("Kết hôn gặp lỗi, đại hiệp cần dùng bí lệnh !lyhon cho cả hai rồi tái hợp duyên!");
										return;
									}
									if (!flag5)
									{
										num = 243;
										CoupleTips(16, text2, text);
										break;
									}
									num = 244;
									var num12 = RNG.Next(10, 30);
									switch (num11)
									{
										case 1000000893:
											num12 = RNG.Next(30, 60);
											break;
										case 1000000892:
											num12 = RNG.Next(20, 40);
											break;
									}
									if (CoupleInTeam)
									{
										num12 *= 2;
									}
									num = 25;
									CapNhatXepHang_HoaHong(this, this, 1);
									FLD_Couple_Love += num12;
									players5.FLD_Couple_Love += num12;
									if (FLD_Couple_Love >= 35000)
									{
										FLD_Couple_Love = 35000;
										FLD_loveDegreeLevel = 1;
									}
									else if (FLD_Couple_Love > 30000)
									{
										if (FLD_loveDegreeLevel == 2)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 1;
									}
									else if (FLD_Couple_Love > 21000)
									{
										if (FLD_loveDegreeLevel == 3)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 2;
									}
									else if (FLD_Couple_Love > 14700)
									{
										if (FLD_loveDegreeLevel == 4)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 3;
									}
									else if (FLD_Couple_Love > 10290)
									{
										if (FLD_loveDegreeLevel == 5)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 4;
									}
									else if (FLD_Couple_Love > 7203)
									{
										if (FLD_loveDegreeLevel == 6)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 5;
									}
									else if (FLD_Couple_Love > 5042)
									{
										if (FLD_loveDegreeLevel == 7)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 6;
									}
									else if (FLD_Couple_Love > 3025)
									{
										if (FLD_loveDegreeLevel == 8)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 7;
									}
									else if (FLD_Couple_Love > 1513)
									{
										if (FLD_loveDegreeLevel == 9)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 8;
									}
									else if (FLD_Couple_Love > 605)
									{
										if (FLD_loveDegreeLevel == 10)
										{
											CoupleTips(27, CharacterName, players5.CharacterName);
										}
										FLD_loveDegreeLevel = 9;
									}
									else
									{
										FLD_loveDegreeLevel = 10;
									}
									if (players5.FLD_Couple_Love >= 35000)
									{
										players5.FLD_Couple_Love = 35000;
										players5.FLD_loveDegreeLevel = 1;
									}
									else if (players5.FLD_Couple_Love > 30000)
									{
										if (players5.FLD_loveDegreeLevel == 2)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 1;
									}
									else if (players5.FLD_Couple_Love > 21000)
									{
										if (players5.FLD_loveDegreeLevel == 3)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 2;
									}
									else if (players5.FLD_Couple_Love > 14700)
									{
										if (players5.FLD_loveDegreeLevel == 4)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 3;
									}
									else if (players5.FLD_Couple_Love > 10290)
									{
										if (players5.FLD_loveDegreeLevel == 5)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 4;
									}
									else if (players5.FLD_Couple_Love > 7203)
									{
										if (players5.FLD_loveDegreeLevel == 6)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 5;
									}
									else if (players5.FLD_Couple_Love > 5042)
									{
										if (players5.FLD_loveDegreeLevel == 7)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 6;
									}
									else if (players5.FLD_Couple_Love > 3025)
									{
										if (players5.FLD_loveDegreeLevel == 8)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 7;
									}
									else if (players5.FLD_Couple_Love > 1513)
									{
										if (players5.FLD_loveDegreeLevel == 9)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 8;
									}
									else if (players5.FLD_Couple_Love > 605)
									{
										if (players5.FLD_loveDegreeLevel == 10)
										{
											players5.CoupleTips(27, players5.CharacterName, CharacterName);
										}
										players5.FLD_loveDegreeLevel = 9;
									}
									else
									{
										players5.FLD_loveDegreeLevel = 10;
									}
									CoupleTips(29, CharacterName, players5.CharacterName);
									players5.CoupleTips(29, players5.CharacterName, CharacterName);
									if (AppendStatusList != null)
									{
										if (GetAddState(1000000891))
										{
											AppendStatusList[1000000891].ThoiGianKetThucSuKien();
										}
										if (GetAddState(1000000892))
										{
											AppendStatusList[1000000892].ThoiGianKetThucSuKien();
										}
										if (GetAddState(1000000893))
										{
											AppendStatusList[1000000893].ThoiGianKetThucSuKien();
										}
									}
									else
									{
										AppendStatusList = new ThreadSafeDictionary<int, StatusEffect>();
									}
									StatusEffect(BitConverter.GetBytes(num11), 1, 1800000);
									StatusEffect xThemVaoTrangThaiLoai = new(this, 1800000, num11, 0);
									AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
									switch (FLD_loveDegreeLevel)
									{
										case 1:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 15;
											FLD_NhanVat_ThemVao_PhongNgu += 15;
											FLD_NhanVat_ThemVao_KhiCong++;
											FLD_NhanVat_ThemVao_PhanTramKinhNghiem += 0.05;
											UpdateKhiCong();
											break;
										case 2:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 15;
											FLD_NhanVat_ThemVao_PhongNgu += 15;
											FLD_NhanVat_ThemVao_KhiCong++;
											UpdateKhiCong();
											break;
										case 3:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 15;
											FLD_NhanVat_ThemVao_PhongNgu += 15;
											break;
										case 4:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 10;
											FLD_NhanVat_ThemVao_PhongNgu += 10;
											break;
										case 5:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 10;
											FLD_NhanVat_ThemVao_PhongNgu += 5;
											break;
										case 6:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 5;
											FLD_NhanVat_ThemVao_PhongNgu += 5;
											break;
										case 7:
											CharactersToAddMax_HP += 150;
											FLD_NhanVat_ThemVao_CongKich += 5;
											break;
										case 8:
											CharactersToAddMax_HP += 150;
											break;
										case 9:
											CharactersToAddMax_HP += 100;
											break;
										case 10:
											CharactersToAddMax_HP += 50;
											break;
									}
									UpdateMartialArtsAndStatus();
									CapNhat_HP_MP_SP();
									break;
								}
						}
						break;
					}
				case 12:
					CoupleTips(38, text, text2, 0);
					CoupleTips(38, text, text2, 1);
					CoupleTips(38, text, text2, 2);
					break;
				case 13:
					{
						int num9 = data[66];
						var num10 = 9101;
						var weddingPattern3 = 0;
						switch (num9)
						{
							case 1:
								weddingPattern3 = 0;
								break;
							case 2:
								weddingPattern3 = 1;
								break;
						}
						if (num9 != 0)
						{
							num = 29;
							if (!CheckTheEntryConditionsOfTheWeddingBanquetHall(num10, 1000000333))
							{
								break;
							}
							num = 30;
							var characterData4 = GetCharacterData(FLD_Couple);
							num = 31;
							if (characterData4 != null)
							{
								num = 32;
								var packagedItems3 = FindItemByItemID(1000000333);
								if (packagedItems3 != null)
								{
									SubtractItem(packagedItems3.VatPhamViTri, 1);
								}
								num = 33;
								WeddingAdmissionTicket = 1000000333;
								WeddingMap = num10;
								WeddingPattern = weddingPattern3;
								Mobile(0f, 280f, 15f, WeddingMap, 0);
								characterData4.Mobile(0f, 280f, 15f, WeddingMap, 0);
								_preparationTime = DateTime.Now.AddMinutes(1.0);
								PreliminaryApplicationCeremonyTimer = new System.Timers.Timer(30000.0);
								PreliminaryApplicationCeremonyTimer.Elapsed += PrepTimeEndEvent;
								PreliminaryApplicationCeremonyTimer.Enabled = true;
								PreliminaryApplicationCeremonyTimer.AutoReset = true;
								OpenStore(168, 2, 0);
								HeThongNhacNho("Đại hiệp đang ở sảnh hôn lễ, có 3 khắc để bắt đầu nghi thức!", 6, "Truyền Âm Các");
								characterData4.HeThongNhacNho("Đại hiệp đang ở sảnh hôn lễ, cô nương hãy chờ phu quân bắt đầu nghi thức!", 6, "Truyền Âm Các");
							}
							else
							{
								CoupleTips(18, CharacterName, FLD_Couple);
							}
						}
						else
						{
							CoupleTips(40, CharacterName, FLD_Couple);
						}
						break;
					}
				case 14:
					{
						int num5 = data[66];
						var num6 = 9001;
						var weddingPattern = 0;
						switch (num5)
						{
							case 1:
								weddingPattern = 0;
								break;
							case 2:
								weddingPattern = 1;
								break;
						}
						if (num5 != 0)
						{
							num = 34;
							if (!CheckTheEntryConditionsOfTheWeddingBanquetHall(num6, 1000000334))
							{
								break;
							}
							num = 35;
							var characterData = GetCharacterData(FLD_Couple);
							num = 36;
							if (characterData != null)
							{
								num = 37;
								var packagedItems = FindItemByItemID(1000000334);
								if (packagedItems != null)
								{
									SubtractItem(packagedItems.VatPhamViTri, 1);
								}
								num = 38;
								WeddingAdmissionTicket = 1000000334;
								WeddingMap = num6;
								WeddingPattern = weddingPattern;
								Mobile(73f, -50f, 15f, WeddingMap, 0);
								characterData.Mobile(73f, -50f, 15f, WeddingMap, 0);
								_preparationTime = DateTime.Now.AddMinutes(3.0);
								PreliminaryApplicationCeremonyTimer = new System.Timers.Timer(30000.0);
								PreliminaryApplicationCeremonyTimer.Elapsed += PrepTimeEndEvent;
								PreliminaryApplicationCeremonyTimer.Enabled = true;
								PreliminaryApplicationCeremonyTimer.AutoReset = true;
								OpenStore(168, 2, 0);
								HeThongNhacNho("Đại hiệp đang ở sảnh hôn lễ, có 3 khắc để ghi danh hôn lễ!", 6, "Truyền Âm Các");
								characterData.HeThongNhacNho("Đại hiệp đang ở sảnh hôn lễ, cô nương hãy chờ phu quân bắt đầu nghi thức!", 6, "Truyền Âm Các");
							}
							else
							{
								CoupleTips(18, CharacterName, FLD_Couple);
							}
						}
						else
						{
							CoupleTips(40, CharacterName, FLD_Couple);
						}
						break;
					}
				case 15:
					{
						int num7 = data[66];
						var num8 = 9201;
						var weddingPattern2 = 0;
						switch (num7)
						{
							case 1:
								weddingPattern2 = 0;
								break;
							case 2:
								weddingPattern2 = 1;
								break;
						}
						if (num7 != 0)
						{
							num = 39;
							if (!CheckTheEntryConditionsOfTheWeddingBanquetHall(num8, 1000000335))
							{
								break;
							}
							num = 40;
							var characterData2 = GetCharacterData(FLD_Couple);
							num = 41;
							if (characterData2 != null)
							{
								num = 42;
								var packagedItems2 = FindItemByItemID(1000000335);
								if (packagedItems2 != null)
								{
									SubtractItem(packagedItems2.VatPhamViTri, 1);
								}
								WeddingAdmissionTicket = 1000000335;
								WeddingMap = num8;
								WeddingPattern = weddingPattern2;
								Mobile(-59f, 53f, 15f, WeddingMap, 0);
								characterData2.Mobile(-48f, -104f, 15f, WeddingMap, 0);
								_preparationTime = DateTime.Now.AddMinutes(3.0);
								PreliminaryApplicationCeremonyTimer = new System.Timers.Timer(30000.0);
								PreliminaryApplicationCeremonyTimer.Elapsed += PrepTimeEndEvent;
								PreliminaryApplicationCeremonyTimer.Enabled = true;
								PreliminaryApplicationCeremonyTimer.AutoReset = true;
								OpenStore(168, 2, 0);
								HeThongNhacNho("Đại hiệp đang ở sảnh hôn lễ, có 3 khắc để ghi danh hôn lễ!", 6, "Truyền Âm Các");
								characterData2.HeThongNhacNho("Đại hiệp đang ở sảnh hôn lễ, cô nương hãy chờ phu quân bắt đầu nghi thức 111!", 6, "Truyền Âm Các");
							}
							else
							{
								CoupleTips(18, CharacterName, FLD_Couple);
							}
						}
						else
						{
							CoupleTips(40, CharacterName, FLD_Couple);
						}
						break;
					}
				case 17:
					{
						if (!ApplicationConditionsForTestingCeremony(WeddingMap, WeddingAdmissionTicket))
						{
							break;
						}
						num = 43;
						var characterData3 = GetCharacterData(FLD_Couple);
						num = 44;
						if (characterData3 != null)
						{
							num = 45;
							if (!World.Weddinglist.ContainsKey(WeddingMap))
							{
								Wedding value = new(this, characterData3, WeddingMap, WeddingPattern);
								World.Weddinglist.Add(WeddingMap, value);
							}
							if (WeddingMap == 9001)
							{
								World.IsHuaMarriageHallInUse = true;
							}
							else if (WeddingMap == 9101)
							{
								World.IsTheDragonHallInUse = true;
							}
							else if (WeddingMap == 9201)
							{
								World.WhetherTheSacramentalHallIsInUse = true;
							}
						}
						break;
					}
				case 20:
					HonNhan_NguoiDat_CauHoiDapAn = data[66];
					break;
				case 21:
					{
						if (Player_Money < 10000000)
						{
							CoupleTips(87, CharacterName, text);
							break;
						}
						if (FLD_Couple.Length == 0)
						{
							CoupleTips(16, CharacterName, text);
							break;
						}
						if (FindItemByItemID(1000000415) == null)
						{
							CoupleTips(85, CharacterName, text);
							break;
						}
						if (GiaiTruQuanHe_Countdown != 0)
						{
							CoupleTips(53, CharacterName, text);
							break;
						}
						num = 46;
						var array3 = new byte[32];
						Buffer.BlockCopy(data, 78, array3, 0, 32);
						var text3 = Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim();
						UpdateCoupleSystem(1, text, text3, GiaiTruQuanHe_Countdown, DateTime.Now);
						NhanCuoiKhacChu = text3;
						CoupleTips(84, CharacterName, text);
						Player_Money -= 10000000L;
						UpdateMoneyAndWeight();
						break;
					}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tặng bông Kết Hôn tại num:[" + num + "] [" + AccountID + "][" + CharacterName + "] [" + Client.PlayerSessionID + "] - " + ex.Message);
			if (num == 241 && Offline_TreoMay_Mode_ON_OFF == 0)
			{
				HeThongNhacNho("Đại hiệp gặp lỗi kết hôn, hãy báo cho Võ Lâm Minh Chủ để xử lý!!", 20, "Thiên cơ các");
			}
		}
	}

	public void CoupleFlowerRequest(int id, string banThanDanhTu, string doiPhuongTen, int hoaHongId)
	{
		var array = Converter.HexStringToByte("AA5552007600791744000300000000000000D0D5BBCA4FC3FBB5DB000000000000306FD3F1C2B66F3000000000000000000022000000000000000000000000000000000000000000000000000000000000000000B7A655AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		var bytes = Encoding.Default.GetBytes(banThanDanhTu);
		var bytes2 = Encoding.Default.GetBytes(doiPhuongTen);
		if (hoaHongId != 0)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(hoaHongId), 0, array, 54, 4);
		}
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(bytes2, 0, array, 33, bytes2.Length);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void CoupleTips(int id, string banThanDanhTu, string doiPhuongTen)
	{
		var array = Converter.HexStringToByte("AA5552002C017A1744000C00000000000000B7B6CEDEBEC8000000000000000000DEB9D2C2B2DD000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		var bytes = Encoding.Default.GetBytes(banThanDanhTu);
		var bytes2 = Encoding.Default.GetBytes(doiPhuongTen);
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(bytes2, 0, array, 33, bytes2.Length);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void CoupleTips(int id, string banThanDanhTu, string doiPhuongTen, int mapId)
	{
		var array = Converter.HexStringToByte("AA5552002C017A1744000C00000000000000B7B6CEDEBEC8000000000000000000DEB9D2C2B2DD000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		var bytes = Encoding.Default.GetBytes(banThanDanhTu);
		var bytes2 = Encoding.Default.GetBytes(doiPhuongTen);
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(bytes2, 0, array, 33, bytes2.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(mapId), 0, array, 50, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void CoupleRequest(int id, string banThanDanhTu, string doiPhuongTen)
	{
		var array = Converter.HexStringToByte("AA553E003F027917300001000000000000000000000000000000000000000000000000000000000000000000000000000000FFFFFFFF0000000000000000000029B955AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		var bytes = Encoding.Default.GetBytes(banThanDanhTu);
		var bytes2 = Encoding.Default.GetBytes(doiPhuongTen);
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(bytes2, 0, array, 33, bytes2.Length);
		Client?.SendMultiplePackage(array, array.Length);
	}
		public void SendOnlineTimeMessage(int num)
	{
		var array = Converter.HexStringToByte("AA5516004305002108000000000000000100000000000000C49655AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void NewDrugEffects(int vatPhamId, int @switch, uint timeStart, uint timeEnd)
	{
		var value = 0u;
		SendingClass sendingClass = new();
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		sendingClass.Write4(vatPhamId);
		sendingClass.Write4(0);
		sendingClass.Write2(0);
		sendingClass.Write2(1);
		if (timeStart != 0)
		{
			value = Convert.ToUInt32(DateTime.ParseExact(timeStart.ToString(), "yyMMddHHmm", CultureInfo.CurrentCulture).AddMinutes(0L - timeEnd).ToString("yyMMddHHmm"));
		}
		if (@switch != 0)
		{
			sendingClass.Write4(timeStart);
		}
		else
		{
			sendingClass.Write4(0);
		}
		sendingClass.Write4(0);
		sendingClass.Write2(@switch);
		sendingClass.Write2(0);
		if (@switch != 0)
		{
			sendingClass.Write4(value);
		}
		else
		{
			sendingClass.Write4(0);
		}
		sendingClass.Write4(0);
		Client?.SendPak(sendingClass, 17153, SessionID);
	}
	
	public void HeThongNhacNhoNew(int id1, int id2, string danhTu1, string danhTu2)
	{
		using SendingClass sendingClass = new();
		sendingClass.Write4(id1);
		sendingClass.Write4(id2);
		sendingClass.WriteName(danhTu1);
		sendingClass.Write4(0);
		sendingClass.Write2(0);
		sendingClass.WriteName("");
		Client?.SendPak(sendingClass, 45080, SessionID);
	}
	public void SendCongBoTinTuc(int type, int message)
	{
		SendingClass w = new();
		w.Write4(2); // 1 Công bố, 2 là system màu vàng
		w.Write4(0);
		w.Write4(type);
		w.Write4(2);
		w.Write4(MapID);
		w.Write4(0);
		w.Write4(0);
		w.Write4(0);
		w.Write4(0);
		w.WriteStringCut("", 246);
		// w.Write(new byte[43]);
		w.Write(0xB4);
		Client?.SendPak(w, 0x51, SessionID);
	}

	public void SendCongBoTinTuc(int type, string message)
	{
		SendingClass w = new();
		w.Write4(1);
		w.Write4(0);
		w.Write4(type);
		w.Write4(0);
		w.Write4(MapID);
		w.Write4(0);
		w.Write4(0);
		w.Write4(0);
		w.Write4(0);
		w.WriteStringCut(message, 246);
		// w.Write(new byte[43]);
		w.Write(0xB4);
		Client?.SendPak(w, 0x51, SessionID);
	}

	public void SendCongBoTinTuc(int ybiId, int mapId, string character, int itemId, int monsterId)
	{
		SendingClass w = new();
		w.Write4(1);
		w.Write4(0);
		w.Write4(ybiId);
		w.Write4(2);
		w.Write4(mapId);
		w.Write4(itemId);
		w.Write4(0);
		w.Write4(monsterId);
		w.Write4(0);
		w.WriteStringCut(character, 16);
		w.Write(new byte[0x7F]);
		w.Write(0xB4);
		Client?.SendPak(w, 0x51, SessionID);
	}

	public void SendAShoutMessageToBroadcastData(byte[] data, int length)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.CharacterName != CharacterName && FindPlayers(300, value) && value.Client != null && value.Client.Running && !value.Client.TreoMay && value.IsJoinWorld)
				{
					value.Client.Send_Map_Data(data, length);
				}
			}
		}
		catch
		{
		}
	}

	public void SendLionRoarMessageBroadcastData(byte[] data, int length)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.Client != null && value.Client.Running && !value.Client.TreoMay && value.IsJoinWorld)
				{
					value.Client.Send_Map_Data(data, length);
				}
			}
		}
		catch
		{
		}
	}

	public void PhongAn_OpenBox_NhacNho(int thamSoVatPhamViTri, int thamSoNhacNhoTinTuc)
	{
		var text = "AA5522002C013B001C000112000083CD9A3B000000002C00000008000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(thamSoVatPhamViTri), 0, array, 11, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(thamSoNhacNhoTinTuc), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[thamSoVatPhamViTri].GetVatPhamSoLuong), 0, array, 26, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void Gold_TroChoi_TieuHao_NhacNho(uint soLuong, int tangHoacGiam)
	{
		var text = "AA551200B00381030C001B000000001F0AFAFFFFFFFF55AA";
		var array = Converter.HexStringToByte(text);
		if (tangHoacGiam == -1)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(0L - soLuong), 0, array, 14, 4);
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 14, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(tangHoacGiam), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDi_CongThanhChien_ThoiGian_ConLai(int 剩余ThoiGian)
	{
		var text = "AA55860067055301800007000A000000010001000000000000007706000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(剩余ThoiGian), 0, array, 26, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public async void GuiDiTruyenThu(byte[] data, int length)
	{
		try
		{
			if ((int)DateTime.Now.Subtract(TruyenThuThoiGian).TotalMilliseconds >= 300000)
			{
				TruyenThuThoiGian = DateTime.Now;
				int num = data[33];
				if (num > 99)
				{
					num = 99;
				}
				var array = new byte[num];
				string text;
				try
				{
					Buffer.BlockCopy(data, 35, array, 0, array.Length);
					text = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Transmit TruyenThu error 11 [" + AccountID + "]-[" + CharacterName + "]" + array.Length + "      " + ex.Message);
					return;
				}
				var array2 = new byte[15];
				Buffer.BlockCopy(data, 12, array2, 0, array2.Length);
				var doiPhuongTen = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
				if (text.Length > 0)
				{
					await CreateBiography(this, doiPhuongTen, 0, text, 2, 0);
				}
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "TruyenThu Sai lầm ：" + ex2.Message);
		}
	}

	public void Send_IceBlock(int characterGlobalId)
	{
		var array = Converter.HexStringToByte("AA5520002C01401538006D0400002C01000036000000010000000000000086807664868076640300000001000000000000006D04000003000000360000000100000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(characterGlobalId), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(characterGlobalId), 0, array, 14, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}


	public void SpiritPetSynthesisHint(int id, int id2, int id3, Item vatPham, int coThanhCongHayKhong)
	{
		var array = Converter.HexStringToByte("AA55C600E2015100B80073010100000000009ACE9A3B000000000E00000094CE9A3B000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000E8030000000000000000000000000000B82200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id2), 0, array, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(vatPham.VatPhamViTri), 0, array, 26, 4);
		Buffer.BlockCopy(vatPham.VatPham_ID, 0, array, 30, 4);
		Buffer.BlockCopy(vatPham.VatPham_ThuocTinh, 0, array, 38, World.VatPham_ThuocTinh_KichThuoc);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 94, 4);
		Buffer.BlockCopy(BitConverter.GetBytes((long)id3), 0, array, 98, 4);
		Buffer.BlockCopy(BitConverter.GetBytes((long)id3), 0, array, 102, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(coThanhCongHayKhong), 0, array, 110, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void AlchemySynthesisHint(int thaoTacD, int id, int id2, Item vatPham, int id3)
	{
		var array = Converter.HexStringToByte("AA55C600E2015100B80073010100000000009ACE9A3B000000000E00000094CE9A3B000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000E8030000000000000000000000000000B82200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id3), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(vatPham.VatPhamViTri), 0, array, 26, 4);
		Buffer.BlockCopy(vatPham.VatPham_ID, 0, array, 30, 4);
		Buffer.BlockCopy(vatPham.VatPham_ThuocTinh, 0, array, 38, World.VatPham_ThuocTinh_KichThuoc);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 94, 4);
		Buffer.BlockCopy(BitConverter.GetBytes((long)id2), 0, array, 98, 4);
		Buffer.BlockCopy(BitConverter.GetBytes((long)id2), 0, array, 102, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

}
