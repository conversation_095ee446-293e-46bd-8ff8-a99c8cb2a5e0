﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_cw {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_cw_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string zrname { get; set; }

		[JsonProperty]
		public int? itmeid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string name { get; set; }

		[JsonProperty]
		public int? fld_zcd { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_exp { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_bs { get; set; }

		[JsonProperty]
		public int? fld_job { get; set; }

		[JsonProperty]
		public int? fld_job_level { get; set; }

		[JsonProperty]
		public int? fld_hp { get; set; }

		[JsonProperty]
		public int? fld_mp { get; set; }

		[JsonProperty]
		public byte[] fld_kongfu { get; set; }

		[JsonProperty]
		public byte[] fld_wearitem { get; set; }

		[JsonProperty]
		public byte[] fld_item { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_magic5 { get; set; }

		[JsonProperty]
		public int? fld_sxbl { get; set; }

	}

}
