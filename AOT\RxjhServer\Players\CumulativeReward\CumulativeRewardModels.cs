
using System;
using System.Collections.Generic;
using System.Linq;

namespace HeroYulgang.RxjhServer.CumulativeReward
{
    public class CumulativeRewardTemplate
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int Month { get; set; }
        public int Year { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }

        // 10 mốc cash requirement
        public int[] MilestoneCashRequirements { get; set; } = new int[10];

        // Dictionary để lưu rewards theo milestone (1-10) và slot (1-6)
        public Dictionary<int, List<CumulativeRewardItem>> MilestoneRewards { get; set; } = new Dictionary<int, List<CumulativeRewardItem>>();

        /// <summary>
        /// Get cash requirement for specific milestone (1-10)
        /// </summary>
        public int GetMilestoneCash(int milestoneNumber)
        {
            if (milestoneNumber < 1 || milestoneNumber > 10) return 0;
            return MilestoneCashRequirements[milestoneNumber - 1];
        }

        /// <summary>
        /// Get rewards for specific milestone (1-10)
        /// </summary>
        public List<CumulativeRewardItem> GetMilestoneRewards(int milestoneNumber)
        {
            if (MilestoneRewards.TryGetValue(milestoneNumber, out var rewards))
                return rewards;
            return new List<CumulativeRewardItem>();
        }

        /// <summary>
        /// Check which milestones player can claim based on cash spent
        /// </summary>
        public List<int> GetClaimableMilestones(int cashSpent, List<int> alreadyClaimedMilestones)
        {
            var claimable = new List<int>();
            for (int i = 1; i <= 10; i++)
            {
                if (cashSpent >= GetMilestoneCash(i) && !alreadyClaimedMilestones.Contains(i))
                {
                    claimable.Add(i);
                }
            }
            return claimable;
        }
    }

    public class CumulativeRewardItem
    {
        public int Id { get; set; }
        public int TemplateId { get; set; }
        public int MilestoneNumber { get; set; }
        public int ItemSlot { get; set; }
        public int ItemId { get; set; }
        public int ItemAmount { get; set; }
    }

}