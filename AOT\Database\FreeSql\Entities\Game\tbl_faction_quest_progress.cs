﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_faction_quest_progress {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_faction_quest_progress_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? factionid { get; set; }

		[JsonProperty]
		public int? questid { get; set; }

		[JsonProperty]
		public int? currentcount { get; set; }

		[JsonProperty]
		public short? status { get; set; }

		[JsonProperty]
		public DateTime? acceptedtime { get; set; }

		[JsonProperty]
		public DateTime? lastupdatetime { get; set; }

		[JsonProperty]
		public DateTime? completedtime { get; set; }

		[JsonProperty]
		public DateTime? cancelledtime { get; set; }

		[JsonProperty]
		public DateTime? lastresettime { get; set; }

		[JsonProperty]
		public DateTime? lastcompletedtime { get; set; }

		/// <summary>
		/// JSON string chứa danh sách đóng góp của người chơi
		/// </summary>
		[JsonProperty, Column(StringLength = -2)]
		public string contributionsjson { get; set; } = "\'[]\'::text";

		/// <summary>
		/// Số lần hoàn thành quest này
		/// </summary>
		[JsonProperty]
		public int? completecount { get; set; } = 0;

		/// <summary>
		/// Thời gian reset quest cuối cùng
		/// </summary>
		[JsonProperty]
		public DateTime? resettime { get; set; }

	}

}
