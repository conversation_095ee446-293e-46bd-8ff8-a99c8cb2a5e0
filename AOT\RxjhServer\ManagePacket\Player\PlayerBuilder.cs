

using System;
using RxjhServer.HelperTools;

namespace RxjhServer.PacketBuilder.Player
{
    public static class PlayerBuilder
    {
        public static byte[] CreateGeneralDeathPacket(Players player)
        {
            var packet = Converter.HexStringToByte("AA552000F80488001C00F804000002000100000000000100000000000000020001000000000055AA");

            // <PERSON><PERSON><PERSON> tra có pill hay không để thiết lập packet
            if (player.PublicDrugs != null)
            {
                var hasReviveItem = !player.KiemTra_Phu() && !player.KiemTra_Phu2();
                Buffer.BlockCopy(BitConverter.GetBytes(hasReviveItem ? 1 : 2), 0, packet, 30, 2);
            }

            Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, packet, 4, 2);
            return packet;
        }
            
    }
   
}