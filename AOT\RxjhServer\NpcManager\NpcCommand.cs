using System;
using System.Text;
using HeroYulgang.Helpers;
using RxjhServer.AOI;
using static RxjhServer.NpcManager.NpcManager;

namespace RxjhServer
{
    /// <summary>
    /// Lớp chứa các command để quản lý và kiểm tra NPC
    /// </summary>
    public partial class Players
    {
        /// <summary>
        /// Xử lý các command liên quan đến NPC
        /// </summary>
        /// <param name="command">Command đầy đủ</param>
        /// <param name="args">M<PERSON>ng tham số</param>
        /// <returns>True nếu command được xử lý thành công</returns>
        public bool HandleNpcCommand(string command, string[] args)
        {
            try
            {
                switch (args[0].ToLower())
                {
                    case "!npcinfo":
                        return HandleNpcInfo(args);
                    case "!npchelp":
                        return HandleNpcHelp();
                    case "!npcconfig":
                        return HandleNpcConfig();
                    case "!npcbehavior":
                        return HandleNpcBehavior(args);
                    case "!npcreset":
                        return HandleNpcReset();
                    case "!npcmove":
                        return HandleNpcMove(args);

                    default:
                        HeThongNhacNho("Command NPC không hợp lệ! Sử dụng !npchelp để xem danh sách command.", 7, "NPC");
                        return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi xử lý NPC command: {ex.Message}");
                HeThongNhacNho("Có lỗi xảy ra khi xử lý command NPC!", 7, "NPC");
                return false;
            }
        }
        public bool HandleNpcMove(string[] args)
        {
            if (args.Length < 3)
            {
                HeThongNhacNho("Sử dụng: !npcmove [x] [y] [type] [speed]", 7, "NPC");
                return false;
            }
            if (CurrentTarget == 0)
            {
                HeThongNhacNho("Bạn phải chọn mục tiêu trước khi di chuyển NPC!", 7, "NPC");
                return false;
            }


            if (!float.TryParse(args[1], out float x))
            {
                HeThongNhacNho("Vị trí X phải là số!", 7, "NPC");
                return false;
            }

            if (!float.TryParse(args[2], out float y))
            {
                HeThongNhacNho("Vị trí Y phải là số!", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[3], out var type))
            {
                HeThongNhacNho("Loại di chuyển không hợp lệ! Sử dụng: Idle, Patrol, Chase, Attack", 7, "NPC");
                return false;
            }
            if (!int.TryParse(args[4], out var speed))
            {
                HeThongNhacNho("Tốc độ di chuyển phải là số!", 7, "NPC");
                return false;
            }

            // Tìm NPC trong danh sách NPC của player
            if (NearbyNpcs.TryGetValue(CurrentTarget, out NpcClass npc))
            {
                // Di chuyển NPC
                npc.Rxjh_X += x;
                npc.Rxjh_Y += y;

                // Cập nhật vị trí trong AOI system
                if (true) // AOI system enabled
                {
                    AOISystem.Instance.UpdateNPCPosition(npc, npc.Rxjh_X, npc.Rxjh_Y);
                }

                // Gửi thông báo di chuyển cho các player trong tầm nhìn
                npc.SendMovingData(npc.Rxjh_X, npc.Rxjh_Y, type, speed);

                HeThongNhacNho($"Đã di chuyển NPC {npc.Name} (SessionID: {npc.NPC_SessionID}) đến ({x:F1}, {y:F1})", 7, "NPC");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {CurrentTarget}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hiển thị thông tin chi tiết của một NPC
        /// Sử dụng: !npcinfo [sessionID]
        /// </summary>
        private bool HandleNpcInfo(string[] args)
        {
            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcinfo [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            // Tìm NPC trong danh sách NPC của player
            if (World.NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                var info = new StringBuilder();
                info.AppendLine($"Thông tin NPC:");
                info.AppendLine($"ID: {npc.FLD_PID}");
                info.AppendLine($"Session ID: {npc.NPC_SessionID}");
                info.AppendLine($"Tên: {npc.Name}");
                info.AppendLine($"Level: {npc.Level}");
                info.AppendLine($"HP: {npc.Rxjh_HP}/{npc.Max_Rxjh_HP}");
                info.AppendLine($"Vị trí: ({npc.Rxjh_X:F1}, {npc.Rxjh_Y:F1}, {npc.Rxjh_Z:F1})");
                info.AppendLine($"Map: {npc.Rxjh_Map}");
                info.AppendLine($"Trạng thái: {(npc.NPCDeath ? "Đã chết" : "Còn sống")}");
                info.AppendLine($"Loại: {(npc.IsNpc == 1 ? "NPC" : "Monster")}");
                info.AppendLine($"Boss: {(npc.FLD_BOSS == 1 ? "Có" : "Không")}");

                if (npc.NPCDeath && npc.timeNpcRevival != DateTime.MinValue)
                {
                    var timeLeft = npc.timeNpcRevival - DateTime.Now;
                    if (timeLeft.TotalSeconds > 0)
                    {
                        info.AppendLine($"• Hồi sinh sau: {timeLeft.TotalSeconds:F0} giây");
                    }
                }

                HeThongNhacNho(info.ToString(), 7, "NPC Info");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

     

        /// <summary>
        /// Hiển thị hướng dẫn sử dụng các command NPC
        /// </summary>
        private bool HandleNpcHelp()
        {
            var help = new StringBuilder();
            help.AppendLine("Hướng dẫn sử dụng NPC Commands:");
            help.AppendLine("─────────────────────────────────");
            help.AppendLine("!npcinfo [sessionID] - Xem thông tin NPC");
            help.AppendLine("!npcconfig - Xem cấu hình NpcManager");
            help.AppendLine("!npcbehavior [attack|chase|activity] [value] - Điều chỉnh khoảng cách");
            help.AppendLine("!npcreset - Reset tất cả cấu hình về mặc định");
            help.AppendLine("!npcmove [x] [y] [type] [speed] - Di chuyển NPC");
            help.AppendLine("─────────────────────────────────");
            help.AppendLine("Ví dụ: !npcbehavior attack 100");
            HeThongNhacNho(help.ToString(), 7, "NPC Help");
            return true;
        }

        /// <summary>
        /// Hiển thị cấu hình hiện tại của NpcManager
        /// </summary>
        private bool HandleNpcConfig()
        {
            var config = new StringBuilder();
            var stats = GetStatistics();
            config.AppendLine("Cấu hình NpcManager hiện tại:");
            config.AppendLine("─────────────────────────────────");
            config.AppendLine($"• Attack Range: {ATTACK_RANGE}");
            config.AppendLine($"• Chase Range: {CHASE_RANGE}");
            config.AppendLine($"• Activity Area: {ACTIVITY_AREA}");
            config.AppendLine($"• Return to Spawn Range: {RETURN_TO_SPAWN_RANGE}");
            config.AppendLine($"• Attack Speed: {ATTACK_SPEED_MS}ms");
            config.AppendLine($"• Registered NPCs: {stats.RegisteredNPCs}");
            config.AppendLine($"• Manager Running: {stats.IsRunning}");

            HeThongNhacNho(config.ToString(), 7, "NPC Config");
            return true;
        }

        /// <summary>
        /// Điều chỉnh các khoảng cách behavior của NPC
        /// </summary>
        private bool HandleNpcBehavior(string[] args)
        {
            if (args.Length < 3)
            {
                HeThongNhacNho("Sử dụng: !npcbehavior [attack|chase|activity] [value]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[2], out int value) || value <= 0)
            {
                HeThongNhacNho("Giá trị phải là số nguyên dương!", 7, "NPC");
                return false;
            }

            string type = args[1].ToLower();
            string oldValue = "";

            try
            {
                switch (type)
                {
                    case "attack":
                        oldValue = ATTACK_RANGE.ToString();
                        SetAttackRange(value);
                        HeThongNhacNho($"Đã thay đổi Attack Range: {oldValue} → {value}", 7, "NPC Config");
                        break;

                    case "chase":
                        oldValue = CHASE_RANGE.ToString();
                        SetChaseRange(value);
                        HeThongNhacNho($"Đã thay đổi Chase Range: {oldValue} → {value}", 7, "NPC Config");
                        break;

                    case "activity":
                        oldValue = ACTIVITY_AREA.ToString();
                        SetActivityArea(value);
                        HeThongNhacNho($"Đã thay đổi Activity Area: {oldValue} → {value}", 7, "NPC Config");
                        break;

                    default:
                        HeThongNhacNho("Loại không hợp lệ! Sử dụng: attack, chase, activity", 7, "NPC");
                        return false;
                }

                LogHelper.WriteLine(LogLevel.Info, $"Player {CharacterName} changed NPC {type} range: {oldValue} → {value}");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error changing NPC behavior: {ex.Message}");
                HeThongNhacNho("Có lỗi xảy ra khi thay đổi cấu hình!", 7, "NPC");
                return false;
            }
        }


        /// <summary>
        /// Reset tất cả cấu hình NpcManager về mặc định
        /// </summary>
        private bool HandleNpcReset()
        {
            try
            {
                var oldConfig = $"Attack: {ATTACK_RANGE}, Chase: {CHASE_RANGE}, Activity: {ACTIVITY_AREA}";
                
                ResetToDefaults();

                var newConfig = new StringBuilder();
                newConfig.AppendLine("Đã reset về cấu hình mặc định:");
                newConfig.AppendLine($"• Attack Range: {ATTACK_RANGE}");
                newConfig.AppendLine($"• Chase Range: {CHASE_RANGE}");
                newConfig.AppendLine($"• Activity Area: {ACTIVITY_AREA}");
                newConfig.AppendLine($"• Return to Spawn Range: {RETURN_TO_SPAWN_RANGE}");
                newConfig.AppendLine($"• Attack Speed: {ATTACK_SPEED_MS}ms");

                HeThongNhacNho(newConfig.ToString(), 7, "NPC Reset");
                LogHelper.WriteLine(LogLevel.Info, $"Player {CharacterName} reset NpcManager to defaults (was: {oldConfig})");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error resetting NPC config: {ex.Message}");
                HeThongNhacNho("Có lỗi xảy ra khi reset cấu hình!", 7, "NPC");
                return false;
            }
        }
    }
}
