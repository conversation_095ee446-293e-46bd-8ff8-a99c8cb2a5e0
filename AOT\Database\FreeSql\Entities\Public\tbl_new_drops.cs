using FreeSql.DatabaseModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public
{
    /// <summary>
    /// Enhanced drop system table with decimal rates and flexible source types
    /// </summary>
    [JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
    public partial class tbl_new_drops
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [JsonProperty, Column(IsIdentity = true, IsPrimary = true)]
        public int id { get; set; }

        /// <summary>
        /// Drop source type: 'level_range', 'npc_specific', 'quest_based'
        /// </summary>
        [JsonProperty, Column(StringLength = 20)]
        public string source_type { get; set; }

        /// <summary>
        /// Source value: '1-20', '15100', '789'
        /// </summary>
        [JsonProperty, Column(StringLength = 50)]
        public string source_value { get; set; }

        /// <summary>
        /// Item ID to drop
        /// </summary>
        [JsonProperty]
        public int item_id { get; set; }

        /// <summary>
        /// Drop rate as decimal (0.000001 to 1.000000)
        /// </summary>
        [JsonProperty, Column(DbType = "decimal(8,6)")]
        public decimal drop_rate { get; set; }

        /// <summary>
        /// Minimum quantity to drop
        /// </summary>
        [JsonProperty]
        public int quantity_min { get; set; } = 1;

        /// <summary>
        /// Maximum quantity to drop
        /// </summary>
        [JsonProperty]
        public int quantity_max { get; set; } = 1;

        /// <summary>
        /// Magic property 0
        /// </summary>
        [JsonProperty]
        public int magic0 { get; set; } = 0;

        /// <summary>
        /// Magic property 1
        /// </summary>
        [JsonProperty]
        public int magic1 { get; set; } = 0;

        /// <summary>
        /// Magic property 2
        /// </summary>
        [JsonProperty]
        public int magic2 { get; set; } = 0;

        /// <summary>
        /// Magic property 3
        /// </summary>
        [JsonProperty]
        public int magic3 { get; set; } = 0;

        /// <summary>
        /// Magic property 4
        /// </summary>
        [JsonProperty]
        public int magic4 { get; set; } = 0;

        /// <summary>
        /// Item expiration in days (0 = permanent)
        /// </summary>
        [JsonProperty]
        public int expire_days { get; set; } = 0;

        /// <summary>
        /// Whether this drop rule is active
        /// </summary>
        [JsonProperty]
        public bool is_active { get; set; } = true;

        /// <summary>
        /// Priority for drop processing (higher = more priority)
        /// </summary>
        [JsonProperty]
        public int priority { get; set; } = 100;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        [JsonProperty]
        public DateTime created_at { get; set; } = DateTime.Now;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        [JsonProperty]
        public DateTime updated_at { get; set; } = DateTime.Now;

        /// <summary>
        /// Get drop rate as percentage string for display
        /// </summary>
        [JsonIgnore]
        public string DropRatePercentage => $"{drop_rate * 100:F4}%";

        /// <summary>
        /// Get equivalent old system FLD_PP value
        /// </summary>
        [JsonIgnore]
        public int EquivalentPP => Math.Max(1, Math.Min(8000, (int)(drop_rate * 8000)));

        /// <summary>
        /// Check if this drop rule applies to the given level
        /// </summary>
        public bool AppliesTo(int level)
        {
            if (source_type != "level_range") return false;

            var parts = source_value?.Split('-');
            if (parts?.Length != 2) return false;

            if (int.TryParse(parts[0], out int min) && int.TryParse(parts[1], out int max))
            {
                return level >= min && level <= max;
            }

            return false;
        }

        /// <summary>
        /// Check if this drop rule applies to the given NPC ID
        /// </summary>
        public bool AppliesTo(int npcId, string sourceType)
        {
            if (source_type != sourceType) return false;
            return source_value == npcId.ToString();
        }

        /// <summary>
        /// Check if this drop rule applies to any of the given quest IDs
        /// </summary>
        public bool AppliesToQuests(List<int> questIds)
        {
            if (source_type != "quest_based" || questIds == null || !questIds.Any()) 
                return false;

            if (int.TryParse(source_value, out int questId))
            {
                return questIds.Contains(questId);
            }

            return false;
        }

        /// <summary>
        /// Validate drop rule data
        /// </summary>
        public bool IsValid()
        {
            // Check required fields
            if (string.IsNullOrEmpty(source_type) || string.IsNullOrEmpty(source_value))
                return false;

            // Check source type
            var validSourceTypes = new[] { "level_range", "npc_specific", "quest_based" };
            if (!validSourceTypes.Contains(source_type))
                return false;

            // Check drop rate range
            if (drop_rate < 0.000001m || drop_rate > 1.000000m)
                return false;

            // Check quantities
            if (quantity_min < 1 || quantity_max < quantity_min)
                return false;

            // Check item ID
            if (item_id <= 0)
                return false;

            // Validate source value format for level_range
            if (source_type == "level_range")
            {
                var parts = source_value.Split('-');
                if (parts.Length != 2) return false;
                
                if (!int.TryParse(parts[0], out int min) || !int.TryParse(parts[1], out int max))
                    return false;
                
                if (min < 1 || max < min || max > 300) // Assuming max level 300
                    return false;
            }

            // Validate source value for npc_specific and quest_based
            if (source_type == "npc_specific" || source_type == "quest_based")
            {
                if (!int.TryParse(source_value, out int id) || id <= 0)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Create a copy of this drop rule with modified properties
        /// </summary>
        public tbl_new_drops Clone()
        {
            return new tbl_new_drops
            {
                source_type = this.source_type,
                source_value = this.source_value,
                item_id = this.item_id,
                drop_rate = this.drop_rate,
                quantity_min = this.quantity_min,
                quantity_max = this.quantity_max,
                magic0 = this.magic0,
                magic1 = this.magic1,
                magic2 = this.magic2,
                magic3 = this.magic3,
                magic4 = this.magic4,
                expire_days = this.expire_days,
                is_active = this.is_active,
                priority = this.priority
            };
        }

        public override string ToString()
        {
            return $"NewDrop[{id}]: {source_type}={source_value}, Item={item_id}, Rate={DropRatePercentage}, Priority={priority}";
        }
    }
}
