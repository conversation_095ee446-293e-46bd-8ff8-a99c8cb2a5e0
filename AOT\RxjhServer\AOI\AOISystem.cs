using HeroYulgang.Helpers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace RxjhServer.AOI
{
    public class AOISystem
    {
        #region Singleton
        private static readonly Lazy<AOISystem> _instance = new(() => new AOISystem());
        public static AOISystem Instance => _instance.Value;
        private AOISystem() { }
        #endregion

        #region Constants
        public const int GRID_SIZE = 512;
        public const int AOI_RADIUS = 512;
        public const int GRIDS_PER_DIMENSION = 10;
        public const int MAP_SIZE = 5120;
        public const int HALF_MAP_SIZE = MAP_SIZE / 2;
        #endregion

        #region Data Structures
        private readonly ConcurrentDictionary<int, ConcurrentDictionary<(int, int), Grid>> _mapGrids = new();
        private readonly ConcurrentDictionary<int, (int, int)> _playerGrids = new();
        private readonly ConcurrentDictionary<int, (int, int)> _npcGrids = new();
        private readonly ConcurrentDictionary<long, (int, int)> _itemGrids = new();

        private static readonly Dictionary<int, (float x, float y)> _mapCenters = new()
        {
            { 201, (2560f, 0f) },
            { 301, (-2560f, 0f) }
        };

        public class Grid
        {
            public int X, Y, MapID;
            public readonly ConcurrentDictionary<int, byte> Players = new();
            public readonly ConcurrentDictionary<int, byte> NPCs = new();
            public readonly ConcurrentDictionary<long, byte> Items = new();
            public DateTime LastUpdate = DateTime.Now;
            public bool IsDirty;

            public int TotalEntities => Players.Count + NPCs.Count + Items.Count;

            public void ForEachPlayer(Action<Players> action)
            {
                foreach (var playerID in Players.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        action(player);
                }
            }

            public IEnumerable<Players> GetPlayers()
            {
                foreach (var playerID in Players.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        yield return player;
                }
            }

            public IEnumerable<NpcClass> GetNPCs()
            {
                foreach (var npcID in NPCs.Keys)
                {
                    if (MapClass.GetnpcTemplate(MapID).TryGetValue(npcID, out var npc))
                        yield return npc;
                }
            }

            public IEnumerable<GroundItem> GroundItems
            {
                get
                {
                    foreach (var itemID in Items.Keys)
                    {
                        if (World.GroundItemList.TryGetValue(itemID, out var item))
                            yield return item;
                    }
                }
            }
        }
        #endregion

        #region Grid Operations
        public (int x, int y) GetGridCoords(float posX, float posY, int mapID)
        {
            var center = _mapCenters.GetValueOrDefault(mapID, (0f, 0f));
            float adjustedX = posX - center.Item1;
            float adjustedY = posY - center.Item2;

            int gridX = Math.Clamp((int)((adjustedX + HALF_MAP_SIZE) / GRID_SIZE), 0, GRIDS_PER_DIMENSION - 1);
            int gridY = Math.Clamp((int)((adjustedY + HALF_MAP_SIZE) / GRID_SIZE), 0, GRIDS_PER_DIMENSION - 1);

            return (gridX, gridY);
        }

        private Grid GetOrCreateGrid(int mapID, int gridX, int gridY)
        {
            var mapGrids = _mapGrids.GetOrAdd(mapID, _ => new ConcurrentDictionary<(int, int), Grid>());
            return mapGrids.GetOrAdd((gridX, gridY), _ => new Grid { X = gridX, Y = gridY, MapID = mapID });
        }

        public List<Grid> GetNearbyGrids(int mapID, int centerX, int centerY)
        {
            var grids = new List<Grid>(9);
            var mapGrids = _mapGrids.GetValueOrDefault(mapID);
            if (mapGrids == null) return grids;

            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    int x = centerX + dx;
                    int y = centerY + dy;
                    if (x >= 0 && x < GRIDS_PER_DIMENSION && y >= 0 && y < GRIDS_PER_DIMENSION)
                    {
                        if (mapGrids.TryGetValue((x, y), out var grid))
                            grids.Add(grid);
                    }
                }
            }
            return grids;
        }

        public List<Grid> GetNearbyGrids(int mapID, float posX, float posY)
        {
            var coords = GetGridCoords(posX, posY, mapID);
            return GetNearbyGrids(mapID, coords.Item1, coords.Item2);
        }
        #endregion

        #region Entity Management
        public void AddPlayer(Players player)
        {
            if (player?.Client?.Running != true) return;

            var coords = GetGridCoords(player.PosX, player.PosY, player.MapID);
            var grid = GetOrCreateGrid(player.MapID, coords.Item1, coords.Item2);

            if (grid.Players.TryAdd(player.SessionID, 0))
            {
                _playerGrids[player.SessionID] = coords;
                grid.IsDirty = true;
                UpdatePlayerAOI(player);
            }
        }

        public void RemovePlayer(int sessionID)
        {

            if (_playerGrids.TryRemove(sessionID, out var coords))
            {
                if (_mapGrids.TryGetValue(World.allConnectedChars.GetValueOrDefault(sessionID)?.MapID ?? 0, out var mapGrids))
                {
                    if (mapGrids.TryGetValue(coords, out var grid))
                    {
                        grid.Players.TryRemove(sessionID, out _);
                        grid.IsDirty = true;
                    }
                }
            }
        }

        public void MovePlayer(Players player, float newX, float newY, int map)
        {
            if (player?.Client?.Running != true) return;

            var oldX = player.PosX;
            var oldY = player.PosY;
            var oldMap = player.MapID;
            var newCoords = GetGridCoords(newX, newY, map);
            
            // CRITICAL FIX: Always update position first
            player.PosX = newX;
            player.PosY = newY;
            player.MapID = map;
            
            // Check if player has existing grid tracking
            bool hasOldCoords = _playerGrids.TryGetValue(player.SessionID, out var oldCoords);
            
            if (hasOldCoords && oldCoords != newCoords)
            {
                player.HeThongNhacNho($"Đã di chuyển sang grid mới từ {oldCoords} đến {newCoords}", 10, "AOI");
                // Move to new grid
                var oldGrid = GetOrCreateGrid(oldMap, oldCoords.Item1, oldCoords.Item2);
                var newGrid = GetOrCreateGrid(map, newCoords.Item1, newCoords.Item2);

                oldGrid.Players.TryRemove(player.SessionID, out _);
                newGrid.Players.TryAdd(player.SessionID, 0);
                _playerGrids[player.SessionID] = newCoords;

                oldGrid.IsDirty = newGrid.IsDirty = true;
                // Notify old grid neighbors that player left their view
                UpdateNearbyPlayersAOI(oldMap, oldCoords.Item1, oldCoords.Item2);
                // Notify new grid neighbors that player entered their view  
                UpdateNearbyPlayersAOI(map, newCoords.Item1, newCoords.Item2);
            }
            else
            {
                // CRITICAL FIX: Always ensure player is in correct grid
                if (!hasOldCoords)
                {
                    // Player not tracked yet - add to AOI system
                    player.HeThongNhacNho($"Thêm player vào AOI grid {newCoords}", 10, "AOI");
                    var newGrid = GetOrCreateGrid(map, newCoords.Item1, newCoords.Item2);
                    newGrid.Players.TryAdd(player.SessionID, 0);
                    _playerGrids[player.SessionID] = newCoords;
                    newGrid.IsDirty = true;
                    UpdateNearbyPlayersAOI(map, newCoords.Item1, newCoords.Item2);
                }
                else
                {
                    // Same grid movement - still update grid tracking
                    player.HeThongNhacNho($"Di chuyển trong cùng grid {newCoords}", 10, "AOI");
                    _playerGrids[player.SessionID] = newCoords; // Always update coordinates
                    
                    // Update nearby players for significant movement
                    if (Math.Abs(oldX - newX) > 10 || Math.Abs(oldY - newY) > 10)
                    {
                        UpdateNearbyPlayersAOI(map, newCoords.Item1, newCoords.Item2);
                    }
                }
            }
        }

        public void AddNPC(NpcClass npc)
        {
            if (npc == null) return;

            var coords = GetGridCoords(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_Map);
            var grid = GetOrCreateGrid(npc.Rxjh_Map, coords.Item1, coords.Item2);

            if (grid.NPCs.TryAdd(npc.NPC_SessionID, 0))
            {
                _npcGrids[npc.NPC_SessionID] = coords;
                grid.IsDirty = true;
                UpdateNearbyPlayersAOI(npc.Rxjh_Map, coords.Item1, coords.Item2);
            }
        }

        public void RemoveNPC(int npcSessionID, int mapID)
        {
            if (_npcGrids.TryRemove(npcSessionID, out var coords))
            {
                if (_mapGrids.TryGetValue(mapID, out var mapGrids) && mapGrids.TryGetValue(coords, out var grid))
                {
                    grid.NPCs.TryRemove(npcSessionID, out _);
                    grid.IsDirty = true;
                    UpdateNearbyPlayersAOI(mapID, coords.Item1, coords.Item2);
                }
            }
        }

        public void AddGroundItem(GroundItem item)
        {
            if (item == null) return;

            var coords = GetGridCoords(item.PosX, item.PosY, item.MapID);
            var grid = GetOrCreateGrid(item.MapID, coords.Item1, coords.Item2);

            if (grid.Items.TryAdd(item.id, 0))
            {
                _itemGrids[item.id] = coords;
                grid.IsDirty = true;
                UpdateNearbyPlayersAOI(item.MapID, coords.Item1, coords.Item2);
            }
        }

        public void RemoveGroundItem(long itemID, int mapID)
        {
            if (_itemGrids.TryRemove(itemID, out var coords))
            {
                if (_mapGrids.TryGetValue(mapID, out var mapGrids) && mapGrids.TryGetValue(coords, out var grid))
                {
                    grid.Items.TryRemove(itemID, out _);
                    grid.IsDirty = true;
                    UpdateNearbyPlayersAOI(mapID, coords.Item1, coords.Item2);
                }
            }
        }

        public void UpdateNPCPosition(NpcClass npc, float newX, float newY)
        {
            if (npc == null) return;

            var newCoords = GetGridCoords(newX, newY, npc.Rxjh_Map);
            if (_npcGrids.TryGetValue(npc.NPC_SessionID, out var oldCoords) && oldCoords != newCoords)
            {
                // Move to new grid
                var oldGrid = GetOrCreateGrid(npc.Rxjh_Map, oldCoords.Item1, oldCoords.Item2);
                var newGrid = GetOrCreateGrid(npc.Rxjh_Map, newCoords.Item1, newCoords.Item2);

                oldGrid.NPCs.TryRemove(npc.NPC_SessionID, out _);
                newGrid.NPCs.TryAdd(npc.NPC_SessionID, 0);
                _npcGrids[npc.NPC_SessionID] = newCoords;

                oldGrid.IsDirty = newGrid.IsDirty = true;
                UpdateNearbyPlayersAOI(npc.Rxjh_Map, newCoords.Item1, newCoords.Item2);
            }

            npc.Rxjh_X = newX;
            npc.Rxjh_Y = newY;
        }

        public void UpdatePlayerPosition(Players player, float newX, float newY) => MovePlayer(player, newX, newY, player.MapID);
        #endregion

        #region AOI Updates
        public void UpdatePlayerAOI(Players player)
        {
            if (player?.Client?.Running != true) return;

            try
            {
                // Chỉ cập nhật cache cho events, không cập nhật toàn bộ visibility
                player.UpdateNearbyPlayersCache();

                // Cập nhật NPC cache nếu cần
                UpdateNPCCache(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI for {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật NPC cache chỉ cho events
        /// </summary>
        private void UpdateNPCCache(Players player)
        {
            try
            {
                if (player?.NearbyNpcs == null) return;

                var coords = GetGridCoords(player.PosX, player.PosY, player.MapID);
                var nearbyGrids = GetNearbyGrids(player.MapID, coords.Item1, coords.Item2);
                var currentVisible = new HashSet<int>();

                // Tìm NPCs hiện tại trong tầm nhìn
                foreach (var grid in nearbyGrids)
                {
                    foreach (var npcID in grid.NPCs.Keys)
                    {
                        if (MapClass.GetnpcTemplate(player.MapID).TryGetValue(npcID, out var npc) &&
                            IsInRange(player.PosX, player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                        {
                            currentVisible.Add(npcID);
                        }
                    }
                }

                // Kiểm tra NPCs cần despawn
                var npcsToDespawn = new Dictionary<int, NpcClass>();
                var toRemove = new List<int>();
                foreach (var kvp in player.NearbyNpcs.ToList())
                {
                    if (!currentVisible.Contains(kvp.Key))
                    {
                        toRemove.Add(kvp.Key);
                        var npc = kvp.Value;
                        if (npc != null)
                        {
                            npcsToDespawn[kvp.Key] = npc;
                        }
                    }
                }

                // Remove despawned NPCs
                foreach (var npcID in toRemove)
                {
                    player.NearbyNpcs.TryRemove(npcID, out _);
                }

                // Kiểm tra NPCs cần spawn
                var npcsToSpawn = new Dictionary<int, NpcClass>();
                foreach (var npcID in currentVisible)
                {
                    if (MapClass.GetnpcTemplate(player.MapID).TryGetValue(npcID, out var npc) &&
                        !player.NearbyNpcs.ContainsKey(npcID))
                    {
                        player.NearbyNpcs.TryAdd(npcID, npc);
                        npcsToSpawn[npcID] = npc;
                    }
                }

                // Trigger spawn/despawn events
                if (npcsToDespawn.Count > 0)
                {
                    NpcClass.UpdateNPC_Despawn(npcsToDespawn, player);
                }
                if (npcsToSpawn.Count > 0)
                {
                    NpcClass.UpdateNPC_Spawn(npcsToSpawn, player);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"UpdateNPCCache error for {player?.CharacterName}: {ex.Message}");
            }
        }
        private void UpdateNearbyPlayersAOI(int mapID, int gridX, int gridY)
        {
            var nearbyGrids = GetNearbyGrids(mapID, gridX, gridY);
            foreach (var grid in nearbyGrids)
            {
                foreach (var playerID in grid.Players.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        UpdatePlayerAOI(player);
                }
            }
        }
        #endregion

        #region Utility
        private bool IsInRange(float x1, float y1, float x2, float y2)
        {
            float dx = x2 - x1, dy = y2 - y1;
            return (dx * dx + dy * dy) <= (AOI_RADIUS * AOI_RADIUS);
        }

        /// <summary>
        /// Public method để kiểm tra khoảng cách cho external usage
        /// </summary>
        public bool IsWithinAOIRange(float x1, float y1, float x2, float y2)
        {
            return IsInRange(x1, y1, x2, y2);
        }

        public void BatchUpdate(List<Players> players)
        {
            foreach (var player in players.Where(p => p?.Client?.Running == true))
                UpdatePlayerAOI(player);
        }

        public int GetEntityCount(int mapID)
        {
            return _mapGrids.GetValueOrDefault(mapID)?.Values.Sum(g => g.TotalEntities) ?? 0;
        }
        #endregion
    }
}