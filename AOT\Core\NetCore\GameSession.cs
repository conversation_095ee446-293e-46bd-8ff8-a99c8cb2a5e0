using System;
using System.Net.Sockets;
using System.Threading;
using NetCoreServer;
using HeroYulgang.Services;
using RxjhServer;
using HeroYulgang.Utils;
using HeroYulgang.Helpers;
using HeroYulgang.Core.Managers;
using System.Collections.Generic;

namespace HeroYulgang.Core.NetCore
{
    /// <summary>
    /// Game session extending NetCoreServer.TcpSession
    /// Handles individual client connections with game-specific functionality
    /// Compatible with existing packet format and encryption
    /// </summary>
    public class GameSession : TcpSession
    {
        #region Fields

        private readonly NetCoreServerWrapper _server;
        private readonly PacketProcessor _packetProcessor;
        private volatile bool _disposed = false;

        // Player state - compatible with existing system
        private string _account = string.Empty;
        private string _characterName = string.Empty;
        private int _connectionId = 0; // ConnectionID cho network layer
        private int _clusterId = 0;
        private int _channelId = 0;

        public bool IsAuthenticated { get; set; } = false;
        public bool HasValidatedPacket1375 { get; set; } = false;
        public string? AccountId { get; set; }

        // PlayerSessionID sẽ được cấp sau khi xác thực thành công
        private int? _playerSessionId = null;
        private DateTime _registerTime = DateTime.UtcNow;
        private DateTime _lastHeartbeat = DateTime.UtcNow;
        private uint _userId = 0;
        private bool _loginStatus = false;

        public bool BindAccount = false;

        // Statistics
        private long _packetsReceived = 0;
        private long _packetsSent = 0;
        private long _bytesReceived = 0;
        private long _bytesSent = 0;

        #endregion

        #region Properties

        public string Account => _account;
        public string CharacterName => _characterName;
        public int ConnectionId => _connectionId;
        public int ClusterId => _clusterId;
        public int ChannelId => _channelId;

        // PlayerSessionID properties
        public int? PlayerSessionId => _playerSessionId;
        public bool HasPlayerSession => _playerSessionId.HasValue;

        // Compatibility properties
        [Obsolete("Use ConnectionId for network operations, PlayerSessionId for game operations")]
        public int SessionID => _connectionId;
        [Obsolete("Use ConnectionId for network operations, PlayerSessionId for game operations")]
        public int SessionId => _connectionId;
        public DateTime RegisterTime => _registerTime;
        public DateTime LastHeartbeat => _lastHeartbeat;
        public uint UserId => _userId;
        public bool LoginStatus => _loginStatus;

        // Player reference (will be set by game logic)
        public Players Player { get; set; }

        // Additional properties
        public byte[] g_cur_key { get; set; }
        public bool ThoatGame { get; set; } = false;
        public bool TreoMay { get; set; } = false;

        public bool IsActive => IsConnected && !_disposed;
        public bool VersionVerification { get; set; } = false;
        // public bool Login => _loginStatus;
        public bool Login
        {
            get => _loginStatus;
            set => _loginStatus = value;
        }
        public bool Online
        {
            get;
            set;
        } = false;
        // Statistics
        public long PacketsReceived => _packetsReceived;
        public long PacketsSent => _packetsSent;
        public new long BytesReceived => _bytesReceived;
        public new long BytesSent => _bytesSent;

        public bool Running = false;

        #endregion

        #region PlayerSessionID Management

        /// <summary>
        /// Cấp PlayerSessionID sau khi xác thực thành công
        /// </summary>
        /// <returns>PlayerSessionID đã được cấp hoặc -1 nếu thất bại</returns>
        public int AllocatePlayerSessionId()
        {
            if (HasPlayerSession)
            {
                return _playerSessionId.Value; // Đã có rồi
            }

            int playerSessionId = SessionIdManager.Instance.AllocatePlayerSessionId();
            if (playerSessionId != -1)
            {
                _playerSessionId = playerSessionId;
                Logger.Instance.Info($"Allocated PlayerSessionID {playerSessionId} for ConnectionID {_connectionId}");
                return playerSessionId;
            }

            Logger.Instance.Error($"Failed to allocate PlayerSessionID for ConnectionID {_connectionId}");
            return -1; // Thất bại
        }

        /// <summary>
        /// Giải phóng PlayerSessionID khi ngắt kết nối
        /// </summary>
        public void ReleasePlayerSessionId()
        {
            if (HasPlayerSession)
            {
                SessionIdManager.Instance.ReleaseSessionId(_playerSessionId.Value);
                Logger.Instance.Info($"Released PlayerSessionID {_playerSessionId.Value} for ConnectionID {_connectionId}");
                _playerSessionId = null;
            }
        }

        #endregion

        #region Constructor

        public GameSession(NetCoreServerWrapper server, PacketProcessor packetProcessor) : base(server)
        {
            _server = server ?? throw new ArgumentNullException(nameof(server));
            _packetProcessor = packetProcessor ?? throw new ArgumentNullException(nameof(packetProcessor));

            // Allocate ConnectionID from ConnectionIdManager (không phải PlayerSessionID)
            _connectionId = ConnectionIdManager.Instance.AllocateConnectionId();
            Logger.Instance.Debug($"GameSession {Id} created with ConnectionId {_connectionId}");

            // PlayerSessionID sẽ được cấp sau khi xác thực thành công
        }

        #endregion

        #region Public Methods
        /// <summary>
        /// Set character name
        /// </summary>
        public void SetCharacterName(string characterName)
        {
            _characterName = characterName ?? string.Empty;
            Logger.Instance.Debug($"Session {Id} character name set: {characterName}");
        }

        /// <summary>
        /// Set login status
        /// </summary>
        public void SetLoginStatus(bool status)
        {
            _loginStatus = status;
            Login = status;
            Logger.Instance.Debug($"Session {Id} login status: {status}");
        }

        /// <summary>
        /// Update heartbeat timestamp
        /// </summary>
        public void UpdateHeartbeat()
        {
            _lastHeartbeat = DateTime.UtcNow;
        }

         public void SendPacket(byte[] data, int length)
        {
            try
            {
                // Mã hóa dữ liệu trước khi gửi
                var encryptedData = Crypto.EncryptPacket(data);

                // Gửi dữ liệu thông qua NetCoreServer
                Send(encryptedData, 0, encryptedData.Length);

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_connectionId}: {ex.Message}");
            }
        }

        public void SendSinglePackage(byte[] toSendBuff, int len)
        {
            var array = new byte[BitConverter.ToInt16(toSendBuff, 9) + 7];
            System.Buffer.BlockCopy(toSendBuff, 5, array, 0, array.Length);
            SendSinglePackage_PackageTransmit(array, array.Length);
        }

        private void SendSinglePackage_PackageTransmit(byte[] toSendBuff, int length)
        {
            var array = new byte[length + 15];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(length + 9), 0, array, 2, 2);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 5, length);
            array[array.Length - 2] = 85;
            array[array.Length - 1] = 170;
            SendPacket(array, array.Length);
        }

        public void Send_Map_Data(byte[] byte_0, int int_1)
        {
            try
            {
                var array = new byte[int_1 + 2];
                array[0] = 170;
                array[1] = 85;
                System.Buffer.BlockCopy(BitConverter.GetBytes(int_1 - 4), 0, array, 2, 2);
                System.Buffer.BlockCopy(byte_0, 4, array, 4, 2);
                System.Buffer.BlockCopy(byte_0, 6, array, 8, int_1 - 8);
                array[^2] = 85;
                array[^1] = 170;
                SendPacket(array, array.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Send_Map_Data Error {_connectionId}: {ex.Message}");
            }
        }
        public void SendPakLE(SendingClass packetDataClass, int id, int worldId)
        {
            try
            {
                var array = packetDataClass.ToArray2(id, worldId);
                System.Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 4, 2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(packetDataClass.Length - 6), 0, array, 6, 2);
                SendMultiplePackageEncryptionByPass(array, array.Length, 1);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi SendPakLE {_connectionId}: {ex.Message}");
            }
        }

        public void SendPak(SendingClass pak, int id, int wordid, bool bypass = false, bool enableBatching = false, int sourceNpcId = 0)
        {
            try
            {
                

                // Fallback to normal sending
                var array = pak.ToArray2(id, wordid);
                if (bypass)
                   SendMultiplePackageEncryptionByPass(array, array.Length, 1);
                else
                   SendMultiplePackageEncryptionNew(array, array.Length, 1);

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendPak error for session {_connectionId}: {ex.Message}");
            }
        }

        private void SendMultiplePackageEncryptionByPass(byte[] toSendBuff, int length, int xl)
        {
            try
            {
                var array = new byte[toSendBuff.Length + 6];
                array[0] = 170;
                array[1] = 85;
                System.Buffer.BlockCopy(BitConverter.GetBytes(toSendBuff.Length), 0, array, 2, 2);
                System.Buffer.BlockCopy(toSendBuff, 0, array, 4, toSendBuff.Length);
                array[^2] = 85;
                array[^1] = 170;
                SendPacket(array, array.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_connectionId}: {ex.Message}");
                ///LogHelper.WriteLine(LogLevel.Error, "Send()_SendMultiplePackageEncryption" + WorldId + "|" + ex.Message);
            }
        }
        private void SendMultiplePackageEncryption(byte[] toSendBuff, int length, int xl)
        {

            try
            {
                int num = SendDuopak3(toSendBuff, length, out byte[] packetBuffer);
                byte[] array = new byte[num + 8];

                System.Buffer.BlockCopy(BitConverter.GetBytes(588), 0, array, 0, 2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(num + 4), 0, array, 2, 2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 4, 2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(length), 0, array, 6, 2);
                System.Buffer.BlockCopy(packetBuffer, 0, array, 8, num);

                SendMultiplePackage_PackageTransmit(array, array.Length, xl);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_connectionId}: {ex.Message}");
            }
        }
        private void SendMultiplePackage_PackageTransmit(byte[] toSendBuff, int length, int int_0)
        {
            var array = new byte[length + 16];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(length + 10), 0, array, 2, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(int_0), 0, array, 4, 2);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 6, length);
            array[array.Length - 2] = 85;
            array[array.Length - 1] = 170;
            Send_Map_Data(array, array.Length);
        }

        public void SendMultiplePackage(byte[] toSendBuff, int len)
        {
            Send_Map_Data(toSendBuff, len);
        }

        private int SendDuopak3(byte[] 进包, int 进包长, out byte[] packetBuffer)
        {
            packetBuffer = new byte[进包长 + 进包长 / 32 + 1];
            var 出包长 = 0;
            var 进包处理长 = 0;
            var i = 6;
            var num = 5;
            var j = 0;
            var num2 = 0;
            var num3 = 0;
            try
            {
                for (; i <= 进包长; i++)
                {
                    while (num > 1 && i - num < 255)
                    {
                        for (; j < 255 && num + j < 进包长 && i + j < 进包长; j++)
                        {
                            if (进包[i + j] != 进包[num + j])
                            {
                                if (num2 >= 3)
                                {
                                    if (进包[i] == 进包[num - 1] && i - num < 254)
                                    {
                                        num3 = 1;
                                        break;
                                    }
                                    OneByte(i, num, j, num2, 进包, ref 进包处理长, ref 出包长, ref packetBuffer);
                                    i = 进包处理长;
                                    num = 进包处理长;
                                    num3 = 0;
                                    num2 = 0;
                                }
                                else
                                {
                                    num2 = 0;
                                }
                                break;
                            }
                            num2++;
                        }
                        if (num2 >= 3 && (进包[i] != 进包[num - 1] || num3 == 0))
                        {
                            OneByte(i, num, j, num2, 进包, ref 进包处理长, ref 出包长, ref packetBuffer);
                            i = 进包处理长;
                            num = 进包处理长;
                            num3 = 0;
                        }
                        j = 0;
                        num2 = 0;
                        num--;
                    }
                    num = i;
                }
                if (进包处理长 >= 进包长)
                {
                    return 出包长;
                }

                var num4 = 进包长 - 进包处理长;
                if (num4 <= 32)
                {
                    if (num4 <= 0)
                    {
                        return 出包长;
                    }
                    packetBuffer[出包长++] = (byte)(进包长 - 进包处理长 - 1);
                    System.Buffer.BlockCopy(进包, 进包处理长, packetBuffer, 出包长, 进包长 - 进包处理长);
                    出包长 += 进包长 - 进包处理长;
                    return 出包长;
                }

                var outlength = 0;
                var array = new byte[num4];
                System.Buffer.BlockCopy(进包, 进包处理长, array, 0, array.Length);
                var array2 = SendDuopak(array, array.Length, out outlength);
                if (出包长 == 0)
                {
                    packetBuffer = new byte[array2.Length];
                }
                System.Buffer.BlockCopy(array2, 0, packetBuffer, 出包长, outlength);
                出包长 += outlength;
                return 出包长;
            }
            catch (Exception value)
            {
                Console.WriteLine(value);
                return 出包长;
            }
        }

        private void OneByte(int j, int i, int n, int x, byte[] 进包, ref int 进包处理长, ref int 出包长, ref byte[] packetBuffer)
        {
            var num = j - 进包处理长;
            if (num > 32)
            {
                var outlength = 0;
                var array = new byte[num];
                System.Buffer.BlockCopy(进包, 进包处理长, array, 0, array.Length);
                System.Buffer.BlockCopy(SendDuopak(array, array.Length, out outlength), 0, packetBuffer, 出包长, outlength);
                出包长 += outlength;
                进包处理长 += num;
            }
            else if (num > 0)
            {
                packetBuffer[出包长++] = (byte)(num - 1);
                System.Buffer.BlockCopy(进包, 进包处理长, packetBuffer, 出包长, j - 进包处理长);
                出包长 += j - 进包处理长;
                进包处理长 += j - 进包处理长;
            }

            var num2 = x - 2;
            if (num2 < 7)
            {
                packetBuffer[出包长++] = (byte)(num2 << 5);
                var num3 = j - i - 1;
                packetBuffer[出包长++] = (byte)num3;
            }
            else
            {
                packetBuffer[出包长++] = 224;
                var num4 = x - 2 - 7;
                packetBuffer[出包长++] = (byte)num4;
                var num5 = j - i - 1;
                packetBuffer[出包长++] = (byte)num5;
            }
            进包处理长 += x;
        }

        private byte[] SendDuopak(byte[] toSendBuff, int length, out int outlength)
        {
            try
            {
                var num = 10;
                var num2 = length / 10;
                while (length - num2 * num > 0 && length - num2 * num < 2)
                {
                    num++;
                    num2 = length / num;
                }
                if (length % num > 0)
                {
                    num2++;
                }

                var array = new byte[length + num2];
                var num3 = 0;
                var num4 = 0;
                var num5 = num;
                do
                {
                    if (num3 + num5 < length)
                    {
                        array[num4] = (byte)(num5 - 1);
                        System.Buffer.BlockCopy(toSendBuff, num3, array, num4 + 1, num5);
                        num3 += num5;
                        num4 += num5 + 1;
                        num5 = num;
                    }
                    else
                    {
                        num5 = length - num3;
                        array[num4] = (byte)(num5 - 1);
                        System.Buffer.BlockCopy(toSendBuff, num3, array, num4 + 1, num5);
                        num3 += num5;
                        num4 += num5 + 1;
                    }
                }
                while (num3 < length);
                outlength = num4;
                return array;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, "Send Send Duopak" + SessionID + "|" + ex.Message); //LogHelper.WriteLine(LogLevel.Error, "Send()SendDuopak" + WorldId + "|" + ex.Message);
                outlength = length;
                return toSendBuff;
            }
        }

        /// <summary>
        /// Check if session is inactive (for cleanup)
        /// </summary>
        public bool IsInactive(TimeSpan timeout)
        {
            return DateTime.UtcNow - _lastHeartbeat > timeout;
        }


        #endregion
        #region New Packet Compression System

        /// <summary>
        /// Check if opcode is a compressed packet opcode
        /// These opcodes indicate the packet contains compressed data
        /// Based on: if ( word_58EBE54 >= 576 && word_58EBE54 <= 638 ) || word_58EBE54 == 192
        /// </summary>
        /// <param name="opcode">Opcode to check</param>
        /// <returns>True if this is a compressed packet opcode</returns>
        private bool IsCompressedPacketOpcode(int opcode)
        {
            return (opcode >= 576 && opcode <= 638) || opcode == 192;
        }

        /// <summary>
        /// New LZ77-like compression algorithm based on reverse engineering
        /// </summary>
        /// <param name="data">Data to compress</param>
        /// <returns>Compressed data</returns>
        private byte[] CompressLZ77(byte[] data)
        {
            if (data == null || data.Length == 0)
                return new byte[0];

            var output = new List<byte>();
            int inputPos = 0;

            while (inputPos < data.Length)
            {
                // Look for back references
                int bestLength = 0;
                int bestOffset = 0;

                // Search for matches in previous data (up to 8191 bytes back)
                int searchStart = Math.Max(0, inputPos - 8191);

                for (int offset = 1; offset <= Math.Min(inputPos - searchStart, 8191); offset++)
                {
                    if (inputPos - offset < 0) continue;

                    int matchLength = 0;
                    int maxLength = Math.Min(data.Length - inputPos, 264); // Max length with extended encoding

                    while (matchLength < maxLength &&
                           inputPos + matchLength < data.Length &&
                           data[inputPos + matchLength] == data[inputPos - offset + matchLength])
                    {
                        matchLength++;
                    }

                    if (matchLength > bestLength && matchLength >= 3) // Minimum match length
                    {
                        bestLength = matchLength;
                        bestOffset = offset;
                    }
                }

                if (bestLength >= 3)
                {
                    // Encode back reference
                    int length = bestLength - 2; // Subtract 2 as per algorithm
                    int offset = bestOffset - 1; // Subtract 1 as per algorithm

                    if (length >= 7)
                    {
                        // Extended length encoding
                        byte controlByte = (byte)((7 << 5) | ((offset >> 8) & 0x1F));
                        output.Add(controlByte);
                        output.Add((byte)(length - 7));
                        output.Add((byte)(offset & 0xFF));
                    }
                    else
                    {
                        // Normal encoding
                        byte controlByte = (byte)((length << 5) | ((offset >> 8) & 0x1F));
                        output.Add(controlByte);
                        output.Add((byte)(offset & 0xFF));
                    }

                    inputPos += bestLength;
                }
                else
                {
                    // Find literal run
                    int literalStart = inputPos;
                    int literalLength = 0;

                    // Look ahead to find optimal literal length
                    while (inputPos < data.Length && literalLength < 32)
                    {
                        // Check if we should start a back reference instead
                        if (literalLength >= 1) // Allow at least 1 literal
                        {
                            bool foundMatch = false;
                            for (int offset = 1; offset <= Math.Min(inputPos - searchStart, 8191); offset++)
                            {
                                if (inputPos - offset < 0) continue;
                                int matchLength = 0;
                                while (matchLength < 10 &&
                                       inputPos + matchLength < data.Length &&
                                       data[inputPos + matchLength] == data[inputPos - offset + matchLength])
                                {
                                    matchLength++;
                                }
                                if (matchLength >= 3)
                                {
                                    foundMatch = true;
                                    break;
                                }
                            }
                            if (foundMatch) break;
                        }

                        literalLength++;
                        inputPos++;
                    }

                    // Encode literals
                    byte controlByte = (byte)(literalLength - 1); // 0-31 range
                    output.Add(controlByte);

                    for (int i = 0; i < literalLength; i++)
                    {
                        output.Add(data[literalStart + i]);
                    }
                }
            }

            return output.ToArray();
        }

        /// <summary>
        /// Create compressed packet following the exact structure from existing SendMultiplePackageEncryption
        /// </summary>
        /// <param name="packets">List of individual packets to compress</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <returns>Complete compressed packet with proper headers</returns>
        private byte[] CreateCompressedPacket(List<byte[]> packets, int compressionOpcode = 587)
        {
            if (packets == null || packets.Count == 0)
                return new byte[0];

            // Concatenate all packets
            var combinedData = new List<byte>();
            foreach (var packet in packets)
            {
                combinedData.AddRange(packet);
            }

            var combinedArray = combinedData.ToArray();

            // Compress the combined data
            var compressedData = CompressLZ77(combinedArray);

            // Create compressed packet following SendMultiplePackageEncryption structure:
            // Offset 0-1: compression opcode (587, 588, etc.) - little endian
            // Offset 2-3: compressed length + 4 - little endian
            // Offset 4-5: compressed length - little endian
            // Offset 6-7: original length - little endian
            // Offset 8+: compressed data

            var result = new byte[compressedData.Length + 8];

            // Compression opcode (little endian)
            System.Buffer.BlockCopy(BitConverter.GetBytes((ushort)compressionOpcode), 0, result, 0, 2);

            // Compressed length + 4 (little endian)
            System.Buffer.BlockCopy(BitConverter.GetBytes((ushort)(compressedData.Length + 4)), 0, result, 2, 2);

            // Compressed length (little endian)
            System.Buffer.BlockCopy(BitConverter.GetBytes((ushort)compressedData.Length), 0, result, 4, 2);

            // Original length (little endian)
            System.Buffer.BlockCopy(BitConverter.GetBytes((ushort)combinedArray.Length), 0, result, 6, 2);

            // Compressed data
            System.Buffer.BlockCopy(compressedData, 0, result, 8, compressedData.Length);

            return result;
        }

        /// <summary>
        /// New packet compression function that can handle single or multiple packets
        /// Replaces the old SendDuopak3 function
        /// </summary>
        /// <param name="packets">Array of packets to compress (can have any opcodes)</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <returns>Compressed packet data with specified compression opcode</returns>
        public byte[] CompressPackets(byte[][] packets, int compressionOpcode = 587)
        {
            try
            {
                if (packets == null || packets.Length == 0)
                    return new byte[0];

                // Validate compression opcode
                if (!IsCompressedPacketOpcode(compressionOpcode))
                {
                    LogHelper.WriteLine(LogLevel.Warning,
                        $"Invalid compression opcode {compressionOpcode}. Using default 587.");
                    compressionOpcode = 587;
                }

                // Convert to List for easier handling
                var packetList = new List<byte[]>(packets);

                // Create compressed packet with specified compression opcode
                return CreateCompressedPacket(packetList, compressionOpcode);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Compression error in session {_connectionId}: {ex.Message}");
                // Return first packet as fallback
                return packets.Length > 0 ? packets[0] : new byte[0];
            }
        }

        /// <summary>
        /// Overload for single packet compression
        /// </summary>
        /// <param name="packet">Single packet to compress</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <returns>Compressed packet data</returns>
        public byte[] CompressPacket(byte[] packet, int compressionOpcode = 587)
        {
            return CompressPackets(new byte[][] { packet }, compressionOpcode);
        }

        /// <summary>
        /// Enhanced SendMultiplePackageEncryption using new compression
        /// </summary>
        /// <param name="toSendBuff">Buffer to send</param>
        /// <param name="length">Length of buffer</param>
        /// <param name="xl">Additional parameter</param>
        private void SendMultiplePackageEncryptionNew(byte[] toSendBuff, int length, int xl)
        {
            try
            {
                // Use new compression system
                var compressedPacket = CompressPacket(toSendBuff);

                if (compressedPacket.Length > 0)
                {
                    SendMultiplePackage_PackageTransmit(compressedPacket, compressedPacket.Length, xl);
                }
                else
                {
                    // Fallback to old method if compression fails
                    SendMultiplePackageEncryption(toSendBuff, length, xl);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"New compression error in session {_connectionId}: {ex.Message}");
                // Fallback to old method
                SendMultiplePackageEncryption(toSendBuff, length, xl);
            }
        }

        /// <summary>
        /// Helper method to send compressed packets using new compression system
        /// Can be used as a drop-in replacement for SendPak with compression
        /// </summary>
        /// <param name="packets">Array of SendingClass packets to compress and send</param>
        /// <param name="originalOpcodes">Array of original opcodes for each packet</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <param name="worldId">World ID</param>
        public void SendCompressedPackets(SendingClass[] packets, int[] originalOpcodes, int compressionOpcode = 587, int worldId = 0)
        {
            try
            {
                if (packets == null || packets.Length == 0)
                    return;

                if (originalOpcodes == null || originalOpcodes.Length != packets.Length)
                {
                    LogHelper.WriteLine(LogLevel.Error, "Original opcodes array must match packets array length");
                    return;
                }

                // Convert SendingClass to byte arrays using their original opcodes
                var packetBytes = new List<byte[]>();
                for (int i = 0; i < packets.Length; i++)
                {
                    var bytes = packets[i].ToArray2(originalOpcodes[i], worldId);
                    packetBytes.Add(bytes);
                }

                // Compress packets with specified compression opcode
                var compressedData = CompressPackets(packetBytes.ToArray(), compressionOpcode);

                if (compressedData.Length > 0)
                {
                    // Send compressed packet using the proper transmission method
                    SendMultiplePackage_PackageTransmit(compressedData, compressedData.Length, 1);
                }
                else
                {
                    // Fallback: send individual packets
                    for (int i = 0; i < packets.Length; i++)
                    {
                        SendPak(packets[i], originalOpcodes[i], worldId);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendCompressedPackets error in session {_connectionId}: {ex.Message}");

                // Fallback: send individual packets
                for (int i = 0; i < packets.Length; i++)
                {
                    try
                    {
                        SendPak(packets[i], originalOpcodes[i], worldId);
                    }
                    catch (Exception innerEx)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Fallback send error: {innerEx.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// Helper method to send a single compressed packet
        /// </summary>
        /// <param name="packet">SendingClass packet to compress and send</param>
        /// <param name="originalOpcode">Original opcode for the packet</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <param name="worldId">World ID</param>
        public void SendCompressedPacket(SendingClass packet, int originalOpcode, int compressionOpcode = 587, int worldId = 0)
        {
            SendCompressedPackets(new SendingClass[] { packet }, new int[] { originalOpcode }, compressionOpcode, worldId);
        }

        /// <summary>
        /// Example usage method showing how to use the new compression system
        /// </summary>
        public void ExampleUsage()
        {
            // Example 1: Compress single packet with original opcode 100, compress to opcode 587
            var singlePacket = new SendingClass();
            singlePacket.Write2(100); // Some data
            SendCompressedPacket(singlePacket, 100, 587, _connectionId); // original=100, compressed=587

            // Example 2: Compress multiple packets with different original opcodes
            var packet1 = new SendingClass();
            packet1.Write2(200);

            var packet2 = new SendingClass();
            packet2.Write2(300);

            // Original opcodes: 200, 300 -> Compressed opcode: 587
            SendCompressedPackets(
                new SendingClass[] { packet1, packet2 },
                new int[] { 200, 300 }, // original opcodes
                587, // compression opcode
                _connectionId);

            // Example 3: Direct byte array compression
            byte[] rawData1 = { 0x01, 0x02, 0x03, 0x04 };
            byte[] rawData2 = { 0x05, 0x06, 0x07, 0x08 };

            // Compress with opcode 587
            var compressedBytes = CompressPackets(new byte[][] { rawData1, rawData2 }, 587);
            if (compressedBytes.Length > 0)
            {
                SendMultiplePackage_PackageTransmit(compressedBytes, compressedBytes.Length, 1);
            }

            // Example 4: Using different compression opcodes
            var packet3 = new SendingClass();
            packet3.Write2(400);

            // Compress with opcode 588 instead of 587
            SendCompressedPacket(packet3, 400, 588, _connectionId);
        }

        #endregion

        #region NetCoreServer Overrides

        protected override void OnConnected()
        {
            Logger.Instance.Info($"GameSession {Id} connected from {Socket.RemoteEndPoint}");

            // Update heartbeat
            UpdateHeartbeat();

            // Send welcome message or initial handshake if needed
            // This can be customized based on game requirements
        }

        protected override void OnDisconnected()
        {
            try
            {
                // Giải phóng ConnectionID
                ConnectionIdManager.Instance.ReleaseConnectionId(_connectionId);

                // Xử lý player logout nếu có PlayerSessionID
                if (HasPlayerSession)
                {
                    var playerSessionId = _playerSessionId.Value;

                    if (Player != null)
                    {
                        try
                        {
                            Player.Logout();
                            World.allConnectedChars.TryRemove(playerSessionId, out _);
                        }
                        catch (Exception ex)
                        {
                            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi logout player {Player.CharacterName}: {ex.Message}");
                        }
                    }

                    // Giải phóng PlayerSessionID
                    ReleasePlayerSessionId();
                }

                // Xóa mapping từ ConnectionID đến PlayerSessionID
                World.RemoveConnectionMapping(_connectionId);

                Logger.Instance.Info($"GameSession {Id} disconnected (ConnectionId: {_connectionId}). Account: {_account}, Character: {_characterName}");
                _server.OnSessionDisconnected(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi trong OnDisconnected cho session {Id}: {ex.Message}");
            }
        }

        protected override void OnReceived(byte[] buffer, long offset, long size)
        {
            try
            {
                // Update statistics
                Interlocked.Increment(ref _packetsReceived);
                Interlocked.Add(ref _bytesReceived, size);
                UpdateHeartbeat();

                // Extract packet data
                var packetData = new byte[size];
                Array.Copy(buffer, offset, packetData, 0, size);

                // Process raw data through packet processor (uses Akka's PacketBuffer)
                //decrypt
                packetData = Crypto.DecryptPacket(packetData);
                _packetProcessor.ProcessRawData(this, packetData);

                // Notify server of packet processing
                _server.OnPacketProcessed();

                // Logger.Instance.Debug($"Received packet from session {Id}, size: {size} bytes");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error processing received packet from session {Id}: {ex.Message}");
            }
        }

        protected override void OnSent(long sent, long pending)
        {
            // Optional: Handle sent confirmation
            Logger.Instance.Debug($"Session {Id} sent {sent} bytes, {pending} pending");
        }

        protected override void OnError(SocketError error)
        {
            Logger.Instance.Error($"GameSession {Id} socket error: {error}");
        }

        #endregion

        #region IDisposable

        protected override void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Logger.Instance.Debug($"GameSession {Id} disposing");

                // Release ConnectionID và PlayerSessionID
                ConnectionIdManager.Instance.ReleaseConnectionId(_connectionId);
                if (HasPlayerSession)
                {
                    ReleasePlayerSessionId();
                }

                _disposed = true;
            }

            base.Dispose(disposing);
        }

        #endregion
        internal void Offline()
        {
            // Chỉ xử lý offline nếu có PlayerSessionID
            if (HasPlayerSession)
            {
                var players = World.FindPlayerBySession(PlayerSessionId.Value);
                ThoatGame = true;
                TreoMay = true;
                if (players != null)
                {
                    if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 1)
                    {
                        World.TreoMay_Offline++;
                    }
                    else if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 0)
                    {
                        World.OffLine_SoLuong++;
                    }
                }
            }
        }
    }
}
