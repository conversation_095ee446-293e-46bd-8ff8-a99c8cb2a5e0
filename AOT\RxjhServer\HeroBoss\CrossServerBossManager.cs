using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;


namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Quản lý Boss Cross Server với đồng bộ hoàn chỉnh
    /// </summary>
    public class CrossServerBossManager : IDisposable
    {
        private static CrossServerBossManager _instance;
        private static readonly object _lock = new();
        private bool _isDisposed;

        // Dictionary lưu trữ thông tin boss cross-server
        // Key: BossID, Value: CrossServerBossInfo
        private readonly ConcurrentDictionary<int, CrossServerBossInfo> _crossServerBosses = new();

        // Dictionary lưu trữ contribution từ tất cả servers
        // Key: BossID, Value: Dictionary<(ServerID, SessionID), CrossServerDamageContribute>
        private readonly ConcurrentDictionary<int, ConcurrentDictionary<(int, int), CrossServerDamageContribute>> _globalContributions = new();

        // Dictionary lưu trữ boss state sync
        // Key: BossID, Value: BossStateSync
        private readonly ConcurrentDictionary<int, BossStateSync> _bossStates = new();

        public static CrossServerBossManager Instance
        {
            get
            {
                if (_instance is null)
                {
                    lock (_lock)
                    {
                        _instance ??= new CrossServerBossManager();
                    }
                }
                return _instance;
            }
        }

        private CrossServerBossManager()
        {
            LogHelper.WriteLine(LogLevel.Info, "CrossServerBossManager initialized");
        }

        /// <summary>
        /// Đăng ký boss cross-server
        /// </summary>
        public void RegisterCrossServerBoss(int bossId, int originServerId, string bossName, int mapId, float x, float y, int durationMinutes)
        {
            try
            {
                var bossInfo = new CrossServerBossInfo
                {
                    BossId = bossId,
                    OriginServerId = originServerId,
                    BossName = bossName,
                    MapId = mapId,
                    X = x,
                    Y = y,
                    DurationMinutes = durationMinutes,
                    SpawnTime = DateTime.Now,
                    IsActive = true
                };

                _crossServerBosses.TryAdd(bossId, bossInfo);
                _globalContributions.TryAdd(bossId, new ConcurrentDictionary<(int, int), CrossServerDamageContribute>());
                
                var bossState = new BossStateSync
                {
                    BossId = bossId,
                    CurrentHP = 0, // Sẽ được cập nhật từ origin server
                    MaxHP = 0,
                    State = CrossServerBossState.Active,
                    LastUpdate = DateTime.Now
                };
                _bossStates.TryAdd(bossId, bossState);

                LogHelper.WriteLine(LogLevel.Info, $"Registered cross-server boss {bossId} from server {originServerId}");

                // Gửi thông tin boss đến tất cả servers khác
                BroadcastBossSpawn(bossInfo);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error registering cross-server boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật contribution từ một server
        /// </summary>
        public void UpdateContribution(int bossId, int serverId, int sessionId, string playerName, long damage, int attackCount)
        {
            try
            {
                if (!_globalContributions.TryGetValue(bossId, out var contributions))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Boss {bossId} not found in global contributions");
                    return;
                }

                var key = (serverId, sessionId);
                contributions.AddOrUpdate(key, 
                    new CrossServerDamageContribute(serverId, sessionId, playerName, damage, attackCount),
                    (k, existing) => 
                    {
                        existing.Damage += damage;
                        existing.AttackCount += attackCount;
                        existing.LastUpdate = DateTime.Now;
                        return existing;
                    });

                LogHelper.WriteLine(LogLevel.Debug, $"Updated contribution for boss {bossId}: {playerName} from server {serverId}, damage: {damage}");

                // Broadcast contribution update đến origin server
                if (serverId != World.ServerID && _crossServerBosses.TryGetValue(bossId, out var bossInfo))
                {
                    BroadcastContributionUpdate(bossId, serverId, sessionId, playerName, damage, attackCount);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating contribution for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật trạng thái boss
        /// </summary>
        public void UpdateBossState(int bossId, int currentHP, int maxHP, CrossServerBossState state)
        {
            try
            {
                if (_bossStates.TryGetValue(bossId, out var bossState))
                {
                    bossState.CurrentHP = currentHP;
                    bossState.MaxHP = maxHP;
                    bossState.State = state;
                    bossState.LastUpdate = DateTime.Now;

                    LogHelper.WriteLine(LogLevel.Debug, $"Updated boss {bossId} state: HP {currentHP}/{maxHP}, State: {state}");

                    // Broadcast state update đến tất cả servers
                    BroadcastBossStateUpdate(bossId, currentHP, maxHP, state);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating boss state {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý khi boss chết
        /// </summary>
        public async Task HandleBossDeath(int bossId, string killerName)
        {
            try
            {
                if (!_crossServerBosses.TryGetValue(bossId, out var bossInfo))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Boss {bossId} not found when handling death");
                    return;
                }

                // Cập nhật trạng thái boss
                if (_bossStates.TryGetValue(bossId, out var bossState))
                {
                    bossState.State = CrossServerBossState.Dead;
                    bossState.KillerName = killerName;
                    bossState.DeathTime = DateTime.Now;
                }

                bossInfo.IsActive = false;
                bossInfo.DeathTime = DateTime.Now;

                LogHelper.WriteLine(LogLevel.Info, $"Boss {bossId} died, killed by {killerName}");

                // Broadcast boss death đến tất cả servers
                BroadcastBossDeath(bossId, killerName);

                // Đợi một chút để đảm bảo tất cả servers nhận được thông tin
                await Task.Delay(2000);

                // Bắt đầu quá trình phân phối reward
                await DistributeCrossServerRewards(bossId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error handling boss death {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Phân phối reward cross-server
        /// </summary>
        private async Task DistributeCrossServerRewards(int bossId)
        {
            try
            {
                if (!_globalContributions.TryGetValue(bossId, out var contributions))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"No contributions found for boss {bossId}");
                    return;
                }

                var allContributions = contributions.Values.ToList();
                if (allContributions.Count == 0)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"No valid contributions for boss {bossId}");
                    return;
                }

                LogHelper.WriteLine(LogLevel.Info, $"Distributing rewards for boss {bossId} to {allContributions.Count} contributors");

                // Sử dụng CrossServerRewardManager để phân phối reward
                await CrossServerRewardManager.Instance.DistributeRewards(bossId, allContributions);

                // Sử dụng CleanupCoordinator để cleanup an toàn
                await CrossServerCleanupCoordinator.Instance.StartCleanupProcess(bossId, 5);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error distributing cross-server rewards for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup boss sau khi hoàn thành (được gọi từ CleanupCoordinator)
        /// </summary>
        public void CleanupBoss(int bossId)
        {
            try
            {
                _crossServerBosses.TryRemove(bossId, out _);
                _globalContributions.TryRemove(bossId, out _);
                _bossStates.TryRemove(bossId, out _);

                LogHelper.WriteLine(LogLevel.Info, $"Cleaned up cross-server boss {bossId} from CrossServerBossManager");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error cleaning up boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast boss spawn đến tất cả servers
        /// </summary>
        private void BroadcastBossSpawn(CrossServerBossInfo bossInfo)
        {
            try
            {
                var spawnInfo = new CrossServerBossSpawnInfo
                {
                    BossId = bossInfo.BossId,
                    OriginServerId = bossInfo.OriginServerId,
                    BossName = bossInfo.BossName,
                    MapId = bossInfo.MapId,
                    X = bossInfo.X,
                    Y = bossInfo.Y,
                    MaxHP = 0, // Sẽ được cập nhật sau
                    DurationMinutes = bossInfo.DurationMinutes,
                    BossType = bossInfo.BossType
                };

                var message = CrossServerBossProtocol.CreateBossSpawnMessage(spawnInfo);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Info, $"Broadcasted boss spawn: {bossInfo.BossId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting boss spawn: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast contribution update
        /// </summary>
        private void BroadcastContributionUpdate(int bossId, int serverId, int sessionId, string playerName, long damage, int attackCount)
        {
            try
            {
                var message = CrossServerBossProtocol.CreateDamageContributeMessage(bossId, serverId, sessionId, playerName, damage, attackCount);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Debug, $"Broadcasted contribution update: Boss {bossId}, Player {playerName}, Damage {damage}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting contribution update: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast boss state update
        /// </summary>
        private void BroadcastBossStateUpdate(int bossId, int currentHP, int maxHP, CrossServerBossState state)
        {
            try
            {
                var message = CrossServerBossProtocol.CreateBossStateUpdateMessage(bossId, currentHP, maxHP, state);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Debug, $"Broadcasted boss state update: Boss {bossId}, HP {currentHP}/{maxHP}, State {state}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting boss state update: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast boss death
        /// </summary>
        private void BroadcastBossDeath(int bossId, string killerName)
        {
            try
            {
                if (!_globalContributions.TryGetValue(bossId, out var contributions))
                    return;

                var totalDamage = contributions.Values.Sum(c => c.Damage);
                var totalParticipants = contributions.Count;

                var deathInfo = new BossDeathInfo
                {
                    BossId = bossId,
                    KillerName = killerName,
                    KillerServerId = World.ServerID,
                    KillerSessionId = 0, // TODO: Get actual session ID
                    TotalDamageDealt = totalDamage,
                    TotalParticipants = totalParticipants
                };

                var message = CrossServerBossProtocol.CreateBossDeathMessage(deathInfo);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Info, $"Broadcasted boss death: Boss {bossId}, Killer {killerName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting boss death: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast reward distribution
        /// </summary>
        private async Task BroadcastRewardDistribution(int bossId, int serverId, List<CrossServerDamageContribute> contributions)
        {
            try
            {
                var rewards = CalculateRewards(bossId, contributions);
                var message = CrossServerBossProtocol.CreateRewardDistributionMessage(bossId, serverId, rewards);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Info, $"Broadcasted reward distribution: Boss {bossId}, Server {serverId}, {rewards.Count} rewards");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting reward distribution: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast boss cleanup
        /// </summary>
        private void BroadcastBossCleanup(int bossId)
        {
            try
            {
                var message = CrossServerBossProtocol.CreateBossCleanupMessage(bossId);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Info, $"Broadcasted boss cleanup: {bossId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting boss cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Tính toán rewards cho contributions
        /// </summary>
        private List<CrossServerRewardInfo> CalculateRewards(int bossId, List<CrossServerDamageContribute> contributions)
        {
            var rewards = new List<CrossServerRewardInfo>();
            var totalDamage = contributions.Sum(c => c.Damage);

            foreach (var contribution in contributions)
            {
                var damagePercentage = totalDamage > 0 ? (double)contribution.Damage / totalDamage * 100 : 0;
                var rewardPoints = CalculateRewardPoints(contribution.Damage, damagePercentage);

                rewards.Add(new CrossServerRewardInfo
                {
                    BossId = bossId,
                    ServerId = contribution.ServerId,
                    SessionId = contribution.SessionId,
                    PlayerName = contribution.PlayerName,
                    TotalDamage = contribution.Damage,
                    AttackCount = contribution.AttackCount,
                    DamagePercentage = damagePercentage,
                    RewardPoints = rewardPoints,
                    HasSpecialReward = ShouldGetSpecialReward(damagePercentage),
                    SpecialRewardItem = GetSpecialRewardItem(damagePercentage)
                });
            }

            return rewards;
        }

        /// <summary>
        /// Tính toán reward points
        /// </summary>
        private int CalculateRewardPoints(long damage, double damagePercentage)
        {
            // Base points từ damage
            var basePoints = (int)(damage / 1000); // 1 point per 1000 damage

            // Bonus points từ damage percentage
            var bonusPoints = damagePercentage switch
            {
                >= 20 => 100,  // Top damage dealer
                >= 10 => 50,   // High damage
                >= 5 => 25,    // Medium damage
                >= 1 => 10,    // Low damage
                _ => 5         // Participation
            };

            return basePoints + bonusPoints;
        }

        /// <summary>
        /// Kiểm tra có nên nhận special reward không
        /// </summary>
        private bool ShouldGetSpecialReward(double damagePercentage)
        {
            // Top 3 damage dealers có cơ hội nhận special reward
            return damagePercentage >= 5;
        }

        /// <summary>
        /// Lấy special reward item
        /// </summary>
        private string GetSpecialRewardItem(double damagePercentage)
        {
            return damagePercentage switch
            {
                >= 20 => "LegendaryItem",
                >= 10 => "EpicItem",
                >= 5 => "RareItem",
                _ => null
            };
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _crossServerBosses.Clear();
                _globalContributions.Clear();
                _bossStates.Clear();
                _isDisposed = true;
                LogHelper.WriteLine(LogLevel.Info, "CrossServerBossManager disposed");
            }
        }
    }
}
