﻿using HeroYulgang.Helpers;
using RxjhServer.AOI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{

	// Leave map tại vị trí cũ
	// Hà<PERSON> này chỉ nên xử lý việc xóa player khỏi AOI, không nên xử lý gì khác
	public void PlayerLeaveMap(int tomap)
	{
		var num = 0;

		try
		{
			if (tomap != 7001 && tomap != 7101 && MapID != 7001 && MapID == 7101 && World.HuyetChien != null && World.HuyetChien.KetThuc == 0)
			{
				if (World.HuyetChien.BangChienChuPhuong.DangKy_BangPhaiID == GuildId)
				{
					if (World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.ContainsKey(SessionID))
					{
						if (GangCharacterLevel == 6)
						{
							World.HuyetChien.ChuPhuong_DiemSo = 0;
							World.HuyetChien.KhachHang_DiemSo = 0;
							World.HuyetChien.KetThuc = 2;
							World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.Remove(SessionID);
							KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							KiemSoatNguyenBao_SoLuong(50, 1);
							Player_Money += 5000000L;
							Save_NguyenBaoData();
							UpdateMoneyAndWeight();
							World.HuyetChien.Dispose();
						}
						else
						{
							World.HuyetChien.ChuPhuong_DiemSo--;
							World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.Remove(SessionID);
						}
						CloseUp = 0;
						HelpStartPrompt(12, 3);
					}
				}
				else if (World.HuyetChien.BangChienKhachHang.DangKy_BangPhaiID == GuildId && World.HuyetChien.BangChienKhachHang.DanhSachUngVien.ContainsKey(SessionID))
				{
					if (GangCharacterLevel == 6)
					{
						World.HuyetChien.ChuPhuong_DiemSo = 0;
						World.HuyetChien.KhachHang_DiemSo = 0;
						World.HuyetChien.KetThuc = 2;
						World.HuyetChien.BangChienKhachHang.DanhSachUngVien.Remove(SessionID);
						KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
						KiemSoatNguyenBao_SoLuong(50, 1);
						Player_Money += 5000000L;
						Save_NguyenBaoData();
						UpdateMoneyAndWeight();
						World.HuyetChien.Dispose();
					}
					else
					{
						World.HuyetChien.KhachHang_DiemSo--;
						World.HuyetChien.BangChienKhachHang.DanhSachUngVien.Remove(SessionID);
					}
					CloseUp = 0;
					HelpStartPrompt(12, 3);
				}
			}
			if (tomap != 7301 && MapID == 7301 && World.BangChien != null && World.MonChien_ProgressNew == 2)
			{
				foreach (var value in World.HelpList.Values)
				{
					if (value.DangKy_BangPhaiID != GuildId)
					{
						continue;
					}
					if (GangCharacterLevel == 6)
					{
						foreach (var value2 in value.DanhSachUngVien.Values)
						{
							value2.HeThongNhacNho("Do bang chủ bản phái rời khỏi bản đồ Hỗn Chiến, Thiên cơ các phán định trận bang chiến này thất bại!", 10, "Thiên cơ các");
							value2.Mobile(529f, 1528f, 15f, 101, 0);
						}
						value.DiemSoHienTai = 0;
						HelpStartPrompt(12, 3);
						break;
					}
					HelpStartPrompt(0, 0);
				}
			}
			// Rmove playerr trước rồi mới xóa khỏi AOI

			try
			{
				// Cần Remove khỏi grid => kích hoạt xóa player ra khỏi tất cả mọi nơi, rồi gọi Update để add lại
				AOI.AOISystem.Instance.RemovePlayer(SessionID);

				// Force update nearby players to ensure they stop seeing this player
				var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(MapID, PosX, PosY);
				foreach (var grid in aoiGrids)
				{
					foreach (var nearbyPlayer in grid.GetPlayers())
					{
						if (nearbyPlayer.SessionID != SessionID && nearbyPlayer.Client != null && nearbyPlayer.Client.Running)
						{
							try
							{
								// UpdateAOI sẽ lọc qua danh sách NearbyPlayers của tất cả người chơi => không tối ưu
								// nearbyPlayer.UpdateAOI();
								nearbyPlayer.NotifyPlayerExit(nearbyPlayer, this);
								nearbyPlayer.NearbyPlayers.TryRemove((World.ServerID, SessionID), out _);
								NotifyPlayerExit(this, nearbyPlayer);
								NearbyPlayers.TryRemove((World.ServerID, nearbyPlayer.SessionID), out _);
								// Thay thế bằng một hàm mới, chỉ xóa bỏ đúng player vừa out
							}
							catch (Exception ex)
							{
								LogHelper.WriteLine(LogLevel.Error, $"Error updating nearby player during leave: {ex.Message}");
							}
						}
					}
				}
			}
			catch (Exception aoiEx)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error removing player from AOI system: {aoiEx.Message}");
			}

			if (NearbyPlayers == null)
			{
				return;
			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Di chuyển Rời khỏi Bản đồ Hiện tại - lỗi: [" + num + "]-[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		finally
		{
			NearbyPlayers?.Clear();
		}
	}
		public void ClearWalkingState(Players play)
	{
		try
		{
			if (play.GetAddState(601101))
			{
				play.AppendStatusList[601101].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(601102))
			{
				play.AppendStatusList[601102].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(601103))
			{
				play.AppendStatusList[601103].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001101))
			{
				play.AppendStatusList[1001101].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001102))
			{
				play.AppendStatusList[1001102].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001201))
			{
				play.AppendStatusList[1001201].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001202))
			{
				play.AppendStatusList[1001202].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001202))
			{
				play.AppendStatusList[1001202].ThoiGianKetThucSuKien();
			}
			play.WalkingStatusId = 1;
		}
		catch
		{
		}
	}

	// public void Death()
	// {
	// 	var num = 0;
	// 	try
	// 	{
	// 		PlayerTuVong = true;

	// 		// Invalidate AOI cache when player dies (affects visibility)
	// 		//RxjhServer.AOI.AOIUpdateService.InvalidatePlayerStateCache(this);
	// 		tracking_status_id1 = 0;
	// 		AbnormalStatusList();
	// 		EndAbnormalAttackStatusList();
	// 		EndTheAbnormalDefenseStatusList();
	// 		EndAbnormalBlueStatusList();
	// 		EndAbnormalBloodDropStatusList();
	// 		ClearWalkingState(this);
	// 		if (PKTuVong)
	// 		{
	// 			AttackList?.Clear();
	// 			foreach (var value2 in PlayerList.Values)
	// 			{
	// 				if (!FindPlayers(80, value2) || value2.Client.TreoMay || value2.SessionID == SessionID || value2.AttackList == null || value2.AttackList.Count == 0)
	// 				{
	// 					continue;
	// 				}
	// 				using (new Lock(value2.AttackList, "AttackList"))
	// 				{
	// 					foreach (var attack in value2.AttackList)
	// 					{
	// 						if (attack.NhanVat_ID == SessionID)
	// 						{
	// 							value2.AttackList.Remove(attack);
	// 							break;
	// 						}
	// 					}
	// 				}
	// 			}
	// 			HookDeath();
	// 		}
	// 		if (MapID == 7001)
	// 		{
	// 			return;
	// 		}
	// 		if (MapID == 7101)
	// 		{
	// 			if (World.HuyetChien != null)
	// 			{
	// 				if (World.HuyetChien.BangChienChuPhuong.DangKy_BangPhaiID == GuildId)
	// 				{
	// 					if (World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.ContainsKey(SessionID))
	// 					{
	// 						World.HuyetChien.ChuPhuong_DiemSo--;
	// 						Mobile(-105f, -105f, 15f, 7101, 0);
	// 					}
	// 				}
	// 				else if (World.HuyetChien.BangChienKhachHang.DangKy_BangPhaiID == GuildId && World.HuyetChien.BangChienKhachHang.DanhSachUngVien.ContainsKey(SessionID))
	// 				{
	// 					World.HuyetChien.KhachHang_DiemSo--;
	// 					Mobile(107f, 107f, 15f, 7101, 0);
	// 				}
	// 			}
	// 			NhanVat_HP = CharacterMax_HP;
	// 			CapNhat_HP_MP_SP();
	// 			PlayerTuVong = false;
	// 			return;
	// 		}
	// 		if (MapID == 7301)
	// 		{
	// 			var num2 = new Random().Next(0, 4);
	// 			var num3 = 0;
	// 			var num4 = 0;
	// 			switch (num2)
	// 			{
	// 				case 0:
	// 					num3 = 0;
	// 					num4 = 300;
	// 					break;
	// 				case 1:
	// 					num3 = -300;
	// 					num4 = 0;
	// 					break;
	// 				case 2:
	// 					num3 = 300;
	// 					num4 = 0;
	// 					break;
	// 				case 3:
	// 					num3 = 0;
	// 					num4 = -300;
	// 					break;
	// 			}
	// 			num = 16;
	// 			Mobile(num3, num4, 15f, 7301, 0);
	// 			NhanVat_HP = CharacterMax_HP;
	// 			CapNhat_HP_MP_SP();
	// 			PlayerTuVong = false;
	// 			return;
	// 		}
	// 		if (MapID == 801)
	// 		{
	// 			if (World.TheLucChien_Progress != 0)
	// 			{
	// 				var array = Converter.HexStringToByte("AA552200F80488001A00F804000002000100000000000100000000000000020001000000000055AA");
	// 				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
	// 				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 10, 4);
	// 				Buffer.BlockCopy(BitConverter.GetBytes(12), 0, array, 30, 2);
	// 				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 32, 2);
	// 				Client?.Send_Map_Data(array, array.Length);
	// 				SendCurrentRangeBroadcastData(array, array.Length);
	// 				TLC_Cho_Chet = 1;
	// 				TLC_Cho_DiChuyen = DateTime.Now;
	// 			}
	// 			PlayerTuVong = true;
	// 			return;
	// 		}
	// 		if (MapID == 2301)
	// 		{
	// 			num = 20;
	// 			Random random = new();
	// 			Mobile(120 + random.Next(-70, 70), random.Next(-70, 70), 15f, 2301, 0);
	// 			NhanVat_HP = CharacterMax_HP;
	// 			CapNhat_HP_MP_SP();
	// 			PlayerTuVong = false;
	// 			return;
	// 		}
	// 		if (MapID == 2341)
	// 		{
	// 			num = 21;
	// 			Random random2 = new();
	// 			Mobile(120 + random2.Next(-70, 70), random2.Next(-70, 70), 15f, 2341, 0);
	// 			NhanVat_HP = CharacterMax_HP;
	// 			CapNhat_HP_MP_SP();
	// 			PlayerTuVong = false;
	// 			return;
	// 		}
	// 		if (MapID == 40101)
	// 		{
	// 			DCH_HS_LuaChon = 0;
	// 			PlayerTuVong = true;
	// 			var array2 = Converter.HexStringToByte("AA5522002C0188001C002C01000000000000000000000000000000000000030000000000000055AA");
	// 			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 10, 2);
	// 			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
	// 			Client?.Send_Map_Data(array2, array2.Length);
	// 			SendCurrentRangeBroadcastData(array2, array2.Length);
	// 			ThoiGianDieDch = DateTime.Now.AddSeconds(10.0);
	// 			ThoiGian2 = new System.Timers.Timer(10000.0);
	// 			ThoiGian2.Elapsed += ThoiGianChoChetDch;
	// 			ThoiGian2.Enabled = true;
	// 			ThoiGian2.AutoReset = true;
	// 			return;
	// 		}
	// 		if (MapID == 42001)
	// 		{
	// 			using (var enumerator3 = World.allConnectedChars.Values.GetEnumerator())
	// 			{
	// 				if (enumerator3.MoveNext())
	// 				{
	// 					var current3 = enumerator3.Current;
	// 					num = 22;
	// 					if (PKTuVong && World.CongThanhChien_Progress != 0)
	// 					{
	// 						num = 23;
	// 						GuiDi_NguoiChoi_BiKick_Thua(current3.CharacterName, CharacterName);
	// 					}
	// 					if (GuildName == World.NguoiChiemGiu_Den_Tenma)
	// 					{
	// 						num = 24;
	// 						PlayerTuVong = true;
	// 						var array3 = Converter.HexStringToByte("AA552600010040021800140020000BFB0288001A00FB0202000100E00300000AA00D000000000000000055AA");
	// 						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 15, 2);
	// 						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 21, 2);
	// 						Client?.Send_Map_Data(array3, array3.Length);
	// 						SendCurrentRangeBroadcastData(array3, array3.Length);
	// 					}
	// 					else
	// 					{
	// 						num = 25;
	// 						PlayerTuVong = true;
	// 						var array4 = Converter.HexStringToByte("AA552600010040021800140020000BFB0288001A00FB0202000100E00300000AA00D000000000000000055AA");
	// 						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 15, 2);
	// 						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 21, 2);
	// 						Client?.Send_Map_Data(array4, array4.Length);
	// 						SendCurrentRangeBroadcastData(array4, array4.Length);
	// 					}
	// 				}
	// 				return;
	// 			}
	// 		}
	// 		if (MapID == 43001)
	// 		{
	// 			num = 26;
	// 			if (副本复活剩余次数 > 0)
	// 			{
	// 				副本复活剩余次数--;
	// 				发送副本复活剩余次数();
	// 				Mobile(20f, -600f, 15f, 43001, 0);
	// 				HeThongNhacNho("Bị yêu quái miểu sát, truyền tống đến điểm phục sinh thảo phạt!", 10, "Thiên cơ các");
	// 				NhanVat_HP = CharacterMax_HP;
	// 				CapNhat_HP_MP_SP();
	// 				PlayerTuVong = false;
	// 			}
	// 			else
	// 			{
	// 				num = 27;
	// 				Mobile(420f, 1500f, 15f, 101, 0);
	// 				HeThongNhacNho("Số lần phục sinh đã hết, truyền tống đến Huyền Bột Phái!", 10, "Thiên cơ các");
	// 				NhanVat_HP = CharacterMax_HP;
	// 				CapNhat_HP_MP_SP();
	// 				PlayerTuVong = false;
	// 			}
	// 			return;
	// 		}

	// 		num = 28;
	// 		NhanVat_HP = 0;
	// 		PlayerTuVong = true;
	// 		X_Toa_Do_Class value = new(PosX, PosY, PosZ, MapID);
	// 		using (new Lock(ThoLinhPhu_ToaDo, "ThoLinhPhu_ToaDo"))
	// 		{
	// 			num = 29;
	// 			if (ThoLinhPhu_ToaDo.ContainsKey(2))
	// 			{
	// 				ThoLinhPhu_ToaDo.Remove(2);
	// 			}
	// 			if (!ThoLinhPhu_ToaDo.ContainsKey(2))
	// 			{
	// 				ThoLinhPhu_ToaDo.Add(2, value);
	// 			}
	// 		}
	// 		num = 30;
	// 		var array5 = Converter.HexStringToByte("AA552000F80488001C00F804000002000100000000000100000000000000020001000000000055AA");
	// 		num = 31;
	// 		if (PublicDrugs != null)
	// 		{
	// 			if (!KiemTra_Phu() && !KiemTra_Phu2())
	// 			{
	// 				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array5, 30, 2);
	// 			}
	// 			else
	// 			{
	// 				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array5, 30, 2);
	// 			}
	// 		}
	// 		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array5, 4, 2);
	// 		Client?.Send_Map_Data(array5, array5.Length);
	// 		SendCurrentRangeBroadcastData(array5, array5.Length);

	// 	}
	// 	catch (Exception ex)
	// 	{
	// 		LogHelper.WriteLine(LogLevel.Error, "Nhân vật tử vong - Lỗi num: [" + num + "] - [" + AccountID + "][" + CharacterName + "] - " + ex.Message);
	// 	}
	// }


}
