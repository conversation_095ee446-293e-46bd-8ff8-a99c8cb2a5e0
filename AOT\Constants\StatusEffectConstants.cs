using System;

namespace HeroYulgang.Constants
{
    /// <summary>
    /// Chứa các constant cho Status Effect IDs để code dễ đọc hiểu hơn
    /// </summary>
    public static class Effects
    {
        #region Phù Hồn Effects (Phù Hồn Status Effects)
        
        /// <summary>
        /// Hiệu ứng Di Tinh - Tránh hoàn toàn damage trong 3 giây
        /// </summary>
        public const int DI_TINH = 1000000954;
        
        #endregion
        
        #region Boss Effects
        
        /// <summary>
        /// Hiệu ứng chảy máu từ boss (bleeding effect)
        /// </summary>
        public const int BOSS_BLEEDING = 700667;
        
        #endregion
        
        #region Helper Methods
        
        /// <summary>
        /// Lấy tên mô tả của status effect từ ID
        /// </summary>
        /// <param name="statusId">ID của status effect</param>
        /// <returns>Tên mô tả của status effect</returns>
        public static string GetStatusEffectName(int statusId)
        {
            return statusId switch
            {
                DI_TINH => "Di Tinh (Evasion)",
                BOSS_BLEEDING => "Boss Bleeding",
                _ => $"Unknown Status Effect ({statusId})"
            };
        }
        
        /// <summary>
        /// Kiểm tra xem status effect có phải là hiệu ứng tích cực không
        /// </summary>
        /// <param name="statusId">ID của status effect</param>
        /// <returns>True nếu là hiệu ứng tích cực, False nếu là hiệu ứng tiêu cực</returns>
        public static bool IsPositiveEffect(int statusId)
        {
            return statusId switch
            {
                DI_TINH => true,  // Di Tinh là hiệu ứng tích cực (tránh damage)
                BOSS_BLEEDING => false,  // Bleeding là hiệu ứng tiêu cực
                _ => false  // Mặc định là tiêu cực nếu không biết
            };
        }
        
        #endregion
    }
}
