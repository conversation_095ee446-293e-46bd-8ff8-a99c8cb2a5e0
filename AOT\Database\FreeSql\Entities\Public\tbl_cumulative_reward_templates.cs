﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_cumulative_reward_templates {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_cumulative_reward_templates_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(IsNullable = false)]
		public string name { get; set; }

		[JsonProperty]
		public int month { get; set; }

		[JsonProperty]
		public int year { get; set; }

		[JsonProperty]
		public bool? is_active { get; set; } = false;

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime? created_date { get; set; }

		[JsonProperty]
		public DateTime start_date { get; set; }

		[JsonProperty]
		public DateTime end_date { get; set; }

		[JsonProperty]
		public int milestone_1_cash { get; set; } = 5000;

		[JsonProperty]
		public int milestone_2_cash { get; set; } = 10000;

		[JsonProperty]
		public int milestone_3_cash { get; set; } = 20000;

		[JsonProperty]
		public int milestone_4_cash { get; set; } = 35000;

		[JsonProperty]
		public int milestone_5_cash { get; set; } = 50000;

		[JsonProperty]
		public int milestone_6_cash { get; set; } = 75000;

		[JsonProperty]
		public int milestone_7_cash { get; set; } = 100000;

		[JsonProperty]
		public int milestone_8_cash { get; set; } = 150000;

		[JsonProperty]
		public int milestone_9_cash { get; set; } = 200000;

		[JsonProperty]
		public int milestone_10_cash { get; set; } = 300000;

	}

}
