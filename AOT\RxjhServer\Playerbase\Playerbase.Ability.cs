﻿using HeroYulgang.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class PlayersBes : X_Khi_Cong_Thuoc_Tinh
    {
    		
	public void UpdateKhiCong()
	{
		using (new Lock(thisLock, "UpdateKhiCong"))
		{
			NhanVat_KhiCong_ThemVao_HP = 0;
			NhanVat_KhiCong_ThemVao_PhanTram_HP = 0;
			NhanVat_KhiCong_ThemVao_MP = 0;
			NhanVat_KhiCong_ThemVao_PhanTram_MP = 0;
			FLD_NhanVat_KhiCong_CongKich = 0.0;
			FLD_NhanVat_KhiCong_PhongNgu = 0;
			FLD_NhanVat_KhiCong_TrungDich = 0;
			FLD_NhanVat_KhiCong_NeTranh = 0;
			FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
			FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
			FLD_NhanVat_KhiCong_TrongLuong = 0;
			FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
			FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
			NhanVat_KhiCong_ThemVao_LucPhongNguVoCong = 0;
			for (var i = 0; i < 12; i++)
			{
				var khiCong_byte = KhiCong[i].KhiCong_byte;
				if (khiCong_byte[0] == byte.MaxValue || BitConverter.ToInt16(khiCong_byte, 0) <= 0)
				{
					continue;
				}
				double num = BitConverter.ToInt16(khiCong_byte, 0);
				if (!(num > 0.0))
				{
					continue;
				}
				var maxKhiCong_Tren1KhiCong = World.MaxKhiCong_Tren1KhiCong;
				var num2 = num + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + NhanGiaTri_TangRieng_CuaKhiCong(i) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
				if (num2 > maxKhiCong_Tren1KhiCong)
				{
					num2 = maxKhiCong_Tren1KhiCong;
				}
				var khiCongBonusValue = GetKhiCongBonusValue(Player_Job, i, 0);
				switch (Player_Job)
				{
					case 1:
						switch (i)
						{
							case 0:
								{
									var num11 = FLD_CongKichThapNhat * num2 * khiCongBonusValue / 100.0 / 2.0;
									if (num11 < 1.0)
									{
										num11 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = (int)(num11 + 0.5);
									break;
								}
							case 1:
								FLD_NhanVat_KhiCong_TrungDich = (int)(FLD_TrungDich * (0.1 + num2 * khiCongBonusValue));
								break;
							case 2:
								DAO_LienHoanPhiVu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 3:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 4:
								FLD_NhanVat_KhiCong_PhongNgu = (int)(FLD_PhongNgu * num2 * khiCongBonusValue);
								NhanVat_KhiCong_ThemVao_HP = (int)(num2 * GetKhiCongBonusValue(Player_Job, i, 1));
								break;
							case 5:
								PhaGiap_TiLe = 5.0 + num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								ChanVu_TuyetKich = num2 * khiCongBonusValue;
								break;
							case 8:
								QuaiVat_PhanSatThuong_TiLe = 10.0 + num2 * khiCongBonusValue;
								NguoiChoi_PhanSatThuong_Tile = 3.0 + num2 * 0.3;
								break;
							case 9:
								AmAnh_TuyetSat = 5.0 + num2 * khiCongBonusValue;
								break;
							case 10:
								DAO_ManhLongSatTran = num2 * (khiCongBonusValue - 0.3);
								break;
							case 11:
								LuuQuang_LoanVu = num2 * khiCongBonusValue;
								break;
						}
						break;
					case 2:
						switch (i)
						{
							case 0:
								FLD_NhanVat_KhiCong_CongKich = num2 * 2.0;
								break;
							case 1:
								KIEM_BachBien_ThanHanh = 10.0 + num2;
								break;
							case 2:
								KIEM_LienHoanPhiVu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 3:
								KIEM_PhaThien_NhatKiem = num2 * 0.03;
								break;
							case 4:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 5:
								KIEM_DiHoa_TiepMoc = num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								KIEM_NoHai_CuongLan = num2 * (khiCongBonusValue - 0.075);
								break;
							case 8:
								KIEM_HoiLieu_ThanPhap = num2 * khiCongBonusValue;
								FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = num2 * (1.5 * GetKhiCongBonusValue(Player_Job, i, 1));
								break;
							case 9:
								KIEM_NhanKiem_NhatThe = num2 * khiCongBonusValue;
								break;
							case 10:
								KIEM_HonNguyen_KiemPhap = num2 * khiCongBonusValue;
								break;
							case 11:
								KIEM_TrungQuan_NhatNo = 5.0 + num2 * (khiCongBonusValue + 0.7);
								break;
						}
						break;
					case 3:
						switch (i)
						{
							case 0:
								FLD_NhanVat_KhiCong_PhongNgu = (int)(num2 * khiCongBonusValue);
								break;
							case 1:
								THUONG_VanKhi_LieuThuong = num2 * khiCongBonusValue + 0.1;
								break;
							case 2:
								THUONG_LienHoanPhiVu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 3:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 4:
								NhanVat_KhiCong_ThemVao_HP = (int)(num2 * khiCongBonusValue);
								break;
							case 5:
								THUONG_ChuyenCongViThu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								THUONG_CuongThanHangThe = num2 * 0.001;
								break;
							case 8:
								{
									FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = num2 * khiCongBonusValue;
									var num9 = FLD_CongKich * 0.2;
									var num10 = FLD_PhongNgu * 0.2;
									if (num9 < 1.0)
									{
										num9 = 1.0;
									}
									if (num10 < 1.0)
									{
										num10 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = num9;
									FLD_NhanVat_KhiCong_PhongNgu = (int)num10;
									THUONG_LienHoanPhiVu *= 1.0 + num2 * GetKhiCongBonusValue(Player_Job, i, 1);
									break;
								}
							case 9:
								THUONG_MatNhatCuongVu = num2 * khiCongBonusValue;
								break;
							case 10:
								THUONG_NoYChiHong = num2 * 0.5;
								break;
							case 11:
								NhanVat_KhiCong_ThemVao_LucPhongNguVoCong = (int)(num2 * khiCongBonusValue);
								break;
						}
						break;
					case 4:
						switch (i)
						{
							case 0:
								FLD_ThemVaoTiLePhanTram_TrungDich = num2 * khiCongBonusValue;
								break;
							case 1:
								CUNG_LiepUngChiNhan = (int)(num2 * khiCongBonusValue);
								break;
							case 2:
								{
									var num8 = FLD_CongKichThapNhat * num2 * khiCongBonusValue / 100.0 / 0.835;
									if (num8 < 1.0)
									{
										num8 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = (int)(num8 + 0.5);
									break;
								}
							case 3:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 4:
								NhanVat_KhiCong_ThemVao_HP = (int)(num2 * khiCongBonusValue);
								break;
							case 5:
								CUNG_NhueLoiChiTien = 5.0 + num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								CUNG_TamThanNgungTu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 8:
								CUNG_LuuTinhTamThi = 10.0 + num2 * khiCongBonusValue;
								break;
							case 9:
								CUNG_HoiLuuChanKhi = num2 * khiCongBonusValue;
								break;
							case 10:
								CUNG_VoMinhAmThi = num2 * khiCongBonusValue;
								break;
							case 11:
								CUNG_TriMenhTuyetSat = 10.0 + num2 * (khiCongBonusValue + 0.3);
								break;
						}
						break;
					case 5:
						{
							var players = World.FindPlayerBySession(SessionID);
							switch (i)
							{
								case 0:
									DAIPHU_VanKhiLieuTam = num2 * khiCongBonusValue;
									break;
								case 1:
									DAIPHU_ThaiCucTamPhap = num2 * khiCongBonusValue;
									break;
								case 2:
									if (players != null)
									{
										NhanVat_KhiCong_ThemVao_PhanTram_HP = (int)(1.0 + num2 * players.Player_Level / 2.0);
									}
									break;
								case 3:
									if (players != null)
									{
										NhanVat_KhiCong_ThemVao_PhanTram_MP = (int)(1.0 + num2 * players.Player_Level / 2.0);
									}
									break;
								case 4:
									DAIPHU_DieuThuHoiXuan = 10.0 + num2 * khiCongBonusValue;
									break;
								case 5:
									DAIPHU_TruongCongKichLuc = num2 * khiCongBonusValue;
									break;
								case 6:
									ThanCo_MinhChau = num2 * khiCongBonusValue;
									break;
								case 7:
									ChanVu_TuyetKich = num2 * khiCongBonusValue;
									break;
								case 8:
									DAIPHU_HapTinhDaiPhap = num2 * khiCongBonusValue;
									break;
								case 9:
									DAIPHU_HoanVuVanHoa = num2 * khiCongBonusValue;
									break;
								case 10:
									DAIPHU_VoTrungSinhHuu = num2 * 0.5;
									break;
								case 11:
									DAIPHU_CuuThienChanKhi = num2 * khiCongBonusValue;
									break;
							}
							break;
						}
					case 6:
						switch (i)
						{
							case 0:
								NINJA_KinhKhaChiNo = num2 * (khiCongBonusValue + 0.7);
								break;
							case 1:
								NINJA_TamHoaTuDinh = 10.0 + num2;
								break;
							case 2:
								NINJA_LienHoanPhiVu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 3:
								NINJA_TamThanNgungTu = 10.0 + num2 * (khiCongBonusValue + 0.3);
								break;
							case 4:
								NINJA_TriThuTuyetMenh = num2 * (khiCongBonusValue / 1.65);
								break;
							case 5:
								NINJA_DiNoHoanNo = num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								NINJA_TienPhatCheNhan = num2 * khiCongBonusValue;
								FLD_NhanVat_KhiCong_TrungDich = (int)(num2 * 0.5 * FLD_TrungDich / 100.0);
								break;
							case 8:
								NINJA_ThienChuVanThu = num2 * khiCongBonusValue;
								break;
							case 9:
								NINJA_LienTieuDaiDa = num2 * khiCongBonusValue;
								break;
							case 10:
								NINJA_KhoaiDaoLoanVu = num2 * khiCongBonusValue;
								break;
							case 11:
								NINJA_NhatChieuTanSat = num2 * (khiCongBonusValue / 50.0);
								break;
						}
						break;
					case 7:
						switch (i)
						{
							case 0:
								{
									var num7 = FLD_LonNhatCongKich * num2 * khiCongBonusValue / 100.0 / 5.0;
									if (num7 < 1.0)
									{
										num7 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = (int)(num7 + 0.5);
									break;
								}
							case 1:
								NhanVat_KhiCong_ThemVao_HP = (int)(num2 * khiCongBonusValue);
								break;
							case 2:
								NhanVat_KhiCong_ThemVao_MP = (int)(num2 * khiCongBonusValue / 100.0 * CharacterMax_MP);
								break;
							case 3:
								FLD_NhanVat_KhiCong_PhongNgu = (int)(num2 * 5.0);
								NhanVat_KhiCong_ThemVao_LucPhongNguVoCong = (int)(num2 * 5.0);
								break;
							case 4:
								FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = num2 * 0.005;
								break;
							case 5:
								CAMSU_CaoSonLuuThuy = num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								CAMSU_NhacDuongTamTuy = 2.5 + num2 * 0.25;
								break;
							case 8:
								CAMSU_MaiHoaTamLong = num2 * khiCongBonusValue;
								break;
							case 9:
								CAMSU_LoanPhuongHoaMinh = num2 * (khiCongBonusValue + 0.05);
								break;
							case 10:
								CAMSU_DuongMinhXuanHieu = num2 * khiCongBonusValue;
								break;
							case 11:
								CAMSU_TieuTuongVuDa = num2 * khiCongBonusValue;
								break;
						}
						break;
					case 8:
						switch (i)
						{
							case 0:
								{
									var num6 = FLD_CongKichThapNhat * num2 * khiCongBonusValue / 100.0 / 2.0;
									if (num6 < 1.0)
									{
										num6 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = (int)(num6 + 0.5);
									break;
								}
							case 1:
								FLD_NhanVat_KhiCong_TrungDich = (int)(FLD_TrungDich * (0.1 + num2 * khiCongBonusValue));
								break;
							case 2:
								FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.1 + num2 * khiCongBonusValue;
								break;
							case 3:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 4:
								HanBaoQuan_ThienMaCuongHuyet = num2 * 1.5;
								break;
							case 5:
								HanBaoQuan_TruyCotHapNguyen = num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								PhaGiap_TiLe = 5.0 + num2 * khiCongBonusValue;
								break;
							case 8:
								ChanVu_TuyetKich = num2 * khiCongBonusValue;
								break;
							case 9:
								HanBaoQuan_HoaLongVanDinh = num2 * khiCongBonusValue;
								break;
							case 10:
								LuuQuang_LoanVu = num2 * khiCongBonusValue;
								break;
							case 11:
								AmAnh_TuyetSat = 5.0 + num2 * khiCongBonusValue;
								break;
						}
						break;
					case 9:
						switch (i)
						{
							case 0:
								{
									var num5 = FLD_LonNhatCongKich * num2 * khiCongBonusValue / 100.0 / 5.0;
									if (num5 < 1.0)
									{
										num5 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = (int)(num5 + 0.5);
									break;
								}
							case 1:
								DamHoaLien_BachBien_ThanHanh = 10.0 + num2;
								break;
							case 2:
								DamHoaLien_LienHoanPhiVu = 5.0 + num2 * (khiCongBonusValue - 1.0);
								break;
							case 3:
								DamHoaLien_ChieuThucTanPhap = 100.0 * num2;
								break;
							case 4:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 5:
								DamHoaLien_HoThan_CuongKhi = 10.0 + num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								DamHoaLien_DiHoa_TiepMoc = num2 * (khiCongBonusValue / 2.5);
								break;
							case 8:
								DamHoaLien_TungHoanhVoSong = 5.0 + num2 * 0.4;
								break;
							case 9:
								DamHoaLien_HoiLieu_ThanPhap = num2;
								FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = num2 * GetKhiCongBonusValue(Player_Job, i, 1);
								break;
							case 10:
								DamHoaLien_NoHai_CuongLan = 5.0 + num2 * khiCongBonusValue;
								break;
							case 11:
								DamHoaLien_TrungQuan_NhatNo = 5.0 + num2 * (khiCongBonusValue + 0.7);
								break;
						}
						break;
					case 10:
						switch (i)
						{
							case 0:
								QuyenSu_CuongThanHangThe = num2 * 0.001;
								break;
							case 1:
								THUONG_VanKhi_LieuThuong = num2 * khiCongBonusValue;
								break;
							case 2:
								{
									var num4 = FLD_CongKichThapNhat * num2 * khiCongBonusValue / 100.0 / 2.0;
									if (num4 < 1.0)
									{
										num4 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich = (int)(num4 + 0.5);
									break;
								}
							case 3:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 4:
								FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram = num2 * khiCongBonusValue;
								break;
							case 5:
								QuyenSu_MaXuThanhCham = 10.0 + num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								QuyenSu_ThuyHoaNhatThe = num2 * 0.6;
								break;
							case 8:
								QuyenSu_KimCuongBatHoai = 10.0 + num2;
								break;
							case 9:
								QuyenSu_ChuyenCongViThu = 5.0 + num2 * khiCongBonusValue;
								break;
							case 10:
								QuyenSu_NoTamXuatKich = num2 * 0.75;
								break;
							case 11:
								QuyenSu_MatNhatCuongVu = num2 * 0.005;
								break;
						}
						break;
					case 11:
						switch (i)
						{
							case 0:
								MaiLieuChan_ChuongLucKichHoat = 5.0 + num2;
								break;
							case 1:
								MaiLieuChan_ChuongLucVanDung = 10.0 + num2 * khiCongBonusValue;
								break;
							case 2:
								FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.1 + num2 * khiCongBonusValue;
								break;
							case 3:
								MaiLieuChan_HuyenVuThanCong = num2 * khiCongBonusValue;
								break;
							case 4:
								MaiLieuChan_HuyenVuDichChiDiem = num2 * khiCongBonusValue;
								FLD_NhanVat_KhiCong_CongKich = FLD_LonNhatCongKich * num2 * GetKhiCongBonusValue(Player_Job, i, 1) / 2.0;
								break;
							case 5:
								MaiLieuChan_HuyenVuCuongKich = num2;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								MaiLieuChan_HuyenVuNguyHoa = num2 * khiCongBonusValue / 2.0;
								break;
							case 8:
								MaiLieuChan_ChuongLucKhoiPhuc = num2 * khiCongBonusValue;
								break;
							case 9:
								MaiLieuChan_TatDoDichHoaThan = num2 * khiCongBonusValue;
								break;
							case 10:
								MaiLieuChan_PhanNoBaoPhat = num2 * (khiCongBonusValue * 2.5);
								break;
							case 11:
								MaiLieuChan_HapHuyetTienKich = num2 * khiCongBonusValue;
								break;
						}
						break;
					case 12:
						switch (i)
						{
							case 0:
								FLD_NhanVat_KhiCong_PhongNgu = (int)(num2 * khiCongBonusValue);
								break;
							case 1:
								THUONG_VanKhi_LieuThuong = num2 * khiCongBonusValue + 0.1;
								break;
							case 2:
								THUONG_LienHoanPhiVu = 10.0 + num2 * khiCongBonusValue;
								break;
							case 3:
								NhanVat_KhiCong_ThemVao_HP = (int)(num2 * khiCongBonusValue);
								break;
							case 4:
								CuongPhong_VanPha = num2 * khiCongBonusValue;
								break;
							case 5:
								ChanVu_TuyetKich = num2 * khiCongBonusValue;
								break;
							case 6:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 7:
								{
									var num3 = 0.0;
									if (Player_Job_level == 3)
									{
										num3 = 5.0;
									}
									else if (Player_Job_level >= 4)
									{
										num3 = 10.0;
									}
									LuuTinhManThien = 10.0 + num2 * khiCongBonusValue + num3;
									break;
								}
							case 8:
								FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = num2 * 0.015;
								THUONG_LienHoanPhiVu *= 1.0 + num2 * GetKhiCongBonusValue(Player_Job, i, 1);
								break;
							case 9:
								TuHao_ChuyenCongViThu = num2 * khiCongBonusValue;
								break;
							case 10:
								CongPhaNhuocDiem = 10.0 + num2 * khiCongBonusValue;
								break;
							case 11:
								KhongGiPhaNoi = num2;
								break;
						}
						break;
					case 13:
						switch (i)
						{
							case 0:
								ThanNu_VanKhiHanhTam = num2 * khiCongBonusValue;
								break;
							case 1:
								ThanNu_ThaiCucTamPhap = num2 * khiCongBonusValue;
								break;
							case 2:
								ThanNu_ThanLucKichPhat = num2 * khiCongBonusValue;
								break;
							case 3:
								ThanNu_SatTinhNghiaKhi = num2 * khiCongBonusValue;
								break;
							case 4:
								NhanVat_KhiCong_ThemVao_MP = (int)(num2 * khiCongBonusValue);
								break;
							case 5:
								ThanCo_MinhChau = num2 * khiCongBonusValue;
								break;
							case 6:
								ThanNu_HacHoaManKhai = num2 * khiCongBonusValue;
								break;
							case 7:
								ThanNu_DieuThuHoiXuan = num2 * (khiCongBonusValue * 0.2);
								break;
							case 8:
								ThanNu_TruongCongKichLuc = num2 * khiCongBonusValue;
								break;
							case 9:
								ThanNu_HacHoaTapTrung = num2 * khiCongBonusValue;
								break;
							case 10:
								ThanNu_ChanVu_TuyetKich = num2 * khiCongBonusValue;
								break;
							case 11:
								ThanNu_VanDocBatXam = num2 * khiCongBonusValue;
								break;
						}
						break;
				}
			}
			if (ThanCo_MinhChau > 0.0)
			{
				var num12 = (int)(FLD_PhongNgu * ThanCo_MinhChau / 30.0);
				var num13 = (int)(FLD_PhongNgu * ThanCo_MinhChau / 40.0);
				NhanVat_KhiCong_ThemVao_HP += num12;
				NhanVat_KhiCong_ThemVao_LucPhongNguVoCong += num13;
			}
			try
			{
				if (Player_Job_level >= 6 && ThangThienKhiCong != null)
				{
					foreach (var value in ThangThienKhiCong.Values)
					{
						if (value.KhiCong_SoLuong <= 0)
						{
							continue;
						}
						var maxKhiCong_Tren1KhiCong2 = World.MaxKhiCong_Tren1KhiCong;
						var num14 = value.KhiCong_SoLuong + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + NhanGiaTri_TangRieng_CuaKhiCong(value.KhiCongID) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
						if (num14 > maxKhiCong_Tren1KhiCong2)
						{
							num14 = maxKhiCong_Tren1KhiCong2;
						}
						var thangthienkhicongBonusValue = GetThangthienkhicongBonusValue(value.KhiCongID);
						switch (value.KhiCongID)
						{
							case 25:
								KIEM_ThangThien_1_KhiCong_HoThan_CuongKhi = 10.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 13:
								DAO_ThangThien_3_KhiCong_HoaLong_ChiHoa = num14 * 0.005;
								break;
							case 150:
								DAIPHU_ThangThien_2_KhiCong_VanVatHoiXuan = num14 * thangthienkhicongBonusValue;
								break;
							case 58:
								FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram = num14 * thangthienkhicongBonusValue;
								break;
							case 33:
								THUONG_ThangThien_3_KhiCong_NoYChiHoa = num14 * 0.01;
								break;
							case 370:
								NINJA_ThangThien_1_KhiCong_DaMaTrienThan = num14 * thangthienkhicongBonusValue;
								break;
							case 371:
								NINJA_ThangThien_2_KhiCong_ThuanThuyThoiChu = num14 * thangthienkhicongBonusValue;
								break;
							case 373:
								ThangThien_4_ManNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 374:
								ThangThien_4_LietNhatViemViem = num14 * thangthienkhicongBonusValue;
								break;
							case 380:
								if (Player_Job != 1 && Player_Job != 4 && Player_Job != 8 && Player_Job != 10)
								{
									FLD_NhanVat_KhiCong_CongKich += (int)(FLD_CongKichThapNhat * num14 * thangthienkhicongBonusValue * 0.02);
								}
								break;
							case 381:
								if (Player_Job != 2 && Player_Job != 9)
								{
									var num15 = FLD_LonNhatCongKich * num14 * thangthienkhicongBonusValue / 100.0 / 5.0;
									if (num15 < 1.0)
									{
										num15 = 1.0;
									}
									FLD_NhanVat_KhiCong_CongKich += (int)(FLD_CongKichThapNhat * num14 * thangthienkhicongBonusValue * 0.01 / 2.0);
								}
								break;
							case 382:
								if (Player_Job != 3 && Player_Job != 7)
								{
									FLD_NhanVat_KhiCong_PhongNgu += (int)(num14 * thangthienkhicongBonusValue);
								}
								break;
							case 383:
								if (Player_Job != 5)
								{
									ThangThien_1_KhiCong_VanKhiHanhTam = num14 * thangthienkhicongBonusValue;
								}
								break;
							case 384:
								if (Player_Job != 4 && Player_Job != 7)
								{
									NhanVat_KhiCong_ThemVao_HP += (int)(num14 * thangthienkhicongBonusValue);
								}
								break;
							case 385:
								if (Player_Job != 3 && Player_Job != 10)
								{
									ThangThien_1_KhiCong_VanKhi_LieuThuong = 10.0 + num14 * thangthienkhicongBonusValue;
								}
								break;
							case 386:
								if (Player_Job != 2 && Player_Job != 6 && Player_Job != 8 && Player_Job != 9)
								{
									FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh += 0.1 + num14 * thangthienkhicongBonusValue;
								}
								break;
							case 387:
								if (Player_Job == 5)
								{
									ThangThien_1_KhiCong_CuongPhongThienY = num14 * thangthienkhicongBonusValue;
								}
								break;
							case 390:
								CAMSU_ThangThien_1_KhiCong_PhiHoaDiemThuy = num14 * thangthienkhicongBonusValue;
								break;
							case 391:
								CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet = 4.5 + num14 * thangthienkhicongBonusValue;
								break;
							case 392:
								CAMSU_ThangThien_3_KhiCong_TuDaThuCa = num14 * thangthienkhicongBonusValue;
								break;
							case 393:
								ThangThien_4_HongNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 394:
								ThangThien_4_HuyenTiChanMach = num14 * thangthienkhicongBonusValue;
								break;
							case 310:
								DAO_ThangThien_1_KhiCong_DonXuatNghichCanh = num14 * thangthienkhicongBonusValue;
								break;
							case 311:
								DAO_ThangThien_2_KhiCong_CungDoMatLo = num14 / 2.0;
								break;
							case 313:
								ThangThien_4_HongNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 314:
								ThangThien_4_DocXaXuatDong = num14 * thangthienkhicongBonusValue;
								break;
							case 315:
								MaiLieuChan_ThangThien_3_KhiCong_QuySatNhan = num14 * thangthienkhicongBonusValue;
								break;
							case 316:
								MaiLieuChan_ThangThien_1_KhiCong_HuyenVuLoiDien = num14 * (thangthienkhicongBonusValue + 1.5);
								break;
							case 321:
								if (Player_Job == 2)
								{
									KIEM_ThangThien_2_KhiCong_ThienDiaDongTho = num14 * thangthienkhicongBonusValue;
								}
								else if (Player_Job == 9)
								{
									DamHoaLien_ThangThien_2_KhiCong_ThienDiaDongTho = num14 * thangthienkhicongBonusValue;
								}
								break;
							case 322:
								if (Player_Job == 2)
								{
									KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu = 5.0 + num14 * thangthienkhicongBonusValue;
								}
								else if (Player_Job == 9)
								{
									DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu = num14 * thangthienkhicongBonusValue;
								}
								break;
							case 323:
								ThangThien_4_HongNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 324:
								ThangThien_4_DocXaXuatDong = num14 * thangthienkhicongBonusValue;
								break;
							case 325:
								MaiLieuChan_ThangThien_2_KhiCong_HuyenVuTroChu = num14 * thangthienkhicongBonusValue;
								break;
							case 326:
								ThangThien_4_LietNhatViemViem = num14 * thangthienkhicongBonusValue;
								break;
							case 327:
								ThangThien_4_ManNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 330:
								THUONG_ThangThien_1_KhiCong_PhaGiapThuHon = num14 * thangthienkhicongBonusValue;
								break;
							case 331:
								THUONG_ThangThien_2_KhiCong_DiThoiViTien = num14 * thangthienkhicongBonusValue;
								break;
							case 333:
								ThangThien_4_HongNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 334:
								ThangThien_4_DocXaXuatDong = num14 * thangthienkhicongBonusValue;
								break;
							case 340:
								CUNG_ThangThien_1_KhiCong_TuyetAnhXaHon = num14 * thangthienkhicongBonusValue;
								break;
							case 341:
								FLD_NhanVat_KhiCong_TrongLuong += (int)(TheTotalWeightOfTheCharacter * num14 * 0.003);
								FLD_NhanVat_KhiCong_PhongNgu += (int)(num14 * thangthienkhicongBonusValue);
								break;
							case 342:
								CUNG_ThangThien_3_KhiCong_ThienNgoaiTamThi = num14 * thangthienkhicongBonusValue;
								break;
							case 343:
								ThangThien_4_ManNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 344:
								ThangThien_4_LietNhatViemViem = num14 * thangthienkhicongBonusValue;
								break;
							case 352:
								DAIPHU_ThangThien_3_KhiCong_MinhKinhChiThuy = num14 * thangthienkhicongBonusValue;
								break;
							case 353:
								ThangThien_4_ManNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 354:
								ThangThien_4_VongMaiThiemHoa = num14 * thangthienkhicongBonusValue;
								break;
							case 170:
								NINJA_ThangThien_3_KhiCong_VoTinhDaKich = 0.1 + num14 * thangthienkhicongBonusValue;
								break;
							case 662:
								LangKinhToiLuyen = num14;
								break;
							case 663:
								TuHao_PhaHuyenCuongPhong = 10.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 664:
								KyQuan_QuanHung = 10.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 665:
								ThangThien_4_DocXaXuatDong = num14 * thangthienkhicongBonusValue;
								break;
							case 666:
								ThangThien_4_HongNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 679:
								ThangThien_5_HoaLongPhapChieu = num14 * thangthienkhicongBonusValue;
								break;
							case 680:
								ThangThien_5_KinhThienDongDia = num14 * thangthienkhicongBonusValue;
								break;
							case 681:
								ThangThien_5_DietTheCuongVu = num14 * thangthienkhicongBonusValue;
								break;
							case 682:
								ThangThien_5_ThienLyNhatKich = num14 * thangthienkhicongBonusValue;
								break;
							case 683:
								ThangThien_5_HinhDiYeuTuong = num14 * 0.2;
								break;
							case 684:
								ThangThien_5_NhatChieuSatThan = num14 * thangthienkhicongBonusValue;
								break;
							case 685:
								ThangThien_5_LongTraoTiemChiThu = num14 * thangthienkhicongBonusValue;
								break;
							case 686:
								ThangThien_5_ThienMaChiLuc = num14 * 0.5 - 0.5;
								break;
							case 687:
								ThangThien_5_DongLinhDienThuat = num14 * thangthienkhicongBonusValue;
								break;
							case 688:
								ThangThien_5_BatTu_ChiKhu = num14 * thangthienkhicongBonusValue;
								break;
							case 689:
								ThangThien_5_MaHonChiLuc = num14 * 2.0;
								break;
							case 690:
								ThangThien_5_PhaKhongTruyTinh = 1.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 700:
								DamHoaLien_ThangThien_1_KhiCong_BaVuongQuyDienGiap = num14 * thangthienkhicongBonusValue;
								break;
							case 701:
								ThangThien_4_TruongHongQuanThien = num14 * thangthienkhicongBonusValue;
								break;
							case 702:
								ThangThien_4_AiHongBienDa = num14 * thangthienkhicongBonusValue;
								break;
							case 600:
								HanBaoQuan_ThangThien_1_KhiCong_HanhPhongLongVu = num14 * thangthienkhicongBonusValue;
								break;
							case 601:
								HanBaoQuan_ThangThien_2_KhiCong_ThienMaHoThe = num14 * thangthienkhicongBonusValue;
								break;
							case 602:
								HanBaoQuan_ThangThien_3_KhiCong_NoiTucHanhTam = num14 * thangthienkhicongBonusValue;
								break;
							case 603:
								ThangThien_4_TruongHongQuanThien = num14 * thangthienkhicongBonusValue;
								break;
							case 604:
								ThangThien_4_AiHongBienDa = num14 * thangthienkhicongBonusValue;
								break;
							case 610:
								ThanNu_PhanNoDieuTiet = num14 * thangthienkhicongBonusValue;
								break;
							case 611:
								ThanNu_CoDocGiaiTru = num14 * thangthienkhicongBonusValue;
								break;
							case 612:
								ThanNu_ThanLucBaoHo = num14 * thangthienkhicongBonusValue;
								break;
							case 613:
								ThangThien_4_ManNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 614:
								ThangThien_4_VongMaiThiemHoa = num14 * thangthienkhicongBonusValue;
								break;
							case 615:
								ThangThien_5_TriTan = 15.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 667:
							case 668:
							case 669:
							case 670:
							case 671:
							case 672:
							case 673:
							case 674:
							case 675:
							case 676:
							case 677:
							case 678:
								ThangThien_5_TriTan = 3.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 616:
								ThanNu_ThiDocBaoPhat = num14 * thangthienkhicongBonusValue;
								break;
							case 561:
								Quyen_ThangThien_1_KhiCong_DoatMenhLienHoan = num14 * thangthienkhicongBonusValue;
								break;
							case 562:
								Quyen_ThangThien_2_KhiCong_DienQuangThachHoa = 10.0 + num14 * thangthienkhicongBonusValue;
								break;
							case 563:
								Quyen_ThangThien_3_KhiCong_TinhIchCauTinh = num14 * thangthienkhicongBonusValue;
								break;
							case 564:
								ThangThien_4_HongNguyetCuongPhong = num14 * thangthienkhicongBonusValue;
								break;
							case 565:
								ThangThien_4_DocXaXuatDong = num14 * thangthienkhicongBonusValue;
								break;
							case 620:
							case 621:
							case 622:
							case 623:
							case 624:
							case 625:
							case 626:
							case 627:
							case 628:
							case 629:
							case 630:
							case 631:
							case 632:
								Giam_Bot_CuongKhi = num14;
								KCTT_16x_All_TinhKimBachLuyen = (num14 - Giam_Bot_CongKich) * (thangthienkhicongBonusValue + 6.0);
								if (KCTT_16x_All_TinhKimBachLuyen < 0.0)
								{
									KCTT_16x_All_TinhKimBachLuyen = 0.0;
								}
								break;
							case 633:
							case 634:
							case 635:
							case 636:
							case 637:
							case 638:
							case 639:
							case 640:
							case 641:
							case 642:
							case 643:
							case 644:
							case 645:
								Giam_Bot_CongKich = num14;
								KCTT_16x_All_HuyetKhiPhuongCuong = (num14 - Giam_Bot_CuongKhi) * (thangthienkhicongBonusValue + 6.0);
								if (KCTT_16x_All_HuyetKhiPhuongCuong < 0.0)
								{
									KCTT_16x_All_HuyetKhiPhuongCuong = 0.0;
								}
								break;
							case 570:
								Dao_ThangThien_6_VeRongDiemMat = num14 * thangthienkhicongBonusValue;
								NhanVat_KhiCong_ThemVao_HP = (int)(num14 * thangthienkhicongBonusValue / 16.0 * CharacterMax_HP);
								break;
							case 571:
								Kiem_ThangThien_6_BachDocBatXam = num14 * 1.0;
								break;
							case 572:
								Thuong_ThangThien_6_HanBangLinhVuc = num14 * 0.3;
								break;
							case 573:
								Cung_ThangThien_6_AcTanMuiTenNgheo = num14;
								break;
							case 574:
								DaiPhu_ThangThien_6_VanTamNguyetTinh = num14 * 0.3;
								break;
							case 575:
								Ninja_ThangThien_6_BenNgoaiVuaBenTrongVua = num14 * thangthienkhicongBonusValue;
								NhanVat_KhiCong_ThemVao_HP = (int)(num14 * thangthienkhicongBonusValue / 20.0 * CharacterMax_HP);
								break;
							case 576:
								CamSu_ThangThien_6_HuyetMachLenCao = num14 * thangthienkhicongBonusValue;
								FLD_NhanVat_KhiCong_PhongNgu = (int)(num14 * (thangthienkhicongBonusValue + 3.0));
								break;
							case 577:
								HanBaoQuan_ThangThien_6_ChanKhiHoanNguyen = num14 * 0.1;
								break;
							case 578:
								DamHoaLien_ThangThien_6_DienQuangSuongMai = num14 - 1.0;
								break;
							case 579:
								QuyenSu_ThangThien_6_KhongChuongNgaiVat = num14 * thangthienkhicongBonusValue;
								break;
							case 580:
								MaiLieuChan_ThangThien_6_BienNguyThanhAn = num14 * thangthienkhicongBonusValue;
								break;
							case 581:
								TuHao_ThangThien_6_BanNguocVoHieu = num14 * thangthienkhicongBonusValue;
								break;
							case 582:
								ThanNu_ThangThien_6_ChongLaiThanPhap = num14 * thangthienkhicongBonusValue;
								break;
						}
					}
				}
				if (Player_Job_level != 7 || ThangThienKhiCong == null)
				{
					return;
				}
				foreach (var value2 in PhanKhiCong.Values)
				{
					if (value2.KhiCong_SoLuong > 0)
					{
						var maxKhiCong_Tren1KhiCong3 = World.MaxKhiCong_Tren1KhiCong;
						var num16 = value2.KhiCong_SoLuong + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + NhanGiaTri_TangRieng_CuaKhiCong(value2.KhiCongID) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
						if (num16 > maxKhiCong_Tren1KhiCong3)
						{
							num16 = maxKhiCong_Tren1KhiCong3;
						}
						var thangthienkhicongBonusValue2 = GetThangthienkhicongBonusValue(value2.KhiCongID);
						switch (value2.KhiCongID)
						{
							case 2001:
								PhanKhiCong_17x_NhanKiemNhatThe_Kiem = num16 * 0.01;
								break;
							case 2002:
								PhanKhiCong_17x_HoThanCanhKhi_Kiem = num16 * 0.01;
								break;
							case 2003:
								PhanKhiCong_17x_TuLuongThienKim_Dao = num16 * 0.01;
								break;
							case 2004:
								PhanKhiCong_17x_BaKhiPhaGiap_Dao = num16 * 0.01;
								break;
							case 2005:
								PhanKhiCong_17x_HongNguyenCuongPhong_Thuong = num16 * 0.01;
								break;
							case 2006:
								PhanKhiCong_17x_DiaPhanXungKhi_Thuong = num16 * 0.01;
								break;
							case 2007:
								PhanKhiCong_17x_VoAnhAmTien_Cung = num16 * 0.01;
								break;
							case 2008:
								PhanKhiCong_17x_DocBaGiangHo_Cung = num16 * 0.01;
								break;
							case 2009:
								PhanKhiCong_17x_BachSuNhuY_DaiPhu = num16 * 0.01;
								break;
							case 2010:
								PhanKhiCong_17x_VanTamNguyetTinh_DaiPhu = num16 * 0.01;
								break;
							case 2011:
								PhanKhiCong_17x_LietNhatDiemDiem_ThichKhach = num16 * 0.01;
								break;
							case 2012:
								PhanKhiCong_17x_NguKhiXungTieu_ThichKhach = num16 * 0.01;
								break;
							case 2013:
								PhanKhiCong_17x_KichLucCongThanh_CamSu = num16 * 0.01;
								break;
							case 2014:
								PhanKhiCong_17x_TienCamTamPhap_CamSu = num16 * 0.01;
								break;
							case 2015:
								PhanKhiCong_17x_ThienMaQuangHuyet_HBQ = num16 * 0.01;
								break;
							case 2016:
								PhanKhiCong_17x_BaKhiPhaGiap_HBQ = num16 * 0.01;
								break;
							case 2017:
								PhanKhiCong_17x_NhuKhacCuong_DHL = num16 * 0.01;
								break;
							case 2018:
								PhanKhiCong_17x_TuNhanDaiPhap_DHL = num16 * 0.01;
								break;
							case 2019:
								PhanKhiCong_17x_NoTamXuatKich_QuyenSu = num16 * 0.01;
								break;
							case 2020:
								PhanKhiCong_17x_DienQuangThachHoa_QuyenSu = num16 * 0.01;
								break;
							case 2021:
								PhanKhiCong_17x_NoKhiXungThien_MLC = num16 * 0.01;
								break;
							case 2022:
								PhanKhiCong_17x_HuyenVuLoiDien_MLC = num16 * 0.01;
								break;
							case 2023:
								PhanKhiCong_17x_DiTinhVanThien_TH = num16 * 0.01;
								break;
							case 2024:
								PhanKhiCong_17x_DiCongViThu_TH = num16 * 0.01;
								break;
							case 2025:
								PhanKhiCong_17x_HacHoaTapTrung_TN = num16 * 0.01;
								break;
							case 2026:
								PhanKhiCong_17x_ThiDocBaoPhat_TN = num16 * 0.01;
								break;
							case 2027:
								PhanKhiCong_17x_VongAmCoDoc_ALL = num16 * 0.01;
								break;
						}
					}
				}
			}
			catch (Exception ex)
			{
				foreach (var value3 in ThangThienKhiCong.Values)
				{
					LogHelper.WriteLine(LogLevel.Error, "Khí Công Thăng Thiên bị Lỗi - ID: [" + value3.KhiCongID + "] - " + ex.ToString());
				}
			}
		}
	}

    
}
