using System;
using System.Threading;
using System.Threading.Tasks;
using Grpc.Core;
using HeroYulgang.Core;
using HeroYulgang.Services;

namespace HeroYulgang.Services
{
    /// <summary>
    /// Manager cho gRPC server trong AOT
    /// </summary>
    public class GrpcServerManager : IDisposable
    {
        private readonly Logger _logger;
        private readonly ConfigManager _configManager;
        private Server? _grpcServer;
        private bool _disposed = false;

        public GrpcServerManager(Logger logger)
        {
            _logger = logger;
            _configManager = ConfigManager.Instance;
        }

        public async Task StartAsync()
        {
            try
            {
                var grpcPort = _configManager.ServerSettings.GrpcPort;
                var serverName = _configManager.ServerSettings.ServerName;

                _logger.Info($"Khởi động gRPC server cho AOT {serverName} trên port {grpcPort}...");

                _grpcServer = new Server
                {
                    Services =
                    {
                        AccountManagement.BindService(new AccountManagementService(_logger))
                    },
                    Ports = { new ServerPort("0.0.0.0", grpcPort, ServerCredentials.Insecure) }
                };

                _grpcServer.Start();

                _logger.Info($"✓ gRPC server đã khởi động thành công trên port {grpcPort}");

                // Cập nhật thông tin gRPC port trong LoginServerClient để đăng ký với LoginServer
                await UpdateLoginServerRegistration();
            }
            catch (Exception ex)
            {
                _logger.Error($"Không thể khởi động gRPC server: {ex.Message}");
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (_grpcServer != null)
            {
                _logger.Info("Đang dừng gRPC server...");

                try
                {
                    await _grpcServer.ShutdownAsync();
                    _logger.Info("✓ gRPC server đã dừng thành công");
                }
                catch (Exception ex)
                {
                    _logger.Error($"Lỗi khi dừng gRPC server: {ex.Message}");
                }
            }
        }

        private async Task UpdateLoginServerRegistration()
        {
            try
            {
                // Đợi một chút để đảm bảo gRPC server đã sẵn sàng
                await Task.Delay(1000);

                // Thông báo cho LoginServerClient để cập nhật thông tin gRPC port
                var loginClient = LoginServerClient.Instance;
                if (loginClient != null)
                {
                    // LoginServerClient sẽ tự động sử dụng GrpcPort từ config khi đăng ký
                    _logger.Info("Thông tin gRPC port sẽ được gửi đến LoginServer khi đăng ký");
                }
            }
            catch (Exception ex)
            {
                _logger.Warning($"Không thể cập nhật thông tin đăng ký với LoginServer: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            
            try
            {
                _grpcServer?.ShutdownAsync().Wait(5000);
            }
            catch (Exception ex)
            {
                _logger?.Error($"Lỗi khi dispose gRPC server: {ex.Message}");
            }
        }
    }
}
