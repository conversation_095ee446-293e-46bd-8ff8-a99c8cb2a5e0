-- =====================================================
-- Test Data for New Drop System
-- =====================================================

-- First, ensure the tables exist and insert default config if not exists
INSERT INTO tbl_drop_config (config_key, config_value, description) VALUES
('new_drop_system_enabled', 'true', 'Enable new drop system (true/false)'),
('global_drop_multiplier', '1.0', 'Global drop rate multiplier (decimal)'),
('debug_drop_logging', 'true', 'Enable detailed drop logging (true/false)'),
('max_drops_per_kill', '5', 'Maximum number of items that can drop from one kill'),
('level_gap_penalty', '0.1', 'Drop rate penalty per level difference'),
('team_drop_bonus', '1.2', 'Drop rate bonus for team kills')
ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    updated_at = NOW();

-- =====================================================
-- Level-based drops (converted from old system examples)
-- =====================================================

-- Low level drops (1-10)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('level_range', '1-10', 100001, 0.250000, 1, 1, 50, 100, true),   -- 25% chance, basic healing potion
('level_range', '1-10', 100002, 0.150000, 1, 2, 25, 90, true),    -- 15% chance, basic mana potion
('level_range', '1-10', 100003, 0.100000, 1, 1, 100, 80, true),   -- 10% chance, basic weapon
('level_range', '1-10', 100004, 0.050000, 1, 1, 150, 70, true),   -- 5% chance, rare item
('level_range', '1-10', 100005, 0.025000, 1, 1, 200, 60, true);   -- 2.5% chance, very rare item

-- Mid level drops (11-30)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('level_range', '11-30', 200001, 0.200000, 1, 1, 100, 100, true), -- 20% chance, intermediate healing potion
('level_range', '11-30', 200002, 0.120000, 1, 2, 75, 90, true),   -- 12% chance, intermediate mana potion
('level_range', '11-30', 200003, 0.080000, 1, 1, 150, 80, true),  -- 8% chance, intermediate weapon
('level_range', '11-30', 200004, 0.040000, 1, 1, 200, 70, true),  -- 4% chance, rare equipment
('level_range', '11-30', 200005, 0.020000, 1, 1, 250, 60, true);  -- 2% chance, very rare equipment

-- High level drops (31-50)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('level_range', '31-50', 300001, 0.150000, 1, 1, 150, 100, true), -- 15% chance, advanced healing potion
('level_range', '31-50', 300002, 0.100000, 1, 2, 125, 90, true),  -- 10% chance, advanced mana potion
('level_range', '31-50', 300003, 0.060000, 1, 1, 200, 80, true),  -- 6% chance, advanced weapon
('level_range', '31-50', 300004, 0.030000, 1, 1, 300, 70, true),  -- 3% chance, epic equipment
('level_range', '31-50', 300005, 0.015000, 1, 1, 400, 60, true);  -- 1.5% chance, legendary equipment

-- End game drops (51-100)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('level_range', '51-100', 400001, 0.100000, 1, 1, 200, 100, true), -- 10% chance, master healing potion
('level_range', '51-100', 400002, 0.080000, 1, 2, 175, 90, true),  -- 8% chance, master mana potion
('level_range', '51-100', 400003, 0.040000, 1, 1, 300, 80, true),  -- 4% chance, master weapon
('level_range', '51-100', 400004, 0.020000, 1, 1, 500, 70, true),  -- 2% chance, artifact equipment
('level_range', '51-100', 400005, 0.010000, 1, 1, 750, 60, true);  -- 1% chance, mythic equipment

-- =====================================================
-- NPC-specific drops (Boss drops)
-- =====================================================

-- Example Boss 1 (NPC ID: 15100)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, magic1, priority, is_active) VALUES
('npc_specific', '15100', 500001, 0.500000, 1, 1, 300, 200, 200, true), -- 50% chance, boss healing potion
('npc_specific', '15100', 500002, 0.250000, 1, 1, 500, 300, 190, true), -- 25% chance, boss weapon
('npc_specific', '15100', 500003, 0.125000, 1, 1, 750, 500, 180, true), -- 12.5% chance, boss armor
('npc_specific', '15100', 500004, 0.062500, 1, 1, 1000, 750, 170, true), -- 6.25% chance, boss accessory
('npc_specific', '15100', 500005, 0.031250, 1, 1, 1500, 1000, 160, true); -- 3.125% chance, boss rare drop

-- Example Boss 2 (NPC ID: 15236)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, magic1, priority, is_active) VALUES
('npc_specific', '15236', 600001, 0.400000, 1, 2, 400, 250, 200, true), -- 40% chance, elite healing potion
('npc_specific', '15236', 600002, 0.200000, 1, 1, 600, 400, 190, true), -- 20% chance, elite weapon
('npc_specific', '15236', 600003, 0.100000, 1, 1, 900, 600, 180, true), -- 10% chance, elite armor
('npc_specific', '15236', 600004, 0.050000, 1, 1, 1200, 900, 170, true), -- 5% chance, elite accessory
('npc_specific', '15236', 600005, 0.025000, 1, 1, 1800, 1200, 160, true); -- 2.5% chance, elite rare drop

-- Example World Boss (NPC ID: 15403)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, magic1, magic2, priority, is_active) VALUES
('npc_specific', '15403', 700001, 1.000000, 3, 5, 500, 300, 200, 250, true), -- 100% chance, world boss reward
('npc_specific', '15403', 700002, 0.750000, 1, 1, 800, 500, 300, 240, true), -- 75% chance, world boss weapon
('npc_specific', '15403', 700003, 0.500000, 1, 1, 1200, 800, 500, 230, true), -- 50% chance, world boss armor
('npc_specific', '15403', 700004, 0.250000, 1, 1, 1800, 1200, 800, 220, true), -- 25% chance, world boss accessory
('npc_specific', '15403', 700005, 0.100000, 1, 1, 2500, 1800, 1200, 210, true); -- 10% chance, world boss legendary

-- =====================================================
-- Quest-based drops
-- =====================================================

-- Quest 1001: Beginner quest drops
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('quest_based', '1001', 800001, 1.000000, 1, 1, 100, 300, true), -- 100% chance when quest 1001 is active
('quest_based', '1001', 800002, 0.500000, 1, 2, 150, 290, true); -- 50% chance for bonus item

-- Quest 1002: Intermediate quest drops
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('quest_based', '1002', 800003, 1.000000, 1, 1, 200, 300, true), -- 100% chance when quest 1002 is active
('quest_based', '1002', 800004, 0.750000, 1, 1, 250, 290, true); -- 75% chance for bonus item

-- Quest 1003: Advanced quest drops
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, magic1, priority, is_active) VALUES
('quest_based', '1003', 800005, 1.000000, 1, 1, 300, 200, 300, true), -- 100% chance when quest 1003 is active
('quest_based', '1003', 800006, 0.600000, 1, 1, 400, 300, 290, true); -- 60% chance for bonus item

-- =====================================================
-- Special event drops (can be disabled easily)
-- =====================================================

-- Christmas event drops (disabled by default)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active, expire_days) VALUES
('level_range', '1-100', 900001, 0.050000, 1, 1, 500, 50, false, 7), -- 5% chance, Christmas item (disabled)
('level_range', '1-100', 900002, 0.025000, 1, 1, 750, 40, false, 7); -- 2.5% chance, rare Christmas item (disabled)

-- =====================================================
-- Test drops with different priorities
-- =====================================================

-- High priority drops (should be processed first)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, quantity_min, quantity_max, magic0, priority, is_active) VALUES
('level_range', '1-5', 999001, 0.100000, 1, 1, 50, 500, true), -- High priority test drop
('level_range', '1-5', 999002, 0.100000, 1, 1, 75, 400, true), -- Medium priority test drop
('level_range', '1-5', 999003, 0.100000, 1, 1, 100, 300, true); -- Normal priority test drop

-- =====================================================
-- Validation and summary
-- =====================================================

-- Check total number of drops inserted
SELECT 
    source_type,
    COUNT(*) as drop_count,
    AVG(drop_rate) as avg_drop_rate,
    MIN(drop_rate) as min_drop_rate,
    MAX(drop_rate) as max_drop_rate
FROM tbl_new_drops 
WHERE is_active = true
GROUP BY source_type
ORDER BY source_type;

-- Check configuration
SELECT * FROM tbl_drop_config ORDER BY config_key;

-- Show some example drops with readable rates
SELECT 
    id,
    source_type,
    source_value,
    item_id,
    CONCAT(ROUND(drop_rate * 100, 4), '%') as drop_percentage,
    quantity_min,
    quantity_max,
    priority,
    is_active
FROM tbl_new_drops 
WHERE is_active = true
ORDER BY source_type, priority DESC, drop_rate DESC
LIMIT 20;

-- =====================================================
-- Comments for testing
-- =====================================================

/*
To test the new drop system:

1. Enable the system:
   UPDATE tbl_drop_config SET config_value = 'true' WHERE config_key = 'new_drop_system_enabled';

2. Enable debug logging:
   UPDATE tbl_drop_config SET config_value = 'true' WHERE config_key = 'debug_drop_logging';

3. Test with different multipliers:
   UPDATE tbl_drop_config SET config_value = '2.0' WHERE config_key = 'global_drop_multiplier';

4. Kill monsters with IDs 15100, 15236, 15403 to test boss drops
5. Kill level 1-10 monsters to test level-based drops
6. Have active quests 1001, 1002, 1003 to test quest-based drops

Expected behavior:
- Level 1-10 monsters should drop items 100001-100005 with specified rates
- Boss 15100 should drop items 500001-500005 with higher rates
- Quest-based drops should only appear when specific quests are active
- Debug logs should show detailed drop calculations
*/
