using System;
using System.Net;
using Akka.Actor;
using HeroYulgang.Core.Networking.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.Network;
using HeroYulgang.Core.NetCore;
using HeroYulgang.Core;

namespace HeroYulgang.Core.Networking.Utils
{
    /// <summary>
    /// Factory class để tạo và quản lý các network components
    /// Renamed từ PlayerNetworkManager với namespace mới
    /// </summary>
    public static class NetworkFactory
    {
        /// <summary>
        /// Tạo một ActorNetState mới từ thông tin kết nối
        /// </summary>
        /// <param name="connection">Tham chiếu đến actor kết nối</param>
        /// <param name="connectionId">ConnectionID cho network layer</param>
        /// <param name="remoteEndPoint">Địa chỉ IP và port của client</param>
        /// <returns>Đ<PERSON>i tượng ActorNetState mới</returns>
        public static ActorNetState CreateActorNetState(IActorRef connection, int connectionId, IPEndPoint remoteEndPoint)
        {
            try
            {
                // Always create UnifiedNetState for Akka mode (backward compatibility)
                var unifiedNetState = new UnifiedNetState(connection, connectionId, remoteEndPoint);
                // LogHelper.WriteLine(LogLevel.Debug, $"Đã tạo UnifiedNetState (Akka mode) cho ConnectionID {connectionId} từ {remoteEndPoint}");
                return unifiedNetState;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi tạo UnifiedNetState: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Cập nhật property Client của Players để sử dụng ActorNetState
        /// </summary>
        /// <param name="player">Đối tượng Players cần cập nhật</param>
        /// <param name="actorNetState">Đối tượng ActorNetState mới</param>
        public static void UpdatePlayerClient(Players player, ActorNetState actorNetState)
        {
            try
            {
                // Gọi phương thức MigrateToActorNetState để cập nhật Client
                player.MigrateToActorNetState(actorNetState);

                // LogHelper.WriteLine(LogLevel.Debug, $"Đã cập nhật Client của người chơi {player.CharacterName} sang ActorNetState");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi cập nhật Client của Players: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Tạo OfflineActorNetState cho offline players
        /// </summary>
        /// <param name="playerSessionId">PlayerSessionID cho offline player</param>
        /// <param name="remoteEndPoint">Địa chỉ IP và port của client</param>
        /// <returns>Đối tượng OfflineActorNetState mới</returns>
        public static OfflineActorNetState CreateOfflineActorNetState(int playerSessionId, IPEndPoint remoteEndPoint)
        {
            try
            {
                var offlineNetState = new OfflineActorNetState(playerSessionId, remoteEndPoint);
                LogHelper.WriteLine(LogLevel.Debug, $"Đã tạo OfflineActorNetState mới cho PlayerSessionID {playerSessionId} từ {remoteEndPoint}");
                return offlineNetState;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi tạo OfflineActorNetState: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Kiểm tra xem một ActorNetState có hợp lệ không
        /// </summary>
        /// <param name="actorNetState">ActorNetState cần kiểm tra</param>
        /// <returns>True nếu hợp lệ, false nếu không</returns>
        public static bool IsValidActorNetState(ActorNetState? actorNetState)
        {
            return actorNetState != null && actorNetState.Running;
        }

        /// <summary>
        /// Cleanup tài nguyên cho một ActorNetState
        /// </summary>
        /// <param name="actorNetState">ActorNetState cần cleanup</param>
        public static void CleanupActorNetState(ActorNetState? actorNetState)
        {
            try
            {
                if (actorNetState != null)
                {
                    actorNetState.Dispose();
                    LogHelper.WriteLine(LogLevel.Debug, $"Đã cleanup ActorNetState cho ConnectionID {actorNetState.ConnectionID}, PlayerSessionID {actorNetState.PlayerSessionID}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi cleanup ActorNetState: {ex.Message}");
            }
        }
    }
}
