using System;
using System.Collections.Generic;
using System.Linq;
using RxjhServer.TestBot.Core;
using RxjhServer.HelperTools;

namespace RxjhServer.TestBot.Modules.Core
{
    /// <summary>
    /// Module quản lý nhặt đồ và inventory
    /// </summary>
    public class ItemModule : BaseBotModule
    {
        public override string ModuleName => "ItemModule";
        public override int Priority => 4;
        
        protected override int UpdateInterval => 2000; // Check every 2 seconds
        
        private DateTime _lastPickupTime = DateTime.MinValue;
        
        protected override bool OnCanExecute()
        {
            return Config.ItemPickupEnabled && base.OnCanExecute();
        }
        
        protected override void OnUpdate()
        {
            // Tìm và nhặt đồ
            HandleItemPickup();
        }
        
        /// <summary>
        /// Xử lý nhặt đồ tự động
        /// </summary>
        private void HandleItemPickup()
        {
            try
            {
                if (!IsTimeToAct(_lastPickupTime, 1000)) // 1 second cooldown
                    return;
                    
                // Tìm item để nhặt
                var itemId = FindItemToPickup();
                if (itemId != 0 && World.GroundItemList.ContainsKey(itemId))
                {
                    // Kiểm tra inventory có chỗ không
                    var vacancyPosition = Player.GetParcelVacancyPosition();
                    if (Player.Offline_TreoMay_Mode_ON_OFF != 0 || vacancyPosition >= 1)
                    {
                        PickupItem(itemId);
                        _lastPickupTime = DateTime.Now;
                    }
                    else
                    {
                        LogDebug("Inventory full, cannot pickup item");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in item pickup: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Tìm item để nhặt (tương tự OfflineAutoNhatItem)
        /// </summary>
        /// <returns>Item ID để nhặt hoặc 0 nếu không có</returns>
        private long FindItemToPickup()
        {
            try
            {
                var availableItems = new List<GroundItem>();
                var random = new Random();
                
                foreach (var groundItem in World.GroundItemList.Values)
                {
                    try
                    {
                        // Kiểm tra item có trong phạm vi không
                        if (!Player.FindGroundItems(Config.ItemPickupRange, groundItem))
                            continue;
                            
                        // Kiểm tra priority (item có thuộc về player này không)
                        if (groundItem.ItemPriority?.CharacterName != Player.CharacterName)
                            continue;
                            
                        // Kiểm tra filter
                        if (!ShouldPickupItem(groundItem))
                            continue;
                            
                        availableItems.Add(groundItem);
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error checking ground item: {ex.Message}");
                    }
                }
                
                if (availableItems.Count > 0)
                {
                    var selectedItem = availableItems[random.Next(0, availableItems.Count)];
                    LogDebug($"Found item to pickup: {selectedItem.Item.GetItemName()}");
                    return selectedItem.Item.GetItemGlobal_ID;
                }
                
                return 0;
            }
            catch (Exception ex)
            {
                LogError($"Error finding item to pickup: {ex.Message}");
                return 0;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem có nên nhặt item này không
        /// </summary>
        /// <param name="groundItem">Ground item</param>
        /// <returns>True nếu nên nhặt</returns>
        private bool ShouldPickupItem(GroundItem groundItem)
        {
            try
            {
                var itemId = (int)groundItem.Item.GetVatPham_ID;
                
                // Kiểm tra blocked list
                if (Config.BlockedItemIds.Contains(itemId))
                {
                    LogDebug($"Item {itemId} is blocked");
                    return false;
                }
                
                // Nếu có allowed list và item không trong đó
                if (Config.AllowedItemIds.Count > 0 && !Config.AllowedItemIds.Contains(itemId))
                {
                    LogDebug($"Item {itemId} not in allowed list");
                    return false;
                }
                
                // Kiểm tra custom filter
                var customFilter = GetSetting("CustomItemFilter", "");
                if (!string.IsNullOrEmpty(customFilter))
                {
                    // TODO: Implement custom filter logic
                }
                
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Error checking item filter: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Nhặt item (tương tự Offline_TreoMay_VatPham)
        /// </summary>
        /// <param name="itemId">Item ID</param>
        private void PickupItem(long itemId)
        {
            try
            {
                var packetHex = "AA5517002C010B000800C676600000000000000000000000000055AA";
                var packet = Converter.HexStringToByte(packetHex);
                
                // Set item ID
                System.Buffer.BlockCopy(BitConverter.GetBytes(itemId), 0, packet, 10, 8);
                
                // Set session ID
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.SessionID), 0, packet, 4, 2);
                
                Player.PickUpItems(packet, packet.Length);
                
                LogDebug($"Picked up item: {itemId}");
            }
            catch (Exception ex)
            {
                LogError($"Error picking up item {itemId}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Thêm item ID vào allowed list
        /// </summary>
        /// <param name="itemId">Item ID</param>
        public void AddAllowedItem(int itemId)
        {
            if (!Config.AllowedItemIds.Contains(itemId))
            {
                Config.AllowedItemIds.Add(itemId);
                LogInfo($"Added allowed item: {itemId}");
            }
        }
        
        /// <summary>
        /// Xóa item ID khỏi allowed list
        /// </summary>
        /// <param name="itemId">Item ID</param>
        public void RemoveAllowedItem(int itemId)
        {
            if (Config.AllowedItemIds.Remove(itemId))
            {
                LogInfo($"Removed allowed item: {itemId}");
            }
        }
        
        /// <summary>
        /// Thêm item ID vào blocked list
        /// </summary>
        /// <param name="itemId">Item ID</param>
        public void AddBlockedItem(int itemId)
        {
            if (!Config.BlockedItemIds.Contains(itemId))
            {
                Config.BlockedItemIds.Add(itemId);
                LogInfo($"Added blocked item: {itemId}");
            }
        }
        
        /// <summary>
        /// Xóa item ID khỏi blocked list
        /// </summary>
        /// <param name="itemId">Item ID</param>
        public void RemoveBlockedItem(int itemId)
        {
            if (Config.BlockedItemIds.Remove(itemId))
            {
                LogInfo($"Removed blocked item: {itemId}");
            }
        }
        
        /// <summary>
        /// Lấy số lượng item có thể nhặt trong phạm vi
        /// </summary>
        /// <returns>Số lượng item</returns>
        public int GetAvailableItemCount()
        {
            try
            {
                var count = 0;
                foreach (var groundItem in World.GroundItemList.Values)
                {
                    if (Player.FindGroundItems(Config.ItemPickupRange, groundItem) &&
                        groundItem.ItemPriority?.CharacterName == Player.CharacterName &&
                        ShouldPickupItem(groundItem))
                    {
                        count++;
                    }
                }
                return count;
            }
            catch
            {
                return 0;
            }
        }
        
        /// <summary>
        /// Lấy thông tin inventory
        /// </summary>
        /// <returns>Thông tin inventory</returns>
        public string GetInventoryInfo()
        {
            try
            {
                var vacancyPosition = Player.GetParcelVacancyPosition();
                var availableItems = GetAvailableItemCount();
                
                return $"Inventory slots: {vacancyPosition} | Available items: {availableItems}";
            }
            catch
            {
                return "Inventory info: Unknown";
            }
        }
        
        /// <summary>
        /// Force pickup item cụ thể
        /// </summary>
        /// <param name="itemId">Item ID</param>
        public void ForcePickupItem(long itemId)
        {
            try
            {
                if (World.GroundItemList.ContainsKey(itemId))
                {
                    PickupItem(itemId);
                    LogInfo($"Force picked up item: {itemId}");
                }
                else
                {
                    LogWarning($"Item {itemId} not found on ground");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error force picking up item {itemId}: {ex.Message}");
            }
        }
    }
}
