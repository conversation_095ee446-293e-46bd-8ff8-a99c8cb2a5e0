﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_player_quests {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public long id { get; set; }

		[JsonProperty, Column(StringLength = -2, IsNullable = false)]
		public string character_name { get; set; }

		[JsonProperty]
		public int quest_id { get; set; }

		[JsonProperty]
		public short quest_status { get; set; } = 0;

		[JsonProperty]
		public int current_stage { get; set; } = 1;

		[JsonProperty, Column(StringLength = -2)]
		public string progress_data { get; set; }

		[JsonProperty]
		public DateTime? start_time { get; set; }

		[JsonProperty]
		public DateTime? complete_time { get; set; }

		[JsonProperty]
		public DateTime? reset_time { get; set; }

		[JsonProperty, Column(StringLength = 20)]
		public string reset_type { get; set; } = "none";

		[JsonProperty]
		public int? reset_interval_days { get; set; } = 0;

		[JsonProperty]
		public int? completion_count { get; set; } = 0;

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime? created_at { get; set; }

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime? updated_at { get; set; }

	}

}
