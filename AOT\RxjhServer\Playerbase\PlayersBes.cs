using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using Akka.Streams.Implementation.Fusing;
using HeroYulgang.Constants;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Database.FreeSql.Extensions;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using RxjhServer.Database;
using RxjhServer.HelperTools;

namespace RxjhServer;

public partial class PlayersBes : X_Khi_Cong_Thuoc_Tinh
{
	/// <summary>
	/// Kiểm tra xem player có phải là offline player không
	/// </summary>
	/// <returns>true nếu là offline player</returns>
	protected bool IsOfflinePlayer()
	{
		return (Client != null && Client.TreoMay) || (Client != null && Client.GetType().Name == "OfflineActorNetState");
	}

	/// <summary>
	/// Lấy Players object phù hợp cho offline/online player
	/// </summary>
	/// <returns>Players object</returns>
	protected Players GetTargetPlayer()
	{
		return IsOfflinePlayer() ? (Players)this : Client?.Player;
	}

	/// <summary>
	/// Kiểm tra xem có thể gửi packet không (chỉ cho online player)
	/// </summary>
	/// <returns>true nếu có thể gửi packet</returns>
	protected bool CanSendPacket()
	{
		return !IsOfflinePlayer() && Client != null;
	}
	
    

	public bool KiemTraVatPhamHeThong(Itimesx itimesx_0)
	{
		if (itimesx_0.ThuocTinhLoaiHinh != 0)
		{
			if (itimesx_0.ThuocTinhLoaiHinh == 1)
			{
				if (World.VatPhamCaoNhatCongKichGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatCongKichGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 2)
			{
				if (World.VatPhamCaoNhatPhongNguGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatPhongNguGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 3)
			{
				if (World.VatPhamCaoNhatHPGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatHPGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 4)
			{
				if (World.VatPhamCaoNhatNoiCongGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatNoiCongGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 5)
			{
				if (World.VatPhamCaoNhatTrungDichGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatTrungDichGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 6)
			{
				if (World.VatPhamCaoNhatNeTranhGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatNeTranhGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 7)
			{
				if (World.VatPhamCaoNhatCongKichVoCongGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatCongKichVoCongGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 8)
			{
				if (World.VatPhamCaoNhatKhiCongGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatKhiCongGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 9)
			{
				if (World.VatPhamCaoNhatHopThanhGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatHopThanhGiaTri)
				{
					return true;
				}
			}
			else if (itimesx_0.ThuocTinhLoaiHinh == 11 && World.VatPhamCaoNhatHopThanhGiaTri != 0 && itimesx_0.ThuocTinhSoLuong >= World.VatPhamCaoNhatPhongNguVoCongGiaTri)
			{
				return true;
			}
		}
		return false;
	}

	public void BanAccount(int int_109, string string_11, string string_12)
	{
		AccountDb.BanAnAccount(string_11, string_12, int_109);
		Client.Dispose();
	}

	public void KiemTraVatPhamHeThong(string string_11, ref Item VatPhamCLass_0)
	{
		DateTime value = new(1970, 1, 1, 8, 0, 0);
		double num = BitConverter.ToInt64(VatPhamCLass_0.ItemGlobal_ID, 0);
		double num2 = BitConverter.ToInt64(VatPhamCLass_0.ItemGlobal_ID, 0);
		if (VatPhamCLass_0.FLD_DAY2 > VatPhamCLass_0.FLD_DAY1 && DateTime.Now.Subtract(value).TotalSeconds > VatPhamCLass_0.FLD_DAY2)
		{
			HeThongNhacNho("Hành trang của đại hiệp có bảo vật quá hạn [" + VatPhamCLass_0.GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Thiên cơ các");
			VatPhamCLass_0.VatPham_byte = new byte[76];
			return;
		}
		switch (World.KiemTraVatPham_BatHopPhap)
		{
			case 2:
				{
					VatPhamCLass_0.DatDuocVatPham_ThuocTinhPhuongThuc(0, 0, AccountID, CharacterName);
					if (!World.KiemTraThietBiList.TryGetValue(VatPhamCLass_0.FLD_RESIDE2, out var value2) || !KiemTraVatPhamHeThong2(VatPhamCLass_0, value2))
					{
						break;
					}
					// var text = $"SELECT  count(*)  FROM  Itme_Log  WHERE  ItmeId={BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0)}";
					// int num3;
					// try
					// {
					// 	num3 = (int)DBA.GetDBValue_3(text, "WebDb");
					// }
					// catch
					// {
					// 	num3 = -1;
					// }
					// if (num3 == 0)
					// {
					// 	LogHelper.WriteLine(LogLevel.Debug, "bất hợp pháp VatPham 11 " + string_11 + " [" + AccountID + "]-[" + CharacterName + "] Position[" + VatPhamCLass_0.VatPhamViTri + "] 编号[" + BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0) + "]  VatPhamTen称[" + VatPhamCLass_0.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(VatPhamCLass_0.VatPhamSoLuong, 0) + "]  ThuocTinh:[" + VatPhamCLass_0.FLD_MAGIC0 + "," + VatPhamCLass_0.FLD_MAGIC1 + "," + VatPhamCLass_0.FLD_MAGIC2 + "," + VatPhamCLass_0.FLD_MAGIC3 + "," + VatPhamCLass_0.FLD_MAGIC4 + "]  KhoaLai[" + VatPhamCLass_0.VatPham_KhoaLai + "]  魂[" + VatPhamCLass_0.FLD_FJ_LowSoul + "]  TienHoa[" + VatPhamCLass_0.FLD_FJ_TienHoa + "]");
					// 	if (World.KiemTraCacHoatDong_BatHopPhap == 1)
					// 	{
					// 		VatPhamCLass_0.VatPham_byte = new byte[World.Item_Db_Byte_Length];
					// 	}
					// 	else if (World.KiemTraCacHoatDong_BatHopPhap == 2)
					// 	{
					// 		BanAccount(82, AccountID, "KiemTraCacHoatDong_BatHopPhap2-6");
					// 	}
					// }
					break;
				}
			case 1:
				if (!World.KiemTraCacMuc_BatHopPhap && VatPhamCLass_0.VatPham_KhoaLai)
				{
					break;
				}
				if (VatPhamCLass_0.ThuocTinh1.ThuocTinhLoaiHinh != 0 && KiemTraVatPhamHeThong(VatPhamCLass_0.ThuocTinh1))
				{
					LogHelper.WriteLine(LogLevel.Debug, "bất hợp pháp VatPham 22 " + string_11 + "  [" + AccountID + "]-[" + CharacterName + "]  Position[" + VatPhamCLass_0.VatPhamViTri + "]  编号[" + BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0) + "]  VatPhamTen称[" + VatPhamCLass_0.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(VatPhamCLass_0.VatPhamSoLuong, 0) + "]  ThuocTinh:[" + VatPhamCLass_0.FLD_MAGIC0 + "," + VatPhamCLass_0.FLD_MAGIC1 + "," + VatPhamCLass_0.FLD_MAGIC2 + "," + VatPhamCLass_0.FLD_MAGIC3 + "," + VatPhamCLass_0.FLD_MAGIC4 + "]");
					if (World.KiemTraCacHoatDong_BatHopPhap == 1)
					{
						VatPhamCLass_0.VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else if (World.KiemTraCacHoatDong_BatHopPhap == 2)
					{
						BanAccount(83, AccountID, "KiemTraCacHoatDong_BatHopPhap2-1");
					}
				}
				else if (VatPhamCLass_0.ThuocTinh2.ThuocTinhLoaiHinh != 0 && KiemTraVatPhamHeThong(VatPhamCLass_0.ThuocTinh2))
				{
					LogHelper.WriteLine(LogLevel.Debug, "bất hợp pháp VatPham 33 " + string_11 + "  [" + AccountID + "]-[" + CharacterName + "]  Position[" + VatPhamCLass_0.VatPhamViTri + "]  编号[" + BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0) + "]  VatPhamTen称[" + VatPhamCLass_0.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(VatPhamCLass_0.VatPhamSoLuong, 0) + "]  ThuocTinh:[" + VatPhamCLass_0.FLD_MAGIC0 + "," + VatPhamCLass_0.FLD_MAGIC1 + "," + VatPhamCLass_0.FLD_MAGIC2 + "," + VatPhamCLass_0.FLD_MAGIC3 + "," + VatPhamCLass_0.FLD_MAGIC4 + "]");
					if (World.KiemTraCacHoatDong_BatHopPhap == 1)
					{
						VatPhamCLass_0.VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else if (World.KiemTraCacHoatDong_BatHopPhap == 2)
					{
						BanAccount(84, AccountID, "KiemTraCacHoatDong_BatHopPhap2-2");
					}
				}
				else if (VatPhamCLass_0.ThuocTinh3.ThuocTinhLoaiHinh != 0 && KiemTraVatPhamHeThong(VatPhamCLass_0.ThuocTinh3))
				{
					LogHelper.WriteLine(LogLevel.Debug, "bất hợp pháp VatPham 44 " + string_11 + "  [" + AccountID + "]-[" + CharacterName + "]  Position[" + VatPhamCLass_0.VatPhamViTri + "]  编号[" + BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0) + "]  VatPhamTen称[" + VatPhamCLass_0.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(VatPhamCLass_0.VatPhamSoLuong, 0) + "]  ThuocTinh:[" + VatPhamCLass_0.FLD_MAGIC0 + "," + VatPhamCLass_0.FLD_MAGIC1 + "," + VatPhamCLass_0.FLD_MAGIC2 + "," + VatPhamCLass_0.FLD_MAGIC3 + "," + VatPhamCLass_0.FLD_MAGIC4 + "]");
					if (World.KiemTraCacHoatDong_BatHopPhap == 1)
					{
						VatPhamCLass_0.VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else if (World.KiemTraCacHoatDong_BatHopPhap == 2)
					{
						BanAccount(85, AccountID, "KiemTraCacHoatDong_BatHopPhap2-3");
					}
				}
				else if (VatPhamCLass_0.ThuocTinh4.ThuocTinhLoaiHinh != 0 && KiemTraVatPhamHeThong(VatPhamCLass_0.ThuocTinh4))
				{
					LogHelper.WriteLine(LogLevel.Debug, "bất hợp pháp VatPham 55 " + string_11 + "  [" + AccountID + "]-[" + CharacterName + "]  Position[" + VatPhamCLass_0.VatPhamViTri + "]  编号[" + BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0) + "]  VatPhamTen称[" + VatPhamCLass_0.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(VatPhamCLass_0.VatPhamSoLuong, 0) + "]  ThuocTinh:[" + VatPhamCLass_0.FLD_MAGIC0 + "," + VatPhamCLass_0.FLD_MAGIC1 + "," + VatPhamCLass_0.FLD_MAGIC2 + "," + VatPhamCLass_0.FLD_MAGIC3 + "," + VatPhamCLass_0.FLD_MAGIC4 + "]");
					if (World.KiemTraCacHoatDong_BatHopPhap == 1)
					{
						VatPhamCLass_0.VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else if (World.KiemTraCacHoatDong_BatHopPhap == 2)
					{
						BanAccount(86, AccountID, "KiemTraCacHoatDong_BatHopPhap2-4");
					}
				}
				else if ((VatPhamCLass_0.FLD_RESIDE2 == 1 || VatPhamCLass_0.FLD_RESIDE2 == 4) && VatPhamCLass_0.FLD_FJ_LowSoul >= World.VatPhamCaoNhatPhuHonGiaTri)
				{
					LogHelper.WriteLine(LogLevel.Debug, "bất hợp pháp VatPham 66 " + string_11 + "  [" + AccountID + "]-[" + CharacterName + "]  Position[" + VatPhamCLass_0.VatPhamViTri + "]  编号[" + BitConverter.ToInt32(VatPhamCLass_0.ItemGlobal_ID, 0) + "]  VatPhamTen称[" + VatPhamCLass_0.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(VatPhamCLass_0.VatPhamSoLuong, 0) + "]  ThuocTinh:[" + VatPhamCLass_0.FLD_MAGIC0 + "," + VatPhamCLass_0.FLD_MAGIC1 + "," + VatPhamCLass_0.FLD_MAGIC2 + "," + VatPhamCLass_0.FLD_MAGIC3 + "," + VatPhamCLass_0.FLD_MAGIC4 + "]");
					if (World.KiemTraCacHoatDong_BatHopPhap == 1)
					{
						VatPhamCLass_0.VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else if (World.KiemTraCacHoatDong_BatHopPhap == 2)
					{
						BanAccount(87, AccountID, "KiemTraCacHoatDong_BatHopPhap2-5");
					}
				}
				else if (num == num2 && VatPhamCLass_0.VatPhamViTri != VatPhamCLass_0.VatPhamViTri)
				{
					BanAccount(89, AccountID, "DupeItem");
				}
				break;
		}
	}

	public bool KiemTraVatPhamHeThong2(Item VatPhamCLass_0, X_Kiem_Tra_Thiet_Bi_Loai KiemTraThietBiClass_0)
	{
		if ((KiemTraThietBiClass_0.VatPhamCaoNhatCongKichGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_LucCongKich_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatCongKichGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatPhongNguGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_LucPhongNgu_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatPhongNguGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatHPGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_SinhMenhLuc_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatHPGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatNoiCongGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_NoiCong_Luc_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatNoiCongGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatTrungDichGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_TiLeChinhXac_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatTrungDichGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatNeTranhGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_NeTranh_Suat_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatNeTranhGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatCongKichVoCongGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_VoCong_LucCongKich >= KiemTraThietBiClass_0.VatPhamCaoNhatCongKichVoCongGiaTri) || (KiemTraThietBiClass_0.VatPhamCaoNhatKhiCongGiaTri != 0 && VatPhamCLass_0.VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang >= KiemTraThietBiClass_0.VatPhamCaoNhatKhiCongGiaTri))
		{
			return true;
		}
		if (KiemTraThietBiClass_0.VatPhamCaoNhatPhuHonGiaTri != 0)
		{
			return VatPhamCLass_0.FLD_FJ_LowSoul >= KiemTraThietBiClass_0.VatPhamCaoNhatPhuHonGiaTri;
		}
		return false;
	}

	public async void ReadOutTheBeastData(long long_5, Players players_0)
	{
		try
		{
			var petData = await GameDb.GetPlayerPet((int)long_5);
			if (petData != null)
			{
				CharacterBeast = new(long_5, Client, petData, players_0);
				if (CharacterBeast != null)
				{
					CharacterBeast.FullServiceID = CharacterBeastFullServiceID;
				}
				else
				{
					HeThongNhacNho("Lỗi triệu tập, xin liên hệ với Quản Mệnh Quan!", 10, "Thiên cơ các");
				}
			}

			// var dBToDataTable = DBA.GetDBToDataTable($"SELECT      *      FROM      TBL_XWWL_Cw      WHERE      ItmeId      ={long_5}");
			// if (dBToDataTable.Rows.Count > 0)
			// {
			// 	CharacterBeast = new(long_5, Client, dBToDataTable, players_0);
			// 	if (CharacterBeast != null)
			// 	{
			// 		CharacterBeast.FullServiceID = CharacterBeastFullServiceID;
			// 	}
			// 	else
			// 	{
			// 		HeThongNhacNho("Lỗi triệu tập, xin liên hệ với Quản Mệnh Quan!", 10, "Thiên cơ các");
			// 	}
			// }
			// dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Read Out The Beast Data error [" + AccountID + "][" + CharacterName + "][" + long_5 + "]      " + ex.Message);
		}
	}

	public double GetKhiCongBonusValue(int int_109, int int_110, int int_111)
	{
		try
		{
			foreach (var value in World.KhiCongTangThem.Values)
			{
				if (value.FLD_JOB == int_109 && value.FLD_INDEX == int_110)
				{
					return (int_111 == 0) ? value.FLD_BonusRateValuePerPoint1 : value.FLD_BonusRateValuePerPoint2;
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "DatDuocKhiCongTangThem Giá trị tỷ lệ error [" + AccountID + "][" + CharacterName + "]" + ex.Message);
		}
		return 0.0;
	}

	public double GetThangthienkhicongBonusValue(int int_109)
	{
		try
		{
			if (World.ThangThienKhiCongList.TryGetValue(int_109, out var value))
			{
				return value.FLD_BonusRateValuePerPoint;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Dat Duoc Thang Thien Khi Cong Tang Them Giá trị tỷ lệ error [" + AccountID + "][" + CharacterName + "]" + ex.Message);
		}
		return 0.0;
	}

	public double NhanGiaTri_TangRieng_CuaKhiCong(int int_109)
	{
		return int_109 switch
		{
			58 => FLD_TrangBi_ThemVao_ThangThien_1HoThanKhiGiap,
			33 => FLD_TrangBi_ThemVao_ThangThien_3NoYChiHoa,
			0 => FLD_TrangBi_ThemVao_KhiCong_0,
			1 => FLD_TrangBi_ThemVao_KhiCong_1,
			2 => FLD_TrangBi_ThemVao_KhiCong_2,
			3 => FLD_TrangBi_ThemVao_KhiCong_3,
			4 => FLD_TrangBi_ThemVao_KhiCong_4,
			5 => FLD_TrangBi_ThemVao_KhiCong_5,
			6 => FLD_TrangBi_ThemVao_KhiCong_6,
			7 => FLD_TrangBi_ThemVao_KhiCong_7,
			8 => FLD_TrangBi_ThemVao_KhiCong_8,
			9 => FLD_TrangBi_ThemVao_KhiCong_9,
			10 => FLD_TrangBi_ThemVao_KhiCong_10,
			11 => FLD_TrangBi_ThemVao_KhiCong_11,
			13 => FLD_TrangBi_ThemVao_ThangThien_3HoaLong_ChiHoa,
			25 => FLD_TrangBi_ThemVao_ThangThien_1HoThan_CuongKhi,
			310 => FLD_TrangBi_ThemVao_ThangThien_1DonXuatNghichCanh,
			311 => FLD_TrangBi_ThemVao_ThangThien_2CungDoMatLo,
			313 => FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong,
			314 => FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong,
			321 => FLD_TrangBi_ThemVao_ThangThien_2ThienDiaDongTho,
			322 => FLD_TrangBi_ThemVao_ThangThien_3HoaPhuongLamTrieu,
			323 => FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong,
			324 => FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong,
			170 => FLD_TrangBi_ThemVao_ThangThien_3VoTinhDaKich,
			150 => FLD_TrangBi_ThemVao_ThangThien_2VanVatHoiXuan,
			370 => FLD_TrangBi_ThemVao_ThangThien_1DaMaTrienThan,
			371 => FLD_TrangBi_ThemVao_ThangThien_2ThuanThuyThoiChu,
			373 => FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong,
			374 => FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem,
			380 => FLD_TrangBi_ThemVao_ThangThien_1LucPhachHoaSon,
			381 => FLD_TrangBi_ThemVao_ThangThien_1TruongHong_QuanNhat,
			382 => FLD_TrangBi_ThemVao_ThangThien_1KimChungCuongKhi,
			383 => FLD_TrangBi_ThemVao_ThangThien_1VanKhiHanhTam,
			384 => FLD_TrangBi_ThemVao_ThangThien_1ChinhBanBoiNguyen,
			385 => FLD_TrangBi_ThemVao_ThangThien_1VanKhi_LieuThuong,
			386 => FLD_TrangBi_ThemVao_ThangThien_1BachBien_ThanHanh,
			387 => FLD_TrangBi_ThemVao_ThangThien_1CuongPhongThienY,
			390 => FLD_TrangBi_ThemVao_ThangThien_1PhiHoaDiemThuy,
			391 => FLD_TrangBi_ThemVao_ThangThien_2TamDamAnhNguyet,
			392 => FLD_TrangBi_ThemVao_ThangThien_3TuDaThuCa,
			393 => FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong,
			394 => FLD_TrangBi_ThemVao_ThangThien_4HuyenTiChanMach,
			352 => FLD_TrangBi_ThemVao_ThangThien_3MinhKinhChiThuy,
			353 => FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong,
			354 => FLD_TrangBi_ThemVao_ThangThien_4VongMaiThiemHoa,
			330 => FLD_TrangBi_ThemVao_ThangThien_1PhaGiapThuHon,
			331 => FLD_TrangBi_ThemVao_ThangThien_2DiThoiViTien,
			333 => FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong,
			334 => FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong,
			340 => FLD_TrangBi_ThemVao_ThangThien_1TuyetAnhXaHon,
			341 => FLD_TrangBi_ThemVao_ThangThien_2ThienQuanApDa,
			342 => FLD_TrangBi_ThemVao_ThangThien_3ThienNgoaiTamThi,
			343 => FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong,
			344 => FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem,
			700 => FLD_TrangBi_ThemVao_ThangThien_3DiNhuKhacCuong,
			701 => FLD_TrangBi_ThemVao_ThangThien_4TruongHongQuanThien,
			702 => FLD_TrangBi_ThemVao_ThangThien_4AiHongBienDa,
			600 => FLD_TrangBi_ThemVao_ThangThien_1HanhPhongLongVu,
			601 => FLD_TrangBi_ThemVao_ThangThien_2ThienMaHoThe,
			602 => FLD_TrangBi_ThemVao_ThangThien_3NoiTucHanhTam,
			603 => FLD_TrangBi_ThemVao_ThangThien_4TruongHongQuanThien,
			604 => FLD_TrangBi_ThemVao_ThangThien_4AiHongBienDa,
			561 => FLD_TrangBi_ThemVao_ThangThien_1DoatMenhLienHoan,
			562 => FLD_TrangBi_ThemVao_ThangThien_1DienQuangThachHoa,
			563 => FLD_TrangBi_ThemVao_ThangThien_1TinhIchCauTinh,
			564 => FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong,
			565 => FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong,
			315 => FLD_TrangBi_ThemVao_ThangThien_3_SatNhanQuy,
			316 => FLD_TrangBi_ThemVao_ThangThien_1_HuyenVuLoiDien,
			325 => FLD_TrangBi_ThemVao_ThangThien_2_HuyenVuTroChu,
			326 => FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem,
			327 => TrangBi_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien,
			610 => FLD_TrangBi_ThemVao_ThangThien_1_PhanNoDieuTiet,
			611 => FLD_TrangBi_ThemVao_ThangThien_2_CoDocGiaiTru,
			612 => FLD_TrangBi_ThemVao_ThangThien_3_ThanLucBaoHo,
			613 => FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong,
			614 => FLD_TrangBi_ThemVao_ThangThien_4VongMaiThiemHoa,
			615 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			616 => FLD_TrangBi_ThemVao_ThangThien_5_ThiDocBaoPhat,
			662 => FLD_TrangBi_ThemVao_ThangThien_1_LangKinhThoiLe,
			663 => FLD_TrangBi_ThemVao_ThangThien_2_SatTinhQuangPhu,
			664 => FLD_TrangBi_ThemVao_ThangThien_3_KyQuanQuanHung,
			665 => FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong,
			666 => FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong,
			667 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			668 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			669 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			670 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			671 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			672 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			673 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			674 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			675 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			676 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			677 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			678 => FLD_TrangBi_ThemVao_ThangThien_5_TriTan,
			679 => FLD_TrangBi_ThemVao_ThangThien_5_LongHong_PhuThe,
			680 => FLD_TrangBi_ThemVao_ThangThien_5_KinhThien_DongDia,
			681 => FLD_TrangBi_ThemVao_ThangThien_5_DietThe_CuongVong,
			682 => FLD_TrangBi_ThemVao_ThangThien_5_ThienLy_NhatKich,
			683 => FLD_TrangBi_ThemVao_ThangThien_5_HinhDi_YeuTuong,
			684 => FLD_TrangBi_ThemVao_ThangThien_5_NhatChieuSatThan,
			685 => FLD_TrangBi_ThemVao_ThangThien_5_LongTraoChiThu,
			686 => FLD_TrangBi_ThemVao_ThangThien_5_ThienMaChiLuc,
			687 => FLD_TrangBi_ThemVao_ThangThien_5_KinhDao_HaiLang,
			688 => FLD_TrangBi_ThemVao_ThangThien_5_BatTu_ChiKhu,
			689 => FLD_TrangBi_ThemVao_ThangThien_5_MaHonChiLuc,
			690 => FLD_TrangBi_ThemVao_ThangThien_5_PhaKhongTruyTinh,
			_ => 0.0,
		};
	}

	public int GetKhiCong_ID(int int_109, int int_110)
	{
		var result = 0;
		foreach (var value in World.KhiCongTangThem.Values)
		{
			if (value.FLD_JOB == int_110 && value.FLD_INDEX == int_109)
			{
				result = value.FLD_PID;
			}
		}
		return result;
	}

	public async void UpdateBangPhai_Level(string string_11)
	{
		try
		{
			var dBToDataTable = await GameDb.FindGuildByMaster(string_11);
			if (dBToDataTable == null)
			{
				return;
			}
			var num = (int)dBToDataTable.thang;
			var num2 = (int)dBToDataTable.id;
			var num3 = (int)dBToDataTable.leve;
			var num4 = 5;
			if (num >= 3 && num < 8)
			{
				num4 = 6;
			}
			else if (num >= 15)
			{
				num4 = 15;
			}
			var num5 = num4;
			if (num3 != num5)
			{
				try
				{
					await GameDb.UpdateGuildLevel(GuildName, num4);
					//DBA.ExeSqlCommand(string.Format("UPDATE TBL_XWWL_Guild SET leve={1} WHERE  ID={0}", num2, num4)).GetAwaiter().GetResult();
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Update Bang Phai Level error 111 - " + ex.ToString());
				}
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "Update Bang Phai Level error 222 - " + ex2.ToString());
		}
	}

	public void IncreaseInGroundItems(byte[] byte_0, float float_3, float float_4, float float_5)
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write(byte_0, 0, 16);
			sendingClass.Write(float_3);
			sendingClass.Write(15f);
			sendingClass.Write(float_4);
			sendingClass.Write(byte_0, 16, 60);
			if (Client != null)
			{
				Client.SendPak(sendingClass, 29184, 9999);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tăng Vật Phẩm Mặt Đất SoLieu 113 error " + Client.PlayerSessionID + "|" + Client.ToString() + "      " + ex);
		}
	}

	public void SendGroundItem(Dictionary<long, GroundItem> dictionary_0)
	{
		try
		{
			if (dictionary_0 == null || dictionary_0.Count <= 0)
			{
				return;
			}
			using SendingClass sendingClass = new();
			sendingClass.Write4(dictionary_0.Count);
			foreach (var value in dictionary_0.Values)
			{
				if (value != null)
				{
					sendingClass.Write(value.ItemByte, 0, 12);
					sendingClass.Write4(0);
					sendingClass.Write(value.ItemByte, 12, 4);
					sendingClass.Write(value.PosX + RNG.Next(-15, 15));
					sendingClass.Write(15f);
					sendingClass.Write(value.PosY + RNG.Next(-15, 15));
					sendingClass.Write(value.ItemByte, 16, 56);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
				}
				else
				{
					LogHelper.WriteLine(LogLevel.Error, "BOSS lỗi nên drop dồn lại 1 cục 222 !!!");
					var text = "[" + AccountID + "][" + CharacterName + "] Lỗi drop 222";
					// logo.Log_Drop_BOSS_loi(text, UserName);
				}
			}
			if (Client != null)
			{
				Client.SendPak(sendingClass, 29184, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Drop Vật Phẩm trên mặt đất số liệu 115 LỖI - " + Client.PlayerSessionID + "|" + Client.ToString() + " " + ex);
		}
	}

	public void RemoveGroundItem(long long_5)
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write8(long_5);
			if (Client != null)
			{
				Client.SendPak(sendingClass, 29440, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Vật phẩm trên mặt đất biến mất SoLieu 119 LỖI - " + Client.PlayerSessionID + "|" + Client.ToString() + " " + ex);
		}
	}
	 private bool CheckUserOnMap(Players players, int[] specialMapIds)
    { return specialMapIds.Contains(players.MapID); }

	private int ThangThien4TrangThai()
	{
		var lietNhat = KiemTraLietNhatViemViemTrangThai();
		var docXa = KiemTraDocXaXuatDongTrangThai();
		var aiHong = KiemTraAiHongBienDaTrangThai();
		var triTan = KiemTraTriTanTrangThai();

		var valueToWrite = 0; // Default if no conditions are met

		if (docXa && aiHong && triTan)
			valueToWrite = 69;
		else if (lietNhat && docXa && aiHong)
			valueToWrite = 7;
		else if (docXa && triTan)
			valueToWrite = 66;
		else if (aiHong && triTan)
			valueToWrite = 68;
		else if (lietNhat && triTan)
			valueToWrite = 65;
		else if (lietNhat && docXa)
			valueToWrite = 3;
		else if (lietNhat && aiHong)
			valueToWrite = 5;
		else if (docXa && aiHong)
			valueToWrite = 6;
		else if (lietNhat)
			valueToWrite = 1;
		else if (docXa)
			valueToWrite = 2;
		else if (aiHong)
			valueToWrite = 4;
		else if (triTan)
			valueToWrite = 64;
		else
			valueToWrite = 0;
		return valueToWrite;
	}

	public void ChangeEquipmentLocation(int int_109, int int_110, int int_111, int int_112, byte[] byte_0, int int_113)
	{
		try
		{
			var num = BitConverter.ToInt32(byte_0, 8);
			if (int_109 == 1 && int_111 == 0)
			{
				num = (int)Item_Wear[int_112].GetVatPham_ID;
				if (Item_Wear[int_112].FLD_Intrgration == 1)
				{
					num -= 5000;
				}
			}
			var array = Converter.HexStringToByte("AA557C000B051B00740001000000015F020000F90C002C01000040EA951DAB67550194CA9A3B000000002C0100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000D52D55AA");
			var bytes = BitConverter.GetBytes(int_109);
			var bytes2 = BitConverter.GetBytes(int_110);
			var bytes3 = BitConverter.GetBytes(int_111);
			var bytes4 = BitConverter.GetBytes(int_112);
			System.Buffer.BlockCopy(bytes, 0, array, 14, 2);
			System.Buffer.BlockCopy(bytes2, 0, array, 16, 2);
			System.Buffer.BlockCopy(bytes3, 0, array, 18, 2);
			System.Buffer.BlockCopy(bytes4, 0, array, 20, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_113), 0, array, 22, 4);
			System.Buffer.BlockCopy(byte_0, 0, array, 26, 8);
			System.Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 34, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_113), 0, array, 42, 4);
			System.Buffer.BlockCopy(bytes, 0, array, 54, 2);
			System.Buffer.BlockCopy(bytes2, 0, array, 56, 2);
			System.Buffer.BlockCopy(bytes3, 0, array, 50, 2);
			System.Buffer.BlockCopy(bytes4, 0, array, 52, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void IncreaseItem3(byte[] byte_0, byte[] byte_1, int int_109, byte[] byte_2, byte[] byte_3)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(byte_1, 0), out var value))
			{
				return;
			}
			var array = Converter.HexStringToByte("AA557200940223006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			var array2 = new byte[World.Item_Db_Byte_Length];
			if (value.FLD_SIDE != 0)
			{
				var array3 = new byte[4];
				System.Buffer.BlockCopy(byte_3, 0, array3, 0, 4);
				var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(byte_1, 0), BitConverter.ToInt32(array3, 0));
				if (BitConverter.ToInt32(byte_1, 0) != 1008000044 && BitConverter.ToInt32(byte_1, 0) != 1008000045)
				{
					if (characterItemType != null)
					{
						int_109 = characterItemType.VatPhamViTri;
						byte_0 = characterItemType.ItemGlobal_ID;
						byte_2 = BitConverter.GetBytes(BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0) + BitConverter.ToInt32(byte_2, 0));
					}
				}
				else
				{
					byte_0 = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
					byte_2 = BitConverter.GetBytes(BitConverter.ToInt32(byte_2, 0));
				}
			}
			else
			{
				byte_2 = BitConverter.GetBytes(1);
			}
			System.Buffer.BlockCopy(byte_0, 0, array2, 0, 8);
			System.Buffer.BlockCopy(byte_1, 0, array2, 8, 4);
			System.Buffer.BlockCopy(byte_2, 0, array2, 12, 4);
			System.Buffer.BlockCopy(byte_3, 0, array2, 16, byte_3.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 40, 2);
			System.Buffer.BlockCopy(array2, 0, array, 14, 12);
			System.Buffer.BlockCopy(array2, 12, array, 30, 4);
			System.Buffer.BlockCopy(array2, 16, array, 46, byte_3.Length);
			Item_In_Bag[int_109].VatPham_byte = array2;
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Increase Item error 66 [" + AccountID + "][" + CharacterName + "]  Position[" + int_109 + "]  SoLuong[" + BitConverter.ToInt32(byte_2, 0) + "]" + ex.Message);
		}
	}

	public int TinhToan_ThangThienVoCong_UyLuc(X_Vo_Cong_Loai VoCongClass_0)
	{
		if (VoCongMoi[VoCongClass_0.FLD_VoCongLoaiHinh, VoCongClass_0.FLD_INDEX] != null)
		{
			if (VoCongClass_0.FLD_VoCongLoaiHinh == 3)
			{
				return VoCongClass_0.FLD_AT + (VoCongMoi[VoCongClass_0.FLD_VoCongLoaiHinh, VoCongClass_0.FLD_INDEX].VoCong_DangCap - 1) * VoCongClass_0.FLD_MoiCapThemNguyHai / World.ThangThienKyNang_CapDoTangThem;
			}
			if (VoCongMoi[VoCongClass_0.FLD_VoCongLoaiHinh, VoCongClass_0.FLD_INDEX].VoCong_DangCap > 1)
			{
				return VoCongClass_0.FLD_AT + (VoCongMoi[VoCongClass_0.FLD_VoCongLoaiHinh, VoCongClass_0.FLD_INDEX].VoCong_DangCap - 1) * VoCongClass_0.FLD_MoiCapThemNguyHai;
			}
			return VoCongClass_0.FLD_AT;
		}
		return 0;
	}

	public void kickidlog(string string_11)
	{
	}
}
