
using System;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.Database;

namespace RxjhServer;

public partial class Players
{


    public async Task HelpingGearReward(int type)
    {
        try
        {
            switch (type)
            {
                case 1:
                    // Vũ khí
                    var weapon = await PublicDb.GetWeaponSuitePlayer(Player_Level, Player_Job, Player_Job_level, Player_Zx);
                    if (weapon != null)
                    {
                        // var item = World.CreateAnItem((int)weapon.fld_pid, 1);
                        var emptySlot = GetParcelVacancy(this);
                        // Set Item option
                        // Tăng clvc 100% +9
                        // kc2
                        var magic = 70000025;
                        if (Player_Job == 4 || Player_Job == 6 || Player_Job == 11)
                        {
                            magic = 80000002;
                        }

                        if (emptySlot != -1)
                        {
                            AddItem_ThuocTinh_int((int)weapon.fld_pid, emptySlot, 1, 1010001510, magic, magic, magic, magic, 0, 0, 0, 1, 7);
                        }
                    }
                    break;
                case 2:
                    var armor = await PublicDb.GetArmorSuitePlayer(Player_Level, Player_Job, Player_Job_level, Player_Zx, Player_Sex);
                    if (armor != null)
                    {
                        // var item = World.CreateAnItem((int)armor.fld_pid, 1);
                        var emptySlot = GetParcelVacancy(this);
                        var magic = 20000006;
                        if (emptySlot != -1)
                        {
                            AddItem_ThuocTinh_int((int)armor.fld_pid, emptySlot, 1, 1020001510, magic, magic, magic, magic, 0, 0, 0, 1, 7);
                        }
                    }
                    // Áo giáp
                    break;
                case 3:
                    {
                        var shoes = await PublicDb.GetShoesSuitePlayer(Player_Level, Player_Job, Player_Job_level, Player_Zx, Player_Sex);
                        // tay chân + 9
                        var emptySlot = GetParcelVacancy(this);
                        var magic = 20000006;
                        if (emptySlot != -1)
                        {
                            AddItem_ThuocTinh_int((int)shoes.fld_pid, emptySlot, 1, 20000010, magic, magic, magic, magic, 0, 0, 0, 1, 7);
                        }
                        var gloves = await PublicDb.GetGlovesSuitePlayer(Player_Level, Player_Job, Player_Job_level, Player_Zx, Player_Sex);
                        emptySlot = GetParcelVacancy(this);
                        if (emptySlot != -1)
                        {
                            AddItem_ThuocTinh_int((int)gloves.fld_pid, emptySlot, 1, 20000010, magic, magic, magic, magic, 0, 0, 0, 1, 7);
                        }
                        emptySlot = GetParcelVacancy(this);
                        if (emptySlot != -1)
                        {
                            AddItem_ThuocTinh_int((int)gloves.fld_pid, emptySlot, 1, 20000010, magic, magic, magic, magic, 0, 0, 0, 1, 7);
                        }
                        break;
                    }

            }


        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "HelpingGearReward error: " + ex.Message);
        }

    }
}