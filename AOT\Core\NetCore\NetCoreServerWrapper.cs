using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using NetCoreServer;
using HeroYulgang.Services;

namespace HeroYulgang.Core.NetCore
{
    /// <summary>
    /// Wrapper for NetCoreServer.TcpServer
    /// Provides simplified interface compatible with existing codebase
    /// Optimized for AOT compilation and high concurrency (2000+ connections)
    /// Supports multiple instances for different ports
    /// </summary>
    public sealed class NetCoreServerWrapper : TcpServer, IDisposable
    {
        #region Static Instance Management

        private static readonly ConcurrentDictionary<int, NetCoreServerWrapper> _instances = new();

        public static NetCoreServerWrapper GetInstance(int port = 13000)
        {
            return _instances.GetOrAdd(port, p => new NetCoreServerWrapper(p));
        }

        public static NetCoreServerWrapper Instance => GetInstance(); // Backward compatibility

        #endregion

        #region Fields

        private readonly SessionManager _sessionManager;
        private readonly PacketProcessor _packetProcessor;
        private volatile bool _isInitialized = false;
        private volatile bool _disposed = false;
        private readonly int _configuredPort;

        // Statistics
        private long _totalConnections = 0;
        private long _activeConnections = 0;
        private long _totalPacketsProcessed = 0;

        #endregion

        #region Properties

        public bool IsInitialized => _isInitialized;
        public int ConfiguredPort => _configuredPort;
        public long TotalConnections => _totalConnections;
        public long ActiveConnections => _activeConnections;
        public long TotalPacketsProcessed => _totalPacketsProcessed;
        public SessionManager SessionManager => _sessionManager;

        #endregion

        #region Constructor

        private NetCoreServerWrapper(int port = 13000) : base(IPAddress.Any, port)
        {
            _configuredPort = port;
            _sessionManager = new SessionManager();
            _packetProcessor = new PacketProcessor();

            Logger.Instance.Info($"NetCoreServerWrapper instance created for port {port}");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the server (port already set in constructor)
        /// </summary>
        public void InitializeAsync(int port)
        {
            if (_disposed)
            {
                Logger.Instance.Error("Cannot initialize disposed NetCoreServerWrapper");
                return;
            }

            if (_isInitialized)
            {
                Logger.Instance.Warning($"NetCoreServerWrapper already initialized on port {_configuredPort}");
                return;
            }

            if (port != _configuredPort)
            {
                Logger.Instance.Warning($"Port mismatch: requested {port}, but instance configured for {_configuredPort}");
            }

            try
            {
                // Stop if already running
                if (IsStarted)
                {
                    Stop();
                }

                _isInitialized = true;

                Logger.Instance.Info($"NetCoreServerWrapper initialized on port {_configuredPort}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to initialize NetCoreServerWrapper: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start the server (async)
        /// </summary>
        public bool StartServer()
        {
            if (!_isInitialized)
            {
                Logger.Instance.Error("NetCoreServerWrapper not initialized. Call InitializeAsync first.");
                return false;
            }

            if (_disposed)
            {
                Logger.Instance.Error("Cannot start disposed NetCoreServerWrapper");
                return false;
            }

            try
            {
                bool result = Start();
                if (result)
                {
                    Logger.Instance.Info($"NetCoreServerWrapper started successfully on port {_configuredPort}");
                }
                else
                {
                    Logger.Instance.Error("Failed to start NetCoreServerWrapper");
                }
                return result;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Exception starting NetCoreServerWrapper: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Stop the server
        /// </summary>
        public bool StopServer()
        {
            try
            {
                bool result = Stop();
                if (result)
                {
                    Logger.Instance.Info("NetCoreServerWrapper stopped successfully");
                }
                return result;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Exception stopping NetCoreServerWrapper: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get session by ID
        /// </summary>
        public GameSession GetSession(Guid sessionId)
        {
            return _sessionManager.GetSession(sessionId);
        }

        /// <summary>
        /// Find GameSession by integer SessionId (for ActorNetState compatibility)
        /// </summary>
        public GameSession? FindSessionById(int sessionId)
        {
            try
            {
                var sessions = _sessionManager.GetAllSessions();
                return sessions.FirstOrDefault(s => s.SessionId == sessionId);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error finding session by ID {sessionId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get all active sessions
        /// </summary>
        public GameSession[] GetAllSessions()
        {
            return _sessionManager.GetAllSessions();
        }

        /// <summary>
        /// Multicast message to all connected sessions
        /// </summary>
        public void MulticastToAll(byte[] data)
        {
            if (data == null || data.Length == 0)
                return;

            try
            {
                Multicast(data);
                Logger.Instance.Debug($"Multicast message sent to all sessions, size: {data.Length} bytes");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error multicasting message: {ex.Message}");
            }
        }

        #endregion

        #region NetCoreServer Overrides

        protected override TcpSession CreateSession()
        {
            var session = new GameSession(this, _packetProcessor);

            // Track statistics
            Interlocked.Increment(ref _totalConnections);
            Interlocked.Increment(ref _activeConnections);

            // Register with session manager
            _sessionManager.RegisterSession(session);

            Logger.Instance.Debug($"Created new GameSession {session.Id}");
            return session;
        }

        protected override void OnStarted()
        {
            Logger.Instance.Info($"NetCoreServer started listening on {Endpoint}");
        }

        protected override void OnStopped()
        {
            Logger.Instance.Info("NetCoreServer stopped");
        }

        protected override void OnError(System.Net.Sockets.SocketError error)
        {
            Logger.Instance.Error($"NetCoreServer socket error: {error}");
        }

        #endregion

        #region Internal Methods

        internal void OnSessionDisconnected(GameSession session)
        {
            // Unregister from session manager
            _sessionManager.UnregisterSession(session.Id);

            // Update statistics
            Interlocked.Decrement(ref _activeConnections);

            Logger.Instance.Debug($"GameSession {session.Id} disconnected");
        }

        internal void OnPacketProcessed()
        {
            Interlocked.Increment(ref _totalPacketsProcessed);
        }

        #endregion

        #region IDisposable

        public new void Dispose()
        {
            if (_disposed)
                return;

            try
            {
                StopServer();
                _sessionManager?.Dispose();
                _packetProcessor?.Dispose();

                base.Dispose();

                _disposed = true;
                Logger.Instance.Info("NetCoreServerWrapper disposed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing NetCoreServerWrapper: {ex.Message}");
            }
        }

        public GameSession FindSessionByAccountId(string accountId)
        {
            return _sessionManager.GetSessionByAccount(accountId);
        }
        
        public GameSession UpdateSessionAccount(GameSession session, string account)
        {
            _sessionManager.UpdateSessionAccount(session, account);
            return session;
        }

        #endregion
    }
}
