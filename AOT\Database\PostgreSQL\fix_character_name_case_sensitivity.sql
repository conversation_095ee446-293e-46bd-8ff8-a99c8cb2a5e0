-- Fix character name case sensitivity issue
-- <PERSON><PERSON><PERSON><PERSON> phục vấn đề phân biệt chữ hoa chữ thường cho tên nhân vật

-- ========================================
-- PART 1: DIAGNOSIS - Ch<PERSON><PERSON> đoán vấn đề
-- ========================================

SELECT '=== DIAGNOSIS - CHẨN ĐOÁN ===' as step;

-- Check current duplicate names (case-insensitive)
SELECT 'Duplicate character names (case-insensitive):' as info;
SELECT 
    LOWER(fld_name) as name_lower,
    COUNT(*) as count,
    STRING_AGG(fld_name, ', ' ORDER BY fld_name) as actual_names,
    STRING_AGG(CAST(id as TEXT), ', ' ORDER BY fld_name) as ids
FROM tbl_xwwl_char 
GROUP BY LOWER(fld_name)
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC;

-- Check current unique index
SELECT 'Current indexes on fld_name:' as info;
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'tbl_xwwl_char' 
    AND indexdef LIKE '%fld_name%';

-- ========================================
-- PART 2: BACKUP DUPLICATE DATA - Sao lưu dữ liệu trùng lặp
-- ========================================

SELECT '=== BACKUP DUPLICATE DATA - SAO LƯU DỮ LIỆU TRÙNG LẶP ===' as step;

-- Create backup table for duplicate characters
CREATE TABLE IF NOT EXISTS tbl_xwwl_char_duplicates_backup AS
SELECT 
    c.*,
    LOWER(c.fld_name) as name_lower,
    ROW_NUMBER() OVER (PARTITION BY LOWER(c.fld_name) ORDER BY c.id) as row_num,
    NOW() as backup_date
FROM tbl_xwwl_char c
WHERE LOWER(c.fld_name) IN (
    SELECT LOWER(fld_name)
    FROM tbl_xwwl_char 
    GROUP BY LOWER(fld_name)
    HAVING COUNT(*) > 1
);

SELECT 'Backed up duplicate characters:' as info;
SELECT COUNT(*) as total_duplicates FROM tbl_xwwl_char_duplicates_backup;

-- ========================================
-- PART 3: RESOLVE DUPLICATES - Giải quyết trùng lặp
-- ========================================

SELECT '=== RESOLVE DUPLICATES - GIẢI QUYẾT TRÙNG LẶP ===' as step;

-- Option 1: Rename duplicate characters by adding suffix
-- Tùy chọn 1: Đổi tên nhân vật trùng lặp bằng cách thêm hậu tố

SELECT 'Renaming duplicate characters...' as info;

-- Update duplicate characters (keep first one, rename others)
UPDATE tbl_xwwl_char 
SET fld_name = fld_name || '_' || CAST(id as TEXT)
WHERE id IN (
    SELECT id 
    FROM (
        SELECT 
            id,
            ROW_NUMBER() OVER (PARTITION BY LOWER(fld_name) ORDER BY id) as row_num
        FROM tbl_xwwl_char
        WHERE LOWER(fld_name) IN (
            SELECT LOWER(fld_name)
            FROM tbl_xwwl_char 
            GROUP BY LOWER(fld_name)
            HAVING COUNT(*) > 1
        )
    ) ranked
    WHERE row_num > 1
);

-- ========================================
-- PART 4: ADD CASE-INSENSITIVE CONSTRAINT - Thêm ràng buộc không phân biệt chữ hoa thường
-- ========================================

SELECT '=== ADD CASE-INSENSITIVE CONSTRAINT - THÊM RÀNG BUỘC ===' as step;

-- Drop existing unique index if it exists
SELECT 'Dropping existing unique index...' as info;
DROP INDEX IF EXISTS tbl_xwwl_char_fld_name_idx;

-- Create case-insensitive unique index
SELECT 'Creating case-insensitive unique index...' as info;
CREATE UNIQUE INDEX tbl_xwwl_char_fld_name_lower_idx 
ON tbl_xwwl_char (LOWER(fld_name));

-- Add comment to the index
COMMENT ON INDEX tbl_xwwl_char_fld_name_lower_idx 
IS 'Case-insensitive unique constraint for character names to prevent similar names';

-- ========================================
-- PART 5: VERIFICATION - Xác minh
-- ========================================

SELECT '=== VERIFICATION - XÁC MINH ===' as step;

-- Check if duplicates are resolved
SELECT 'Remaining duplicates after fix:' as info;
SELECT 
    LOWER(fld_name) as name_lower,
    COUNT(*) as count,
    STRING_AGG(fld_name, ', ' ORDER BY fld_name) as actual_names
FROM tbl_xwwl_char 
GROUP BY LOWER(fld_name)
HAVING COUNT(*) > 1;

-- Check new index
SELECT 'New index information:' as info;
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'tbl_xwwl_char' 
    AND indexname = 'tbl_xwwl_char_fld_name_lower_idx';

-- Show renamed characters
SELECT 'Renamed characters:' as info;
SELECT 
    id,
    fld_name,
    fld_id as account_id
FROM tbl_xwwl_char 
WHERE fld_name ~ '_[0-9]+$'
ORDER BY id;

-- ========================================
-- PART 6: TEST - Kiểm tra
-- ========================================

SELECT '=== TEST - KIỂM TRA ===' as step;

-- Test case-insensitive constraint
SELECT 'Testing case-insensitive constraint...' as info;

-- This should work (different case-insensitive names)
-- Điều này sẽ hoạt động (tên khác nhau không phân biệt chữ hoa thường)

-- This should fail (same case-insensitive name)
-- Điều này sẽ thất bại (tên giống nhau không phân biệt chữ hoa thường)
-- Uncomment to test:
/*
INSERT INTO tbl_xwwl_char (fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
    fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
    fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
    fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
    fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
    fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
    fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
    fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
    fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
    nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel, fld_xb
) VALUES (
    'test_account_case', 'TestCaseName', 0, 1, 1, '0', 1, 0,
    418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
    '10000', '0', 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0.0, 0, 0, 0, 0, 0, '', '', '', '', 0,
    0, 0, 0, 'd1', '', 0, 0, 0, 0, '', 0,
    0, 0, 0, 0, 0, '', 1, false, '', 0, '', 0, 0
);

-- This should fail with unique constraint violation
INSERT INTO tbl_xwwl_char (fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
    fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
    fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
    fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
    fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
    fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
    fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
    fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
    fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
    nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel, fld_xb
) VALUES (
    'test_account_case2', 'testcasename', 0, 1, 1, '0', 1, 0,
    418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
    '10000', '0', 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0.0, 0, 0, 0, 0, 0, '', '', '', '', 0,
    0, 0, 0, 'd1', '', 0, 0, 0, 0, '', 0,
    0, 0, 0, 0, 0, '', 1, false, '', 0, '', 0, 0
);

-- Clean up test data
DELETE FROM tbl_xwwl_char WHERE fld_id LIKE 'test_account_case%';
*/

SELECT '=== FIX COMPLETED SUCCESSFULLY - KHẮC PHỤC HOÀN TẤT ===' as result;
