using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Event_Boss_Map
{
	private System.Timers.Timer ThoiGian1;

	private DateTime dateTime_0;

	public Event_Boss_Map()
	{
		try
		{
			World.Boss_Map_Progress = 1;
			World.Npc_Boss_Map.Clear();
			dateTime_0 = DateTime.Now.AddMinutes(15.0);
			ThoiGian1 = new(3000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				Call_Boss_Map_Newbie();
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (!value.Client.TreoMay && value.IsJoinWorld)
						{
							value.HeThongNhacNho("<PERSON>h<PERSON>nh Thần Địa tung hoành khắp chốn, đạ<PERSON> hiệp có [" + num / 60 + "] khắc để tiêu diệt tại <PERSON>lgang Hero!", 10, "Thiên cơ các");
							value.HeThongNhacNho("Sau [" + num / 60 + "] khắc, nếu chưa bị tiêu diệt, đại ma đầu sẽ tan biến khỏi giang hồ!", 10, "Thiên cơ các");
							GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num);
						}
					}
					return;
				}
			}
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BOSS Máp Progress = 1 lỗi !! ----------" + ex.Message);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			using (var enumerator = World.allConnectedChars.Values.GetEnumerator())
			{
				if (enumerator.MoveNext())
				{
					var current = enumerator.Current;
					if (!current.Client.TreoMay)
					{
						current.HeThongNhacNho("Thủ Lĩnh Thần Địa đã tan biến khỏi Yulgang Hero, quần hùng tiếc nuối!", 10, "Thiên cơ các");
						World.SystemRollingAnnouncement("Thủ Lĩnh Thần Địa đã biến mất khỏi máy chủ Yulgang Hero !!");
					}
				}
			}
			World.Boss_Map_Progress = 0;
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			World.Boss_Map_Event.Dispose();
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tính toán BOSS Map lỗi 111 " + ex);
		}
	}

	public static void GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void Call_Boss_Map_Newbie()
	{
		try
		{
			//var text = "BOSS Thu Li辬h Th馓n 衖騛 餫ng xu忪t hi牝n ta騣 [K阯h " + World.ServerID + "] ca靋 ba襫 痿 [HBP, TTQ, LCQ, BHBC] !!!";
			//World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text);
			//World.Boss_Map_Progress = 2;
			//switch (RNG.Next(1, 4))
			//{
			//case 1:
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), -1290f, -1845f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), 289f, 1152f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), -1494f, 1152f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), -1476f, 2070f, 101, 1);
			//	break;
			//case 2:
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), 2125f, -282f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), 1598f, 603f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), -1494f, 1152f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), -1476f, 2070f, 101, 1);
			//	break;
			//case 3:
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), 2125f, -282f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), 1598f, 603f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), -381f, 1653f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), -245f, -357f, 101, 1);
			//	break;
			//default:
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), 2230f, -2288f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), -28f, -448f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15253, 15254), 712f, -741f, 101, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15260, 15261), 953f, -1872f, 101, 1);
			//	break;
			//}
			//switch (RNG.Next(0, 3))
			//{
			//case 0:
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), -7080f, 1700f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), -5236f, 499f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), 5220f, 1507f, 201, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), 6750f, -1430f, 201, 1);
			//	break;
			//case 1:
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), -7146f, 1438f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), -5982f, 2105f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), 7076f, 2040f, 201, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), 5718f, 1502f, 201, 1);
			//	break;
			//case 2:
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), -6969f, -1589f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), -4840f, -603f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), 5123f, 936f, 201, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15255, 15261), 4489f, 81f, 201, 1);
			//	break;
			//case 3:
			//	Add_Boss_Map_SoLuong(RNG.Next(15264, 15265), -4069f, -95f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15264, 15265), -5789f, 1734f, 301, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15264, 15265), 3124f, -1277f, 201, 1);
			//	Add_Boss_Map_SoLuong(RNG.Next(15264, 15265), 5280f, 63f, 201, 1);
			//	break;
			//}
			//switch (RNG.Next(1, 6))
			//{
			//case 1:
			//	Add_Boss_Map_SoLuong(RNG.Next(15419, 15424), -103f, -1546f, 5001, 1);
			//	break;
			//case 2:
			//	Add_Boss_Map_SoLuong(RNG.Next(15419, 15424), -314f, -1865f, 5001, 1);
			//	break;
			//case 3:
			//	Add_Boss_Map_SoLuong(RNG.Next(15419, 15424), -705f, -1960f, 5001, 1);
			//	break;
			//case 4:
			//	Add_Boss_Map_SoLuong(RNG.Next(15419, 15424), -1011f, -1457f, 5001, 1);
			//	break;
			//case 5:
			//	Add_Boss_Map_SoLuong(RNG.Next(15419, 15424), -428f, -1084f, 5001, 1);
			//	break;
			//default:
			//	Add_Boss_Map_SoLuong(RNG.Next(15419, 15424), 293f, -1242f, 5001, 1);
			//	break;
			//}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "BOSS Map Event lỗi !! ----------");
		}
	}

	public void Add_Boss_Map_SoLuong(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				if (!World.MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					continue;
				}
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.fld_pid;
				npcClass.Name = value.fld_name;
				npcClass.Level = value.fld_level;
				npcClass.Rxjh_Exp = value.fld_exp;
				npcClass.Rxjh_X = RNG.Next((int)float_0 - 100, (int)float_0 + 100);
				npcClass.Rxjh_Y = RNG.Next((int)float_1 - 100, (int)float_1 + 100);
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = RNG.Next((int)float_0 - 100, (int)float_0 + 100);
				npcClass.Rxjh_cs_Y = RNG.Next((int)float_1 - 100, (int)float_1 + 100);
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = RNG.Next(-1, 1);
				npcClass.FLD_FACE2 = RNG.Next(-1, 1);
				npcClass.Max_Rxjh_HP = value.fld_hp;
				npcClass.Rxjh_HP = value.fld_hp;
				npcClass.FLD_AT = value.fld_at;
				npcClass.FLD_DF = value.fld_df;
				npcClass.FLD_AUTO = value.fld_auto;
				npcClass.FLD_BOSS = 1;
				npcClass.FLD_NEWTIME = 10000000;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					World.MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.ScanNearbyPlayer();
				World.Npc_Boss_Map.Add(npcClass.NPC_SessionID, npcClass);
				if (World.Boss_Map_Progress != 2 || npcClass.FLD_BOSS != 1 || !npcClass.QuaiXuatHien_DuyNhatMotLan)
				{
					continue;
				}
				var name_TiengViet = X_Toa_Do_Class.GetName_TiengViet(npcClass.Rxjh_Map);
				foreach (var value3 in World.allConnectedChars.Values)
				{
					value3.HeThongNhacNho("Đại ma đầu vừa tái sinh tại bản đồ " + name_TiengViet + " - Tọa độ: [" + (int)npcClass.Rxjh_X + "," + (int)npcClass.Rxjh_Y + "]", 8, "Truyền Âm Các");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Boss Map - lỗi [" + int_0 + "]error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.Npc_Boss_Map.Values)
			{
				list.Add(value);
			}
			if (list != null)
			{
				foreach (var item in list)
				{
					item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				}
				list.Clear();
			}
			World.Npc_Boss_Map.Clear();
			World.Boss_Map_Progress = 0;
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
			}
			World.Boss_Map_Event = null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Boss Map kết thúc Dispose lỗi !! - " + ex.Message);
		}
	}
}
