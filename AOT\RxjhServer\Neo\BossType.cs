using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using RxjhServer;

namespace YulgangServer
{
    // Enum định nghĩa các loại boss
    public enum BossType
    {
        Normal = 0,     // Boss thường
        WorldBoss = 1,  // Boss thế giới
        GuildBoss = 2,  // Boss bang hội
        SummonBoss = 3, // Boss triệu hồi
        EventBoss = 4   // Boss sự kiện
    }

    // Enum định nghĩa các loại phần thưởng
    public enum RewardDistributionType
    {
        ContributionBased = 0,  // Phân phối dựa trên đóng góp
        EqualChance = 1,        // Phân phối với tỉ lệ bằng nhau
        TopDamager = 2,         // Chỉ cho người gây sát thương cao nhất
        Random = 3              // Hoàn toàn ngẫu nhiên
    }

    // Class quản lý thông tin trao thưởng cho từng loại boss
    public class BossRewardConfig
    {
        // ID của boss
        public int BossId { get; set; }

        // Loại boss
        public BossType Type { get; set; }

        // Tên boss
        public string Name { get; set; }

        // Danh sách vật phẩm có thể rơi ra
        public List<BossDropItem> PotentialDrops { get; set; } = new List<BossDropItem>();

        // Danh sách vật phẩm hiếm có thể rơi ra
        public List<BossDropItem> RareDrops { get; set; } = new List<BossDropItem>();

        // Số lượng vật phẩm hiếm chắc chắn sẽ rơi ra theo đóng góp (áp dụng cho WorldBoss)
        public int GuaranteedContributionRareDrops { get; set; } = 0;

        // Số lượng vật phẩm hiếm chắc chắn sẽ rơi ra với tỉ lệ bằng nhau (áp dụng cho WorldBoss)
        public int GuaranteedEqualChanceRareDrops { get; set; } = 0;

        // Tỉ lệ rơi vật phẩm hiếm theo đóng góp (áp dụng cho GuildBoss, SummonBoss)
        public float ContributionRareDropRate { get; set; } = 0.0f;

        // Tỉ lệ rơi vật phẩm hiếm với tỉ lệ bằng nhau (áp dụng cho GuildBoss, SummonBoss)
        public float EqualChanceRareDropRate { get; set; } = 0.0f;

        // Các thuộc tính điều chỉnh chỉ số của boss
        public BossStatModifiers StatModifiers { get; set; } = new BossStatModifiers();

        // Phương thức trả về danh sách vật phẩm dựa trên loại phân phối
        public List<BossDropResult> GetDrops(RewardDistributionType distributionType)
        {
            try
            {
                List<BossDropResult> drops = new List<BossDropResult>();

                switch (Type)
                {
                    case BossType.WorldBoss:
                        if (distributionType == RewardDistributionType.ContributionBased)
                        {
                            // Lấy vật phẩm hiếm chắc chắn dựa trên đóng góp
                            GetGuaranteedRareDrops(drops, GuaranteedContributionRareDrops, distributionType);
                        }
                        else if (distributionType == RewardDistributionType.EqualChance)
                        {
                            // Lấy vật phẩm hiếm chắc chắn với tỉ lệ bằng nhau
                            GetGuaranteedRareDrops(drops, GuaranteedEqualChanceRareDrops, distributionType);
                        }
                        break;

                    case BossType.GuildBoss:
                    case BossType.SummonBoss:
                        if (distributionType == RewardDistributionType.ContributionBased)
                        {
                            // Lấy vật phẩm hiếm ngẫu nhiên dựa trên đóng góp
                            GetRandomRareDrops(drops, ContributionRareDropRate, distributionType);
                        }
                        else if (distributionType == RewardDistributionType.EqualChance)
                        {
                            // Lấy vật phẩm hiếm ngẫu nhiên với tỉ lệ bằng nhau
                            GetRandomRareDrops(drops, EqualChanceRareDropRate, distributionType);
                        }
                        break;

                    case BossType.EventBoss:
                        // Xử lý đặc biệt cho boss sự kiện nếu cần
                        break;

                    default:
                        // Boss thường có thể có tỉ lệ rơi đồ thấp hơn
                        GetRandomRareDrops(drops, 0.05f, distributionType);
                        break;
                }

                return drops;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi lấy danh sách vật phẩm: {ex.Message}");
                return new List<BossDropResult>();
            }
        }

        // Lấy ngẫu nhiên vật phẩm hiếm theo tỉ lệ
        private void GetRandomRareDrops(List<BossDropResult> drops, float dropRate, RewardDistributionType distributionType)
        {
            // Kiểm tra xem có rơi vật phẩm hiếm không
            if (RNG.Next(0, 100) < dropRate * 100)
            {
                // Nếu có, chọn ngẫu nhiên một vật phẩm hiếm
                if (RareDrops.Count > 0)
                {
                    int index = RNG.Next(0, RareDrops.Count);
                    drops.Add(new BossDropResult
                    {
                        Item = RareDrops[index],
                        DistributionType = distributionType
                    });
                }
            }
        }

        // Lấy một số lượng cố định vật phẩm hiếm (đảm bảo luôn có)
        private void GetGuaranteedRareDrops(List<BossDropResult> drops, int count, RewardDistributionType distributionType)
        {
            // Nếu không có vật phẩm hiếm nào, return
            if (RareDrops.Count == 0)
                return;

            // Tạo một bản sao của danh sách để không ảnh hưởng đến danh sách gốc
            List<BossDropItem> rareItemsCopy = new List<BossDropItem>(RareDrops);

            int index = RNG.Next(0, rareItemsCopy.Count - 1);
            drops.Add(new BossDropResult
            {
                Item = rareItemsCopy[index],
                DistributionType = distributionType
            });
        }

    }

    // Class mô tả một vật phẩm có thể rơi ra từ boss
    public class BossDropItem
    {
        // ID của vật phẩm
        public int ItemId { get; set; }

        // Tên vật phẩm
        public string Name { get; set; }

        // Có phải vật phẩm hiếm hay không
        public bool IsRare { get; set; }

        // Số lượng vật phẩm
        public int Count { get; set; } = 1;

        // Tỉ lệ rơi (nếu cần)
        public float DropRate { get; set; } = 100.0f;

        public BossDropItem(int itemId, string name = "", bool isRare = false, int count = 1, float dropRate = 100.0f)
        {
            ItemId = itemId;
            Name = name;
            IsRare = isRare;
            Count = count;
            DropRate = dropRate;
        }
    }

    // Class chứa kết quả drop từ boss
    public class BossDropResult
    {
        // Vật phẩm drop
        public BossDropItem Item { get; set; }

        // Loại phân phối
        public RewardDistributionType DistributionType { get; set; }
    }

    // Class chứa các thuộc tính điều chỉnh chỉ số của boss
    public class BossStatModifiers
    {
        // Hệ số nhân HP
        public float HpMultiplier { get; set; } = 1.0f;

        // Hệ số nhân MP
        public float MpMultiplier { get; set; } = 1.0f;

        // Hệ số nhân sát thương
        public float DamageMultiplier { get; set; } = 1.0f;

        // Hệ số nhân phòng thủ vật lý
        public float PhysicalDefenseMultiplier { get; set; } = 1.0f;

        // Hệ số nhân phòng thủ phép thuật
        public float MagicDefenseMultiplier { get; set; } = 1.0f;

        // Hệ số nhân tốc độ
        public float SpeedMultiplier { get; set; } = 1.0f;

        // Giảm phòng thủ vật lý (%)
        public int PhysicalDefenseReduction { get; set; } = 0;

        // Giảm phòng thủ phép thuật (%)
        public int MagicDefenseReduction { get; set; } = 0;

        // Tăng kháng hiệu ứng (%)
        public int StatusResistance { get; set; } = 0;
    }

    // Khóa kết hợp cho cấu hình boss
    public struct BossConfigKey
    {
        public int BossId { get; }
        public BossType Type { get; }

        public BossConfigKey(int bossId, BossType type)
        {
            BossId = bossId;
            Type = type;
        }

        public override bool Equals(object obj)
        {
            if (!(obj is BossConfigKey))
                return false;

            var other = (BossConfigKey)obj;
            return BossId == other.BossId && Type == other.Type;
        }

        public override int GetHashCode()
        {
            return BossId.GetHashCode() ^ Type.GetHashCode();
        }

        public override string ToString()
        {
            return $"BossId: {BossId}, Type: {Type}";
        }
    }

    // Class quản lý cấu hình boss toàn cục
    public static class BossConfigManager
    {
        // Danh sách cấu hình boss
        private static Dictionary<BossConfigKey, BossRewardConfig> _bossConfigs = new Dictionary<BossConfigKey, BossRewardConfig>();

        // Khởi tạo cấu hình mặc định
        static BossConfigManager()
        {
            InitializeDefaultConfigs();
        }

        // Khởi tạo cấu hình mặc định
        private static void InitializeDefaultConfigs()
        {
            // Cấu hình cho Boss Thế Giới
            BossRewardConfig worldBossConfig = new BossRewardConfig
            {
                BossId = 15423,
                Type = BossType.WorldBoss,
                Name = "Boss Thế Giới",
                GuaranteedContributionRareDrops = 1,  // Chắc chắn rơi 1 item hiếm theo đóng góp
                GuaranteedEqualChanceRareDrops = 1,   // Chắc chắn rơi 1 item hiếm với tỉ lệ bằng nhau
                RareDrops = new List<BossDropItem>
                {
                    new(1000001390, "Bảo Châu 1", true, 1, 100.0f),
                    new(1000001391, "Bảo Châu 2", true, 1, 100.0f),
                    new(1000001392, "Bảo Châu 3", true, 1, 100.0f),
                    new(1000001393, "Bảo Châu 4", true, 1, 100.0f),
                    new(1000001394, "Bảo Châu 5", true, 1, 100.0f)
                },
                PotentialDrops = new List<BossDropItem>
                {
                    new(1000000100, "Vật Phẩm Thường 1", false, 1, 80.0f),
                    new(1000000101, "Vật Phẩm Thường 2", false, 1, 80.0f),
                    new(1000000102, "Vật Phẩm Thường 3", false, 1, 80.0f)
                },
                StatModifiers = new BossStatModifiers
                {
                    HpMultiplier = 2.0f,                  // Tăng HP lên 200%
                    PhysicalDefenseReduction = 100,       // Giảm 100% phòng thủ vật lý
                    MagicDefenseReduction = 100,          // Giảm 100% phòng thủ phép thuật
                    DamageMultiplier = 1.5f,              // Tăng sát thương lên 150%
                    StatusResistance = 50                 // Tăng 50% kháng hiệu ứng
                }
            };
            _bossConfigs.Add(new BossConfigKey(worldBossConfig.BossId, worldBossConfig.Type), worldBossConfig);

            // Cấu hình cho Boss Bang Hội
            BossRewardConfig guildBossConfig = new BossRewardConfig
            {
                BossId = 15423,
                Type = BossType.GuildBoss,
                Name = "Boss Bang Hội",
                ContributionRareDropRate = 0.3f,      // 30% tỉ lệ rơi item hiếm theo đóng góp
                EqualChanceRareDropRate = 0.2f,       // 20% tỉ lệ rơi item hiếm với tỉ lệ bằng nhau
                RareDrops = new List<BossDropItem>
                {
                    new(1000002390, "Bảo Vật Bang Hội 1", true, 1, 100.0f),
                    new(1000002391, "Bảo Vật Bang Hội 2", true, 1, 100.0f),
                    new(1000002392, "Bảo Vật Bang Hội 3", true, 1, 100.0f)
                },
                PotentialDrops = new List<BossDropItem>
                {
                    new(1000000200, "Vật Phẩm Bang Hội 1", false, 1, 80.0f),
                    new(1000000201, "Vật Phẩm Bang Hội 2", false, 1, 80.0f)
                },
                StatModifiers = new BossStatModifiers
                {
                    HpMultiplier = 1.5f,                  // Tăng HP lên 150%
                    PhysicalDefenseReduction = 50,        // Giảm 50% phòng thủ vật lý
                    MagicDefenseReduction = 50,           // Giảm 50% phòng thủ phép thuật
                    DamageMultiplier = 1.2f               // Tăng sát thương lên 120%
                }
            };
            _bossConfigs.Add(new BossConfigKey(guildBossConfig.BossId, guildBossConfig.Type), guildBossConfig);

            // Cấu hình cho Boss Triệu Hồi
            BossRewardConfig summonBossConfig = new BossRewardConfig
            {
                BossId = 15423,
                Type = BossType.SummonBoss,
                Name = "Boss Triệu Hồi",
                ContributionRareDropRate = 0.2f,      // 20% tỉ lệ rơi item hiếm theo đóng góp
                EqualChanceRareDropRate = 0.1f,       // 10% tỉ lệ rơi item hiếm với tỉ lệ bằng nhau
                RareDrops = new List<BossDropItem>
                {
                    new(1000003390, "Bảo Vật Triệu Hồi 1", true, 1, 100.0f),
                    new(1000003391, "Bảo Vật Triệu Hồi 2", true, 1, 100.0f)
                },
                PotentialDrops = new List<BossDropItem>
                {
                    new(1000000300, "Vật Phẩm Triệu Hồi 1", false, 1, 80.0f),
                    new(1000000301, "Vật Phẩm Triệu Hồi 2", false, 1, 80.0f)
                },
                StatModifiers = new BossStatModifiers
                {
                    HpMultiplier = 1.3f,                  // Tăng HP lên 130%
                    PhysicalDefenseReduction = 30,        // Giảm 30% phòng thủ vật lý
                    MagicDefenseReduction = 30,           // Giảm 30% phòng thủ phép thuật
                    DamageMultiplier = 1.1f               // Tăng sát thương lên 110%
                }
            };
            _bossConfigs.Add(new BossConfigKey(summonBossConfig.BossId, summonBossConfig.Type), summonBossConfig);
        }

        // Lấy cấu hình boss theo ID và loại
        public static BossRewardConfig GetBossConfig(int bossId, BossType type = BossType.WorldBoss)
        {
            var key = new BossConfigKey(bossId, type);
            if (_bossConfigs.TryGetValue(key, out var config))
                return config;

            // Nếu không tìm thấy cấu hình cho loại cụ thể, thử tìm cấu hình cho loại WorldBoss
            if (type != BossType.WorldBoss)
            {
                key = new BossConfigKey(bossId, BossType.WorldBoss);
                if (_bossConfigs.TryGetValue(key, out config))
                    return config;
            }

            // Nếu không tìm thấy, trả về cấu hình mặc định
            return new BossRewardConfig
            {
                BossId = bossId,
                Type = type,
                Name = $"Boss {type} Không Xác Định",
                StatModifiers = new BossStatModifiers()
            };
        }

        // Lấy cấu hình boss chỉ theo ID (trả về cấu hình đầu tiên tìm thấy)
        public static BossRewardConfig GetBossConfig(int bossId)
        {
            foreach (var config in _bossConfigs.Values)
            {
                if (config.BossId == bossId)
                    return config;
            }

            // Nếu không tìm thấy, trả về cấu hình mặc định
            return new BossRewardConfig
            {
                BossId = bossId,
                Type = BossType.Normal,
                Name = "Boss Không Xác Định",
                StatModifiers = new BossStatModifiers()
            };
        }

        // Thêm hoặc cập nhật cấu hình boss
        public static void AddOrUpdateBossConfig(BossRewardConfig config)
        {
            var key = new BossConfigKey(config.BossId, config.Type);
            _bossConfigs[key] = config;
        }

        // Xóa cấu hình boss
        public static void RemoveBossConfig(int bossId, BossType type)
        {
            var key = new BossConfigKey(bossId, type);
            if (_bossConfigs.ContainsKey(key))
                _bossConfigs.Remove(key);
        }

        // Áp dụng các thuộc tính điều chỉnh cho boss
        public static void ApplyStatModifiers(NpcClass boss)
        {
            try
            {
                if (boss == null || !boss.IsWorldBoss)
                    return;

                // Lấy cấu hình dựa trên ID và loại boss
                var config = GetBossConfig(boss.FLD_PID, boss.BossType);
                if (config == null)
                    return;

                var modifiers = config.StatModifiers;

                // Lưu lại các giá trị gốc để log
                int originalHP = boss.Max_Rxjh_HP;
                double originalAT = boss.FLD_AT;
                double originalDF = boss.FLD_DF;

                // Áp dụng các thuộc tính điều chỉnh
                boss.Max_Rxjh_HP = (int)(boss.Max_Rxjh_HP * modifiers.HpMultiplier);
                boss.Rxjh_HP = boss.Max_Rxjh_HP;

                // Áp dụng giảm phòng thủ
                if (modifiers.PhysicalDefenseReduction > 0)
                {
                    // Giảm phòng thủ vật lý (FLD_DF)
                    boss.FLD_DF *= (1 - modifiers.PhysicalDefenseReduction / 100.0f);
                }

                // Áp dụng tăng sát thương
                boss.FLD_AT *= modifiers.DamageMultiplier;

                LogHelper.WriteLine(LogLevel.Info, $"Đã áp dụng thuộc tính điều chỉnh cho boss {boss.ID} ({boss.Name}) loại {boss.BossType}:");
                LogHelper.WriteLine(LogLevel.Info, $"- HP: {originalHP} -> {boss.Max_Rxjh_HP} (x{modifiers.HpMultiplier})");
                LogHelper.WriteLine(LogLevel.Info, $"- AT: {originalAT} -> {boss.FLD_AT} (x{modifiers.DamageMultiplier})");
                LogHelper.WriteLine(LogLevel.Info, $"- DF: {originalDF} -> {boss.FLD_DF} (-{modifiers.PhysicalDefenseReduction}%)");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi áp dụng thuộc tính điều chỉnh cho boss: {ex.Message}");
            }
        }
    }
}