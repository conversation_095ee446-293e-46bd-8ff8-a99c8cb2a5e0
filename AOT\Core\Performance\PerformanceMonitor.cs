using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Performance
{
    /// <summary>
    /// Performance monitoring for database and actor operations
    /// </summary>
    public static class PerformanceMonitor
    {
        private static readonly ConcurrentDictionary<string, PerformanceMetric> _metrics = new();
        private static readonly Timer _reportTimer;
        private static long _totalOperations = 0;
        private static long _slowOperations = 0;

        static PerformanceMonitor()
        {
            // Report metrics every 30 seconds
            _reportTimer = new Timer(ReportMetrics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// Track operation performance
        /// </summary>
        public static IDisposable TrackOperation(string operationName)
        {
            return new OperationTracker(operationName);
        }

        /// <summary>
        /// Record operation completion
        /// </summary>
        public static void RecordOperation(string operationName, TimeSpan duration, bool success = true)
        {
            Interlocked.Increment(ref _totalOperations);
            
            if (duration.TotalMilliseconds > 1000) // Slow operation threshold
            {
                Interlocked.Increment(ref _slowOperations);
                Logger.Instance.Warning($"Slow operation detected: {operationName} took {duration.TotalMilliseconds:F2}ms");
            }

            _metrics.AddOrUpdate(operationName, 
                new PerformanceMetric(operationName, duration, success),
                (key, existing) => existing.Update(duration, success));
        }

        /// <summary>
        /// Get current performance statistics
        /// </summary>
        public static string GetPerformanceReport()
        {
            var report = $"=== Performance Report ===\n";
            report += $"Total Operations: {_totalOperations}\n";
            report += $"Slow Operations: {_slowOperations} ({(_slowOperations * 100.0 / Math.Max(_totalOperations, 1)):F1}%)\n";
            report += $"Active Metrics: {_metrics.Count}\n\n";

            foreach (var metric in _metrics.Values)
            {
                if (metric.Count > 0)
                {
                    report += $"{metric.Name}: {metric.Count} ops, avg {metric.AverageMs:F1}ms, max {metric.MaxMs:F1}ms, success {metric.SuccessRate:F1}%\n";
                }
            }

            return report;
        }

        private static void ReportMetrics(object? state)
        {
            try
            {
                if (_totalOperations > 0)
                {
                    Logger.Instance.Debug(GetPerformanceReport());
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error reporting performance metrics: {ex.Message}");
            }
        }

        private class OperationTracker : IDisposable
        {
            private readonly string _operationName;
            private readonly Stopwatch _stopwatch;
            private bool _disposed = false;

            public OperationTracker(string operationName)
            {
                _operationName = operationName;
                _stopwatch = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _stopwatch.Stop();
                    RecordOperation(_operationName, _stopwatch.Elapsed);
                    _disposed = true;
                }
            }
        }

        private class PerformanceMetric
        {
            private readonly object _lock = new object();
            
            public string Name { get; }
            public long Count { get; private set; }
            public double TotalMs { get; private set; }
            public double MaxMs { get; private set; }
            public long SuccessCount { get; private set; }

            public double AverageMs => Count > 0 ? TotalMs / Count : 0;
            public double SuccessRate => Count > 0 ? (SuccessCount * 100.0 / Count) : 0;

            public PerformanceMetric(string name, TimeSpan duration, bool success)
            {
                Name = name;
                Update(duration, success);
            }

            public PerformanceMetric Update(TimeSpan duration, bool success)
            {
                lock (_lock)
                {
                    Count++;
                    var durationMs = duration.TotalMilliseconds;
                    TotalMs += durationMs;
                    MaxMs = Math.Max(MaxMs, durationMs);
                    
                    if (success)
                        SuccessCount++;
                }
                return this;
            }
        }
    }
}
