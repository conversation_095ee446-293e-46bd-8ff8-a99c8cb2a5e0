public static class ItemDef
{

	public static class QuestRewardPillItem
	{
		// EXP
		public const int ExpBuff_120_90Min = 9000085;
		public const int ExpBuff_120_120Min = 9000120;
		public const int ExpBuff_120_120Min_2 = 9000528;
		public const int ExpBuff_120_120Min_3 = 9000562;
		public const int ExpBuff_120_120Min_4 = 9001211;
		public const int ExpBuff_120_120Min_5 = 9009652;
		public const int ExpBuff_120_120Min_6 = 9009713;
		public const int ExpBuff_120_120Min_7 = 9009745;
		public const int ExpBuff_120_120Min_8 = 9009803;
		public const int ExpBuff_120_120Min_9 = 9009838;
		public const int ExpBuff_120_120Min_10 = 9009879;

		// skill EXP
		public const int SkillExpBuff_150_90Min = 9000088;
		public const int SkillExpBuff_150_90Min_2 = 9009256;

		public const int SkillExpBuff_150_120Min = 9000104;
		public const int SkillExpBuff_150_120Min_2 = 9000145;
		public const int SkillExpBuff_150_120Min_3 = 9000545;
		public const int SkillExpBuff_150_120Min_4 = 9000568;
		public const int SkillExpBuff_150_120Min_5 = 9001214;
		public const int SkillExpBuff_150_120Min_6 = 9009653;
	}

	public static class Item
	{
		public const int KimCuongThach = 800000001;

		public const int HanNgocThach = 800000002;

		public const int CuongHoaThach = 800000006;

		public const int KimCuongThachRandom = 800000011;

		public const int HanNgocThachRandom = 800000012;

		public const int NhietHuyetThach = 800000013;

		public const int KimCuongThachCaoCap = 800000023;

		public const int HanNgocThachCaoCap = 800000024;

		public const int KimCuongThach_DaKich = 800000025;

		public const int KimCuongThach_VoCong = 800000026;

		public const int ThuocTinhThachRandom = 800000027;

		public const int ThuocTinhThach = 800000028;

		public const int SoCapKyNgocThach = 800000046;

		public const int TrungCapKyNgocThach = 800000047;

		public const int CaoCapKyNgocThach = 800000048;

		public const int ThuongCapKyNgocThach = 800000049;

		public const int CuongHoaThachSieuCap = 800000060;

		public const int KimCuongThachSieuCap = 800000061;

		public const int HanNgocThachSieuCap = 800000062;

		public const int HanNgocThachHonNguyen = 1000001651;

		public const int KimCuongThachHonNguyen = 1000001650;

		public const int ExperiencePoint = 909000001;

		public const int Money = 909000004;

		public const int Money2 = 2000000000;

		public const int DanhVongTheLucChien = 900000600;

		public const int VoHuanNgauNhien10_10000 = 909000012;

		public const int voHoangNgauNhien5_50000 = 909000019;

		public const int ExpRandom1percent = 909000014;

		public const int DanhVongBangHoi = 999000001;

		public const int GiayChungNhanNhiemVuBangHoi = 999001043;
	}

	public static class TapHonThach
	{
		public const int HaCapTapHonChau = 1000000321;
		public const int TrungCapTapHonCha = 1000000323;
		public const int ThuongCapTapHonChau = 1000000325;
		public const int TuLinhTapHonChau = 1000000327;
	}

	public static class RESIDE1
	{
		public const int BladeMan = 1;

		public const int SwordMan = 2;

		public const int SpearMan = 3;

		public const int BowMan = 4;

		public const int Healer = 5;

		public const int Assassin = 6;

		public const int Musician = 7;

		public const int HanBaoQuan = 8;

		public const int DamHoaLien = 9;

		public const int Fighter = 10;

		public const int DieuYen = 11;

		public const int TuHao = 12;

		public const int Meigo = 13;

		public const int Other = 0;
	}

	public static class RESIDE2
	{
		public const int Clothes = 1;

		public const int Gloves = 2;

		public const int Weapon = 3;

		public const int Shoes = 4;

		public const int Armor = 5;

		public const int Necklace = 6;

		public const int Earring = 7;

		public const int Ring = 8;

		public const int Cloak = 9;

		public const int Stone = 10;

		public const int LuckySymbol = 11;

		public const int StoneOfDestiny = 12;

		public const int QigongBook = 13;

		public const int Trigger = 15;

		public const int Box = 17;

		public const int AttributeStone = 19;

		public const int Arrow = 20;

		public const int ArmorOfPet = 21;

		public const int ArmorOfPet2 = 22;

		public const int PetItem1 = 23;

		public const int PetItem2 = 24;

		public const int PetItem3 = 25;

		public const int PetItem4 = 26;

		public const int PetItem5 = 27;

		public const int PetItem6 = 28;

		public const int PetItem7 = 30;

		public const int Flower = 31;

		public const int GemWeapon = 32;

		public const int GemClothes = 33;

		public const int GemArmor = 34;

		public const int GemGloves = 35;

		public const int GemShoes = 36;
	}

	public struct MyItem
	{
		public string Text;

		public int Value;

		public MyItem(string text, int value)
		{
			Text = text;
			Value = value;
		}

		public override string ToString()
		{
			return Text;
		}
	}

	
	public static bool TrangBi_Position_CoChinhXacKhong(int FLD_RESIDE2, int 目标Position)
	{
		switch (FLD_RESIDE2)
		{
		case 1:
			if (目标Position == 0)
			{
				break;
			}
			return false;
		case 2:
			if (目标Position == 1 || 目标Position == 2)
			{
				break;
			}
			return false;
		case 3:
			if (目标Position == 3)
			{
				break;
			}
			return false;
		case 4:
			if (目标Position == 4)
			{
				break;
			}
			return false;
		case 5:
			if (目标Position == 5)
			{
				break;
			}
			return false;
		case 6:
			if (目标Position == 6)
			{
				break;
			}
			return false;
		case 7:
			if (目标Position == 7 || 目标Position == 8)
			{
				break;
			}
			return false;
		case 8:
			if (目标Position == 9 || 目标Position == 10)
			{
				break;
			}
			return false;
		case 9:
			if (目标Position == 11)
			{
				break;
			}
			return false;
		case 10:
		case 11:
		case 12:
		case 13:
		case 14:
		case 15:
		case 16:
		case 17:
		case 18:
		case 19:
		case 23:
		case 24:
		case 25:
		case 26:
		case 27:
		case 28:
		case 29:
			return false;
		case 20:
			if (目标Position == 12)
			{
				break;
			}
			return false;
		case 21:
			if (目标Position == 13)
			{
				break;
			}
			return false;
		case 22:
			if (目标Position == 14)
			{
				break;
			}
			return false;
		case 30:
			if (目标Position == 15)
			{
				break;
			}
			return false;
		case 31:
			if (目标Position == 16)
			{
				break;
			}
			return false;
		case 32:
			if (目标Position == 3)
			{
				break;
			}
			return false;
		case 33:
			if (目标Position == 0)
			{
				break;
			}
			return false;
		case 34:
			if (目标Position == 5)
			{
				break;
			}
			return false;
		case 35:
			if (目标Position == 1 || 目标Position == 2)
			{
				break;
			}
			return false;
		case 36:
			if (目标Position == 4)
			{
				break;
			}
			return false;
		default:
			return false;
		}
		return true;
	}

	public static bool VatPhamNgheNghiep_CoTrungKhopKhong(int PlayerJob, int RESIDE1, int RESIDE2, int pid)
	{
		switch (PlayerJob)
		{
		case 1:
		case 2:
		case 3:
		case 4:
		case 5:
			if (PlayerJob == RESIDE1 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 6:
			if (RESIDE1 == 11 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 7:
			if (RESIDE1 == 12 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 8:
			if (RESIDE1 == 13 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 9:
			if (RESIDE1 == 14 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 10:
			if (RESIDE1 == 16 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 11:
			if (RESIDE1 == 17)
			{
				return true;
			}
			switch (RESIDE2)
			{
			case 9:
				return true;
			case 6:
				if (pid == 1100005 || pid == 1100006)
				{
					return true;
				}
				break;
			case 30:
				return true;
			case 20:
				return true;
			case 21:
				return true;
			case 22:
				return true;
			case 32:
				return true;
			case 33:
				return true;
			case 34:
				return true;
			case 35:
				return true;
			case 36:
				return true;
			}
			break;
		case 12:
			if (RESIDE1 == 18 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 13:
			if (RESIDE1 == 19 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		}
		return false;
	}
}
