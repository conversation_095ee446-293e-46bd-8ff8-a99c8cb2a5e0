﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_attendance_templates {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_attendance_templates_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(IsNullable = false)]
		public string name { get; set; }

		[JsonProperty]
		public int month { get; set; }

		[JsonProperty]
		public int year { get; set; }

		[JsonProperty]
		public bool is_active { get; set; } = false;

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime created_date { get; set; }

		[JsonProperty]
		public DateTime start_date { get; set; }

		[JsonProperty]
		public DateTime end_date { get; set; }

		/// <summary>
		/// Loại attendance: normal, comeback_14day
		/// </summary>
		[JsonProperty, Column(StringLength = 50, IsNullable = false)]
		public string attendance_type { get; set; } = "normal";

		/// <summary>
		/// Số ngày không login để đủ điều kiện (0 = không yêu cầu)
		/// </summary>
		[JsonProperty]
		public int eligible_after_days { get; set; } = 1000;

	}

}
