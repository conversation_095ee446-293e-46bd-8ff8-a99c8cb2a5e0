using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;
using YulgangServer;

namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Protocol messages cho Boss Cross Server
    /// </summary>
    public static class CrossServerBossProtocol
    {
        // Message types
        public const string BOSS_SPAWN = "CROSS_SERVER_BOSS_SPAWN";
        public const string BOSS_HP_UPDATE = "CROSS_SERVER_BOSS_HP_UPDATE";
        public const string BOSS_DAMAGE_CONTRIBUTE = "CROSS_SERVER_BOSS_DAMAGE";
        public const string BOSS_STATE_UPDATE = "CROSS_SERVER_BOSS_STATE";
        public const string BOSS_DEATH = "CROSS_SERVER_BOSS_DEATH";
        public const string BOSS_REWARD_DISTRIBUTION = "CROSS_SERVER_BOSS_REWARD";
        public const string BOSS_CLEANUP = "CROSS_SERVER_BOSS_CLEANUP";

        /// <summary>
        /// Tạo message spawn boss
        /// Format: CROSS_SERVER_BOSS_SPAWN|OriginServerID|BossID|BossName|MapID|X|Y|MaxHP|DurationMinutes|BossType|AccessCondition|GuildID|Radius
        /// </summary>
        public static string CreateBossSpawnMessage(CrossServerBossSpawnInfo spawnInfo)
        {
            return $"{BOSS_SPAWN}|{spawnInfo.OriginServerId}|{spawnInfo.BossId}|{spawnInfo.BossName}|{spawnInfo.MapId}|{spawnInfo.X}|{spawnInfo.Y}|{spawnInfo.MaxHP}|{spawnInfo.DurationMinutes}|{(int)spawnInfo.BossType}|{spawnInfo.AccessCondition}|{spawnInfo.GuildId}|{spawnInfo.Radius}";
        }

        /// <summary>
        /// Parse message spawn boss
        /// </summary>
        public static CrossServerBossSpawnInfo ParseBossSpawnMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 13)
                    throw new ArgumentException("Invalid boss spawn message format");

                return new CrossServerBossSpawnInfo
                {
                    OriginServerId = int.Parse(parts[1]),
                    BossId = int.Parse(parts[2]),
                    BossName = parts[3],
                    MapId = int.Parse(parts[4]),
                    X = float.Parse(parts[5]),
                    Y = float.Parse(parts[6]),
                    MaxHP = int.Parse(parts[7]),
                    DurationMinutes = int.Parse(parts[8]),
                    BossType = (BossType)int.Parse(parts[9]),
                    AccessCondition = int.Parse(parts[10]),
                    GuildId = int.Parse(parts[11]),
                    Radius = float.Parse(parts[12])
                };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing boss spawn message: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Tạo message cập nhật HP boss
        /// Format: CROSS_SERVER_BOSS_HP_UPDATE|BossID|CurrentHP|MaxHP|DamageDealt|AttackerServerID|AttackerSessionID|AttackerName
        /// </summary>
        public static string CreateBossHPUpdateMessage(BossHPUpdateInfo hpInfo)
        {
            return $"{BOSS_HP_UPDATE}|{hpInfo.BossId}|{hpInfo.CurrentHP}|{hpInfo.MaxHP}|{hpInfo.DamageDealt}|{hpInfo.AttackerServerId}|{hpInfo.AttackerSessionId}|{hpInfo.AttackerName}";
        }

        /// <summary>
        /// Parse message cập nhật HP boss
        /// </summary>
        public static BossHPUpdateInfo ParseBossHPUpdateMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 8)
                    throw new ArgumentException("Invalid boss HP update message format");

                return new BossHPUpdateInfo
                {
                    BossId = int.Parse(parts[1]),
                    CurrentHP = int.Parse(parts[2]),
                    MaxHP = int.Parse(parts[3]),
                    DamageDealt = int.Parse(parts[4]),
                    AttackerServerId = int.Parse(parts[5]),
                    AttackerSessionId = int.Parse(parts[6]),
                    AttackerName = parts[7]
                };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing boss HP update message: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Tạo message damage contribution
        /// Format: CROSS_SERVER_BOSS_DAMAGE|BossID|ServerID|SessionID|PlayerName|Damage|AttackCount
        /// </summary>
        public static string CreateDamageContributeMessage(int bossId, int serverId, int sessionId, string playerName, long damage, int attackCount)
        {
            return $"{BOSS_DAMAGE_CONTRIBUTE}|{bossId}|{serverId}|{sessionId}|{playerName}|{damage}|{attackCount}";
        }

        /// <summary>
        /// Parse message damage contribution
        /// </summary>
        public static (int bossId, int serverId, int sessionId, string playerName, long damage, int attackCount) ParseDamageContributeMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 7)
                    throw new ArgumentException("Invalid damage contribute message format");

                return (
                    int.Parse(parts[1]),    // bossId
                    int.Parse(parts[2]),    // serverId
                    int.Parse(parts[3]),    // sessionId
                    parts[4],               // playerName
                    long.Parse(parts[5]),   // damage
                    int.Parse(parts[6])     // attackCount
                );
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing damage contribute message: {ex.Message}");
                return (0, 0, 0, "", 0, 0);
            }
        }

        /// <summary>
        /// Tạo message cập nhật state boss
        /// Format: CROSS_SERVER_BOSS_STATE|BossID|CurrentHP|MaxHP|State
        /// </summary>
        public static string CreateBossStateUpdateMessage(int bossId, int currentHP, int maxHP, CrossServerBossState state)
        {
            return $"{BOSS_STATE_UPDATE}|{bossId}|{currentHP}|{maxHP}|{(int)state}";
        }

        /// <summary>
        /// Parse message cập nhật state boss
        /// </summary>
        public static (int bossId, int currentHP, int maxHP, CrossServerBossState state) ParseBossStateUpdateMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 5)
                    throw new ArgumentException("Invalid boss state update message format");

                return (
                    int.Parse(parts[1]),                        // bossId
                    int.Parse(parts[2]),                        // currentHP
                    int.Parse(parts[3]),                        // maxHP
                    (CrossServerBossState)int.Parse(parts[4])   // state
                );
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing boss state update message: {ex.Message}");
                return (0, 0, 0, CrossServerBossState.Active);
            }
        }

        /// <summary>
        /// Tạo message boss death
        /// Format: CROSS_SERVER_BOSS_DEATH|BossID|KillerName|KillerServerID|KillerSessionID|TotalDamage|TotalParticipants
        /// </summary>
        public static string CreateBossDeathMessage(BossDeathInfo deathInfo)
        {
            return $"{BOSS_DEATH}|{deathInfo.BossId}|{deathInfo.KillerName}|{deathInfo.KillerServerId}|{deathInfo.KillerSessionId}|{deathInfo.TotalDamageDealt}|{deathInfo.TotalParticipants}";
        }

        /// <summary>
        /// Parse message boss death
        /// </summary>
        public static BossDeathInfo ParseBossDeathMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 7)
                    throw new ArgumentException("Invalid boss death message format");

                return new BossDeathInfo
                {
                    BossId = int.Parse(parts[1]),
                    KillerName = parts[2],
                    KillerServerId = int.Parse(parts[3]),
                    KillerSessionId = int.Parse(parts[4]),
                    TotalDamageDealt = long.Parse(parts[5]),
                    TotalParticipants = int.Parse(parts[6])
                };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing boss death message: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Tạo message reward distribution
        /// Format: CROSS_SERVER_BOSS_REWARD|BossID|ServerID|RewardData
        /// RewardData: SessionID:PlayerName:Damage:Points:HasSpecial:SpecialItem;...
        /// </summary>
        public static string CreateRewardDistributionMessage(int bossId, int serverId, List<CrossServerRewardInfo> rewards)
        {
            var rewardData = string.Join(";", rewards.Select(r => 
                $"{r.SessionId}:{r.PlayerName}:{r.TotalDamage}:{r.RewardPoints}:{r.HasSpecialReward}:{r.SpecialRewardItem ?? ""}"));
            
            return $"{BOSS_REWARD_DISTRIBUTION}|{bossId}|{serverId}|{rewardData}";
        }

        /// <summary>
        /// Parse message reward distribution
        /// </summary>
        public static (int bossId, int serverId, List<CrossServerRewardInfo> rewards) ParseRewardDistributionMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 4)
                    throw new ArgumentException("Invalid reward distribution message format");

                var bossId = int.Parse(parts[1]);
                var serverId = int.Parse(parts[2]);
                var rewardData = parts[3];

                var rewards = new List<CrossServerRewardInfo>();
                
                if (!string.IsNullOrEmpty(rewardData))
                {
                    var rewardEntries = rewardData.Split(';');
                    foreach (var entry in rewardEntries)
                    {
                        var rewardParts = entry.Split(':');
                        if (rewardParts.Length >= 6)
                        {
                            rewards.Add(new CrossServerRewardInfo
                            {
                                BossId = bossId,
                                ServerId = serverId,
                                SessionId = int.Parse(rewardParts[0]),
                                PlayerName = rewardParts[1],
                                TotalDamage = long.Parse(rewardParts[2]),
                                RewardPoints = int.Parse(rewardParts[3]),
                                HasSpecialReward = bool.Parse(rewardParts[4]),
                                SpecialRewardItem = string.IsNullOrEmpty(rewardParts[5]) ? null : rewardParts[5]
                            });
                        }
                    }
                }

                return (bossId, serverId, rewards);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing reward distribution message: {ex.Message}");
                return (0, 0, new List<CrossServerRewardInfo>());
            }
        }

        /// <summary>
        /// Tạo message cleanup boss
        /// Format: CROSS_SERVER_BOSS_CLEANUP|BossID
        /// </summary>
        public static string CreateBossCleanupMessage(int bossId)
        {
            return $"{BOSS_CLEANUP}|{bossId}";
        }

        /// <summary>
        /// Parse message cleanup boss
        /// </summary>
        public static int ParseBossCleanupMessage(string[] parts)
        {
            try
            {
                if (parts.Length < 2)
                    throw new ArgumentException("Invalid boss cleanup message format");

                return int.Parse(parts[1]);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error parsing boss cleanup message: {ex.Message}");
                return 0;
            }
        }
    }
}
