﻿using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang;

class Program
{
    public static void Main(string[] args)
    {
        // Register CodePagesEncodingProvider to support Windows-1252 encoding
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        // Check for custom config path
        string configPath = null;
        string appSettingsPath = null;
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i].StartsWith("--config=", StringComparison.OrdinalIgnoreCase))
            {
                configPath = args[i].Substring("--config=".Length);
            }
            else if (args[i].StartsWith("--appsettings=", StringComparison.OrdinalIgnoreCase))
            {
                appSettingsPath = args[i].Substring("--appsettings=".Length);
            }
        }

        // Run in headless mode by default
        var world = World.Instance;
        // world.StartAsync().Wait();
        _ = Task.Run(async () => await RunHeadlessDI(world, true, configPath, appSettingsPath));

        // Start console interface (this will block and handle user input)
        RunHeadlessConsoleNonBlocking(world).Wait();
    }

    private static async Task RunHeadlessDI(World world, bool autoStart, string customConfigPath, string customAppSettingsPath = null)
    {
        try
        {
            Console.WriteLine("=== HeroYulgang GameServer (Headless Mode) ===");

            // Load custom appsettings if specified, or auto-detect from config path
            if (!string.IsNullOrEmpty(customAppSettingsPath))
            {
                Console.WriteLine($"Loading custom appsettings: {customAppSettingsPath}");
                ConfigManager.Instance.LoadCustomAppSettings(customAppSettingsPath);
            }
            else if (!string.IsNullOrEmpty(customConfigPath))
            {
                // Auto-detect appsettings.json in the same directory as config.json
                string configDirectory = Path.GetDirectoryName(customConfigPath);
                if (!string.IsNullOrEmpty(configDirectory))
                {
                    string autoAppSettingsPath = Path.Combine(configDirectory, "appsettings.json");
                    if (File.Exists(autoAppSettingsPath))
                    {
                        Console.WriteLine($"Auto-loading appsettings from config directory: {autoAppSettingsPath}");
                        ConfigManager.Instance.LoadCustomAppSettings(autoAppSettingsPath);
                    }
                }
            }

            // Load custom config if specified
            if (!string.IsNullOrEmpty(customConfigPath))
            {
                Console.WriteLine($"Loading custom config: {customConfigPath}");
                ConfigManager.Instance.LoadCustomConfig(customConfigPath);
            }

            // Initialize logger
            var logger = Logger.Instance;
            logger.Info("GameServer starting in headless mode...");

            // Initialize configuration
            var config = ConfigManager.Instance;
            logger.Info($"Server: {config.ServerSettings.ServerName}");
            logger.Info($"Port: {config.ServerSettings.GameServerPort}");

            // Initialize World
            logger.Info("Initializing World...");

            if (autoStart)
            {
                // Auto-start the world
                logger.Info("Auto-starting World...");
                bool success = await world.StartAsync();

                if (success)
                {
                    logger.Info("✓ GameServer started successfully in headless mode");
                    logger.Info($"✓ Server is listening on port {config.ServerSettings.GameServerPort}");
                    logger.Info("✓ Ready to accept player connections");

                    // Keep the application running without blocking console input
                    await RunHeadlessConsoleNonBlocking(world);
                    Console.WriteLine("GameServer is running. Commands: 'q' to quit, 's' to show status...");
                }
                else
                {
                    logger.Error("✗ Failed to start GameServer");
                    Environment.Exit(1);
                }
            }
            else
            {
                logger.Info("GameServer initialized. Use 'start' command to begin.");
                await RunHeadlessConsoleNonBlocking(world);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fatal error in headless mode: {ex}");
            Environment.Exit(1);
        }
    }

    private static async Task RunHeadlessConsoleNonBlocking(World world)
    {
        // Set console encoding to UTF-8 for Vietnamese characters
        try
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not set console encoding: {ex.Message}");
        }

        var cancellationTokenSource = new System.Threading.CancellationTokenSource();

        // Handle console input in background task - only if console is available
        Task consoleTask = null;

        try
        {
            // Check if console input is available (not redirected)
            if (!Console.IsInputRedirected && Environment.UserInteractive)
            {
                consoleTask = Task.Run(async () =>
                {
                    Console.WriteLine("Console ready. Type 'help' for available commands.");

                    while (!cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            // Simple blocking read - this works better for console input
                            var input = await Task.Run(() => Console.ReadLine(), cancellationTokenSource.Token);

                            if (!string.IsNullOrEmpty(input))
                            {
                                var trimmedInput = input.ToLower().Trim();
                                Console.WriteLine($"Processing command: {trimmedInput}");
                                await HandleConsoleCommand(trimmedInput, world, cancellationTokenSource);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            break;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Console input error: {ex.Message}");
                            await Task.Delay(1000, cancellationTokenSource.Token);
                        }
                    }
                }, cancellationTokenSource.Token);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Console setup error: {ex.Message}");
        }

        // Keep the main thread alive
        try
        {
            if (consoleTask != null)
            {
                await consoleTask;
            }
            else
            {
                // If no console available, just wait indefinitely
                await Task.Delay(-1, cancellationTokenSource.Token);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when shutting down
        }
    }

    private static async Task HandleConsoleCommand(string input, World world, System.Threading.CancellationTokenSource cancellationTokenSource)
    {
        switch (input)
        {
            case "q":
            case "quit":
            case "exit":
                Console.WriteLine("Shutting down GameServer...");
                if (world.State == WorldState.Running)
                {
                    await world.StopAsync();
                }
                cancellationTokenSource.Cancel();
                Environment.Exit(0);
                break;

            case "s":
            case "status":
                ShowStatus(world);
                break;

            case "start":
                if (world.State != WorldState.Running)
                {
                    Console.WriteLine("Starting World...");
                    bool success = await world.StartAsync();
                    Console.WriteLine(success ? "✓ World started" : "✗ Failed to start World");
                }
                else
                {
                    Console.WriteLine("World is already running");
                }
                break;

            case "stop":
                if (world.State == WorldState.Running)
                {
                    Console.WriteLine("Stopping World...");
                    bool success = await world.StopAsync();
                    Console.WriteLine(success ? "✓ World stopped" : "✗ Failed to stop World");
                }
                else
                {
                    Console.WriteLine("World is not running");
                }
                break;

            case "restart":
                Console.WriteLine("Restarting World...");
                if (world.State == WorldState.Running)
                {
                    await world.StopAsync();
                    await Task.Delay(2000);
                }
                bool restartSuccess = await world.StartAsync();
                Console.WriteLine(restartSuccess ? "✓ World restarted" : "✗ Failed to restart World");
                break;

            case "help":
            case "h":
                ShowHelp();
                break;

            // Drop system commands
            case var cmd when cmd.StartsWith("enablenewdrop") || cmd.StartsWith("disablenewdrop") ||
                             cmd.StartsWith("dropmultiplier") || cmd.StartsWith("dropdebug") ||
                             cmd.StartsWith("refreshdrops") || cmd.StartsWith("dropstatus") ||
                             cmd.StartsWith("testdrop") || cmd.StartsWith("dropstats") ||
                             cmd.StartsWith("drophelp"):
                RxjhServer.Commands.DropSystemCommands.ProcessDropCommand(input);
                break;

            default:
                if (!string.IsNullOrEmpty(input))
                {
                    Console.WriteLine($"Unknown command: {input}. Type 'help' for available commands.");
                }
                break;
        }
    }
    private static void ShowStatus(World world)
    {
        Console.WriteLine("\n=== GameServer Status ===");
        Console.WriteLine($"State: {world.State}");
        Console.WriteLine($"Uptime: {(world.State == WorldState.Running ? DateTime.Now - world.StartTime : TimeSpan.Zero):hh\\:mm\\:ss}");
        Console.WriteLine($"Port: {ConfigManager.Instance.ServerSettings.GameServerPort}");
        Console.WriteLine($"Max Players: {ConfigManager.Instance.ServerSettings.MaximumOnline}");
        Console.WriteLine($"Server Name: {ConfigManager.Instance.ServerSettings.ServerName}");
        Console.WriteLine("========================\n");
    }

    private static void ShowHelp()
    {
        Console.WriteLine("\n=== Available Commands ===");
        Console.WriteLine("start    - Start the World");
        Console.WriteLine("stop     - Stop the World");
        Console.WriteLine("restart  - Restart the World");
        Console.WriteLine("status   - Show server status");
        Console.WriteLine("quit     - Shutdown server");
        Console.WriteLine("help     - Show this help");
        Console.WriteLine("");
        Console.WriteLine("=== Drop System Commands ===");
        Console.WriteLine("drophelp           - Show drop system commands");
        Console.WriteLine("dropstatus         - Show drop system status");
        Console.WriteLine("enablenewdrop      - Enable new drop system");
        Console.WriteLine("disablenewdrop     - Disable new drop system");
        Console.WriteLine("dropmultiplier <x> - Set drop rate multiplier");
        Console.WriteLine("dropdebug [on/off] - Toggle drop debug logging");
        Console.WriteLine("refreshdrops       - Reload drop configuration");
        Console.WriteLine("testdrop <npc> <lvl> - Test drop for NPC");
        Console.WriteLine("dropstats          - Show drop statistics");
        Console.WriteLine("=========================\n");
    }


}
