using System;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.RxjhServer.Attendance;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;

namespace RxjhServer
{
    public partial class Players
    {
        /// <summary>
        /// Current attendance progress của player
        /// </summary>
        public PlayerAttendance CurrentAttendanceProgress { get; set; }

        /// <summary>
        /// Load attendance data khi player login
        /// </summary>
        public async Task LoadPlayerAttendanceAsync()
        {
            try
            {
                if (World.CurrentAttendance == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"No current attendance available for {CharacterName}");
                    return;
                }

                CurrentAttendanceProgress = await GameDb.LoadPlayerAttendanceAsync(
                    CharacterName, World.CurrentAttendance.Id);

                if (CurrentAttendanceProgress == null)
                {
                    // Tạo mới nếu chưa có
                    CurrentAttendanceProgress = new PlayerAttendance
                    {
                        PlayerName = CharacterName,
                        AttendanceId = World.CurrentAttendance.Id,
                        ReceivedDaysString = "",
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };

                    LogHelper.WriteLine(LogLevel.Info,
                        $"Created new attendance progress for {CharacterName} - Attendance: {World.CurrentAttendance.Name}");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Info,
                        $"Loaded attendance for {CharacterName}: {CurrentAttendanceProgress.GetReceivedDays().Count} days received - Attendance: {World.CurrentAttendance.Name}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to load player attendance: {ex.Message}");
            }
        }

        /// <summary>
        /// Refresh attendance data nếu attendance đã thay đổi
        /// </summary>
        public async Task RefreshAttendanceIfNeededAsync()
        {
            try
            {
                if (World.CurrentAttendance == null) return;

                // Nếu attendance ID khác với current attendance, cần load lại
                if (CurrentAttendanceProgress == null ||
                    CurrentAttendanceProgress.AttendanceId != World.CurrentAttendance.Id)
                {
                    LogHelper.WriteLine(LogLevel.Info,
                        $"Attendance changed for {CharacterName}, refreshing...");
                    await LoadPlayerAttendanceAsync();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to refresh attendance: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra có thể nhận thưởng ngày hôm nay không
        /// </summary>
        public bool CanReceiveTodayReward()
        {
            if (World.CurrentAttendance == null || CurrentAttendanceProgress == null)
                return false;

            return AttendanceHelper.CanReceiveTodayReward(CurrentAttendanceProgress);
        }

        /// <summary>
        /// Lấy reward cho ngày có thể nhận tiếp theo
        /// </summary>
        public AttendanceReward GetNextAvailableReward()
        {
            if (World.CurrentAttendance == null || CurrentAttendanceProgress == null)
                return null;

            var nextDay = AttendanceHelper.GetNextAvailableDay(CurrentAttendanceProgress);
            if (nextDay == -1) return null;

            return World.CurrentAttendance.GetRewardForDay(nextDay);
        }

        /// <summary>
        /// Lấy ngày có thể nhận tiếp theo
        /// </summary>
        public int GetNextAvailableDay()
        {
            if (CurrentAttendanceProgress == null) return 1;
            return AttendanceHelper.GetNextAvailableDay(CurrentAttendanceProgress);
        }

        /// <summary>
        /// Lấy trạng thái reward cho một ngày cụ thể
        /// </summary>
        public AttendanceRewardStatus GetRewardStatus(int dayNumber)
        {
            return AttendanceHelper.GetRewardStatus(dayNumber, CurrentAttendanceProgress);
        }

       

        /// <summary>
        /// Check attendance và nhận thưởng
        /// </summary>
        public async void CheckAttendance()
        {
            try
            {
                if (World.CurrentAttendance == null)
                {
                    SendAttendanceResponse(false, "No active attendance");
                    HeThongNhacNho("Không có sự kiện điểm danh nào đang hoạt động", 10, "Attendance");
                    return;
                }

                var canReceive = CanReceiveTodayReward();
                var success = false;

                if (canReceive)
                {
                    var reward = GetNextAvailableReward();
                    var nextDay = GetNextAvailableDay();

                    if (reward != null && nextDay != -1)
                    {
                        // Tạo item
                        var item = World.CreateAnItem(reward.ItemId, reward.ItemAmount);
                        var emptySlot = GetParcelVacancy(this);

                        if (emptySlot != -1)
                        {
                            // Thêm item vào inventory
                            AddItems(item.ItemGlobal_ID, item.VatPham_ID, emptySlot,
                                    item.VatPhamSoLuong, item.VatPham_ThuocTinh);

                            // Cập nhật attendance progress
                            CurrentAttendanceProgress.AddReceivedDay(nextDay);
                            CurrentAttendanceProgress.LastReceivedDate = DateTime.Now;

                            // Lưu vào database
                            await GameDb.SavePlayerAttendanceAsync(CurrentAttendanceProgress);

                            success = true;
                            LogHelper.WriteLine(LogLevel.Info,
                                $"{CharacterName} received attendance reward day {nextDay}: {reward.ItemId} x{reward.ItemAmount}");
                        }
                        else
                        {
                            SendAttendanceResponse(false, "Inventory full");
                            return;
                        }
                    }
                }

                SendAttendanceResponse(success, success ? "Success" : "Cannot receive today");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Attendance error for {CharacterName}: {ex.Message}");
                SendAttendanceResponse(false, "Server error");
            }
        }

        /// <summary>
        /// Gửi response cho attendance check
        /// </summary>
        private void SendAttendanceResponse(bool success, string message = "")
        {
            try
            {
                SendingClass w = new();
                w.Write2(0);
                w.Write4(success ? 1 : 0);
                Client?.SendPak(w, 0x2605, SessionID);

                if (!string.IsNullOrEmpty(message))
                {
                    LogHelper.WriteLine(LogLevel.Info, $"Attendance response for {CharacterName}: {message}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to send attendance response: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi attendance packet cho client
        /// </summary>
        public async void SendAttendancePacket()
        {
            try
            {
                // Refresh attendance nếu cần
                await RefreshAttendanceIfNeededAsync();

                var attendanceEvent = new AttendanceEvent();
                var w = attendanceEvent.BuildAttendancePacket(this);
                Client?.SendPak(w, 0x2405, SessionID, true);

                LogHelper.WriteLine(LogLevel.Info, $"Sent attendance packet to {CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to send attendance packet to {CharacterName}: {ex.Message}");
            }
        }
        public async void CheckAttendance14Day()
        {
            try
            {
                // Lấy attendance 14 ngày cho player này
                var comeback14DayAttendance = World.CurrentAttendance14Day;

                if (comeback14DayAttendance == null)
                {
                    SendAttendanceResponse(false, "Not eligible for comeback attendance");
                    HeThongNhacNho("Hiện tại không có sự kiện điểm danh trở lại", 10, "Attendance");
                    return;
                }

                // Load attendance progress cho attendance 14 ngày
                var attendanceProgress = await GameDb.LoadPlayerAttendanceAsync(CharacterName, comeback14DayAttendance.Id);

                if (attendanceProgress == null)
                {
                    // Tạo mới nếu chưa có
                    attendanceProgress = new PlayerAttendance
                    {
                        PlayerName = CharacterName,
                        AttendanceId = comeback14DayAttendance.Id,
                        ReceivedDaysString = "",
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                }

                // Lấy ngày có thể nhận tiếp theo
                var nextDay = AttendanceHelper.GetNextAvailableDay(attendanceProgress);
                var canReceive = AttendanceHelper.CanReceiveTodayReward(attendanceProgress);

                bool success = false;

                if (canReceive && nextDay != -1)
                {
                    var reward = comeback14DayAttendance.GetRewardForDay(nextDay);

                    if (reward != null)
                    {
                        // Tạo item
                        var item = World.CreateAnItem(reward.ItemId, reward.ItemAmount);
                        var emptySlot = GetParcelVacancy(this);

                        if (emptySlot != -1)
                        {
                            // Thêm item vào inventory
                            AddItems(item.ItemGlobal_ID, item.VatPham_ID, emptySlot,
                                    item.VatPhamSoLuong, item.VatPham_ThuocTinh);

                            // Cập nhật attendance progress
                            attendanceProgress.AddReceivedDay(nextDay);
                            attendanceProgress.LastReceivedDate = DateTime.Now;

                            // Lưu vào database
                            await GameDb.SavePlayerAttendanceAsync(attendanceProgress);

                            success = true;
                            LogHelper.WriteLine(LogLevel.Info,
                                $"{CharacterName} received comeback 14-day attendance reward day {nextDay}: {reward.ItemId} x{reward.ItemAmount}");
                        }
                        else
                        {
                            SendAttendanceResponse(false, "Inventory full");
                            HeThongNhacNho("Hành trang đầy, không thể nhận thưởng điểm danh", 10, "Attendance");
                            return;
                        }
                    }
                }

                if (success)
                {
                    SendAttendanceResponse(true, "Comeback attendance reward received");
                    HeThongNhacNho($"Đã nhận thưởng điểm danh trở lại ngày {nextDay}", 10, "Attendance");
                }
                else
                {
                    SendAttendanceResponse(false, "Cannot receive reward today");
                    HeThongNhacNho("Hôm nay bạn đã nhận thưởng hoặc chưa đến lượt", 10, "Attendance");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to check comeback 14-day attendance for {CharacterName}: {ex.Message}");
                SendAttendanceResponse(false, "System error");
            }
        }
        public async void SendAttendance14DayPacket()
        {
            try
            {
                // Lấy attendance 14 ngày cho player này
                var comeback14DayAttendance = World.CurrentAttendance14Day;

                SendingClass w = new();

                if (comeback14DayAttendance == null || CreatedAt < comeback14DayAttendance.StartDate)
                {
                    // Player không đủ điều kiện hoặc không có attendance 14 ngày active
                    w.Write2(0); // 0 = không có hoạt động
                    Client?.SendPak(w, 0x3003, SessionID, true);
                    LogHelper.WriteLine(LogLevel.Info, $"Player {CharacterName} not eligible for comeback 14-day attendance");
                    return;
                }

                // Load attendance progress cho attendance 14 ngày
                var attendanceProgress = await GameDb.LoadPlayerAttendanceAsync(CharacterName, comeback14DayAttendance.Id);

                if (attendanceProgress == null)
                {
                    // Tạo mới nếu chưa có
                    attendanceProgress = new PlayerAttendance
                    {
                        PlayerName = CharacterName,
                        AttendanceId = comeback14DayAttendance.Id,
                        ReceivedDaysString = "",
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                }

                var receivedDays = attendanceProgress.GetReceivedDays();
                var canReceiveToday = AttendanceHelper.CanReceiveTodayReward(attendanceProgress);
                var nextAvailableDay = AttendanceHelper.GetNextAvailableDay(attendanceProgress);

                w.Write2(1); // 1 = có điểm danh 14 ngày

                // Gửi thông tin cho 14 ngày
                for (int day = 1; day <= 14; day++)
                {
                    var reward = comeback14DayAttendance.GetRewardForDay(day);
                    if (reward != null)
                    {
                        // Lấy trạng thái reward cho ngày này
                        var status = AttendanceHelper.GetRewardStatus(day, attendanceProgress);

                        // Nếu không thể nhận hôm nay và status là CanReceive, chuyển thành CannotReceive
                        if (!canReceiveToday && status == AttendanceRewardStatus.CanReceive)
                        {
                            status = AttendanceRewardStatus.CannotReceive;
                        }

                        w.Write4((int)status); // 0=can't receive, 1=can receive, 2=already received
                        w.Write8(reward.ItemId);
                        w.Write2(reward.ItemAmount);
                    }
                    else
                    {
                        // Không có reward cho ngày này
                        w.Write4(0); // can't receive
                        w.Write8(0); // no item
                        w.Write2(0); // no amount
                    }
                }

                Client?.SendPak(w, 0x3003, SessionID, true);
                LogHelper.WriteLine(LogLevel.Info, $"Sent comeback 14-day attendance packet to {CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to send comeback 14-day attendance packet to {CharacterName}: {ex.Message}");

                // Gửi packet trống nếu có lỗi
                SendingClass w = new();
                w.Write2(0);
                Client?.SendPak(w, 0x3003, SessionID, true);
            }
        }
    }
}