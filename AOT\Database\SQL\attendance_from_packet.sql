-- =====================================================
-- ATTENDANCE TEMPLATE TỪ PACKET DATA
-- =====================================================

-- Phân tích packet: aa5594016f02000024058a01...
-- Bắt đầu từ offset 0x12, mỗi item có format:
-- [4 bytes] ItemId (little endian)
-- [4 bytes] Padding  
-- [4 bytes] Amount (little endian)
-- [2 bytes] Padding

-- Parsed 28 items từ packet:
-- Day 01: ItemId = 1008003464 (0x3C14DD08) x1
-- Day 02: ItemId = 1008003665 (0x3C14E651) x1  
-- Day 03: ItemId = 1008003666 (0x3C14E652) x1
-- Day 04: ItemId = 1008003667 (0x3C14E653) x1
-- Day 05: ItemId = 1008003668 (0x3C14E654) x1
-- Day 06: ItemId = 1008003668 (0x3C14E654) x1
-- Day 07: ItemId = 999900128 (0x3B9ACFE0) x50
-- Day 08: ItemId = 1008003535 (0x3C0599CF) x1
-- Day 09: ItemId = 1008003465 (0x3C14DD09) x1
-- Day 10: ItemId = 1008003669 (0x3C14E655) x1
-- Day 11: ItemId = 1008003670 (0x3C14E656) x1
-- Day 12: ItemId = 1008003671 (0x3C14E657) x1
-- Day 13: ItemId = 1008003672 (0x3C14E658) x1
-- Day 14: ItemId = 999900128 (0x3B9ACFE0) x100
-- Day 15: ItemId = 1008003541 (0x3C0599D5) x1
-- Day 16: ItemId = 1008003571 (0x3C14DDF3) x1
-- Day 17: ItemId = 1008003702 (0x3C14E076) x1
-- Day 18: ItemId = 1008003678 (0x3C14E45E) x1
-- Day 19: ItemId = 1008003533 (0x3C14E3CD) x1
-- Day 20: ItemId = 1008003595 (0x3C14E20B) x1
-- Day 21: ItemId = 1008003596 (0x3C14E20C) x1
-- Day 22: ItemId = 1008003601 (0x3C14E011) x1
-- Day 23: ItemId = 1008003538 (0x3C14E2D2) x1
-- Day 24: ItemId = 1008003594 (0x3C14E60A) x1
-- Day 25: ItemId = 1008003673 (0x3C14E659) x1
-- Day 26: ItemId = 1008003537 (0x3C14E5D1) x1
-- Day 27: ItemId = 1008003546 (0x3C14E5DA) x1
-- Day 28: ItemId = 1008003421 (0x3C14DD5D) x1

-- Tạo attendance template mới
INSERT INTO tbl_attendance_templates (name, month, year, is_active, start_date, end_date)
VALUES (
    'Điểm danh từ Packet Data',
    EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER,
    EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER,
    true,
    DATE_TRUNC('month', CURRENT_DATE),
    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'
) ON CONFLICT (month, year) DO UPDATE SET
    name = EXCLUDED.name,
    is_active = EXCLUDED.is_active;

-- Thêm 28 rewards từ packet data
DO $$
DECLARE
    template_id INTEGER;
BEGIN
    -- Lấy ID của template vừa tạo hoặc update
    SELECT id INTO template_id
    FROM tbl_attendance_templates
    WHERE month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER 
    AND year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER;
    
    -- Xóa rewards cũ nếu có
    DELETE FROM tbl_attendance_rewards WHERE attendance_id = template_id;
    
    -- Thêm 28 rewards mới
    INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount) VALUES
    (template_id, 1, 1008003464, 1),   -- 0x3C14DD08
    (template_id, 2, 1008003665, 1),   -- 0x3C14E651
    (template_id, 3, 1008003666, 1),   -- 0x3C14E652
    (template_id, 4, 1008003667, 1),   -- 0x3C14E653
    (template_id, 5, 1008003668, 1),   -- 0x3C14E654
    (template_id, 6, 1008003668, 1),   -- 0x3C14E654 (duplicate)
    (template_id, 7, 999900128, 50),   -- 0x3B9ACFE0 (special reward)
    (template_id, 8, 1008003535, 1),   -- 0x3C0599CF
    (template_id, 9, 1008003465, 1),   -- 0x3C14DD09
    (template_id, 10, 1008003669, 1),  -- 0x3C14E655
    (template_id, 11, 1008003670, 1),  -- 0x3C14E656
    (template_id, 12, 1008003671, 1),  -- 0x3C14E657
    (template_id, 13, 1008003672, 1),  -- 0x3C14E658
    (template_id, 14, 999900128, 100), -- 0x3B9ACFE0 (bigger special reward)
    (template_id, 15, 1008003541, 1),  -- 0x3C0599D5
    (template_id, 16, 1008003571, 1),  -- 0x3C14DDF3
    (template_id, 17, 1008003702, 1),  -- 0x3C14E076
    (template_id, 18, 1008003678, 1),  -- 0x3C14E45E
    (template_id, 19, 1008003533, 1),  -- 0x3C14E3CD
    (template_id, 20, 1008003595, 1),  -- 0x3C14E20B
    (template_id, 21, 1008003596, 1),  -- 0x3C14E20C
    (template_id, 22, 1008003601, 1),  -- 0x3C14E011
    (template_id, 23, 1008003538, 1),  -- 0x3C14E2D2
    (template_id, 24, 1008003594, 1),  -- 0x3C14E60A
    (template_id, 25, 1008003673, 1),  -- 0x3C14E659
    (template_id, 26, 1008003537, 1),  -- 0x3C14E5D1
    (template_id, 27, 1008003546, 1),  -- 0x3C14E5DA
    (template_id, 28, 1008003421, 1);  -- 0x3C14DD5D
    
    RAISE NOTICE 'Attendance template created/updated with ID: % and 28 rewards added', template_id;
END $$;

-- Verify the data
SELECT 
    t.id,
    t.name,
    t.month,
    t.year,
    t.is_active,
    COUNT(r.id) as reward_count
FROM tbl_attendance_templates t
LEFT JOIN tbl_attendance_rewards r ON t.id = r.attendance_id
WHERE t.month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER 
AND t.year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER
GROUP BY t.id, t.name, t.month, t.year, t.is_active;

-- Show all rewards
SELECT 
    r.day_number,
    r.item_id,
    r.item_amount,
    '0x' || UPPER(TO_HEX(r.item_id)) as item_id_hex
FROM tbl_attendance_rewards r
JOIN tbl_attendance_templates t ON r.attendance_id = t.id
WHERE t.month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER 
AND t.year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER
ORDER BY r.day_number;
