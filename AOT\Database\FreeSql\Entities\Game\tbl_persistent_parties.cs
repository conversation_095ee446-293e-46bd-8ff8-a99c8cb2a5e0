﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_persistent_parties {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_persistent_parties_party_id_seq'::regclass)")]
		public int party_id { get; set; }

		[JsonProperty, Column(StringLength = 36, IsNullable = false)]
		public string party_uuid { get; set; }

		[JsonProperty]
		public int? team_id { get; set; }

		[JsonProperty]
		public int server_id { get; set; }

		[JsonProperty, Column(StringLength = 50, IsNullable = false)]
		public string leader_name { get; set; }

		[JsonProperty]
		public int? loot_type { get; set; } = 1;

		[JsonProperty]
		public int? max_members { get; set; } = 8;

		[JsonProperty]
		public int? persistence_mode { get; set; } = 1;

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime? created_at { get; set; }

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime? last_active_at { get; set; }

		[JsonProperty]
		public bool? is_active { get; set; } = false;

		[JsonProperty, Column(InsertValueSql = "'{}'::jsonb")]
		public JToken members { get; set; }

		[JsonProperty]
		public JToken metadata { get; set; }

	}

}
