using System;
using System.Net;
using HeroYulgang.Services;
using HeroYulgang.Core.Networking.Network;
using RxjhServer;
using Akka.Actor;
using HeroYulgang.Core.Networking.Utils;
using HeroYulgang.Utils;

namespace HeroYulgang.Core.NetCore
{
    /// <summary>
    /// Unified NetState that works with both Akka.net and NetCoreServer
    /// Uses composition instead of inheritance to avoid override issues
    /// </summary>
    public class UnifiedNetState : ActorNetState
    {
        private readonly GameSession? _gameSession;
        private readonly ActorNetState? _akkaNetState;

        /// <summary>
        /// Constructor for NetCoreServer mode
        /// Sử dụng ConnectionID cho network operations
        /// </summary>
        public UnifiedNetState(GameSession gameSession)
            : base(ActorRefs.NoSender, gameSession.ConnectionId, new IPEndPoint(IPAddress.Parse("127.0.0.1"), 13000))
        {
            _gameSession = gameSession ?? throw new ArgumentNullException(nameof(gameSession));
            _akkaNetState = null;
        }

        /// <summary>
        /// Constructor for Akka.net mode
        /// </summary>
        public UnifiedNetState(IActorRef connection, int sessionId, IPEndPoint remoteEndPoint)
            : base(connection, sessionId, remoteEndPoint)
        {
            _gameSession = null;
            _akkaNetState = new ActorNetState(connection, sessionId, remoteEndPoint);
        }
    }
}
