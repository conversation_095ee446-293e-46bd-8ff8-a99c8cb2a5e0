

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FreeSql;
using HeroYulgang.Core;
using HeroYulgang.Database.FreeSql.Entities.BBG;
using HeroYulgang.Services;
using RxjhServer;
using static RxjhServer.MailSystem;

namespace HeroYulgang.Database.FreeSql
{
    public static class BBGDb
    {
        private static IFreeSql _freeSql;


        public static bool Initialize()
        {
            try
            {
                if (_freeSql != null) return true;

                var connectionString = ConfigManager.Instance.PogresSettings.BBGDb;
                //Logger.Instance.Info("BBGDb connection string: " + connectionString);
                // Create FreeSql instance
                _freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    //.UseAutoSyncStructure(true)
                    .UseAdoConnectionPool(true)
                    // .UseNoneCommandParameter(true)
                    .Build();

               // Logger.Instance.Error("✓ BBGDb initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to initialize BBGDb: {ex.Message}");
                return false;
            }

        }
        #region CashShop

        public static void LoadCashShopCategory()
        {
            try
            {
                World.WebShopCategoryList.Clear();
                var categories = _freeSql.Select<shopcategory>().ToList();
                foreach (var category in categories)
                {
                    World.WebShopCategoryList.Add(category.id , new World.X_WebShop_Category
                    {
                        ID = category.id ,
                        NAME = category.name ?? string.Empty,
                        PARENTID = category.parentid ?? 0,
                        DISPLAYORDER = category.displayorder ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.WebShopCategoryList.Count} BBG categories");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load BBG categories: {ex.Message}");
            }
        }
        /// <summary>
        /// Lấy danh sách item trong shop
        /// </summary>

        public static void LoadCashShopItem()
        {
            try
            {
                World.WebShopItemList.Clear();
                var items = _freeSql.Select<cashshop>().ToList();
                foreach (var item in items)
                {
                    World.WebShopItemList.Add(item.id.ToString(), new()
                    {
                        ID = item.id,
                        PRODUCT_CODE = item.product_code ?? string.Empty,
                        PID = item.fld_pid,
                        NAME = item.fld_name ?? string.Empty,
                        PRICE = item.fld_price ?? 0,
                        PRICE_OLD = item.fld_price_old ?? 0,
                        DESC = item.fld_desc ?? string.Empty,
                        RETURN = item.fld_return ?? 0,
                        NUMBER = item.fld_number ?? 0,
                        MAGIC0 = item.fld_magic1 ?? 0,
                        MAGIC1 = item.fld_magic2 ?? 0,
                        MAGIC2 = item.fld_magic3 ?? 0,
                        MAGIC3 = item.fld_magic4 ?? 0,
                        MAGIC4 = item.fld_magic5 ?? 0,
                        ThucTinh = item.fld_socapphuhon ?? 0,
                        TrungCapHon = item.fld_trungcapphuhon ?? 0,
                        TienHoa = item.fld_tienhoa ?? 0,
                        KhoaLai = item.fld_phaichangkhoalai ?? 0,
                        NgaySuDung = item.fld_days ?? 0,
                        CATEGORY_ID = item.category_id ?? 0,
                    });
                }

            }

            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load BBG items: {ex.Message}");
            }
        }
        /// <summary>
        /// Cập nhật trạng thái của CashShop log
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <returns></returns>

        public static async Task<bool> UpdateCashShopLog(long id, string status)
        {
            try
            {
                await _freeSql.Update<cash_shop_log>()
                    .Set(a => a.status == status)
                    .Where(a => a.id == id)
                    .ExecuteAffrowsAsync();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to update CashShop log: {ex.Message}");
                return false;
            }
        }

        public static async Task<long> InsertCashShopLog(string status, string message, string buyer, int amount, long price,
            int marketId, string productId)
        {
            try
            {
                var insertedId = await _freeSql.Insert(new cash_shop_log
                {
                    status = status,
                    message = message,
                    username = buyer,
                    amount = amount,
                    price = price,
                    item_id = marketId,
                    product_id = productId,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now,
                }).ExecuteIdentityAsync();
                return insertedId;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to insert CashShop log: {ex.Message}");
                return -1;
            }
        }



        /// <summary>
        /// Cash shop purchase history data model
        /// Model dữ liệu lịch sử mua hàng cash shop
        /// </summary>
        public class CashShopPurchaseData
        {
            public int RowNum { get; set; }
            public string ItemName { get; set; } = string.Empty;
            public int ItemCount { get; set; }
            public long BuyCash { get; set; }
            public string BuyDate { get; set; } = string.Empty;
            public string CashType { get; set; } = string.Empty;
            public string DType { get; set; } = string.Empty;
            public string State { get; set; } = string.Empty;
            public int BuyNum { get; set; }
        }

        /// <summary>
        /// Cash shop purchase history result
        /// Kết quả lịch sử mua hàng cash shop
        /// </summary>
        public class CashShopPurchaseResult
        {
            public List<CashShopPurchaseData> Data { get; set; } = new();
            public int TotalCount { get; set; }
            public int CurrentPage { get; set; }
            public int PageSize { get; set; }
            public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        }

        /// <summary>
        /// Get cash shop purchase history with pagination
        /// Lấy lịch sử mua hàng cash shop có phân trang
        /// </summary>
        /// <param name="username">Username - Tên người dùng</param>
        /// <param name="month">Month to filter - Tháng cần lọc</param>
        /// <param name="page">Page number (1-based) - Số trang (bắt đầu từ 1)</param>
        /// <param name="pageSize">Items per page - Số item mỗi trang</param>
        /// <returns>Cash shop purchase history result</returns>
        public static async Task<CashShopPurchaseResult> GetCashShopPurchaseHistory(string username, int month, int page = 1, int pageSize = 10)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    Logger.Instance.Error("✗ GetCashShopPurchaseHistory: Username cannot be null or empty");
                    return new CashShopPurchaseResult();
                }

                // Get total count for the month
                var totalCount = await _freeSql
                    .Select<cash_shop_log>()
                    .Where(a => a.username == username && a.created_at.Value.Month == month)
                    .CountAsync();

                if (totalCount == 0)
                {
                    return new CashShopPurchaseResult
                    {
                        Data = new List<CashShopPurchaseData>(),
                        TotalCount = 0,
                        CurrentPage = page,
                        PageSize = pageSize
                    };
                }

                // Get paginated data
                var skip = (page - 1) * pageSize;
                var cashShopLogs = await _freeSql
                    .Select<cash_shop_log>()
                    .Where(a => a.username == username && a.created_at.Value.Month == month)
                    .OrderByDescending(a => a.created_at)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                // Format data for response
                var formattedData = new List<CashShopPurchaseData>();
                var rowIndex = skip + 1;

                foreach (var log in cashShopLogs)
                {
                    // Get item information from WebShopItemList
                    World.WebShopItemList.TryGetValue(log.product_id ?? string.Empty, out var item);

                    if (item == null)
                    {
                        Logger.Instance.Error($"⚠ Item not found in WebShopItemList for product_id: {log.product_id}");
                        continue;
                    }

                    var purchaseData = new CashShopPurchaseData
                    {
                        RowNum = rowIndex,
                        ItemName = World.Unitoccp1258(item.NAME),
                        ItemCount = log.amount ?? 0,
                        BuyCash = log.price ?? 0,
                        BuyDate = log.created_at?.ToString("yyyy-MM-dd HH:mm:ss") ?? string.Empty,
                        CashType = string.Empty,
                        DType = string.Empty,
                        State = string.Empty,
                        BuyNum = 0
                    };

                    formattedData.Add(purchaseData);
                    rowIndex++;
                }

                var result = new CashShopPurchaseResult
                {
                    Data = formattedData,
                    TotalCount = (int)totalCount,
                    CurrentPage = page,
                    PageSize = pageSize
                };

                Logger.Instance.Debug($"✓ Retrieved {formattedData.Count} cash shop records for {username} (Month: {month}, Page: {page})");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error getting cash shop purchase history: {ex.Message}");
                return new CashShopPurchaseResult();
            }
        }


        /// <summary>
        /// Cash shop purchase grid data model for history grid
        /// Model dữ liệu grid lịch sử mua hàng cash shop
        /// </summary>
        public class CashShopGridData
        {
            public string ProductCode { get; set; } = string.Empty;
            public int ItemId { get; set; }
            public string ItemName { get; set; } = string.Empty;
            public int ItemType { get; set; }
            public int Price { get; set; }
            public int OldPrice { get; set; }
            public int Number { get; set; }
        }

        /// <summary>
        /// Cash shop purchase grid result with pagination
        /// Kết quả grid lịch sử mua hàng với phân trang
        /// </summary>
        public class CashShopGridResult
        {
            public List<CashShopGridData> Data { get; set; } = new();
            public int TotalCount { get; set; }
            public int CurrentPage { get; set; }
            public int PageSize { get; set; }
            public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
            public int CurrentItemCount => Math.Min(PageSize, Math.Max(0, TotalCount - CurrentPage * PageSize));
        }

        /// <summary>
        /// Get successful cash shop purchases for history grid with pagination
        /// Lấy lịch sử mua hàng thành công cho grid với phân trang
        /// </summary>
        /// <param name="username">Username - Tên người dùng</param>
        /// <param name="page">Page number (0-based) - Số trang (bắt đầu từ 0)</param>
        /// <param name="pageSize">Items per page - Số item mỗi trang</param>
        /// <returns>Cash shop grid result with pagination</returns>
        public static async Task<CashShopGridResult> GetCashShopSuccessfulPurchases(string username, int page = 0, int pageSize = 12)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    Logger.Instance.Error("✗ GetCashShopSuccessfulPurchases: Username cannot be null or empty");
                    return new CashShopGridResult();
                }

                // Get total count of successful purchases
                var totalCount = await _freeSql
                    .Select<cash_shop_log>()
                    .Where(a => a.username == username && a.status == "SUCCESS")
                    .CountAsync();

                if (totalCount == 0)
                {
                    return new CashShopGridResult
                    {
                        Data = new List<CashShopGridData>(),
                        TotalCount = 0,
                        CurrentPage = page,
                        PageSize = pageSize
                    };
                }

                // Get paginated data
                var skip = page * pageSize;
                var cashShopLogs = await _freeSql
                    .Select<cash_shop_log>()
                    .Where(a => a.username == username && a.status == "SUCCESS")
                    .OrderByDescending(a => a.id)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                // Format data for grid
                var gridData = new List<CashShopGridData>();

                foreach (var log in cashShopLogs)
                {
                    // Get item information from WebShopItemList
                    World.WebShopItemList.TryGetValue(log.product_id ?? string.Empty, out var item);

                    if (item == null)
                    {
                        Logger.Instance.Error($"⚠ Item not found in WebShopItemList for product_id: {log.product_id}");
                        continue;
                    }

                    var gridItem = new CashShopGridData
                    {
                        ProductCode = item.ID.ToString(),
                        ItemId = item.PID,
                        ItemName = item.NAME ?? string.Empty,
                        ItemType = item.CATEGORY_ID,
                        Price = item.PRICE,
                        OldPrice = item.PRICE_OLD != 0 ? item.PRICE_OLD : item.PRICE,
                        Number = item.NUMBER
                    };

                    gridData.Add(gridItem);
                }

                var result = new CashShopGridResult
                {
                    Data = gridData,
                    TotalCount = (int)totalCount,
                    CurrentPage = page,
                    PageSize = pageSize
                };

                Logger.Instance.Info($"✓ Retrieved {gridData.Count} cash shop grid items for {username} (Page: {page}, Total: {totalCount})");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error getting cash shop successful purchases: {ex.Message}");
                return new CashShopGridResult();
            }
        }

        #endregion


        #region MailSystem

        public static async Task<List<mailcod>> FetchAdminMail(string accountId, int status)
        {
            try
            {
                return await _freeSql
                    .Select<mailcod>()
                    .Where(a => a.account_id == accountId && a.status == status)
                    .Take(30)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                 Logger.Instance.Error($"✗ Error fetching mail for character: {ex.Message}");
                return new List<mailcod>();
            }
        }

        public static async Task<List<mailcod>> FetchMailForCharacter(string characterName, int status)
        {
            try
            {
                return await _freeSql
                    .Select<mailcod>()
                    .Where(a => a.receiver == characterName && a.status == status)
                    .Take(30)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error fetching mail for character: {ex.Message}");
                return new List<mailcod>();
            }
        }
        public static async Task<List<mailcod>> FetchMailSendByCharacter(string characterName, int status, int status2)
        {
            try
            {
                return await _freeSql
                    .Select<mailcod>()
                    .Where(a => a.sender == characterName && (a.status == status || a.status == status2))
                    .Take(30)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error fetching mail for character: {ex.Message}");
                return new List<mailcod>();
            }
        }

        public static async Task<bool> UpdateMailStatusByID(int id, int status)
        {
            try
            {
                return await _freeSql
                    .Update<mailcod>()
                    .Set(a => a.status == status)
                    .Where(a => a.id == id)
                    .ExecuteAffrowsAsync() > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error updating mail status: {ex.Message}");
                return false;
            }
        }

        public static async Task<bool> UpdateMailStatusByID(int id, int status, bool accept)
        {
            try
            {
                return await _freeSql
                    .Update<mailcod>()
                    .Set(a => a.status == status)
                    .Set(a => a.paid == accept)
                    .Where(a => a.id == id)
                    .ExecuteAffrowsAsync() > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error updating mail status: {ex.Message}");
                return false;
            }
        }

        public static async Task<List<mailcod>> FindMailByID(int id, int status, string receiver, string account_id)
        {
            try
            {
                if (status == 3 || status == 6)
                {
                    return await _freeSql
                    .Select<mailcod>()
                    .Where(a => a.id == id && a.status == status && (a.receiver == receiver || a.account_id == account_id))
                    .ToListAsync();
                }
                else
                {
                    return await _freeSql
                        .Select<mailcod>()
                        .Where(a => a.id == id && a.status == status && a.sender == receiver)
                        .ToListAsync();
                }

            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error finding mail by ID: {ex.Message}");
                return new List<mailcod>();
            }
        }

        public static async Task<bool> InsertGmMail(string sender, string accountId, Item item, double price, byte[] description)
        {
             try
            {
                var mail = new mailcod
                {
                    sender = sender,
                    account_id = accountId,
                    itembyte = item.VatPham_byte,
                    status = (int)MailStatus.Sent,
                    created_at = DateTime.Now,
                    expired_at = DateTime.Now.AddDays(30),
                    paid = false,
                    price = (long)price,
                    description = description,
                    item_name = item.GetItemName()
                };

                var result = await _freeSql
                    .Insert(mail)
                    .ExecuteAffrowsAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error inserting mail: {ex.Message}");
                return false;
            }
        }

        public static async Task<bool> InsertMail(string sender, string receiver, Item item, double price, byte[] description)
        {
            try
            {
                var mail = new mailcod
                {
                    sender = sender,
                    receiver = receiver,
                    itembyte = item.VatPham_byte,
                    status = (int)MailStatus.Sent,
                    created_at = DateTime.Now,
                    expired_at = DateTime.Now.AddDays(1),
                    paid = false,
                    price = (long)price,
                    description = description,
                    item_name = item.GetItemName()
                };

                var result = await _freeSql
                    .Insert(mail)
                    .ExecuteAffrowsAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error inserting mail: {ex.Message}");
                return false;
            }

        }

        public static async Task<bool> UpdateExpiredMail()
        {
            try
            {
                return await _freeSql
                    .Update<mailcod>()
                    .Set(a => a.status == 4)
                    .Where(a => a.status == 3 && a.expired_at <= DateTime.Now)
                    .ExecuteAffrowsAsync() > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error updating mail status: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Cache Management

        /// <summary>
        /// Refresh all cached data from database
        /// </summary>
        public static async Task<bool> RefreshAsync()
        {
            try
            {
                Logger.Instance.Info("Refreshing BBGDb cached data...");

                // Reload cash shop categories
                LoadCashShopCategory();
                Logger.Instance.Info("✓ Cash shop categories reloaded");

                // Reload cash shop items
                LoadCashShopItem();
                Logger.Instance.Info("✓ Cash shop items reloaded");

                Logger.Instance.Info("✓ BBGDb refresh completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to refresh BBGDb: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region marketplace


        public static async Task<marketplace> FindMarketplaceItem(int id, string seller_name, string status)
        {
            try
            {
                return await _freeSql
                    .Select<marketplace>()
                    .Where(a => a.id == id && a.seller_name == seller_name && a.status == status)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error finding marketplace item: {ex.Message}");
                return null;
            }
        }

        public static async Task<long> TotalSelling(string characterName)
        {
            try
            {
                return await _freeSql.Select<marketplace>()
                    .Where(a => a.seller_name == characterName && a.status == "SELLING")
                    .CountAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error getting total selling: {ex.Message}");
                return 0;
            }
        }

        public static async Task<int> RegisterItemInMarket(string characterName, string accountId, string productCode, string itemName, int amount, long price, long fee, int currentAmount, int filter1, int filter2, int filter3, int filter4, int filter5, byte[] itemByte, DateTime createdAt, DateTime expiredAt, bool isCoin = false)
        {
            try
            {
                var marketItem = new marketplace
                {
                    product_code = productCode,
                    item_name = itemName,
                    seller_id = accountId,
                    seller_name = characterName,
                    base_amount = amount,
                    base_price = price,
                    fld_price = fee,
                    current_amount = currentAmount,
                    filter_1 = filter1,
                    filter_2 = filter2,
                    filter_3 = filter3,
                    filter_4 = filter4,
                    filter_5 = filter5,
                    item = itemByte,
                    created_at = createdAt,
                    expired_at = expiredAt,
                    status = "SELLING",
                    iscoin = isCoin,
                };

                var result = await _freeSql
                    .Insert(marketItem)
                    .ExecuteAffrowsAsync();
                return result;

            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error registering item in market: {ex.Message}");
                return 0;
            }
        }

        public static async Task<bool> UpdateMarketItemStatus(string productCode, string status)
        {
            try
            {
                return await _freeSql
                    .Update<marketplace>()
                    .Set(a => a.status == status)
                    .Where(a => a.product_code == productCode)
                    .ExecuteAffrowsAsync() > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error updating market item status: {ex.Message}");
                return false;
            }
        }

        public static async Task<bool> UpdateMarketItemStatus(int id, string status)
        {
            try
            {
                return await _freeSql
                    .Update<marketplace>()
                    .Set(a => a.status == status)
                    .Where(a => a.id == id)
                    .ExecuteAffrowsAsync() > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error updating market item status: {ex.Message}");
                return false;
            }
        }

        public static async Task<int> CountTotalItemSelling(string characterName, string status)
        {
            try
            {
                return (int)await _freeSql
                    .Select<marketplace>()
                    .Where(a => a.seller_name == characterName && a.status == status)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error counting total item selling: {ex.Message}");
                return 0;
            }
        }

        public static async Task<List<marketplace>> GetMarketplaceItems(string characterName, string status, int offset, int pageSize, bool isCoin = false)
        {
            try
            {
                return await _freeSql
                    .Select<marketplace>()
                    .Where(a => a.seller_name == characterName && a.status == status && a.iscoin == isCoin)
                    .Skip(offset)
                    .Take(pageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Error finding market item by character: {ex.Message}");
                return new List<marketplace>();
            }
        }

        /// <summary>
        /// Marketplace filter criteria
        /// Tiêu chí lọc marketplace
        /// </summary>
        public class MarketplaceFilter
        {
            public int Filter1 { get; set; } = 0;
            public int Filter2 { get; set; } = 0;
            public int Filter3 { get; set; } = 0;
            public int Filter4 { get; set; } = 0;
            public int Filter5 { get; set; } = 0;
            public string SearchWord { get; set; } = string.Empty;
            public int SortOrder { get; set; } = 0; // 0=default, 1=filter1 asc, 2=filter1 desc, 3=price asc, 4=price desc

            public bool IsCoin {get; set;} = false;
        }

        /// <summary>
        /// Marketplace search result with pagination
        /// Kết quả tìm kiếm marketplace với phân trang
        /// </summary>
        public class MarketplaceSearchResult
        {
            public List<marketplace> Items { get; set; } = new();
            public int TotalCount { get; set; }
            public int CurrentPage { get; set; }
            public int PageSize { get; set; }
            public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        }

        /// <summary>
        /// Search marketplace items with filters and pagination
        /// Tìm kiếm vật phẩm marketplace với bộ lọc và phân trang
        /// </summary>
        /// <param name="filter">Filter criteria - Tiêu chí lọc</param>
        /// <param name="page">Page number (0-based) - Số trang (bắt đầu từ 0)</param>
        /// <param name="pageSize">Items per page - Số item mỗi trang</param>
        /// <returns>Marketplace search result with pagination</returns>
        public static async Task<MarketplaceSearchResult> SearchMarketplaceItems(MarketplaceFilter filter, int page = 0, int pageSize = 10)
        {
            try
            {
                if (filter == null)
                {
                    Console.WriteLine("✗ SearchMarketplaceItems: Filter cannot be null");
                    return new MarketplaceSearchResult();
                }

                var currentTime = DateTime.Now;

                // Build base query with filters
                var baseQuery = _freeSql
                    .Select<marketplace>()
                    .Where(a => a.status == "SELLING" && a.expired_at >= currentTime);

                // Apply filters (only if not 0)
                if (filter.Filter1 != 0)
                    baseQuery = baseQuery.Where(a => a.filter_1 == filter.Filter1);

                if (filter.Filter2 != 0)
                    baseQuery = baseQuery.Where(a => a.filter_2 == filter.Filter2);

                if (filter.Filter3 != 0)
                    baseQuery = baseQuery.Where(a => a.filter_3 == filter.Filter3);

                if (filter.Filter4 != 0)
                    baseQuery = baseQuery.Where(a => a.filter_4 == filter.Filter4);

                if (filter.Filter5 != 0)
                    baseQuery = baseQuery.Where(a => a.filter_5 == filter.Filter5);

                if (filter.IsCoin)
                    baseQuery = baseQuery.Where(a => a.iscoin);

                // Apply search word filter
                if (!string.IsNullOrEmpty(filter.SearchWord))
                    baseQuery = baseQuery.Where(a => a.item_name.Contains(filter.SearchWord));

                // Get total count
                var totalCount = await baseQuery.CountAsync();

                if (totalCount == 0)
                {
                    return new MarketplaceSearchResult
                    {
                        Items = new List<marketplace>(),
                        TotalCount = 0,
                        CurrentPage = page,
                        PageSize = pageSize
                    };
                }

                // Apply sorting
                var sortedQuery = filter.SortOrder switch
                {
                    1 => baseQuery.OrderBy(a => a.filter_1),
                    2 => baseQuery.OrderByDescending(a => a.filter_1),
                    3 => baseQuery.OrderBy(a => a.base_price),
                    4 => baseQuery.OrderByDescending(a => a.base_price),
                    _ => baseQuery.OrderByDescending(a => a.created_at)
                };

                // Apply pagination
                var skip = page * pageSize;
                var items = await sortedQuery
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                var result = new MarketplaceSearchResult
                {
                    Items = items,
                    TotalCount = (int)totalCount,
                    CurrentPage = page,
                    PageSize = pageSize
                };

                Console.WriteLine($"✓ Retrieved {items.Count} marketplace items (Page: {page}, Total: {totalCount})");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error searching marketplace items: {ex.Message}");
                return new MarketplaceSearchResult();
            }
        }

        /// <summary>
        /// Marketplace profit data model
        /// Model dữ liệu lợi nhuận marketplace
        /// </summary>
        public class MarketplaceProfitData
        {
            public marketplace MarketplaceItem { get; set; }
            public order_detail OrderDetail { get; set; }
            public long ProfitAmount { get; set; }
            public string BuyerName { get; set; } = string.Empty;
        }

        /// <summary>
        /// Get marketplace profit information for taking profit
        /// Lấy thông tin lợi nhuận marketplace để nhận tiền
        /// </summary>
        /// <param name="marketplaceId">Marketplace item ID - ID vật phẩm marketplace</param>
        /// <param name="sellerName">Seller name - Tên người bán</param>
        /// <returns>Marketplace profit data or null if not found</returns>
        public static async Task<MarketplaceProfitData> GetMarketplaceProfitData(int marketplaceId, string sellerName)
        {
            try
            {
                if (string.IsNullOrEmpty(sellerName))
                {
                    Console.WriteLine("✗ GetMarketplaceProfitData: Seller name cannot be null or empty");
                    return null;
                }

                // Get marketplace item with order detail using JOIN
                var result = await _freeSql
                    .Select<marketplace, order_detail>()
                    .InnerJoin((m, od) => m.id == od.marketplace_id)
                    .Where((m, od) => m.id == marketplaceId && m.seller_name == sellerName && m.status == "SOLD")
                    .FirstAsync((m, od) => new { Marketplace = m, OrderDetail = od });

                if (result == null)
                {
                    Console.WriteLine($"⚠ No sold marketplace item found with ID: {marketplaceId} for seller: {sellerName}");
                    return null;
                }

                var profitData = new MarketplaceProfitData
                {
                    MarketplaceItem = result.Marketplace,
                    OrderDetail = result.OrderDetail,
                    ProfitAmount = result.Marketplace.fld_price ?? 0,
                    BuyerName = result.OrderDetail.buyyer ?? string.Empty
                };

                Console.WriteLine($"✓ Retrieved marketplace profit data for item {marketplaceId}, profit: {profitData.ProfitAmount}");
                return profitData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error getting marketplace profit data: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Complete marketplace transaction and mark as completed
        /// Hoàn thành giao dịch marketplace và đánh dấu đã hoàn tất
        /// </summary>
        /// <param name="marketplaceId">Marketplace item ID - ID vật phẩm marketplace</param>
        /// <param name="sellerName">Seller name - Tên người bán</param>
        /// <returns>Profit data if successful, null if failed</returns>
        public static async Task<MarketplaceProfitData> CompleteMarketplaceTransaction(int marketplaceId, string sellerName)
        {
            try
            {
                // First get the profit data
                var profitData = await GetMarketplaceProfitData(marketplaceId, sellerName);
                if (profitData == null)
                {
                    return null;
                }

                // Update marketplace status to COMPLETED
                var updateResult = await UpdateMarketItemStatus(marketplaceId, "COMPLETED");
                if (!updateResult)
                {
                    Console.WriteLine($"✗ Failed to update marketplace item {marketplaceId} status to COMPLETED");
                    return null;
                }

                Console.WriteLine($"✓ Marketplace transaction completed for item {marketplaceId}");
                return profitData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error completing marketplace transaction: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Marketplace purchase result
        /// Kết quả mua hàng marketplace
        /// </summary>
        public class MarketplacePurchaseResult
        {
            public bool Success { get; set; }
            public marketplace Item { get; set; }
            public int OrderId { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
        }

        /// <summary>
        /// Marketplace cancel result
        /// Kết quả hủy bán marketplace
        /// </summary>
        public class MarketplaceCancelResult
        {
            public bool Success { get; set; }
            public marketplace Item { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
        }

        /// <summary>
        /// Purchase marketplace item with race condition protection
        /// Mua vật phẩm marketplace với bảo vệ race condition
        /// </summary>
        /// <param name="productId">Product ID - ID sản phẩm</param>
        /// <param name="buyerName">Buyer name - Tên người mua</param>
        /// <param name="amount">Amount to buy - Số lượng mua</param>
        /// <param name="price">Expected price - Giá dự kiến</param>
        /// <param name="itemValidation">Item validation data - Dữ liệu validation vật phẩm</param>
        /// <returns>Purchase result</returns>
        public static async Task<MarketplacePurchaseResult> PurchaseMarketplaceItem(
            int productId,
            string buyerName,
            int amount,
            long price,
            (long itemId, int option, int magic1, int magic2, int magic3, int magic4) itemValidation)
        {
            try
            {
                if (string.IsNullOrEmpty(buyerName))
                {
                    return new MarketplacePurchaseResult
                    {
                        Success = false,
                        ErrorMessage = "Buyer name cannot be null or empty"
                    };
                }

                // Use distributed lock to prevent race conditions across multiple servers
                var lockKey = $"marketplace_purchase_{productId}";
                var lockTimeout = TimeSpan.FromSeconds(30);

                // Simulate distributed lock (you may need to implement Redis-based locking)
                using (await AcquireDistributedLock(lockKey, lockTimeout))
                {
                    // Get marketplace item with current status
                    var item = await _freeSql
                        .Select<marketplace>()
                        .Where(a => a.id == productId && a.status == "SELLING" && a.expired_at >= DateTime.Now)
                        .FirstAsync();

                    if (item == null)
                    {
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Item not found or already sold/expired"
                        };
                    }

                    // Validate item data to prevent cheating
                    if (!ValidateMarketplaceItem(item, amount, price, itemValidation))
                    {
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Item validation failed"
                        };
                    }

                    // Create order detail first
                    var orderId = await CreateOrderDetail(buyerName, productId, amount, price);
                    if (orderId == -1)
                    {
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Failed to create order"
                        };
                    }

                    // Update marketplace status to SOLD atomically
                    var updateResult = await _freeSql
                        .Update<marketplace>()
                        .Set(a => a.status == "SOLD")
                        .Where(a => a.id == productId && a.status == "SELLING")
                        .ExecuteAffrowsAsync();

                    if (updateResult == 0)
                    {
                        // Item was already sold by another transaction
                        await UpdateOrderStatus(orderId, "FAILED");
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Item was already sold"
                        };
                    }

                    // Update order to success
                    await UpdateOrderStatus(orderId, "SUCCESSED");

                    Console.WriteLine($"✓ Marketplace item {productId} purchased by {buyerName} for {price}");
                    return new MarketplacePurchaseResult
                    {
                        Success = true,
                        Item = item,
                        OrderId = orderId
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error purchasing marketplace item: {ex.Message}");
                return new MarketplacePurchaseResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public static async Task<MarketplacePurchaseResult> PurchaseMarketplaceItemCoin(
            int productId,
            string buyerName,
            int amount,
            long price,
            (long itemId, int option, int magic1, int magic2, int magic3, int magic4) itemValidation)
        {
            try
            {
                if (string.IsNullOrEmpty(buyerName))
                {
                    return new MarketplacePurchaseResult
                    {
                        Success = false,
                        ErrorMessage = "Buyer name cannot be null or empty"
                    };
                }

                // Use distributed lock to prevent race conditions across multiple servers
                var lockKey = $"marketplace_purchase_{productId}";
                var lockTimeout = TimeSpan.FromSeconds(30);

                // Simulate distributed lock (you may need to implement Redis-based locking)
                using (await AcquireDistributedLock(lockKey, lockTimeout))
                {
                    // Get marketplace item with current status
                    var item = await _freeSql
                        .Select<marketplace>()
                        .Where(a => a.id == productId && a.status == "SELLING" && a.expired_at >= DateTime.Now && a.iscoin)
                        .FirstAsync();

                    if (item == null)
                    {
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Item not found or already sold/expired"
                        };
                    }

                    // Validate item data to prevent cheating
                    if (!ValidateMarketplaceItem(item, amount, price, itemValidation))
                    {
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Item validation failed"
                        };
                    }

                    // Create order detail first
                    var orderId = await CreateOrderDetail(buyerName, productId, amount, price);
                    if (orderId == -1)
                    {
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Failed to create order"
                        };
                    }

                    // Update marketplace status to SOLD atomically
                    var updateResult = await _freeSql
                        .Update<marketplace>()
                        .Set(a => a.status == "SOLD")
                        .Where(a => a.id == productId && a.status == "SELLING")
                        .ExecuteAffrowsAsync();

                    if (updateResult == 0)
                    {
                        // Item was already sold by another transaction
                        await UpdateOrderStatus(orderId, "FAILED");
                        return new MarketplacePurchaseResult
                        {
                            Success = false,
                            ErrorMessage = "Item was already sold"
                        };
                    }

                    // Update order to success
                    await UpdateOrderStatus(orderId, "SUCCESSED");

                    Console.WriteLine($"✓ Marketplace item {productId} purchased by {buyerName} for {price}");
                    return new MarketplacePurchaseResult
                    {
                        Success = true,
                        Item = item,
                        OrderId = orderId
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error purchasing marketplace item: {ex.Message}");
                return new MarketplacePurchaseResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// Cancel marketplace selling with validation
        /// Hủy bán marketplace với validation
        /// </summary>
        /// <param name="productId">Product ID - ID sản phẩm</param>
        /// <param name="sellerName">Seller name - Tên người bán</param>
        /// <param name="amount">Expected amount - Số lượng dự kiến</param>
        /// <param name="itemValidation">Item validation data - Dữ liệu validation vật phẩm</param>
        /// <returns>Cancel result</returns>
        public static async Task<MarketplaceCancelResult> CancelMarketplaceSelling(
            int productId,
            string sellerName,
            int amount,
            (long itemId, int option, int magic1, int magic2, int magic3, int magic4) itemValidation)
        {
            try
            {
                if (string.IsNullOrEmpty(sellerName))
                {
                    return new MarketplaceCancelResult
                    {
                        Success = false,
                        ErrorMessage = "Seller name cannot be null or empty"
                    };
                }

                // Get marketplace item
                var item = await FindMarketplaceItem(productId, sellerName, "SELLING");
                if (item == null)
                {
                    return new MarketplaceCancelResult
                    {
                        Success = false,
                        ErrorMessage = "Item not found or not selling"
                    };
                }

                // Validate item data to prevent cheating
                if (!ValidateMarketplaceItem(item, amount, 0, itemValidation))
                {
                    return new MarketplaceCancelResult
                    {
                        Success = false,
                        ErrorMessage = "Item validation failed"
                    };
                }

                // Update status to CANCELLED
                var updateResult = await UpdateMarketItemStatus(productId, "CANCELLED");
                if (!updateResult)
                {
                    return new MarketplaceCancelResult
                    {
                        Success = false,
                        ErrorMessage = "Failed to update item status"
                    };
                }

                Console.WriteLine($"✓ Marketplace item {productId} cancelled by {sellerName}");
                return new MarketplaceCancelResult
                {
                    Success = true,
                    Item = item
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error cancelling marketplace item: {ex.Message}");
                return new MarketplaceCancelResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// Create order detail for marketplace purchase
        /// Tạo chi tiết đơn hàng cho mua hàng marketplace
        /// </summary>
        /// <param name="buyerName">Buyer name - Tên người mua</param>
        /// <param name="marketplaceId">Marketplace ID - ID marketplace</param>
        /// <param name="amount">Amount - Số lượng</param>
        /// <param name="price">Price - Giá</param>
        /// <returns>Order detail ID or -1 if failed</returns>
        public static async Task<int> CreateOrderDetail(string buyerName, int marketplaceId, int amount, long price)
        {
            try
            {
                var orderDetail = new order_detail
                {
                    status = "PENDING",
                    message = null,
                    buyyer = buyerName,
                    amount = amount,
                    price = price,
                    marketplace_id = marketplaceId,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };

                var result = await _freeSql
                    .Insert(orderDetail)
                    .ExecuteIdentityAsync();

                Console.WriteLine($"✓ Order detail created with ID: {result}");
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error creating order detail: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Update order status
        /// Cập nhật trạng thái đơn hàng
        /// </summary>
        /// <param name="orderId">Order ID - ID đơn hàng</param>
        /// <param name="status">New status - Trạng thái mới</param>
        /// <returns>True if successful</returns>
        public static async Task<bool> UpdateOrderStatus(int orderId, string status)
        {
            try
            {
                var result = await _freeSql
                    .Update<order_detail>()
                    .Set(a => a.status == status)
                    .Set(a => a.updated_at == DateTime.Now)
                    .Where(a => a.id == orderId)
                    .ExecuteAffrowsAsync();

                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error updating order status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Validate marketplace item data to prevent cheating
        /// Kiểm tra dữ liệu vật phẩm marketplace để chống gian lận
        /// </summary>
        /// <param name="item">Marketplace item - Vật phẩm marketplace</param>
        /// <param name="amount">Expected amount - Số lượng dự kiến</param>
        /// <param name="price">Expected price - Giá dự kiến (0 to skip price check)</param>
        /// <param name="itemValidation">Item validation data - Dữ liệu validation vật phẩm</param>
        /// <returns>True if validation passes</returns>
        private static bool ValidateMarketplaceItem(
            marketplace item,
            int amount,
            long price,
            (long itemId, int option, int magic1, int magic2, int magic3, int magic4) itemValidation)
        {
            try
            {
                if (item?.item == null) return false;

                // Create Item object from marketplace item data
                var marketItem = new Item { VatPham_byte = item.item };

                // Validate amount
                if (amount != item.current_amount) return false;

                // Validate price (skip if price is 0)
                if (price > 0 && price != item.base_price) return false;

                // Validate item properties
                if (itemValidation.itemId != marketItem.GetVatPham_ID) return false;
                if (itemValidation.option != marketItem.FLD_MAGIC0) return false;
                if (itemValidation.magic1 != marketItem.FLD_MAGIC1) return false;
                if (itemValidation.magic2 != marketItem.FLD_MAGIC2) return false;
                if (itemValidation.magic3 != marketItem.FLD_MAGIC3) return false;
                if (itemValidation.magic4 != marketItem.FLD_MAGIC4) return false;

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error validating marketplace item: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Acquire distributed lock (placeholder implementation)
        /// Lấy distributed lock (implementation tạm thời)
        /// </summary>
        /// <param name="lockKey">Lock key - Khóa lock</param>
        /// <param name="timeout">Timeout - Thời gian chờ</param>
        /// <returns>Disposable lock object</returns>
        private static async Task<IDisposable> AcquireDistributedLock(string lockKey, TimeSpan timeout)
        {
            // TODO: Implement Redis-based distributed locking for production
            // For now, use a simple in-memory lock (not suitable for multi-server)
            await Task.Delay(1); // Simulate async operation
            return new DisposableLock();
        }

        /// <summary>
        /// Simple disposable lock implementation
        /// Implementation lock đơn giản
        /// </summary>
        private class DisposableLock : IDisposable
        {
            public void Dispose()
            {
                // Release lock resources
            }
        }

        #endregion

        #region Cumulative Reward System

        /// <summary>
        /// Tính tổng cash đã sử dụng của player trong khoảng thời gian
        /// </summary>
        public static async Task<long> GetPlayerCashSpentInPeriod(string playerName, DateTime startDate, DateTime endDate)
        {
            try
            {
                if (_freeSql == null) return 0;

                var totalSpent = await _freeSql
                    .Select<cash_shop_log>()
                    .Where(log => log.username == playerName
                                  && log.status == "SUCCESS"
                                  && log.created_at >= startDate
                                  && log.created_at <= endDate)
                    .SumAsync(log => log.price ?? 0);

                return (long)totalSpent;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to get player cash spent: {ex.Message}");
                return 0;
            }
        }

        #endregion
    }
}


