﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
	private void YuanbaoPersonalStore(byte[] data, int length)
	{
		switch (data[10])
		{
			case 1:
				{
					var array3 = new byte[BitConverter.ToUInt16(data, 11)];
					Buffer.BlockCopy(data, 13, array3, 0, array3.Length);
					var text = Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim();
					if (OpenWarehouse)
					{
						YuanbaoPersonalStoreFeaturePack(1, 11, text, null);
					}
					else if (text.Length >= 2 && text.Length <= 16)
					{
						if (Player_Level <= 20)
						{
							YuanbaoPersonalStoreFeaturePack(1, 13, text, null);
							break;
						}
						YuanbaoPersonalStoreFeaturePack(1, 1, text, null);
						OpenWarehouse = true;
						CuaHangCaNhan = new X_Nguoi_Cua_Hang_Loai();
						CuaHangCaNhan.StoreName = array3;
						CuaHangCaNhan.StoreType = 2;
					}
					else
					{
						YuanbaoPersonalStoreFeaturePack(1, 12, text, null);
					}
					break;
				}
			case 2:
				{
					var dst = new byte[4];
					var dst2 = new byte[8];
					var array = new byte[8];
					var array2 = new byte[4];
					Buffer.BlockCopy(data, 11, dst, 0, 4);
					Buffer.BlockCopy(data, 19, dst2, 0, 8);
					Buffer.BlockCopy(data, 27, array2, 0, 2);
					Buffer.BlockCopy(data, 31, array, 0, 8);
					int num = BitConverter.ToInt16(data, 29);
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0)
					{
						YuanbaoPersonalStoreFeaturePack(2, 21, null, null);
						break;
					}
					if (BitConverter.ToInt32(array2, 0) <= 0)
					{
						YuanbaoPersonalStoreFeaturePack(2, 22, null, null);
						break;
					}
					if (BitConverter.ToInt32(array2, 0) > 9999)
					{
						YuanbaoPersonalStoreFeaturePack(2, 23, null, null);
						break;
					}
					if (BitConverter.ToInt32(array, 0) <= 0)
					{
						YuanbaoPersonalStoreFeaturePack(2, 24, null, null);
						break;
					}
					if (BitConverter.ToInt32(array, 0) > 100000)
					{
						YuanbaoPersonalStoreFeaturePack(2, 25, null, null);
						break;
					}
					if (CuaHangCaNhan.StoreItemList.Count >= 8)
					{
						YuanbaoPersonalStoreFeaturePack(2, 26, null, null);
						break;
					}
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPhamSoLuong, 0) < BitConverter.ToInt32(array2, 0))
					{
						YuanbaoPersonalStoreFeaturePack(2, 27, null, null);
						break;
					}
					if (Item_In_Bag[num].VatPham_KhoaLai)
					{
						YuanbaoPersonalStoreFeaturePack(2, 27, null, null);
						break;
					}
					if (CuaHangCaNhan.StoreItemList.TryGetValue(BitConverter.ToInt64(Item_In_Bag[num].ItemGlobal_ID, 0), out var _))
					{
						YuanbaoPersonalStoreFeaturePack(2, 27, null, null);
						break;
					}
					X_Nguoi_Cua_Hang_Vat_Pham_Loai xNguoiCuaHangVatPhamLoai = new();
					xNguoiCuaHangVatPhamLoai.SoLuong = BitConverter.ToInt32(array2, 0);
					xNguoiCuaHangVatPhamLoai.Price = BitConverter.ToInt32(array, 0);
					xNguoiCuaHangVatPhamLoai.Position = num;
					xNguoiCuaHangVatPhamLoai.VatPham = Item_In_Bag[num];
					CuaHangCaNhan.StoreItemList.Add(BitConverter.ToInt64(Item_In_Bag[num].ItemGlobal_ID, 0), xNguoiCuaHangVatPhamLoai);
					YuanbaoPersonalStoreFeaturePack(2, 2, null, xNguoiCuaHangVatPhamLoai);
					break;
				}
			case 3:
				YuanbaoPersonalStoreFeaturePack(3, 3, null, null);
				PersonalStoreDisplayPackage(3, SessionID, CuaHangCaNhan.StoreName);
				CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa = true;
				break;
			case 4:
				YuanbaoPersonalStoreClosed();
				break;
			case 5:
				if (_whetherTheYuanbaoPersonalStoreIsOpen == 0)
				{
					InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore(0);
				}
				else if (_whetherTheYuanbaoPersonalStoreIsOpen == 1)
				{
					InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore(1);
				}
				break;
		}
	}

	private void YuanbaoPersonalStoreClosed()
	{
		if (CuaHangCaNhan != null)
		{
			OpenWarehouse = false;
			YuanbaoPersonalStoreFeaturePack(4, 4, null, null);
			PersonalStoreDisplayPackage(4, SessionID, null);
			if (CuaHangCaNhan.NhanVaoNguoiMua != null)
			{
				CuaHangCaNhan.NhanVaoNguoiMua.InTheShop = false;
				CuaHangCaNhan.NhanVaoNguoiMua.OpenWarehouse = false;
				CuaHangCaNhan.NhanVaoNguoiMua.InTheShopID = 0;
				CuaHangCaNhan.NhanVaoNguoiMua.EnterYuanbaoPersonalStoreFeaturePack(3, 3, SessionID, CuaHangCaNhan.NhanVaoNguoiMua.SessionID, null, null);
			}
			CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa = false;
			CuaHangCaNhan.StoreItemList.Clear();
			CuaHangCaNhan.Dispose();
			CuaHangCaNhan.NhanVaoNguoiMua = null;
			CuaHangCaNhan = null;
		}
		else
		{
			if (!InTheShop || InTheShopID == 0)
			{
				return;
			}
			var characterData = GetCharacterData(InTheShopID);
			if (characterData != null && characterData.CuaHangCaNhan != null)
			{
				if (characterData.CuaHangCaNhan.StoreType == 1)
				{
					OutOfShop(InTheShopID);
				}
				else if (characterData.CuaHangCaNhan.StoreType == 2)
				{
					YuanbaoPersonalStoreIsOut(InTheShopID);
				}
			}
		}
	}

	private void EnterYuanbaoPersonalStore(byte[] data, int length)
	{
		PacketModification(data, length);
		var num = BitConverter.ToInt32(data, 11);
		switch (data[10])
		{
			case 4:
				break;
			case 1:
				try
				{
					if (OpenWarehouse)
					{
						EnterYuanbaoPersonalStoreFeaturePack(1, 14, num, 0, null, null);
						break;
					}
					var characterData2 = GetCharacterData(num);
					if (characterData2 != null && characterData2.CuaHangCaNhan != null && characterData2.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa)
					{
						if (!FindPlayers(40, characterData2))
						{
							EnterYuanbaoPersonalStoreFeaturePack(1, 12, num, 0, null, null);
							break;
						}
						if (characterData2.CuaHangCaNhan.CuaHangCaNhan_PhaiChangDangSuDungBenTrong)
						{
							EnterYuanbaoPersonalStoreFeaturePack(1, 13, num, 0, null, null);
							break;
						}
						characterData2.CuaHangCaNhan.CuaHangCaNhan_PhaiChangDangSuDungBenTrong = true;
						characterData2.CuaHangCaNhan.NhanVaoNguoiMua = this;
						InTheShop = true;
						InTheShopID = num;
						OpenWarehouse = true;
						EnterYuanbaoPersonalStoreFeaturePack(1, 1, characterData2.SessionID, 0, characterData2.CuaHangCaNhan.StoreName, characterData2.CuaHangCaNhan.StoreItemList);
						characterData2.EnterYuanbaoPersonalStoreFeaturePack(1, 1, characterData2.SessionID, SessionID, null, null);
					}
					break;
				}
				catch (Exception ex3)
				{
					LogHelper.WriteLine(LogLevel.Error, "Vào NguyenBao CuaHang CaNhan 1 error: " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex3);
					break;
				}
			case 2:
				try
				{
					var num2 = BitConverter.ToInt32(data, 15);
					var num3 = BitConverter.ToInt64(data, 23);
					int num4 = BitConverter.ToInt16(data, 31);
					var characterData = GetCharacterData(num);
					if (characterData == null || !characterData.OpenWarehouse || !characterData.CuaHangCaNhan.StoreItemList.TryGetValue(num3, out var value))
					{
						break;
					}
					var parcelVacancy = GetParcelVacancy(this);
					if (parcelVacancy == -1)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 24, num, 0, null, null);
						break;
					}
					if (num4 > value.VatPham.GetVatPhamSoLuong)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 21, num, 0, null, null);
						break;
					}
					if (BitConverter.ToInt32(characterData.Item_In_Bag[value.Position].VatPham_ID, 0) == 0)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 21, num, 0, null, null);
						break;
					}
					if (BitConverter.ToInt64(characterData.Item_In_Bag[value.Position].ItemGlobal_ID, 0) != num3)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 21, num, 0, null, null);
						break;
					}
					if (BitConverter.ToInt32(characterData.Item_In_Bag[value.Position].VatPham_ID, 0) != num2)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 21, num, 0, null, null);
						break;
					}
					if (num3 == 0)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 21, num, 0, null, null);
						break;
					}
					if (FLD_RXPIONT < value.Price * num4)
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 22, num, 0, null, null);
						break;
					}
					var characterItemsGlobalId = GetCharacterItemsGlobal_ID(characterData, num3);
					if (characterItemsGlobalId != null)
					{
						if (BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) < value.SoLuong)
						{
							LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp NguyenBaoCuaHangCaNhan Vào cửa hàng 1: [" + AccountID + "]-[" + CharacterName + "]VatPhamTen称[" + characterItemsGlobalId.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) + "]  SoLuong[" + value.SoLuong + "]");
							break;
						}
						if (World.ItemList[BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0)].FLD_SIDE == 0 && BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) > 1)
						{
							LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp NguyenBaoCuaHangCaNhan Vào cửa hàng 2: [" + AccountID + "]-[" + CharacterName + "]VatPhamTen称[" + characterItemsGlobalId.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) + "]  SoLuong[" + value.SoLuong + "]");
							break;
						}
						LogHelper.WriteLine(LogLevel.Debug, "[" + characterData.AccountID + "][" + characterData.CharacterName + "] NguyenBaoCuaHangCaNhan买出VatPham: [" + AccountID + "][" + CharacterName + "]  (VatPham:" + characterItemsGlobalId.GetItemName() + "/编号:" + BitConverter.ToInt32(characterItemsGlobalId.DatDuocGlobal_ID(), 0) + "ThuocTinh:[" + characterItemsGlobalId.FLD_MAGIC0 + "," + characterItemsGlobalId.FLD_MAGIC1 + "," + characterItemsGlobalId.FLD_MAGIC2 + "," + characterItemsGlobalId.FLD_MAGIC3 + "," + characterItemsGlobalId.FLD_MAGIC4 + "]  Price[" + value.Price + "]");
						characterData.KiemSoatNguyenBao_SoLuong((int)(value.Price * num4), 1);
						KiemSoatNguyenBao_SoLuong((int)(value.Price * num4), 0);
						var userid = characterData.AccountID;
						var userName = characterData.CharacterName;
						var userid2 = AccountID;
						var userName2 = CharacterName;
						double num5 = BitConverter.ToInt64(characterItemsGlobalId.ItemGlobal_ID, 0);
						var num6 = BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0);
						var text = characterItemsGlobalId.GetItemName();
						var num7 = num4;
						var text2 = characterItemsGlobalId.FLD_MAGIC0 + "-" + characterItemsGlobalId.FLD_MAGIC1 + "-" + characterItemsGlobalId.FLD_MAGIC2 + "-" + characterItemsGlobalId.FLD_MAGIC3 + "-" + characterItemsGlobalId.FLD_MAGIC4 + "初" + characterItemsGlobalId.FLD_FJ_LowSoul + "中" + characterItemsGlobalId.FLD_FJ_TrungCapPhuHon + "进" + characterItemsGlobalId.FLD_FJ_TienHoa;
						var num8 = (int)(value.Price * num4);
						RxjhClass.ItemRecord(userid, userName, userid2, userName2, num5, num6, text, num7, text2, num8, "NguyenBao商店");
						SendingClass sendingClass = new();
						sendingClass.Write(2);
						sendingClass.Write(2);
						sendingClass.Write4(num);
						sendingClass.Write8(value.VatPham.GetVatPham_ID);
						sendingClass.Write8(num3);
						sendingClass.Write2(num4);
						sendingClass.Write2(value.Position);
						sendingClass.Write8(value.Price);
						sendingClass.Write4(value.VatPham.FLD_MAGIC0);
						sendingClass.Write4(value.VatPham.FLD_MAGIC1);
						sendingClass.Write4(value.VatPham.FLD_MAGIC2);
						sendingClass.Write4(value.VatPham.FLD_MAGIC3);
						sendingClass.Write4(value.VatPham.FLD_MAGIC4);
						sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC0);
						sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC1);
						sendingClass.Write2(value.VatPham.FLD_FJ_TrungCapPhuHon);
						sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC2);
						sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC3);
						sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC4);
						sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC5);
						sendingClass.Write2(0);
						sendingClass.Write4(value.VatPham.FLD_DAY1);
						sendingClass.Write4(value.VatPham.FLD_DAY2);
						sendingClass.Write2(value.VatPham.FLD_FJ_NJ);
						sendingClass.Write4(value.VatPham.FLD_FJ_LowSoul);
						sendingClass.Write2(0);
						sendingClass.Write2(value.VatPham.FLD_FJ_TienHoa);
						sendingClass.Write2(0);
						sendingClass.Write8(num3);
						//var array = sendingClass.ToArray3();
						Client?.SendPak(sendingClass, 3868, SessionID);

						characterData.Client?.SendPak(sendingClass, 3868, SessionID);
						AddItems(characterItemsGlobalId.ItemGlobal_ID, characterItemsGlobalId.VatPham_ID, parcelVacancy, BitConverter.GetBytes(num4), characterItemsGlobalId.VatPham_ThuocTinh);
						characterData.SubtractItem(characterItemsGlobalId.VatPhamViTri, num4);
						if (value.SoLuong - num4 > 0)
						{
							value.SoLuong -= num4;
						}
						else
						{
							characterData.CuaHangCaNhan.StoreItemList.Remove(num3);
						}
						Save_NguyenBaoData();
						characterData.Save_NguyenBaoData();
						if (characterData.CuaHangCaNhan.StoreItemList.Count == 0)
						{
							characterData.YuanbaoPersonalStoreClosed();
						}
					}
					else
					{
						EnterYuanbaoPersonalStoreFeaturePack(2, 21, num, 0, null, null);
					}
					break;
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "Vào NguyenBao Cua Hang Ca Nhan 2 error: " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex2);
					break;
				}
			case 3:
				try
				{
					YuanbaoPersonalStoreIsOut(num);
					break;
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Vào NguyenBao Cua Hang Ca Nhan 3 error: " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.ToString());
					break;
				}
			case 5:
				if (_whetherTheYuanbaoPersonalStoreIsOpen == 0)
				{
					InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore(0);
				}
				else if (_whetherTheYuanbaoPersonalStoreIsOpen == 1)
				{
					InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore(1);
				}
				break;
		}
	}

	private void YuanbaoPersonalStoreIsOut(int nhanVatId)
	{
		InTheShop = false;
		OpenWarehouse = false;
		InTheShopID = 0;
		EnterYuanbaoPersonalStoreFeaturePack(3, 3, nhanVatId, SessionID, null, null);
		var characterData = GetCharacterData(nhanVatId);
		if (characterData != null && characterData.CuaHangCaNhan != null)
		{
			characterData.CuaHangCaNhan.CuaHangCaNhan_PhaiChangDangSuDungBenTrong = false;
			characterData.CuaHangCaNhan.NhanVaoNguoiMua = null;
			characterData.EnterYuanbaoPersonalStoreFeaturePack(3, 3, characterData.SessionID, SessionID, null, null);
		}
	}

	private void PersonalStoreDisplayPackage(int functionId, int nhanVatId, byte[] storeName)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(1);
		sendingClass.Write4(nhanVatId);
		if (functionId == 3)
		{
			sendingClass.Write4(nhanVatId);
		}
		else
		{
			sendingClass.Write4(uint.MaxValue);
		}
		if (storeName != null)
		{
			sendingClass.Write2((byte)storeName.Length);
			sendingClass.Write(storeName, 0, storeName.Length);
		}

		Client?.SendPak(sendingClass, 2588, SessionID);
		SendCurrentRangeBroadcastData(sendingClass, 2588, SessionID);
	}

	private void YuanbaoPersonalStoreFeaturePack(int functionId, int idMieuTa, string storeName, X_Nguoi_Cua_Hang_Vat_Pham_Loai vatPham)
	{
		SendingClass sendingClass = new();
		sendingClass.Write(functionId);
		sendingClass.Write(idMieuTa);
		if (storeName != null)
		{
			sendingClass.WriteAsciiFixed(storeName);
		}
		if (vatPham != null)
		{
			sendingClass.Write8(vatPham.VatPham.GetVatPham_ID);
			sendingClass.Write8(vatPham.VatPham.GetItemGlobal_ID);
			sendingClass.Write2(vatPham.SoLuong);
			sendingClass.Write2(vatPham.Position);
			sendingClass.Write8(vatPham.Price);
			sendingClass.Write4(vatPham.VatPham.FLD_MAGIC0);
			sendingClass.Write4(vatPham.VatPham.FLD_MAGIC1);
			sendingClass.Write4(vatPham.VatPham.FLD_MAGIC2);
			sendingClass.Write4(vatPham.VatPham.FLD_MAGIC3);
			sendingClass.Write4(vatPham.VatPham.FLD_MAGIC4);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_MAGIC0);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_MAGIC1);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_TrungCapPhuHon);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_MAGIC2);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_MAGIC3);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_MAGIC4);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_MAGIC5);
			sendingClass.Write2(0);
			sendingClass.Write(vatPham.VatPham.FLD_DAY1);
			sendingClass.Write(vatPham.VatPham.FLD_DAY2);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_NJ);
			sendingClass.Write4(vatPham.VatPham.FLD_FJ_LowSoul);
			sendingClass.Write2(0);
			sendingClass.Write2(vatPham.VatPham.FLD_FJ_TienHoa);
			sendingClass.Write2(0);
		}

		Client?.SendPak(sendingClass, 3100, SessionID);
	}

	private void EnterYuanbaoPersonalStoreFeaturePack(int functionId, int idMieuTa, int nhanVatId, int nhanVatId2, byte[] storeName, ThreadSafeDictionary<long, X_Nguoi_Cua_Hang_Vat_Pham_Loai> vatPham)
	{
		SendingClass sendingClass = new();
		sendingClass.Write(functionId);
		sendingClass.Write(idMieuTa);
		sendingClass.Write4(nhanVatId);
		if (functionId != idMieuTa)
		{
			sendingClass.Write(1);
		}
		if (storeName != null)
		{
			sendingClass.Write2(storeName.Length);
			sendingClass.Write(storeName, 0, storeName.Length);
			if (vatPham != null)
			{
				sendingClass.Write2(vatPham.Count);
				foreach (var value in vatPham.Values)
				{
					sendingClass.Write8(value.VatPham.GetVatPham_ID);
					sendingClass.Write8(value.VatPham.GetItemGlobal_ID);
					sendingClass.Write2(value.SoLuong);
					sendingClass.Write2(value.Position);
					sendingClass.Write8(value.Price);
					sendingClass.Write4(value.VatPham.FLD_MAGIC0);
					sendingClass.Write4(value.VatPham.FLD_MAGIC1);
					sendingClass.Write4(value.VatPham.FLD_MAGIC2);
					sendingClass.Write4(value.VatPham.FLD_MAGIC3);
					sendingClass.Write4(value.VatPham.FLD_MAGIC4);
					sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC0);
					sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC1);
					sendingClass.Write2(value.VatPham.FLD_FJ_TrungCapPhuHon);
					sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC2);
					sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC3);
					sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC4);
					sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC5);
					sendingClass.Write2(0);
					sendingClass.Write4(value.VatPham.FLD_DAY1);
					sendingClass.Write4(value.VatPham.FLD_DAY2);
					sendingClass.Write2(value.VatPham.FLD_FJ_NJ);
					sendingClass.Write4(value.VatPham.FLD_FJ_LowSoul);
					sendingClass.Write2(0);
					sendingClass.Write2(value.VatPham.FLD_FJ_TienHoa);
				}
			}
		}
		else if (functionId == idMieuTa)
		{
			sendingClass.Write4(nhanVatId2);
		}

		Client?.SendPak(sendingClass, 3868, SessionID);
	}

	private void InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore(int id)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(id);
		Client?.SendPak(sendingClass, 12316, SessionID);
	}

	private void YuanbaoPersonalStoreInquiryPackage(int id)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(id);
		Client?.SendPak(sendingClass, 50200, SessionID);
	}

	private void YuanbaoPersonalStoreInquiryAgreement(byte[] data, int length)
	{
		YuanbaoPersonalStoreAgreementPackage(1);
	}

	private void YuanbaoPersonalStoreInquiryAgreementOpened(byte[] data, int length)
	{
		_whetherTheYuanbaoPersonalStoreIsOpen = 1;
		SendingClass sendingClass = new();
		sendingClass.Write4(1);
		sendingClass.Write4(FLD_RXPIONT);
		sendingClass.Write4(25);
		Client?.SendPak(sendingClass, 6428, SessionID);
	}

	private void YuanbaoPersonalStoreAgreementPackage(int id)
	{
		YuanbaoPersonalStoreInquiryPackage(1);
		KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
		YuanbaoPersonalStoreInquiryPackage(0);
		SendingClass sendingClass = new();
		sendingClass.Write4(id);
		sendingClass.Write4(FLD_RXPIONT);
		Client?.SendPak(sendingClass, 5660, SessionID);
	}

	public string Cc(int a)
	{
		var result = "无";
		switch (a)
		{
			case 0:
				result = "剪Dao";
				break;
			case 1:
				result = "石头";
				break;
			case 2:
				result = "布";
				break;
		}
		return result;
	}

	public string Bb(int a)
	{
		var result = "无";
		switch (a)
		{
			case 0:
				result = "Hoa局";
				break;
			case 1:
				result = "赢";
				break;
			case 2:
				result = "输";
				break;
		}
		return result;
	}
	
	public void Shop(string storeName)
	{
		try
		{
			storeName = FilterSpecial(storeName);
			var bytes = Encoding.Default.GetBytes(storeName);
			OpenWarehouse = true;
			CuaHangCaNhan = new X_Nguoi_Cua_Hang_Loai();
			CuaHangCaNhan.StoreType = 1;
			var array = Converter.HexStringToByte("AA5517000000CD00090001010500");
			var array2 = Converter.HexStringToByte("000000000000000055AA");
			var array3 = new byte[array.Length + array2.Length + bytes.Length];
			Buffer.BlockCopy(array, 0, array3, 0, array.Length);
			Buffer.BlockCopy(bytes, 0, array3, 15, bytes.Length);
			Buffer.BlockCopy(array2, 0, array3, array3.Length - array2.Length, array2.Length);
			array3[2] = (byte)(18 + bytes.Length);
			array3[9] = (byte)(4 + bytes.Length);
			array3[12] = (byte)bytes.Length;
			CuaHangCaNhan.StoreName = bytes;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
			Client?.Send_Map_Data(array3, array3.Length);
		}
		catch
		{
		}
	}

	public void Shop(byte[] data, int length)
	{
		var num = 0;
		try
		{
			if (Exiting || InTheShop)
			{
				return;
			}
			if (MapID != 101)
			{
				HeThongNhacNho("Chỉ có thể mở sạp hàng tại Huyền Bột Phái!");
				var array = Converter.HexStringToByte("AA5510000000CD0002000404000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
				return;
			}
			//if (World.ServerID != World.Kenh_Treo_Shop)
			//{
			//	HeThongNhacNho("Chỉ tại [Kênh " + World.Kenh_Treo_Shop + "] mới có thể mở sạp hàng!!");
			//	var array2 = Converter.HexStringToByte("AA5510000000CD0002000404000000000000000055AA");
			//	System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			//	Client?.Send_Map_Data(array2, array2.Length);
			//	return;
			//}
			PacketModification(data, length);
			var array3 = new byte[4];
			Buffer.BlockCopy(data, 10, array3, 0, 1);
			var num2 = BitConverter.ToInt32(array3, 0);
			num = 1;
			switch (num2)
			{
				case 1:
					{
						if (OpenWarehouse || InTheShop)
						{
							break;
						}
						int num3 = data[11];
						var array16 = new byte[num3];
						for (var i = 0; i < num3; i++)
						{
							array16[i] = data[13 + i];
						}
						if (num3 > 24)
						{
							NameReminder(3);
							var array17 = Converter.HexStringToByte("AA5510000000CD0002000404000000000000000055AA");
							Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array17, 4, 2);
							Client?.Send_Map_Data(array17, array17.Length);
						}
						else
						{
							StoreName = Encoding.Default.GetString(array16).Trim();
							Shop(StoreName);
						}
						break;
					}
				case 2:
					{
						var array8 = new byte[4];
						var dst = new byte[8];
						var array9 = new byte[8];
						var array10 = new byte[4];
						var array11 = new byte[4];
						Buffer.BlockCopy(data, 11, array8, 0, 4);
						Buffer.BlockCopy(data, 19, dst, 0, 8);
						Buffer.BlockCopy(data, 27, array10, 0, 2);
						Buffer.BlockCopy(data, 29, array11, 0, 2);
						Buffer.BlockCopy(data, 31, array9, 0, 8);
						num = 6;
						if (BitConverter.ToInt32(array10, 0) <= 0 || BitConverter.ToInt64(array9, 0) <= 0)
						{
							break;
						}
						num = 7;
						if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array11, 0)].VatPhamSoLuong, 0) < BitConverter.ToInt32(array10, 0))
						{
							var array12 = Converter.HexStringToByte("AA551100AE04CD000300021B000000000000009BE255AA");
							Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array12, 4, 2);
							Client?.Send_Map_Data(array12, array12.Length);
							break;
						}
						num = 8;
						if (Item_In_Bag[BitConverter.ToInt32(array11, 0)].VatPham_KhoaLai)
						{
							var array13 = Converter.HexStringToByte("AA551100AE04CD000300021B000000000000009BE255AA");
							Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array13, 4, 2);
							Client?.Send_Map_Data(array13, array13.Length);
						}
						else
						{
							if (!World.ItemList.TryGetValue(BitConverter.ToInt32(array8, 0), out var value))
							{
								break;
							}
							if (value.FLD_LOCK == 1)
							{
								HeThongNhacNho("Bảo vật này cấm mua bán trong giang hồ!");
								var array14 = Converter.HexStringToByte("AA551100AE04CD000300021B000000000000009BE255AA");
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array14, 4, 2);
								Client?.Send_Map_Data(array14, array14.Length);
								break;
							}
							num = 9;
							if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array11, 0)].VatPham_ID, 0) == 0)
							{
								var array15 = Converter.HexStringToByte("AA551100AE04CD000300021B000000000000009BE255AA");
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array15, 4, 2);
								Client?.Send_Map_Data(array15, array15.Length);
								break;
							}
							num = 10;
							Item_In_Bag[BitConverter.ToInt32(array11, 0)].Lock_Move = true;
							num = 11;
							X_Nguoi_Cua_Hang_Vat_Pham_Loai xNguoiCuaHangVatPhamLoai = new();
							xNguoiCuaHangVatPhamLoai.SoLuong = BitConverter.ToInt32(array10, 0);
							xNguoiCuaHangVatPhamLoai.Price = BitConverter.ToInt64(array9, 0);
							xNguoiCuaHangVatPhamLoai.Position = BitConverter.ToInt32(array11, 0);
							num = 12;
							xNguoiCuaHangVatPhamLoai.VatPham = Item_In_Bag[BitConverter.ToInt32(array11, 0)];
							num = 13;
							if (CuaHangCaNhan != null && CuaHangCaNhan.StoreItemList != null)
							{
								CuaHangCaNhan.StoreItemList.Add(BitConverter.ToInt64(Item_In_Bag[BitConverter.ToInt32(array11, 0)].ItemGlobal_ID, 0), xNguoiCuaHangVatPhamLoai);
								num = 14;
								SendingClass sendingClass = new();
								sendingClass.Write(2);
								sendingClass.Write(2);
								sendingClass.Write8(xNguoiCuaHangVatPhamLoai.VatPham.GetVatPham_ID);
								sendingClass.Write8(xNguoiCuaHangVatPhamLoai.VatPham.GetItemGlobal_ID);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.SoLuong);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.Position);
								sendingClass.Write8(xNguoiCuaHangVatPhamLoai.Price);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_MAGIC0);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_MAGIC1);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_MAGIC2);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_MAGIC3);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_MAGIC4);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_MAGIC0);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_MAGIC1);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_TrungCapPhuHon);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_MAGIC2);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_MAGIC3);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_MAGIC4);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_MAGIC5);
								sendingClass.Write2(0);
								num = 15;
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_DAY1);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_DAY2);
								num = 16;
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_NJ);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_LowSoul);
								sendingClass.Write2(0);
								sendingClass.Write2(xNguoiCuaHangVatPhamLoai.VatPham.FLD_FJ_TienHoa);
								sendingClass.Write2(0);
								sendingClass.Write4(xNguoiCuaHangVatPhamLoai.VatPham.FLD_TuLinh);
								sendingClass.Write4(0);
								//var text = Converter.ToString(sendingClass.ToArray3());
								Client?.SendPak(sendingClass, 52480, SessionID);
								num = 17;
								if (Is_ItASpiritBeast((int)xNguoiCuaHangVatPhamLoai.VatPham.GetVatPham_ID))
								{
									SendSpiritBeastData((int)xNguoiCuaHangVatPhamLoai.VatPham.GetItemGlobal_ID);
								}
							}
						}
						break;
					}
				case 3:
					{
						CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa = true;
						num = 18;
						var array4 = Converter.HexStringToByte("AA5510000000CD0002000303000000000000000055AA");
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
						Client?.Send_Map_Data(array4, array4.Length);
						num = 19;
						var array5 = Converter.HexStringToByte("AA5522000000CA0014000100000000000000000000000600");
						var array6 = Converter.HexStringToByte("000000000000000055AA");
						num = 20;
						var array7 = new byte[array5.Length + array6.Length + CuaHangCaNhan.StoreName.Length];
						num = 21;
						Buffer.BlockCopy(array5, 0, array7, 0, array5.Length);
						Buffer.BlockCopy(CuaHangCaNhan.StoreName, 0, array7, 24, CuaHangCaNhan.StoreName.Length);
						Buffer.BlockCopy(array6, 0, array7, array7.Length - array6.Length, array6.Length);
						num = 22;
						array7[2] = (byte)(28 + CuaHangCaNhan.StoreName.Length);
						array7[9] = (byte)(14 + CuaHangCaNhan.StoreName.Length);
						array7[22] = (byte)CuaHangCaNhan.StoreName.Length;
						num = 23;
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 14, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 18, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 4, 2);
						CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa = true;
						Client?.Send_Map_Data(array7, array7.Length);
						num = 24;
						SendCurrentRangeBroadcastData(array7, array7.Length);
						break;
					}
				case 4:
					num = 26;
					CloseShop();
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "CuaHangCaNhan Cửa hàng error![" + AccountID + "]-[" + CharacterName + "]" + num + "|" + ex.Message);
		}
	}

	public void IntoTheStore(Players playe)
	{
		try
		{
			playe.CuaHangCaNhan.CuaHangCaNhan_PhaiChangDangSuDungBenTrong = true;
			InTheShop = true;
			InTheShopID = playe.SessionID;
			OpenWarehouse = true;
			SendingClass sendingClass = new();
			sendingClass.Write(1);
			sendingClass.Write(1);
			sendingClass.Write4(playe.SessionID);
			sendingClass.Write2(playe.CuaHangCaNhan.StoreName.Length);
			sendingClass.Write(playe.CuaHangCaNhan.StoreName, 0, playe.CuaHangCaNhan.StoreName.Length);
			sendingClass.Write2(playe.CuaHangCaNhan.StoreItemList.Count);
			foreach (var value in playe.CuaHangCaNhan.StoreItemList.Values)
			{
				sendingClass.Write8(value.VatPham.GetVatPham_ID);
				sendingClass.Write8(value.VatPham.GetItemGlobal_ID);
				sendingClass.Write2(value.SoLuong);
				sendingClass.Write2(value.Position);
				sendingClass.Write8(value.Price);
				sendingClass.Write4(value.VatPham.FLD_MAGIC0);
				sendingClass.Write4(value.VatPham.FLD_MAGIC1);
				sendingClass.Write4(value.VatPham.FLD_MAGIC2);
				sendingClass.Write4(value.VatPham.FLD_MAGIC3);
				sendingClass.Write4(value.VatPham.FLD_MAGIC4);
				sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC0);
				sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC1);
				sendingClass.Write2(value.VatPham.FLD_FJ_TrungCapPhuHon);
				sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC2);
				sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC3);
				sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC4);
				sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC5);
				sendingClass.Write2(0);
				sendingClass.Write4(value.VatPham.FLD_DAY1);
				sendingClass.Write4(value.VatPham.FLD_DAY2);
				sendingClass.Write2(value.VatPham.FLD_FJ_NJ);
				sendingClass.Write4(value.VatPham.FLD_FJ_LowSoul);
				sendingClass.Write2(0);
				sendingClass.Write2(value.VatPham.FLD_FJ_TienHoa);
				sendingClass.Write2(0);
				sendingClass.Write4(value.VatPham.FLD_TuLinh);
				sendingClass.Write8((int)value.VatPham.GetItemGlobal_ID);
				//var text = Converter.ToString(sendingClass.ToArray3());
				if (Is_ItASpiritBeast((int)value.VatPham.GetVatPham_ID))
				{
					SendSpiritBeastData((int)value.VatPham.GetItemGlobal_ID);
				}
			}

			Client?.SendPak(sendingClass, 52992, SessionID);
			var array = Converter.HexStringToByte("AA5516006501591008000100000000000000000000000000B11A55AA");
			Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
			var array2 = Converter.HexStringToByte("AA5518000000CF000A0001010000000001000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array2, 12, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 16, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			playe.Client?.Send_Map_Data(array2, array2.Length);
			playe.CuaHangCaNhan.NhanVaoNguoiMua = this;
		}
		catch
		{
		}
	}

	public void StoreClosingPrompt(int nhanVatId)
	{
		var array = Converter.HexStringToByte("AA5514000000CF000600030319000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(nhanVatId), 0, array, 12, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void IntoTheStore(byte[] data, int length)
	{
		try
		{
			PacketModification(data, length);
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(data, 10, array, 0, 1);
			Buffer.BlockCopy(data, 11, array2, 0, 4);
			var num = BitConverter.ToInt32(array, 0);
			var num2 = BitConverter.ToInt32(array2, 0);
			switch (num)
			{
				case 1:
					{
						if (OpenWarehouse || InTheShop)
						{
							break;
						}
						var characterData2 = GetCharacterData(num2);
						if (characterData2 == null)
						{
							break;
						}
						if (characterData2.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa && characterData2.CuaHangCaNhan.StoreType != 2)
						{
							if (!FindPlayers(40, characterData2))
							{
								StoreTips(12);
								StoreClosingPrompt(num2);
								break;
							}
							if (characterData2.CuaHangCaNhan.CuaHangCaNhan_PhaiChangDangSuDungBenTrong)
							{
								StoreTips(13);
								StoreClosingPrompt(num2);
								break;
							}
							var num7 = 25;
							if (Player_Level >= num7)
							{
								IntoTheStore(characterData2);
								InTheShopID = characterData2.SessionID;
							}
							else
							{
								StoreClosingPrompt(num2);
								HeThongNhacNho("Đạt cấp độ [" + num7 + "] mới có thể bước vào cửa hàng!!");
							}
						}
						else
						{
							StoreTips(14);
							StoreClosingPrompt(num2);
						}
						break;
					}
				case 2:
					{
						if (InTheShopID == 0)
						{
							break;
						}
						if (num2 == 0)
						{
							OutOfShop(InTheShopID);
							break;
						}
						if (InTheShopID != num2)
						{
							OutOfShop(InTheShopID);
							break;
						}
						if (NguyenBao_TaiKhoanTrangThai)
						{
							OutOfShop(InTheShopID);
							HeThongNhacNho("Điểm số bị khóa trong trạng thái truyền âm!");
							break;
						}
						var characterData = GetCharacterData(num2);
						if (characterData == null)
						{
							OutOfShop(InTheShopID);
							break;
						}
						if (!characterData.OpenWarehouse)
						{
							OutOfShop(InTheShopID);
							break;
						}
						if (characterData.Exiting)
						{
							OutOfShop(InTheShopID);
							break;
						}
						var num3 = BitConverter.ToInt32(data, 15);
						var num4 = BitConverter.ToInt64(data, 23);
						int num5 = BitConverter.ToInt16(data, 31);
						if (!characterData.CuaHangCaNhan.StoreItemList.TryGetValue(num4, out var value))
						{
							OutOfShop(InTheShopID);
							break;
						}
						var parcelVacancy = GetParcelVacancy(this);
						if (parcelVacancy == -1)
						{
							PurchaseItemReminder(14);
							OutOfShop(InTheShopID);
						}
						else if (num5 > value.SoLuong)
						{
							OutOfShop(InTheShopID);
						}
						else if (BitConverter.ToInt32(characterData.Item_In_Bag[value.Position].VatPham_ID, 0) == 0)
						{
							OutOfShop(InTheShopID);
						}
						else if (BitConverter.ToInt64(characterData.Item_In_Bag[value.Position].ItemGlobal_ID, 0) != num4)
						{
							OutOfShop(InTheShopID);
						}
						else if (BitConverter.ToInt32(characterData.Item_In_Bag[value.Position].VatPham_ID, 0) != num3)
						{
							OutOfShop(InTheShopID);
						}
						else if (num4 == 0)
						{
							OutOfShop(InTheShopID);
						}
						else if (value.Price >= 0 && num5 >= 1)
						{
							if (characterData.CuaHangCaNhan.StoreType == 2)
							{
								OutOfShop(InTheShopID);
								break;
							}
							if (Player_Money < value.Price * num5)
							{
								PurchaseItemReminder(13);
								OutOfShop(InTheShopID);
								break;
							}
							var characterItemsGlobalId = GetCharacterItemsGlobal_ID(characterData, num4);
							var getVatPhamId = characterData.Item_In_Bag[characterItemsGlobalId.VatPhamViTri].GetVatPham_ID;
							var getItemGlobalId = characterData.Item_In_Bag[characterItemsGlobalId.VatPhamViTri].GetItemGlobal_ID;
							if (getVatPhamId != characterItemsGlobalId.GetVatPham_ID || getItemGlobalId != characterItemsGlobalId.GetItemGlobal_ID)
							{
								characterData.HeThongNhacNho("Đại hiệp hãy ngừng ngay hành vi này, Thiên Cơ Các đã phát hiện và ghi lại dấu tích!!", 6, "Cảnh Báo Các");
								HeThongNhacNho("Hiệp khách [" + characterData.CharacterName + "] có hành vi sao chép bảo vật. Mau báo cho Võ Lâm Minh Chủ!!", 6, "Cảnh Báo Các");
								var txt = "[" + AccountID + "] [" + CharacterName + "]" + getVatPhamId + "] [" + getItemGlobalId + "]";
								// logo.Log_Bug_Item_Treo_Shop(txt);
								OutOfShop(InTheShopID);
								break;
							}
							if (characterItemsGlobalId != null)
							{
								if (BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) < value.SoLuong)
								{
									OutOfShop(InTheShopID);
									LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp CuaHangCaNhan Vào cửa hàng 11 [" + AccountID + "]-[" + CharacterName + "]VatPhamTen称[" + characterItemsGlobalId.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) + "]  SoLuong[" + value.SoLuong + "]");
									break;
								}
								if (World.ItemList[BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0)].FLD_SIDE == 0 && BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) > 1)
								{
									OutOfShop(InTheShopID);
									LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp CuaHangCaNhan Vào cửa hàng 22 [" + AccountID + "]-[" + CharacterName + "]VatPhamTen称[" + characterItemsGlobalId.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(characterItemsGlobalId.VatPhamSoLuong, 0) + "]  SoLuong[" + value.SoLuong + "]");
									break;
								}
								if (num5 < 1 || value.Price < 0)
								{
									OutOfShop(InTheShopID);
									break;
								}
								var array3 = new string[25]
								{
							"[",
							characterData.AccountID,
							"][",
							characterData.CharacterName,
							"]  Shop ban dc Vat Pham  :[",
							AccountID,
							"][",
							CharacterName,
							"]  (VatPham:",
							characterItemsGlobalId.GetItemName(),
							"/编号:",
							BitConverter.ToInt32(characterItemsGlobalId.DatDuocGlobal_ID(), 0).ToString(),
							"ThuocTinh:[",
							characterItemsGlobalId.FLD_MAGIC0.ToString(),
							",",
							characterItemsGlobalId.FLD_MAGIC1.ToString(),
							",",
							characterItemsGlobalId.FLD_MAGIC2.ToString(),
							",",
							null,
							null,
							null,
							null,
							null,
							null
								};
								array3[19] = characterItemsGlobalId.FLD_MAGIC3.ToString();
								array3[20] = ",";
								array3[21] = characterItemsGlobalId.FLD_MAGIC4.ToString();
								array3[22] = "]  Price[";
								array3[23] = value.Price.ToString();
								array3[24] = "]";
								LogHelper.WriteLine(LogLevel.Debug, string.Concat(array3));
								var num6 = value.Price * num5;
								characterData.Player_Money += num6;
								Player_Money -= num6;
								var userid = characterData.AccountID;
								var userName = characterData.CharacterName;
								var userid2 = AccountID;
								var userName2 = CharacterName;
								double txt2 = BitConverter.ToInt64(characterItemsGlobalId.ItemGlobal_ID, 0);
								var txt3 = BitConverter.ToInt32(characterItemsGlobalId.VatPham_ID, 0);
								var txt4 = characterItemsGlobalId.GetItemName();
								var txt5 = num5;
								var txt6 = characterItemsGlobalId.FLD_MAGIC0 + "-" + characterItemsGlobalId.FLD_MAGIC1 + "-" + characterItemsGlobalId.FLD_MAGIC2 + "-" + characterItemsGlobalId.FLD_MAGIC3 + "-" + characterItemsGlobalId.FLD_MAGIC4 + "初" + 0 + "中" + 0 + "进" + 0;
								var txt7 = (int)(value.Price * num5);
								if (World.CoHayKo_MoRa_Log_TreoShop != 0)
								{
									// logo.Log_Treo_Shop(userid, userName, userid2, userName2, txt2, txt3, txt4, txt5, txt6, txt7);
								}
								RxjhClass.ItemRecord(userid, userName, userid2, userName2, txt2, txt3, txt4, txt5, txt6, txt7, "NguyenBao商店");
								SendingClass sendingClass = new();
								sendingClass.Write(2);
								sendingClass.Write(2);
								sendingClass.Write4(num2);
								sendingClass.Write8(value.VatPham.GetVatPham_ID);
								sendingClass.Write8(value.VatPham.GetItemGlobal_ID);
								sendingClass.Write2(num5);
								sendingClass.Write2(value.Position);
								sendingClass.Write8(value.Price);
								sendingClass.Write4(value.VatPham.FLD_MAGIC0);
								sendingClass.Write4(value.VatPham.FLD_MAGIC1);
								sendingClass.Write4(value.VatPham.FLD_MAGIC2);
								sendingClass.Write4(value.VatPham.FLD_MAGIC3);
								sendingClass.Write4(value.VatPham.FLD_MAGIC4);
								sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC0);
								sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC1);
								sendingClass.Write2(value.VatPham.FLD_FJ_TrungCapPhuHon);
								sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC2);
								sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC3);
								sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC4);
								sendingClass.Write2(value.VatPham.FLD_FJ_MAGIC5);
								sendingClass.Write2(0);
								sendingClass.Write4(value.VatPham.FLD_DAY1);
								sendingClass.Write4(value.VatPham.FLD_DAY2);
								sendingClass.Write2(value.VatPham.FLD_FJ_NJ);
								sendingClass.Write4(value.VatPham.FLD_FJ_LowSoul);
								sendingClass.Write2(0);
								sendingClass.Write2(value.VatPham.FLD_FJ_TienHoa);
								sendingClass.Write2(0);
								sendingClass.Write4(0);
								sendingClass.Write4(0);
								sendingClass.Write4(0);
								sendingClass.Write8(num4);
								Client?.SendPak(sendingClass, 52992, SessionID);

								characterData.Client?.SendPak(sendingClass, 52992, SessionID);
								characterItemsGlobalId.FLD_FJ_NJ = 0;
								AddItems(characterItemsGlobalId.ItemGlobal_ID, characterItemsGlobalId.VatPham_ID, parcelVacancy, BitConverter.GetBytes(num5), characterItemsGlobalId.VatPham_ThuocTinh);
								characterData.SubtractItem(characterItemsGlobalId.VatPhamViTri, num5);
								if (value.SoLuong - num5 > 0)
								{
									value.SoLuong -= num5;
								}
								else
								{
									characterData.CuaHangCaNhan.StoreItemList.Remove(num4);
								}
								characterData.check_bug_gold_tang_bat_thuong = true;
								check_bug_gold_tang_bat_thuong = true;
								characterData.UpdateMoneyAndWeight();
								UpdateMoneyAndWeight();
							}
							if (characterData.CuaHangCaNhan.StoreItemList.Count == 0)
							{
								characterData.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa = false;
								characterData.CuaHangCaNhan.StoreItemList.Clear();
								characterData.CuaHangCaNhan.NhanVaoNguoiMua = null;
								characterData.CuaHangCaNhan.Dispose();
								characterData.CuaHangCaNhan = null;
								characterData.OpenWarehouse = false;
								var array4 = Converter.HexStringToByte("AA5514000000CF000600030319000000000000000000000055AA");
								Buffer.BlockCopy(array2, 0, array4, 12, 4);
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
								Client?.Send_Map_Data(array4, array4.Length);
								var array5 = Converter.HexStringToByte("AA5510000000CD0002000404000000000000000055AA");
								Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array5, 4, 2);
								characterData.Client?.Send_Map_Data(array5, array5.Length);
								var array6 = Converter.HexStringToByte("AA551A000000CA000C000100000000000000FFFFFFFF000000000000000055AA");
								Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array6, 14, 2);
								Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array6, 4, 2);
								characterData.Client?.Send_Map_Data(array6, array6.Length);
								characterData.SendCurrentRangeBroadcastData(array6, array6.Length);
								InTheShop = false;
								InTheShopID = 0;
								OpenWarehouse = false;
							}
							else
							{
								characterData.SaveCharacterDataAsync().GetAwaiter().GetResult();
							}
							SaveCharacterDataAsync().GetAwaiter().GetResult();
						}
						else
						{
							OutOfShop(InTheShopID);
						}
						break;
					}
				case 3:
					OutOfShop(num2);
					break;
			}
		}
		catch (Exception ex)
		{
			OutOfShop(InTheShopID);
			LogHelper.WriteLine(LogLevel.Error, "CuaHangCaNhan Vào cửa hàng error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void CloseShop()
	{
		var num = 0;
		try
		{
			var array = Converter.HexStringToByte("AA5510000000CD0002000404000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
			num = 1;
			if (CuaHangCaNhan.NhanVaoNguoiMua != null)
			{
				num = 2;
				var array2 = Converter.HexStringToByte("AA5514000000CF000600030319000000000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 12, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(CuaHangCaNhan.NhanVaoNguoiMua.SessionID), 0, array2, 4, 2);
				CuaHangCaNhan.NhanVaoNguoiMua.InTheShop = false;
				num = 3;
				CuaHangCaNhan.NhanVaoNguoiMua.OpenWarehouse = false;
				num = 4;
				CuaHangCaNhan.NhanVaoNguoiMua.InTheShopID = 0;
				num = 5;
				CuaHangCaNhan.NhanVaoNguoiMua.Client?.Send_Map_Data(array2, array2.Length);
				num = 6;
			}
			OpenWarehouse = false;
			if (CuaHangCaNhan != null)
			{
				CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa = false;
				num = 7;
				CuaHangCaNhan.StoreItemList.Clear();
				num = 8;
				CuaHangCaNhan.Dispose();
				num = 9;
				CuaHangCaNhan.NhanVaoNguoiMua = null;
				CuaHangCaNhan = null;
			}
			num = 10;
			var array3 = Converter.HexStringToByte("AA551A000000CA000C000100000000000000FFFFFFFF000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
			Client?.Send_Map_Data(array3, array3.Length);
			num = 11;
			SendCurrentRangeBroadcastData(array3, array3.Length);
			HeThong_HopThanh_MoKhoa();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "CuaHangCaNhan Đóng cửa hàng error![" + AccountID + "]-[" + CharacterName + "]" + num + "|" + ex.Message);
		}
	}

	public void OutOfShop(int nhanVatId)
	{
		var array = Converter.HexStringToByte("AA5514000000CF000600030319000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(nhanVatId), 0, array, 12, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		InTheShop = false;
		OpenWarehouse = false;
		InTheShopID = 0;
		var characterData = GetCharacterData(nhanVatId);
		if (characterData != null)
		{
			characterData.CuaHangCaNhan.CuaHangCaNhan_PhaiChangDangSuDungBenTrong = false;
			characterData.CuaHangCaNhan.NhanVaoNguoiMua = null;
			var array2 = Converter.HexStringToByte("AA5518000000CF000A0003030000000001000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array2, 12, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array2, 4, 2);
			characterData.Client?.Send_Map_Data(array2, array2.Length);
		}
	}

}
