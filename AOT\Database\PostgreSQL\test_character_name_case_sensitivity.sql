-- Test script for character name case sensitivity
-- Script test cho tính năng phân biệt chữ hoa chữ thường của tên nhân vật

-- ========================================
-- PART 1: SETUP TEST DATA - Thiết lập dữ liệu test
-- ========================================

SELECT '=== SETUP TEST DATA - THIẾT LẬP DỮ LIỆU TEST ===' as step;

-- Clean up any existing test data
DELETE FROM tbl_xwwl_char WHERE fld_id LIKE 'test_case_%';

-- Insert test character
INSERT INTO tbl_xwwl_char (
    fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
    fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
    fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
    fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
    fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
    fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
    fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
    fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
    fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
    nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel, fld_xb
) VALUES (
    'test_case_001', 'TestCharacter', 0, 1, 1, '0', 1, 0,
    418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
    '10000', '0', 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0.0, 0, 0, 0, 0, 0, '', '', '', '', 0,
    0, 0, 0, 'd1', '', 0, 0, 0, 0, '', 0,
    0, 0, 0, 0, 0, '', 1, false, '', 0, '', 0, 0
);

SELECT 'Test character inserted:' as info;
SELECT id, fld_name, fld_id FROM tbl_xwwl_char WHERE fld_id = 'test_case_001';

-- ========================================
-- PART 2: TEST CASE-INSENSITIVE CONSTRAINT - Test ràng buộc không phân biệt chữ hoa thường
-- ========================================

SELECT '=== TEST CASE-INSENSITIVE CONSTRAINT - TEST RÀNG BUỘC ===' as step;

-- Test 1: Try to insert same name with different case (should fail)
SELECT 'Test 1: Attempting to insert "testcharacter" (should fail)...' as info;

DO $$
BEGIN
    BEGIN
        INSERT INTO tbl_xwwl_char (
            fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
            fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
            fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
            fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
            fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
            fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
            fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
            fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
            fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
            nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel, fld_xb
        ) VALUES (
            'test_case_002', 'testcharacter', 0, 1, 1, '0', 1, 0,
            418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
            '10000', '0', 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0.0, 0, 0, 0, 0, 0, '', '', '', '', 0,
            0, 0, 0, 'd1', '', 0, 0, 0, 0, '', 0,
            0, 0, 0, 0, 0, '', 1, false, '', 0, '', 0, 0
        );
        RAISE NOTICE 'ERROR: Insert should have failed but succeeded!';
    EXCEPTION
        WHEN unique_violation THEN
            RAISE NOTICE 'SUCCESS: Insert correctly failed with unique constraint violation';
        WHEN OTHERS THEN
            RAISE NOTICE 'UNEXPECTED ERROR: %', SQLERRM;
    END;
END $$;

-- Test 2: Try to insert same name with mixed case (should fail)
SELECT 'Test 2: Attempting to insert "TESTCHARACTER" (should fail)...' as info;

DO $$
BEGIN
    BEGIN
        INSERT INTO tbl_xwwl_char (
            fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
            fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
            fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
            fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
            fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
            fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
            fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
            fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
            fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
            nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel, fld_xb
        ) VALUES (
            'test_case_003', 'TESTCHARACTER', 0, 1, 1, '0', 1, 0,
            418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
            '10000', '0', 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0.0, 0, 0, 0, 0, 0, '', '', '', '', 0,
            0, 0, 0, 'd1', '', 0, 0, 0, 0, '', 0,
            0, 0, 0, 0, 0, '', 1, false, '', 0, '', 0, 0
        );
        RAISE NOTICE 'ERROR: Insert should have failed but succeeded!';
    EXCEPTION
        WHEN unique_violation THEN
            RAISE NOTICE 'SUCCESS: Insert correctly failed with unique constraint violation';
        WHEN OTHERS THEN
            RAISE NOTICE 'UNEXPECTED ERROR: %', SQLERRM;
    END;
END $$;

-- Test 3: Try to insert different name (should succeed)
SELECT 'Test 3: Attempting to insert "DifferentName" (should succeed)...' as info;

INSERT INTO tbl_xwwl_char (
    fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
    fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
    fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
    fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
    fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
    fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
    fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
    fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
    fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
    nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel, fld_xb
) VALUES (
    'test_case_004', 'DifferentName', 0, 1, 1, '0', 1, 0,
    418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
    '10000', '0', 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0.0, 0, 0, 0, 0, 0, '', '', '', '', 0,
    0, 0, 0, 'd1', '', 0, 0, 0, 0, '', 0,
    0, 0, 0, 0, 0, '', 1, false, '', 0, '', 0, 0
);

SELECT 'SUCCESS: Different name inserted successfully' as result;

-- ========================================
-- PART 3: VERIFICATION - Xác minh
-- ========================================

SELECT '=== VERIFICATION - XÁC MINH ===' as step;

-- Show all test characters
SELECT 'All test characters:' as info;
SELECT id, fld_name, fld_id FROM tbl_xwwl_char WHERE fld_id LIKE 'test_case_%' ORDER BY id;

-- Test case-insensitive search
SELECT 'Case-insensitive search test:' as info;
SELECT 
    'Searching for "testcharacter" (lowercase):' as search_type,
    COUNT(*) as found_count
FROM tbl_xwwl_char 
WHERE LOWER(fld_name) = LOWER('testcharacter');

SELECT 
    'Searching for "TESTCHARACTER" (uppercase):' as search_type,
    COUNT(*) as found_count
FROM tbl_xwwl_char 
WHERE LOWER(fld_name) = LOWER('TESTCHARACTER');

-- ========================================
-- PART 4: CLEANUP - Dọn dẹp
-- ========================================

SELECT '=== CLEANUP - DỌN DẸP ===' as step;

-- Clean up test data
DELETE FROM tbl_xwwl_char WHERE fld_id LIKE 'test_case_%';

SELECT 'Test data cleaned up successfully' as result;

SELECT '=== TEST COMPLETED SUCCESSFULLY - TEST HOÀN TẤT ===' as final_result;
