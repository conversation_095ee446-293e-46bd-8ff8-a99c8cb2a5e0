using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Net.Sockets;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Networking.Network;
using HeroYulgang.Core.Networking.Utils;
using HeroYulgang.Core.Managers;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Networking.Core
{
    /// <summary>
    /// Actor quản lý kết nối TCP
    /// </summary>
    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)]
    public class TcpManagerActor : ReceiveActor
    {
        private readonly ConfigManager _configManager;
        private readonly Dictionary<IActorRef, ClientSession> _sessions = [];
        private bool _isRunning = false;
        private bool _isStarting = false; // Thêm flag để tránh start nhiều lần
        private ICancelable _inactiveConnectionsChecker;

        public TcpManagerActor()
        {
            _configManager = ConfigManager.Instance;

            // <PERSON><PERSON><PERSON> nghĩa các message handler
            Receive<StartServer>(msg => StartServer());
            Receive<StopServer>(msg => StopServer());
            Receive<Tcp.Bound>(msg => HandleBound(msg));
            Receive<Tcp.CommandFailed>(msg => HandleCommandFailed(msg));
            Receive<Tcp.Connected>(msg => HandleConnected(msg));
            Receive<Tcp.ConnectionClosed>(msg => HandleConnectionClosed(msg));
            Receive<Tcp.Received>(msg => HandleReceivedDirectly(msg));
            Receive<CheckInactiveConnections>(msg => CheckInactiveConnections());
            Receive<SendPacket>(msg => SendPacket(msg));
            Receive<BroadcastPacket>(msg => BroadcastPacket(msg));
            Receive<CloseConnection>(msg => HandleCloseConnection(msg));
            Receive<GetSessionCount>(msg => Sender.Tell(new SessionCountResponse(_sessions.Count)));
            Receive<GetAllSessions>(msg => Sender.Tell(new AllSessionsResponse(new List<ClientSession>(_sessions.Values))));
            Receive<GetSessionIdFromConnection>(msg => HandleGetSessionIdFromConnection(msg));
        }

        protected override void PreStart()
        {
            // Tự động khởi động server khi actor được tạo
            Self.Tell(new StartServer());
        }

        protected override void PostStop()
        {
            // Dừng server khi actor bị dừng
            StopServer();
        }

        private void StartServer()
        {
            if (_isRunning)
            {
                Logger.Instance.Warning("Máy chủ mạng đã đang chạy");
                return;
            }

            if (_isStarting)
            {
                Logger.Instance.Warning("Máy chủ mạng đang trong quá trình khởi động, bỏ qua yêu cầu khởi động thứ 2");
                return;
            }

            _isStarting = true; // Đánh dấu đang khởi động

            try
            {
                int port = _configManager.ServerSettings.GameServerPort;
                Logger.Instance.Info($"Đang khởi động máy chủ mạng trên cổng {port}...");

                // Lấy tham chiếu đến Tcp manager của Akka.IO
                var tcpManager = Context.System.Tcp();

                // Bind vào cổng được chỉ định
                tcpManager.Tell(new Tcp.Bind(Self, new IPEndPoint(IPAddress.Any, port)));

                // Chỉ đánh dấu _isRunning = true khi bind thành công (trong HandleBound)
                // Bắt đầu task kiểm tra các kết nối không hoạt động sẽ được gọi trong HandleBound
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động máy chủ mạng: {ex.Message}");
                _isRunning = false;
                _isStarting = false; // Reset flag khi có exception
            }
        }

        private void StopServer()
        {
            if (!_isRunning)
            {
                Logger.Instance.Warning("Máy chủ mạng không chạy");
                return;
            }

            try
            {
                // Hủy task kiểm tra kết nối không hoạt động
                _inactiveConnectionsChecker?.Cancel();

                // Đóng tất cả các kết nối
                foreach (var session in _sessions)
                {
                    session.Key.Tell(Tcp.Close.Instance);
                }

                _sessions.Clear();
                _isRunning = false;
                Logger.Instance.Info("Máy chủ mạng đã dừng");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng máy chủ mạng: {ex.Message}");
            }
        }

        private void HandleConnected(Tcp.Connected connected)
        {
            // Lấy thông tin về IP và port của server
            int serverPort = _configManager.ServerSettings.GameServerPort;
            string serverIp = GetLocalIPAddress();

            // Logger.Instance.Info($"Client kết nối: {connected.RemoteAddress} tới máy chủ {serverIp}:{serverPort}");

            // Chấp nhận kết nối
            Sender.Tell(new Tcp.Register(Self));

            // Tạo session mới sử dụng ConnectionIdManager (không phải PlayerSessionID)
            int connectionId = ConnectionIdManager.Instance.AllocateConnectionId();

            var clientSession = new ClientSession(connectionId, connected.RemoteAddress, Sender);

            // Lưu session
            _sessions[Sender] = clientSession;

            // Tạo ClientConnection duy nhất để xử lý cả kết nối và packet
            // ConnectionID giờ luôn duy nhất (không tái sử dụng) nên tên actor sẽ không bị trùng
            Context.ActorOf(Props.Create(() => new ClientConnection(Sender, clientSession)), $"client-{connectionId}");

            // Logger.Instance.Info($"Client kết nối: {connected.RemoteAddress} (ConnectionID: {connectionId}) tới máy chủ {serverIp}:{serverPort}");
        }

        private static string GetLocalIPAddress()
        {
            string localIP = "127.0.0.1";
            try
            {
                // Lấy địa chỉ IP của máy chủ
                using Socket socket = new(AddressFamily.InterNetwork, SocketType.Dgram, 0);
                socket.Connect("*******", 65530);
                if (socket.LocalEndPoint is System.Net.IPEndPoint endPoint)
                {
                    localIP = endPoint.Address.ToString();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"Không thể lấy địa chỉ IP: {ex.Message}. Sử dụng 127.0.0.1");
            }
            return localIP;
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed closed)
        {
            if (_sessions.TryGetValue(Sender, out var session))
            {
                // Logger.Instance.Info($"Client ngắt kết nối: {session.RemoteEndPoint} (ConnectionID: {session.ConnectionId})");

                // Thực hiện cleanup toàn diện
                CleanupSession(session);

                // Xóa session khỏi dictionary
                _sessions.Remove(Sender);
            }
        }

        /// <summary>
        /// Cleanup toàn diện cho session khi ngắt kết nối
        /// </summary>
        private void CleanupSession(ClientSession session)
        {
            try
            {
                // 1. Giải phóng ConnectionID
                ConnectionIdManager.Instance.ReleaseConnectionId(session.ConnectionId);
                // Logger.Instance.Debug($"Đã giải phóng ConnectionID: {session.ConnectionId}");

                // 2. Nếu có PlayerSessionID, thực hiện cleanup player
                if (session.HasPlayerSession)
                {
                    var playerSessionId = session.PlayerSessionId.Value;
                    // Logger.Instance.Info($"Cleanup Player với PlayerSessionID: {playerSessionId}");

                    // Logout player nếu tồn tại
                    if (World.allConnectedChars.TryGetValue(playerSessionId, out var player))
                    {
                        try
                        {
                            player.Logout();
                            // Logger.Instance.Debug($"Player {player.AccountID} đã logout thành công");
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Lỗi khi logout player {player.AccountID}: {ex.Message}");
                        }
                    }

                    // Xóa khỏi World.allConnectedChars
                    World.allConnectedChars.TryRemove(playerSessionId, out _);

                    // Xóa mapping ConnectionID -> PlayerSessionID
                    World.RemoveConnectionMapping(session.ConnectionId);

                    // Giải phóng PlayerSessionID
                    session.ReleasePlayerSessionId();
                    // Logger.Instance.Debug($"Đã giải phóng PlayerSessionID: {playerSessionId}");
                }

                // Logger.Instance.Debug($"Cleanup hoàn tất cho ConnectionID: {session.ConnectionId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong quá trình cleanup session {session.ConnectionId}: {ex.Message}");
            }
        }

        private void ScheduleInactiveConnectionsCheck()
        {
            // Kiểm tra mỗi 30 giây
            // disalbe in development mode to use breakpoint
            if (_configManager.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }
            _inactiveConnectionsChecker = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                TimeSpan.FromSeconds(30),
                TimeSpan.FromSeconds(30),
                Self,
                new CheckInactiveConnections(),
                Self);
        }

        private void CheckInactiveConnections()
        {
            try
            {
                int timeoutSeconds = _configManager.ServerSettings.AutomaticConnectionTime;
                var now = DateTime.Now;
                var timeoutTime = now.AddSeconds(-timeoutSeconds);

                // Tìm các session không hoạt động
                var inactiveSessions = new List<KeyValuePair<IActorRef, ClientSession>>();
                foreach (var session in _sessions)
                {
                    if (session.Value.LastActivityTime < timeoutTime)
                    {
                        inactiveSessions.Add(session);
                    }
                }

                // Đóng các session không hoạt động
                foreach (var session in inactiveSessions)
                {
                    Logger.Instance.Info($"Đóng kết nối không hoạt động: {session.Value.RemoteEndPoint} (ConnectionID: {session.Value.ConnectionId})");

                    // Cleanup session trước khi đóng
                    CleanupSession(session.Value);

                    // Đóng kết nối và xóa khỏi sessions
                    session.Key.Tell(Tcp.Close.Instance);
                    _sessions.Remove(session.Key);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi kiểm tra kết nối không hoạt động: {ex.Message}");
            }
        }

        private void SendPacket(SendPacket message)
        {
            try
            {
                // Bỏ qua nếu connection là null
                if (message.Connection == null)
                {
                   // Logger.Instance.Warning("SendPacket: Connection là null");
                    return;
                }

                // Bỏ qua nếu client là offline (sử dụng ActorRefs.NoSender)
                if (message.Connection == Akka.Actor.ActorRefs.NoSender)
                {
                    // if (World.Debug > 1)
                    // {
                    //     Logger.Instance.Debug("Bỏ qua gửi packet cho offline client (NoSender)");
                    // }
                    return;
                }

                // Kiểm tra session tồn tại và gửi packet
                if (_sessions.TryGetValue(message.Connection, out var session))
                {
                    message.Connection.Tell(Tcp.Write.Create(ByteString.FromBytes(message.Data)));
                    session.UpdateActivity();
                }
                else
                {
                    if (World.Debug > 1)
                    {
                        Logger.Instance.Warning($"Không tìm thấy session cho connection: {message.Connection.Path}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi gói tin: {ex.Message}");
            }
        }

        private void BroadcastPacket(BroadcastPacket message)
        {
            try
            {
                foreach (var session in _sessions)
                {
                    if (message.Filter == null || message.Filter(session.Value))
                    {
                        session.Key.Tell(Tcp.Write.Create(ByteString.FromBytes(message.Data)));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi broadcast gói tin: {ex.Message}");
            }
        }

        private void HandleBound(Tcp.Bound bound)
        {
            _isRunning = true;
            _isStarting = false; // Reset flag khi bind thành công
            Logger.Instance.Info($"Máy chủ đã bind thành công vào {bound.LocalAddress}");

            // Bắt đầu task kiểm tra các kết nối không hoạt động
            ScheduleInactiveConnectionsCheck();
        }

        private void HandleCommandFailed(Tcp.CommandFailed failed)
        {
            if (failed.Cmd is Tcp.Bind bindCmd)
            {
                _isStarting = false; // Reset flag khi bind thất bại
                Logger.Instance.Error($"Bind thất bại cho địa chỉ {bindCmd.LocalAddress}");

                // Nếu là lỗi địa chỉ đã được sử dụng, thử bind lại sau một khoảng thời gian
                var causeMessage = failed.Cause.HasValue ? failed.Cause.Value.Message : "";
                if (causeMessage.Contains("normally permitted"))
                {
                    Logger.Instance.Warning("Cổng đã được sử dụng, sẽ thử lại sau 5 giây...");

                    // Lên lịch thử lại sau 5 giây
                    Context.System.Scheduler.ScheduleTellOnce(
                        TimeSpan.FromSeconds(5),
                        Self,
                        new StartServer(),
                        Self);
                }
                else
                {
                    Logger.Instance.Error($"Lỗi bind không thể khôi phục: {causeMessage}");
                    _isRunning = false;
                }
            }
        }

        private void HandleReceivedDirectly(Tcp.Received received)
        {
            // Debug logging cho tất cả packet nhận được
            byte[] rawData = received.Data.ToArray();
           // Logger.Instance.Debug($"[TCP DEBUG] HandleReceivedDirectly - Raw packet length: {rawData.Length}");
           // Logger.Instance.Debug($"[TCP DEBUG] Raw packet hex: {BitConverter.ToString(rawData).Replace("-", "")}");

            // Chuyển tiếp tin nhắn đến ClientConnection tương ứng
            // Thông thường, tin nhắn này sẽ được gửi trực tiếp đến ClientConnection
            // Nhưng chúng ta xử lý nó ở đây để tránh dead letters

            if (_sessions.TryGetValue(Sender, out var session))
            {
               // Logger.Instance.Debug($"[TCP DEBUG] Found session {session.ConnectionId} for packet");

                // Tìm ClientConnection tương ứng
                var clientActorPath = $"/user/tcpManager/client-{session.ConnectionId}";
                var clientActor = Context.ActorSelection(clientActorPath);

                //Logger.Instance.Debug($"[TCP DEBUG] Forwarding packet to ClientConnection: {clientActorPath}");

                // Chuyển tiếp tin nhắn trực tiếp mà không cần kiểm tra resolve
                // ActorSelection sẽ tự động xử lý việc gửi tin nhắn đến actor đích
                // Nếu actor không tồn tại, tin nhắn sẽ được gửi đến DeadLetters
                clientActor.Tell(received);

                // Cập nhật thời gian hoạt động
                session.UpdateActivity();
            }
            else
            {
                Logger.Instance.Warning($"Nhận dữ liệu từ kết nối không xác định: {Sender.Path}");
            }
        }

        private void HandleGetSessionIdFromConnection(GetSessionIdFromConnection message)
        {
            if (_sessions.TryGetValue(message.Connection, out var session))
            {
                // Trả về ConnectionID (cho network operations)
                // Nếu cần PlayerSessionID, sử dụng method khác
                message.Callback(session.ConnectionId);
            }
            else
            {
                message.Callback(-1);
            }
        }

        private void HandleCloseConnection(CloseConnection message)
        {
            try
            {
                if (_sessions.TryGetValue(message.Connection, out var session))
                {
                    Logger.Instance.Info($"Đóng kết nối từ yêu cầu: {session.RemoteEndPoint} (ConnectionID: {session.ConnectionId})");

                    // Cleanup session trước khi đóng
                    CleanupSession(session);

                    // Đóng kết nối và xóa khỏi sessions
                    message.Connection.Tell(Tcp.Close.Instance);
                    _sessions.Remove(message.Connection);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi đóng kết nối: {ex.StackTrace}");
            }
        }
    }

    // Các message classes cho TcpManager
    public class StartServer { }
    public class StopServer { }
    public class CheckInactiveConnections { }
    public class GetSessionCount { }



    public class GetAllSessions { }

    public class AllSessionsResponse
    {
        public List<ClientSession> Sessions { get; }

        public AllSessionsResponse(List<ClientSession> sessions)
        {
            Sessions = sessions;
        }
    }

    public class GetSessionIdFromConnection
    {
        public IActorRef Connection { get; }
        public Action<int> Callback { get; }

        public GetSessionIdFromConnection(IActorRef connection, Action<int> callback)
        {
            Connection = connection;
            Callback = callback;
        }
    }
}
