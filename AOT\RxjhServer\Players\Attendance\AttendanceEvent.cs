﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DotNetty.Transport.Channels;
using HeroYulgang.Helpers;
using HeroYulgang.RxjhServer.Attendance;
using HeroYulgang.Utils;
using RxjhServer;
using RxjhServer.HelperTools;

namespace HeroYulgang.RxjhServer.Attendance
{
    public class AttendanceEvent
    {
        /// <summary>
        /// Build attendance packet để gửi cho client
        /// </summary>
        public SendingClass BuildAttendancePacket(Players player)
        {
            try
            {
                SendingClass w = new();
                w.Write2(0);

                var currentAttendance = World.CurrentAttendance;
                if (currentAttendance == null)
                {
                    // Không có attendance active, gửi packet trống
                    return w;
                }

                var playerProgress = player.CurrentAttendanceProgress;
                var receivedDays = playerProgress?.GetReceivedDays() ?? new List<int>();

                // Kiểm tra xem player có thể nhận reward hôm nay không
                var canReceiveToday = AttendanceHelper.CanReceiveTodayReward(playerProgress);
                var nextAvailableDay = AttendanceHelper.GetNextAvailableDay(playerProgress);

                // Gửi thông tin cho 28 ngày
                for (int day = 1; day <= 28; day++)
                {
                    var reward = currentAttendance.GetRewardForDay(day);
                    if (reward != null)
                    {
                        // Lấy trạng thái reward cho ngày này
                        var status = AttendanceHelper.GetRewardStatus(day, playerProgress);
                        if (!canReceiveToday && status == AttendanceRewardStatus.CanReceive)
                        {
                            status = AttendanceRewardStatus.CannotReceive;
                        }

                        w.Write4((int)status); // 0=can't receive, 1=can receive, 2=already received
                        w.Write8(reward.ItemId);
                        w.Write2(reward.ItemAmount);
                    }
                    else
                    {
                        // Không có reward cho ngày này
                        w.Write4(0); // can't receive
                        w.Write8(0); // no item
                        w.Write2(0); // no amount
                    }
                }

                return w;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error building attendance packet: {ex.Message}");

                // Gửi packet trống nếu có lỗi
                SendingClass w = new();
                w.Write2(0);
                return w;
            }
        }
    }
}
