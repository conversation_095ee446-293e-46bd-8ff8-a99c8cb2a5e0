-- Complete fix and test for tbl_xwwl_char duplicate key issue
-- <PERSON><PERSON><PERSON><PERSON> phục hoàn chỉnh và test lỗi duplicate key cho tbl_xwwl_char

-- ========================================
-- PART 1: DIAGNOSIS - Ch<PERSON>n đoán vấn đề
-- ========================================

SELECT '=== DIAGNOSIS - CHẨN ĐOÁN ===' as step;

-- Check current table structure
SELECT 'Current table structure:' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'tbl_xwwl_char' AND column_name IN ('id', 'fld_id', 'fld_name', 'fld_index')
ORDER BY ordinal_position;

-- Check primary key constraint
SELECT 'Primary key constraint:' as info;
SELECT 
    tc.constraint_name, 
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'tbl_xwwl_char' 
    AND tc.constraint_type = 'PRIMARY KEY';

-- Check existing data
SELECT 'Current data:' as info;
SELECT id, fld_id, fld_name, fld_index FROM tbl_xwwl_char ORDER BY id;

-- Check if sequence exists
SELECT 'Existing sequences:' as info;
SELECT schemaname, sequencename FROM pg_sequences WHERE sequencename LIKE '%tbl_xwwl_char%';

-- ========================================
-- PART 2: FIX - Khắc phục vấn đề
-- ========================================

SELECT '=== FIX - KHẮC PHỤC ===' as step;

-- Create sequence if not exists
SELECT 'Creating sequence...' as info;
CREATE SEQUENCE IF NOT EXISTS tbl_xwwl_char_id_seq;

-- Set sequence to start from next available ID
SELECT 'Setting sequence start value...' as info;
SELECT setval('tbl_xwwl_char_id_seq', COALESCE((SELECT MAX(id) FROM tbl_xwwl_char), 0) + 1, false);

-- Set default value for id column
SELECT 'Setting default value for id column...' as info;
ALTER TABLE tbl_xwwl_char 
ALTER COLUMN id SET DEFAULT nextval('tbl_xwwl_char_id_seq');

-- Make sequence owned by table column
SELECT 'Setting sequence ownership...' as info;
ALTER SEQUENCE tbl_xwwl_char_id_seq OWNED BY tbl_xwwl_char.id;

-- ========================================
-- PART 3: VERIFICATION - Xác minh
-- ========================================

SELECT '=== VERIFICATION - XÁC MINH ===' as step;

-- Check sequence info
SELECT 'Sequence information:' as info;
SELECT 
    schemaname,
    sequencename,
    last_value,
    start_value,
    increment_by,
    is_called
FROM pg_sequences 
WHERE sequencename = 'tbl_xwwl_char_id_seq';

-- Check updated column default
SELECT 'Updated column default:' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default 
FROM information_schema.columns 
WHERE table_name = 'tbl_xwwl_char' AND column_name = 'id';

-- ========================================
-- PART 4: TEST - Kiểm tra
-- ========================================

SELECT '=== TEST - KIỂM TRA ===' as step;

-- Test 1: Insert first test character
SELECT 'Test 1: Inserting first test character...' as info;
INSERT INTO tbl_xwwl_char (
    fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
    fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
    fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
    fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
    fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
    fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
    fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
    fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
    fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
    nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel,
    fld_xb
) VALUES (
    'test_account_001', 'TestChar001', 0, 1, 1, '0', 1, 0,
    418.0, 1780.0, 15.0, 101, 145, 116, 0, 0, 0, 0,
    '10000', '0', 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0.0, 0, 0, 0,
    0, 0, '', '', '', '', 0,
    0, 0, 0, 'd1', '', 0,
    0, 0, 0, '', 0,
    0, 0, 0, 0, 0, '', 1,
    false, '', 0, '', 0,
    0
);

-- Test 2: Insert second test character for same account
SELECT 'Test 2: Inserting second test character for same account...' as info;
INSERT INTO tbl_xwwl_char (
    fld_id, fld_name, fld_index, fld_job, fld_level, fld_exp, fld_zx, fld_job_level,
    fld_x, fld_y, fld_z, fld_menow, fld_hp, fld_mp, fld_sp, fld_wx, fld_se, fld_point,
    fld_money, fld_jl, fld_zbver, fld_zztype, fld_zzsl, fld_zs, fld_online, fld_get_wx,
    fld_tongkim, fld_taisinh, fld_vipdj, fld_七彩, fld_vip_at, fld_vip_df, fld_vip_hp,
    fld_vip_level, fld_zscs, fld_sjjl, fld_在线时间, fld_在线等级, fld_领奖标志, fld_reserved,
    fld_签名类型, fld_任务等级4, fld_师傅, fld_徒弟1, fld_徒弟2, fld_徒弟3, fld_师徒武功1_1,
    fld_师徒武功1_2, fld_师徒武功1_3, fld_tlc, fld_fqid, fld_giaitruthoigian, fld_titlepoints,
    fld_rosetitlepoints, fld_speakingtype, fld_mlz, fld_love_word, fld_marital_status,
    fld_married, fld_fb_time, fld_lost_wx, fld_hd_time, fld_whtb, fld_config, version,
    nhanqualandau, tlc_random_phe, vohuan_gioihan_theongay, vohuan_time, fld_moneyextralevel,
    fld_xb
) VALUES (
    'test_account_001', 'TestChar002', 1, 2, 1, '0', 1, 0,
    418.0, 1780.0, 15.0, 101, 133, 118, 0, 0, 0, 0,
    '10000', '0', 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0.0, 0, 0, 0,
    0, 0, '', '', '', '', 0,
    0, 0, 0, 'd1', '', 0,
    0, 0, 0, '', 0,
    0, 0, 0, 0, 0, '', 1,
    false, '', 0, '', 0,
    0
);

-- Check results
SELECT 'Test results:' as info;
SELECT id, fld_id, fld_name, fld_index FROM tbl_xwwl_char ORDER BY id;

-- Clean up test data
SELECT 'Cleaning up test data...' as info;
DELETE FROM tbl_xwwl_char WHERE fld_id = 'test_account_001';

SELECT '=== FIX COMPLETED SUCCESSFULLY - KHẮC PHỤC HOÀN TẤT ===' as result;
