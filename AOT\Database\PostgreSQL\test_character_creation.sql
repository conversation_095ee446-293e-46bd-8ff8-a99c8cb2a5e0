-- Test script for character creation
-- <PERSON>ript test cho vi<PERSON><PERSON> tạo nhân vật

-- Check current state of tbl_xwwl_char
-- <PERSON><PERSON><PERSON> tra trạng thái hiện tại của tbl_xwwl_char
SELECT 'Current characters:' as info;
SELECT id, fld_id, fld_name, fld_index FROM tbl_xwwl_char ORDER BY id;

-- Check sequence current value
-- Ki<PERSON><PERSON> tra giá trị hiện tại của sequence
SELECT 'Sequence info:' as info;
SELECT 
    schemaname,
    sequencename,
    last_value,
    start_value,
    increment_by,
    is_called
FROM pg_sequences 
WHERE sequencename = 'tbl_xwwl_char_id_seq';

-- Test insert without specifying ID (should auto-increment)
-- Test insert không chỉ định ID (sẽ tự động tăng)
SELECT 'Testing auto-increment insert:' as info;

-- This should work after running the fix script
-- <PERSON><PERSON><PERSON><PERSON> nà<PERSON> sẽ hoạt động sau khi chạy script khắc phục
/*
INSERT INTO tbl_xwwl_char (
    fld_id, 
    fld_name, 
    fld_index, 
    fld_job, 
    fld_level,
    fld_exp,
    fld_zx,
    fld_job_level,
    fld_x,
    fld_y,
    fld_z,
    fld_menow,
    fld_hp,
    fld_mp,
    fld_sp,
    fld_wx,
    fld_se,
    fld_point,
    fld_money,
    fld_jl,
    fld_zbver,
    fld_zztype,
    fld_zzsl,
    fld_zs,
    fld_online,
    fld_get_wx,
    fld_tongkim,
    fld_taisinh,
    fld_vipdj
) VALUES (
    'test_account_001',
    'TestCharacter001',
    0,
    1,
    1,
    '0',
    1,
    0,
    418.0,
    1780.0,
    15.0,
    101,
    145,
    116,
    0,
    0,
    0,
    0,
    '10000',
    '0',
    1,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
);
*/

-- Check the result
-- Kiểm tra kết quả
SELECT 'After insert:' as info;
SELECT id, fld_id, fld_name, fld_index FROM tbl_xwwl_char ORDER BY id;
