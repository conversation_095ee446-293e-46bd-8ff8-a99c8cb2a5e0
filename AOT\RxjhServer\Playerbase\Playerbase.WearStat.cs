﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer
{
    public partial class PlayersBes : <PERSON>_<PERSON><PERSON>_Cong_Thuoc_Tinh
    {
        	public bool PhanDoan_TrangPhucTinhXao(long long_5, int int_109)
	{
		switch (int_109)
		{
			case 1:
				if (long_5 != 115301001 && long_5 != 115302001 && long_5 != 125301001 && long_5 != 125302001 && long_5 != 215301001 && long_5 != 215302001 && long_5 != 225301001 && long_5 != 225302001 && long_5 != 315301001 && long_5 != 315302001 && long_5 != 325301001 && long_5 != 325302001 && long_5 != 415301001 && long_5 != 415302001 && long_5 != 425301001 && long_5 != 425302001 && long_5 != 515301001 && long_5 != 515302001 && long_5 != 525301001 && long_5 != 525302001 && long_5 != 715301001 && long_5 != 715302001 && long_5 != 725301001 && long_5 != 725302001 && long_5 != 815301001 && long_5 != 815302001 && long_5 != 825301001)
				{
					return long_5 == 825302001;
				}
				return true;
			case 2:
				if (long_5 != 725502001 && long_5 != 725501001 && long_5 != 715502001 && long_5 != 715501001 && long_5 != 525502001 && long_5 != 525501001 && long_5 != 515502001 && long_5 != 515501001 && long_5 != 425502001 && long_5 != 425501001 && long_5 != 415502001 && long_5 != 415501001 && long_5 != 325502001 && long_5 != 325501001 && long_5 != 315502001 && long_5 != 315501001 && long_5 != 225502001 && long_5 != 225501001 && long_5 != 215502001 && long_5 != 215501001 && long_5 != 125502001 && long_5 != 125501001 && long_5 != 115502001 && long_5 != 115501001 && long_5 != 815501001 && long_5 != 815502001 && long_5 != 825501001)
				{
					return long_5 == 825502001;
				}
				return true;
			default:
				return false;
			case 5:
				if (long_5 != 115801001 && long_5 != 115802001 && long_5 != 125801001 && long_5 != 125802001 && long_5 != 215801001 && long_5 != 215802001 && long_5 != 225801001 && long_5 != 225802001 && long_5 != 315801001 && long_5 != 315802001 && long_5 != 325801001 && long_5 != 325802001 && long_5 != 415801001 && long_5 != 415802001 && long_5 != 425801001 && long_5 != 425802001 && long_5 != 515801001 && long_5 != 515802001 && long_5 != 525801001 && long_5 != 525802001 && long_5 != 715801001 && long_5 != 715802001 && long_5 != 725801001 && long_5 != 725802001 && long_5 != 815801001 && long_5 != 815802001 && long_5 != 825801001)
				{
					return long_5 == 825802001;
				}
				return true;
			case 6:
				if (long_5 != 115401001 && long_5 != 115402001 && long_5 != 125401001 && long_5 != 125402001 && long_5 != 215401001 && long_5 != 215402001 && long_5 != 225401001 && long_5 != 225402001 && long_5 != 315401001 && long_5 != 315402001 && long_5 != 325401001 && long_5 != 325402001 && long_5 != 415401001 && long_5 != 415402001 && long_5 != 425401001 && long_5 != 425402001 && long_5 != 515401001 && long_5 != 515402001 && long_5 != 525401001 && long_5 != 525402001 && long_5 != 715401001 && long_5 != 715402001 && long_5 != 725401001 && long_5 != 725402001 && long_5 != 815401001 && long_5 != 815402001 && long_5 != 825401001)
				{
					return long_5 == 825402001;
				}
				return true;
		}
	}

	public bool XacDinhPVPTrangBi(int int_109, int int_110)
	{
		if (World.PVP_TrangBi.TryGetValue(int_109, out var value))
		{
			return value.VatPhamDangCap == int_110;
		}
		return false;
	}

	public bool XacDinhPVPTrangBi_16x_Chan(int int_109, int int_110)
	{
		if (World.PVP_TrangBi_16x_Chan.TryGetValue(int_109, out var value))
		{
			return value.VatPhamDangCap == int_110;
		}
		return false;
	}

	public bool XacDinhPVPTrangBiCuongHoa(int int_109)
	{
		PVPClass value;
		return World.PVP_TrangBi.TryGetValue(int_109, out value);
	}

	public void PVPTrangBiTangThem()
	{
		var num = 0;
		var num2 = 0;
		var num3 = 0;
		var num4 = 0;
		var num5 = 0;
		var num6 = 0;
		var num7 = 0;
		var num8 = 0;
		var num9 = 0;
		var num10 = 0;
		for (var i = 0; i < 17; i++)
		{
			if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 115) && Item_Wear[i].FLD_FJ_NJ > 0)
			{
				num2++;
			}
			if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 110) && Item_Wear[i].FLD_FJ_NJ > 0)
			{
				num3++;
			}
			else if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 120) && Item_Wear[i].FLD_FJ_NJ > 0)
			{
				num4++;
			}
			else if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 130) && Item_Wear[i].FLD_FJ_NJ > 0)
			{
				num5++;
			}
			else if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 140) && Item_Wear[i].FLD_FJ_NJ > 0)
			{
				num6++;
			}
			else if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 150) && Item_Wear[i].FLD_FJ_NJ > 0)
			{
				num7++;
			}
			else if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 160) && Item_Wear[i].FLD_FJ_NJ > 0 && !Item_Wear[i].GetItemName().ToLower().Contains("chan"))
			{
				num9++;
			}
			else if (XacDinhPVPTrangBi((int)Item_Wear[i].GetVatPham_ID, 160) && Item_Wear[i].FLD_FJ_NJ > 0 && Item_Wear[i].GetItemName().ToLower().Contains("chan"))
			{
				num8++;
			}
			else if (XacDinhPVPTrangBi_16x_Chan((int)Item_Wear[i].GetVatPham_ID, 160) && Item_Wear[i].FLD_CuongHoaSoLuong >= 15)
			{
				num10++;
			}
		}
		switch (num2)
		{
			case 2:
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_ThemVao_PhongNguNew += 20;
				break;
			case 3:
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_ThemVao_PhongNguNew += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
				break;
			case 4:
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_ThemVao_PhongNguNew += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
				break;
			case 5:
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_ThemVao_PhongNguNew += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
				FLD_TrangBi_ThemVao_CongKich += 20;
				FLD_TrangBi_ThemVao_HP += 200;
				break;
		}
		if (num3 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 2:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
					}
					break;
				case 3:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
					}
					break;
				case 4:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 6:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
					}
					break;
				case 7:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 8:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 9:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 10:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
				case 11:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
				case 12:
					switch (num3)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
			}
		}
		else if (num4 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 400;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 2:
				case 3:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 400;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 80;
							FLD_TrangBi_ThemVao_PhongNguNew += 80;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 300;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 300;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 4:
				case 6:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
					}
					break;
				case 7:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 300;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 300;
							FLD_TrangBi_ThemVao_PhongNgu += 80;
							FLD_TrangBi_ThemVao_PhongNguNew += 80;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 8:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 100;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 100;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 9:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 100;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 100;
							FLD_TrangBi_ThemVao_PhongNgu += 80;
							FLD_TrangBi_ThemVao_PhongNguNew += 80;
							break;
					}
					break;
				case 10:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 100;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 100;
							FLD_TrangBi_ThemVao_PhongNgu += 80;
							FLD_TrangBi_ThemVao_PhongNguNew += 80;
							break;
					}
					break;
				case 11:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 100;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 100;
							FLD_TrangBi_ThemVao_PhongNgu += 80;
							FLD_TrangBi_ThemVao_PhongNguNew += 80;
							break;
					}
					break;
				case 12:
					switch (num4)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 30;
							FLD_TrangBi_ThemVao_PhongNguNew += 30;
							FLD_TrangBi_ThemVao_HP += 100;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 100;
							FLD_TrangBi_ThemVao_PhongNgu += 80;
							FLD_TrangBi_ThemVao_PhongNguNew += 80;
							break;
					}
					break;
			}
		}
		else if (num5 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 2:
				case 3:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 4:
				case 6:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
					}
					break;
				case 7:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 400;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 8:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 150;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 150;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 9:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 150;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 150;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
				case 10:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 150;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 150;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
				case 11:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 150;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 150;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
				case 12:
					switch (num5)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 150;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 150;
							FLD_TrangBi_ThemVao_PhongNgu += 90;
							FLD_TrangBi_ThemVao_PhongNguNew += 90;
							break;
					}
					break;
			}
		}
		else if (num6 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 600;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 600;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 2:
				case 3:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 600;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 600;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 4:
				case 6:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 600;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.4;
							FLD_TrangBi_ThemVao_HP += 600;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
					}
					break;
				case 7:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 8:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 9:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 10:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 11:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 12:
					switch (num6)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
			}
		}
		else if (num7 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 600;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 600;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 2:
				case 3:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 600;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 600;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 4:
				case 6:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.2;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 600;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.4;
							FLD_TrangBi_ThemVao_HP += 600;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
					}
					break;
				case 7:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 500;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							FLD_TrangBi_ThemVao_MP += 1000;
							break;
					}
					break;
				case 8:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_CongKich += 50;
							break;
					}
					break;
				case 9:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							break;
						case 5:
							CharacterAdditionalCombatPower += 10;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 50;
							FLD_TrangBi_ThemVao_PhongNguNew += 50;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 10:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 11:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
				case 12:
					switch (num7)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 12;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_PhongNguNew += 40;
							FLD_TrangBi_ThemVao_HP += 200;
							break;
						case 6:
							CharacterAdditionalCombatPower += 25;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_HP += 200;
							FLD_TrangBi_ThemVao_PhongNgu += 100;
							FLD_TrangBi_ThemVao_PhongNguNew += 100;
							break;
					}
					break;
			}
		}
		else if (num9 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 2:
				case 3:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 4:
				case 6:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 7:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 8:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 9:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 10:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 11:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 12:
					switch (num9)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
			}
		}
		else if (num8 > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 2:
				case 3:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 4:
				case 6:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 7:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 8:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 9:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 10:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 11:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
				case 12:
					switch (num8)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 7;
							break;
						case 4:
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 5:
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 7;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
						case 6:
							FLD_TrangBi_ThemVao_CongKich += 50;
							FLD_TrangBi_ThemVao_HP += 800;
							CharacterAdditionalCombatPower += 13;
							FLD_TrangBi_ThemVao_PhongNgu += 60;
							FLD_TrangBi_ThemVao_PhongNguNew += 60;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							break;
					}
					break;
			}
		}
		else if (num > 0)
		{
			switch (Player_Job)
			{
				case 1:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 2:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							break;
					}
					break;
				case 3:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 15;
							break;
						case 5:
							CharacterAdditionalCombatPower += 15;
							break;
					}
					break;
				case 4:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							break;
					}
					break;
				case 5:
				case 13:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 6:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							FLD_TrangBi_ThemVao_CongKich += 20;
							break;
					}
					break;
				case 7:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 8:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 12;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 9:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 10:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 11:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
							FLD_TrangBi_ThemVao_PhongNgu += 40;
							break;
					}
					break;
				case 12:
					switch (num)
					{
						case 2:
							CharacterAdditionalCombatPower += 5;
							break;
						case 3:
							CharacterAdditionalCombatPower += 10;
							break;
						case 4:
							CharacterAdditionalCombatPower += 20;
							break;
						case 5:
							CharacterAdditionalCombatPower += 20;
							break;
					}
					break;
			}
		}
		else if (num10 == 5)
		{
			switch (num10)
			{
				case 1:
					break;
				case 2:
					break;
				case 3:
					break;
				case 4:
					break;
				case 5:
					FLD_TrangBi_ThemVao_HP += 1000;
					FLD_TrangBi_ThemVao_MP += 500;
					FLD_TrangBi_ThemVao_PhongNgu += 100;
					FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 100;
					FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.05;
					FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
					FLD_TrangBi_ThemVao_KhiCong++;
					break;
			}
		}
	}

	public int CalculateTheFourGods(int int_109, int int_110)
	{
		var result = 0;
		switch (int_109)
		{
			case 0:
				switch (int_110)
				{
					case 0:
						result = 0;
						break;
					case 1:
						result = -5;
						break;
					case 2:
						result = -5;
						break;
					case 3:
						result = -5;
						break;
					case 4:
						result = -5;
						break;
				}
				break;
			case 1:
				switch (int_110)
				{
					case 0:
						result = 5;
						break;
					case 1:
						result = 0;
						break;
					case 2:
						result = 15;
						break;
					case 3:
						result = 5;
						break;
					case 4:
						result = -15;
						break;
				}
				break;
			case 2:
				switch (int_110)
				{
					case 0:
						result = 5;
						break;
					case 1:
						result = -15;
						break;
					case 2:
						result = 0;
						break;
					case 3:
						result = 15;
						break;
					case 4:
						result = 5;
						break;
				}
				break;
			case 3:
				switch (int_110)
				{
					case 0:
						result = 5;
						break;
					case 1:
						result = 5;
						break;
					case 2:
						result = -15;
						break;
					case 3:
						result = 0;
						break;
					case 4:
						result = 15;
						break;
				}
				break;
			case 4:
				switch (int_110)
				{
					case 0:
						result = 5;
						break;
					case 1:
						result = 15;
						break;
					case 2:
						result = 5;
						break;
					case 3:
						result = -15;
						break;
					case 4:
						result = 0;
						break;
				}
				break;
		}
		return result;
	}

	public void CalculateCharacterEquipmentData()
	{
		using (new Lock(thisLock, "CalculateCharacterEquipmentData"))
		{
			var num = 0;
			var num2 = 0;
			var num3 = 0;
			var num4 = 0;
			var num5 = 0;
			FLD_CuongKhi_TrangBi = 0;
			ClearStat();

			if (Item_Wear[3].GetVatPham_ID != 0L && Item_Wear[3].FLDThuocTinhLoaiHinh == 1)
			{
				FLD_TrangBi_ThuocTinhHoa_ThemVao_TangDame_QuaiVat += (1.0 + (Item_Wear[3].FLDThuocTinhSoLuong + 1) * 0.01) / 5.0;
			}

			HandleBiThuongGiam();
			HandleTrangSucStatCuongHoa();
			// HandleThanKhiSlot12();
			// HandleMountSlot15();
			HandlePearlSlot16();
			HandleBaoChau();
			HandleCalculateEquipment(ref num, ref num2, ref num3, ref num4, ref num5);
			Update_CuongKhi_TrangBi(0);
			if (num == 3)
			{
				FLD_TrangBi_ThemVao_HP += 80;
				FLD_TrangBi_ThemVao_CongKich += 10;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.09;
			}
			if (num2 >= 2)
			{
				switch (Player_Job)
				{
					case 1:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_8 += 2.0;
								break;
						}
						break;
					case 2:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_6 += 2.0;
								break;
						}
						break;
					case 3:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_6 += 2.0;
								break;
						}
						break;
					case 4:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_TrungDich += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_TrungDich += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_TrungDich += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_TrungDich += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_5 += 2.0;
								break;
						}
						break;
					case 5:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_HP += 100;
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								break;
							case 3:
								FLD_TrangBi_ThemVao_HP += 100;
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_KhiCong_2 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_HP += 100;
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_KhiCong_2 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_HP += 100;
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_KhiCong_2 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
						}
						break;
					case 6:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_8 += 2.0;
								break;
						}
						break;
					case 7:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_MP += 200;
								break;
							case 3:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_MP += 200;
								FLD_TrangBi_ThemVao_KhiCong_6 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_MP += 200;
								FLD_TrangBi_ThemVao_KhiCong_6 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_CongKich += 10;
								FLD_TrangBi_ThemVao_MP += 200;
								FLD_TrangBi_ThemVao_KhiCong_6 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_8 += 2.0;
								break;
						}
						break;
					case 8:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_PhongNgu += 20;
								FLD_TrangBi_ThemVao_PhongNguNew += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_10 += 2.0;
								break;
						}
						break;
					case 9:
						switch (num2)
						{
							case 2:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								break;
							case 3:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								break;
							case 4:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								break;
							case 5:
								FLD_TrangBi_ThemVao_PhongNgu += 10;
								FLD_TrangBi_ThemVao_PhongNguNew += 10;
								FLD_TrangBi_ThemVao_NeTranh += 20;
								FLD_TrangBi_ThemVao_KhiCong_0 += 2.0;
								FLD_TrangBi_ThemVao_KhiCong++;
								FLD_TrangBi_ThemVao_KhiCong_9 += 2.0;
								break;
						}
						break;
				}
			}
			PVPTrangBiTangThem();
			UpdateKinhNghiemVaTraiNghiem();
		}
		UpdateKhiCong();
	}

	private void HandleCalculateEquipment(ref int num, ref int num2, ref int num3, ref int num4, ref int num5)
	{
		for (var j = 0; j <= 16; j++)
		{
			var currentItem = Item_Wear[j];
			var itemOption = ItemOptionClass.GetItemOption(currentItem.GetVatPham_ID);
			if (itemOption != null)
			{
				FLD_TrangBi_ThemVao_HP += itemOption.Bonus_HP;
				FLD_ThemVaoTiLePhanTram_HPCaoNhat += itemOption.Bonus_PercentHP / 100.0;
				FLD_TrangBi_ThemVao_MP += itemOption.Bonus_MP;
				FLD_ThemVaoTiLePhanTram_MPCaoNhat += itemOption.Bonus_PercentMP / 100.0;
				FLD_TrangBi_ThemVao_CongKich += itemOption.Bonus_ATK;
				FLD_ThemVaoTiLePhanTram_CongKich += itemOption.Bonus_PercentATK / 100.0;
				FLD_TrangBi_ThemVao_PhongNgu += itemOption.Bonus_DF;
				FLD_TrangBi_ThemVao_PhongNguNew += itemOption.Bonus_DF;
				FLD_ThemVaoTiLePhanTram_PhongNgu += itemOption.Bonus_PercentDF / 100.0;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += itemOption.Bonus_PercentATKSkill /
						100.0;
				FLD_TrangBi_LucPhongNguVoCong += itemOption.Bonus_DefSkill;
				FLD_TrangBi_ThemVao_KhiCong += itemOption.Bonus_Qigong;
				FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram +=
									itemOption.Bonus_DropGold /
									100.0;
				FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram += itemOption.Bonus_Lucky /
									100.0;
				FLD_TrangBi_ThemVao_TrungDich += itemOption.Bonus_Accuracy;
				FLD_TrangBi_ThemVao_NeTranh += itemOption.Bonus_Evasion;
				//FLD_ThemV += itemOption.Bonus_DiemHoangKim;
			}
			if (Item_Wear[j].GetVatPham_ID == 0L || !World.ItemList.TryGetValue(BitConverter.ToInt32(Item_Wear[j].VatPham_ID, 0), out var value))
			{
				continue;
			}
			if (value.FLD_NJ > 0 && Item_Wear[j].FLD_FJ_NJ <= 0)
			{
				Item_Wear[j].FLD_FJ_NJ = 0;
			}
			else
			{
				if ((value.FLD_RESIDE2 == 14 && !KiemTraMonGiap_DieuKien((int)Item_Wear[j].GetVatPham_ID)) || Player_Level < value.FLD_LEVEL || Player_Job_level < value.FLD_JOB_LEVEL)
				{
					continue;
				}
				if (value.FLD_RESIDE1 != 0)
				{
					if (value.FLD_RESIDE2 == 13)
					{
						if (Player_Job == 11)
						{
							if (value.FLD_PID == 1000000992 || value.FLD_PID == 1000000993 || value.FLD_PID == 1000000994 || value.FLD_PID == 1000000995)
							{
								continue;
							}
						}
						else if ((Player_Job == 4 && (value.FLD_PID == 1000001536 || value.FLD_PID == 1000001537 || value.FLD_PID == 1000001538 || value.FLD_PID == 1000001539 || value.FLD_PID == **********)) || (Player_Job == 6 && (value.FLD_PID == ********** || value.FLD_PID == ********** || value.FLD_PID == **********)))
						{
							continue;
						}
					}
					else if (value.FLD_RESIDE1 != Player_Job)
					{
						continue;
					}
				}
				if (value.FLD_SEX != 0 && Player_Sex != value.FLD_SEX)
				{
					continue;
				}
				FLD_TrangBi_ThemVao_ThucTinh = 0;
				Item_Wear[j].DatDuocVatPham_ThuocTinhPhuongThuc(FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa, TriggerAttributePromotion, AccountID, CharacterName);
				if (Item_Wear[j].FLD_MAGIC0 > 0)
				{
					num4 = int.Parse(Item_Wear[j].FLD_MAGIC0.ToString().Substring(Item_Wear[j].FLD_MAGIC0.ToString().Length - 4, 1));
					num5 = int.Parse(Item_Wear[j].FLD_MAGIC0.ToString().Substring(Item_Wear[j].FLD_MAGIC0.ToString().Length - 3, 1)) + 1;
					num3 = int.Parse(Item_Wear[j].FLD_MAGIC0.ToString().Substring(0, 1));
					if (num3 == 1)
					{
						if (Item_Wear[j].FLD_RESIDE2 == 1)
						{
							if (num4 != 0 && num5 > 0)
							{
								var num6 = num4;
								var num7 = num6;
								if (num7 == 1)
								{
									Item_Wear[j].VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich = num5 * 0.01;
								}
							}
						}
						else if (num3 == 2 && Item_Wear[j].FLD_RESIDE2 == 1 && num4 != 0 && num5 > 0)
						{
							switch (num4)
							{
								case 1:
									Item_Wear[j].VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich = num5 * 0.01;
									break;
								case 2:
									Item_Wear[j].VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang = num5;
									break;
								case 3:
									Item_Wear[j].VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh += num5 * 0.01;
									break;
								case 4:
									Item_Wear[j].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang += num5 * 8;
									Item_Wear[j].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew += num5 * 4;
									break;
								case 5:
									Item_Wear[j].Vat_Pham_Luc_Phong_Ngu += num5 * 3;
									Item_Wear[j].Vat_Pham_Luc_Phong_NguNew += num5 * 3;
									break;
								case 6:
									Item_Wear[j].VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram += num5 * 0.01;
									break;
							}
						}
					}
				}
				FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich += Item_Wear[j].VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich;
				FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu += Item_Wear[j].VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu;
				FLD_TrangBi_ThemVao_Phan_NoKhi += Item_Wear[j].VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang;
				FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram;
				FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram;
				FLD_TrangBi_ThemVao_TrungDich += Item_Wear[j].VatPham_ThuocTinh_TiLeChinhXac_GiaTang;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich;
				FLD_TrangBi_ThemVao_NeTranh += Item_Wear[j].VatPham_ThuocTinh_NeTranh_Suat_GiaTang;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh;
				FLD_TrangBi_ThemVao_MucThuongTon += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MucThuongTon;
				FLD_TrangBi_GiamXuong_MucThuongTon += Item_Wear[j].VatPham_ThuocTinh_GiamXuong_MucThuongTon;
				FLD_TrangBi_ThemVao_CongKich += (Item_Wear[j].Vat_Pham_Luc_Cong_Kich + Item_Wear[j].Vat_Pham_Luc_Cong_KichMAX) / 2;
				FLD_TrangBi_ThemVao_PhongNgu += Item_Wear[j].Vat_Pham_Luc_Phong_Ngu;
				FLD_TrangBi_ThemVao_PhongNguNew += Item_Wear[j].Vat_Pham_Luc_Phong_NguNew;
				if (j == 3)
				{
					VuKhi_LucCongKich += (Item_Wear[j].Vat_Pham_Luc_Cong_KichNew + Item_Wear[j].Vat_Pham_Luc_Cong_KichMaxNew) / 2;
				}
				if (j == 0)
				{
					YPhuc_LucPhongNgu += Item_Wear[j].Vat_Pham_Luc_Phong_NguNew;
				}
				if (Item_Wear[j].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang > 0)
				{
					FLD_TrangBi_LucPhongNguVoCong += Item_Wear[j].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang;
					FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew * (1.0 - World.TyLePhanTram_PhongNguVoCong) * 0.01;
					if (j == 0)
					{
						YPhuc_LucPhongNguVoCong_TiLePhanTram += FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram;
					}
				}
				if (Item_Wear[j].VatPham_ThuocTinh_VoCong_LucCongKich > 0)
				{
					FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_VoCong_LucCongKich * World.TyLePhanTram_CongKichVoCong;
					if (j == 3)
					{
						VuKhiTyLePhanTram_CongKichVoCong += FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram;
					}
				}
				FLD_TrangBi_ThemVao_KhiCong += Item_Wear[j].VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
				FLD_TrangBi_ThemVao_HP += Item_Wear[j].VatPham_ThuocTinh_SinhMenhLuc_GiaTang;
				FLD_TrangBi_ThemVao_MP += Item_Wear[j].VatPham_ThuocTinh_NoiCong_Luc_GiaTang;
				FLD_TrangBi_ThemVao_RecoveryMoney += Item_Wear[j].VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += Item_Wear[j].Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += Item_Wear[j].Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich;
				if (Player_Job == 11)
				{
					FLD_TrangBi_ThemVao_LaChan += Item_Wear[j].VatPham_ThuocTinh_LaChan_GiaTang;
				}
				else
				{
					FLD_TrangBi_ThemVao_LaChan = 0;
				}
				if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 22 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 21)
				{
					if (Item_Wear[j].FLD_FJ_LowSoul > 0)
					{
						FLD_TrangBi_ThemVao_ThucTinh = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 20;
					}
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 20 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 16)
				{
					FLD_TrangBi_ThemVao_Than = (int)((Item_Wear[j].FLD_FJ_TrungCapPhuHon - 15) * 0.01 * FLD_Than);
					FLD_TrangBi_ThemVao_TrungDich += FLD_TrangBi_ThemVao_Than;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 15 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 11)
				{
					FLD_TrangBi_ThemVao_Luc = (int)((Item_Wear[j].FLD_FJ_TrungCapPhuHon - 10) * 0.01 * FLD_Luc);
					FLD_TrangBi_ThemVao_PhongNgu += FLD_TrangBi_ThemVao_Luc;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 10 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 6)
				{
					FLD_TrangBi_ThemVao_The = (int)((Item_Wear[j].FLD_FJ_TrungCapPhuHon - 5) * 0.01 * FLD_The);
					FLD_TrangBi_ThemVao_CongKich += FLD_TrangBi_ThemVao_The;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 5 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 1)
				{
					FLD_TrangBi_ThemVao_Tam = (int)(Item_Wear[j].FLD_FJ_TrungCapPhuHon * 0.01 * FLD_Tam);
					FLD_TrangBi_ThemVao_NeTranh += FLD_TrangBi_ThemVao_Tam;
				}
				if (Item_Wear[5].FLD_FJ_TrungCapPhuHon >= 47 && Item_Wear[5].FLD_FJ_TrungCapPhuHon <= 51)
				{
					TrungCapPhuHon_HonNguyen_Giap = Item_Wear[5].FLD_FJ_TrungCapPhuHon - 46;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 51 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 47)
				{
					TrungCapPhuHon_HonNguyen = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 46;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 46 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 42)
				{
					TrungCapPhuHon_HoThe = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 41;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 41 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 37)
				{
					TrungCapPhuHon_DiTinh = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 36;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 36 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 34)
				{
					TrungCapPhuHon_PhanNo = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 33;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 33 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 31)
				{
					TrungCapPhuHon_KyDuyen = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 30;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 30 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 28)
				{
					TrungCapPhuHon_HapHon = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 27;
				}
				else if (Item_Wear[j].FLD_FJ_TrungCapPhuHon <= 27 && Item_Wear[j].FLD_FJ_TrungCapPhuHon >= 23)
				{
					TrungCapPhuHon_PhucCuu = Item_Wear[j].FLD_FJ_TrungCapPhuHon - 22;
				}
				if (Item_Wear[j].FLD_RESIDE2 == 12)
				{
					KiemTraNguHanhAoChoang_ThuocTinhPhu(Item_Wear[j]);
					if (Item_Wear[j].GetVatPham_ID == 26900772 || Item_Wear[j].GetVatPham_ID == 16900772)
					{
					}
					FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich;
					FLD_TrangBi_ThemVao_NeTranh += Item_Wear[j].VatPham_ThuocTinh_NeTranh_Suat_GiaTang;
				}
				// if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 7)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi7GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 8)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi8GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 9)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi9GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 10)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi10GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 11)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi11GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 12)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi12GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 13)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi13GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 14)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi14GiaiDoan_TangThemCongKich;
				// }
				// else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 15)
				// {
				// 	FLD_TrangBi_ThemVao_CongKich += World.VuKhi15GiaiDoan_TangThemCongKich;
				// }
				if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 16)
				{
					FLD_TrangBi_ThemVao_VoCongTrungDich++;
					FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.01;
					// FLD_TrangBi_ThemVao_CongKich += World.VuKhi16GiaiDoan_TangThemCongKich;
					// FLD_TrangBi_ThemVao_DoiQuai_CongKich += World.VuKhi16GiaiDoan_TangThemCongKich / 5;
					if (Item_Wear[j].FLD_CuongHoaSoLuong > 0)
					{
						FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong += Item_Wear[j].FLD_CuongHoaSoLuong * 0.1;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 17)
				{
					FLD_TrangBi_ThemVao_VoCongTrungDich++;
					FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.01;
					// FLD_TrangBi_ThemVao_CongKich += World.VuKhi17GiaiDoan_TangThemCongKich;
					// FLD_TrangBi_ThemVao_DoiQuai_CongKich += World.VuKhi17GiaiDoan_TangThemCongKich / 4;
					if (Item_Wear[j].FLD_CuongHoaSoLuong > 0)
					{
						FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong += Item_Wear[j].FLD_CuongHoaSoLuong * 0.1;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 18)
				{
					FLD_TrangBi_ThemVao_VoCongTrungDich++;
					FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.01;
					// FLD_TrangBi_ThemVao_CongKich += World.VuKhi18GiaiDoan_TangThemCongKich;
					// FLD_TrangBi_ThemVao_DoiQuai_CongKich += World.VuKhi18GiaiDoan_TangThemCongKich / 3;
					if (Item_Wear[j].FLD_CuongHoaSoLuong > 0)
					{
						FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong += Item_Wear[j].FLD_CuongHoaSoLuong * 0.1;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 4 && Item_Wear[j].VatPham_ThuocTinh_Manh == 19)
				{
					FLD_TrangBi_ThemVao_VoCongTrungDich++;
					FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.01;
					// FLD_TrangBi_ThemVao_CongKich += World.VuKhi19GiaiDoan_TangThemCongKich;
					// FLD_TrangBi_ThemVao_DoiQuai_CongKich += World.VuKhi19GiaiDoan_TangThemCongKich / 2;
					if (Item_Wear[j].FLD_CuongHoaSoLuong > 0)
					{
						FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong += Item_Wear[j].FLD_CuongHoaSoLuong * 0.1;
					}
				}
				if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 7)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc7GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 70.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 84.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && (Item_Wear[j].GetItemName().ToLower().Contains("chan") || !Item_Wear[j].GetItemName().ToLower().Contains("chan")))
					{
						FLD_TrangBi_LucPhongNguVoCong += 84.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 8)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc8GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 70.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 84.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && (Item_Wear[j].GetItemName().ToLower().Contains("chan") || !Item_Wear[j].GetItemName().ToLower().Contains("chan")))
					{
						FLD_TrangBi_LucPhongNguVoCong += 84.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 9)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc9GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 70.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 84.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 84.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 98.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 10)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc10GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 143.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 163.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 163.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 183.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 11)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc11GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 40;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 176.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 198.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 198.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 220.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 12)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc12GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 50;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 209.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 257.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 13)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc13GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 260;
						FLD_TrangBi_LucPhongNguVoCong += 242.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 260;
						FLD_TrangBi_LucPhongNguVoCong += 268.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 268.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 294.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 14)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc14GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 710;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 260;
						FLD_TrangBi_LucPhongNguVoCong += 275.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 260;
						FLD_TrangBi_LucPhongNguVoCong += 275.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 100;
						FLD_TrangBi_LucPhongNguVoCong += 303.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 100;
						FLD_TrangBi_LucPhongNguVoCong += 331.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 15)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc15GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 450;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 338.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 250;
						FLD_TrangBi_LucPhongNguVoCong += 368.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 16)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc16GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.YPhuc16GiaiDoan_TangThemPhongNgu / 5;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 450;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 338.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 250;
						FLD_TrangBi_LucPhongNguVoCong += 368.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 17)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc17GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.YPhuc17GiaiDoan_TangThemPhongNgu / 4;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 450;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 338.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 250;
						FLD_TrangBi_LucPhongNguVoCong += 368.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 18)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc18GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.YPhuc18GiaiDoan_TangThemPhongNgu / 3;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 450;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 338.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 250;
						FLD_TrangBi_LucPhongNguVoCong += 368.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 1 && Item_Wear[j].VatPham_ThuocTinh_Manh == 19)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.YPhuc19GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.YPhuc19GiaiDoan_TangThemPhongNgu / 2;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 450;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 308.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 410;
						FLD_TrangBi_LucPhongNguVoCong += 338.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 250;
						FLD_TrangBi_LucPhongNguVoCong += 368.0;
					}
				}
				if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 7)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay7GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 20;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 63.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 8)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay8GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 9)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay9GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 10)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay10GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 93.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 93.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 113.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 113.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 11)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay11GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 121.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 121.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 143.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 143.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 12)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay12GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 149.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 149.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 173.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 173.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 13)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay13GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 420;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 177.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 177.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 203.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 203.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 14)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay14GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 520;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 205.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 205.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 15)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay15GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 16)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay16GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.Giay16GiaiDoan_TangThemPhongNgu / 5;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 17)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay17GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.Giay17GiaiDoan_TangThemPhongNgu / 4;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 18)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay18GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.Giay18GiaiDoan_TangThemPhongNgu / 3;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 5 && Item_Wear[j].VatPham_ThuocTinh_Manh == 19)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.Giay19GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.Giay19GiaiDoan_TangThemPhongNgu / 2;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 7)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu7GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 8)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu8GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 9)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu9GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 10)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu10GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 93.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 93.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 113.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 113.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 11)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu11GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 121.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 121.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 143.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 143.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 12)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu12GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 149.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 149.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 173.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 160;
						FLD_TrangBi_LucPhongNguVoCong += 173.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 13)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu13GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 420;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 177.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 177.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 203.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 420;
						FLD_TrangBi_LucPhongNguVoCong += 203.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 14)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu14GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 520;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 205.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 205.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 520;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 15)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu15GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 16)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu16GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.HoThu16GiaiDoan_TangThemPhongNgu / 5;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 17)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu17GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.HoThu17GiaiDoan_TangThemPhongNgu / 4;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 18)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu18GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.HoThu18GiaiDoan_TangThemPhongNgu / 3;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 2 && Item_Wear[j].VatPham_ThuocTinh_Manh == 19)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.HoThu19GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.HoThu19GiaiDoan_TangThemPhongNgu / 2;
					if (Item_Wear[j].FLD_LEVEL <= 50)
					{
						FLD_TrangBi_ThemVao_HP += 260;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 60 && Item_Wear[j].FLD_LEVEL <= 120)
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && !Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 150 && Item_Wear[j].GetItemName().ToLower().Contains("chan"))
					{
						FLD_TrangBi_ThemVao_HP += 670;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 6)
				{
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 40;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 40;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 40;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 7)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap7GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 80;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 8)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap8GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 140;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 140;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 140;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 9)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap9GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 200;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 200;
						FLD_TrangBi_LucPhongNguVoCong += 35.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 200;
						FLD_TrangBi_LucPhongNguVoCong += 49.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 10)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap10GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 93.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 93.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 113.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 11)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap11GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 121.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 121.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 143.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 12)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap12GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 149.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 149.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 300;
						FLD_TrangBi_LucPhongNguVoCong += 173.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 13)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap13GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 560;
						FLD_TrangBi_LucPhongNguVoCong += 177.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 560;
						FLD_TrangBi_LucPhongNguVoCong += 177.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 560;
						FLD_TrangBi_LucPhongNguVoCong += 203.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 14)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap14GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 660;
						FLD_TrangBi_LucPhongNguVoCong += 205.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 660;
						FLD_TrangBi_LucPhongNguVoCong += 205.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 660;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 15)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap15GiaiDoan_TangThemPhongNgu;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 16)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap16GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.NoiGiap16GiaiDoan_TangThemPhongNgu / 5;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 17)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap17GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.NoiGiap17GiaiDoan_TangThemPhongNgu / 4;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 18)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap18GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.NoiGiap18GiaiDoan_TangThemPhongNgu / 3;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				else if (Item_Wear[j].FLD_RESIDE2 == 6 && Item_Wear[j].VatPham_ThuocTinh_Manh == 19)
				{
					// FLD_TrangBi_ThemVao_PhongNgu += World.NoiGiap19GiaiDoan_TangThemPhongNgu;
					// FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += World.NoiGiap19GiaiDoan_TangThemPhongNgu / 2;
					if (Item_Wear[j].FLD_LEVEL == 135)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL == 145)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 233.0;
					}
					else if (Item_Wear[j].FLD_LEVEL >= 155)
					{
						FLD_TrangBi_ThemVao_HP += 810;
						FLD_TrangBi_LucPhongNguVoCong += 263.0;
					}
				}
				if (Item_Wear[j].FLD_CuongHoaSoLuong > 5 && Item_Wear[j].FLD_RESIDE2 != 4 && Item_Wear[j].FLD_RESIDE2 != 6)
				{
					var num8 = Item_Wear[j].FLD_CuongHoaSoLuong + FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa;
					GiamSatThuong_DoiPhuong += num8 * 5 + 5;
				}
				if (Item_Wear[j].FLD_RESIDE2 == 14)
				{
					var num9 = BitConverter.ToInt32(Item_Wear[j].VatPham_ID, 0);
					if (num9 == 900105 || num9 == 900106 || num9 == 900107 || num9 == 900108 || num9 == 900109 || num9 == 900110 || num9 == 900111 || num9 == 900112)
					{
						if (Item_Wear[j].FLD_CuongHoaSoLuong == 6)
						{
							FLD_TrangBi_ThemVao_HP += 5;
							AoGuild_ThemVao_PhanTram_CongKich += 0.03;
							AoGuild_ThemVao_PhanTram_PhongThu += 0.03;
						}
						if (Item_Wear[j].FLD_CuongHoaSoLuong == 7)
						{
							FLD_TrangBi_ThemVao_HP += 10;
							AoGuild_ThemVao_PhanTram_CongKich += 0.06;
							AoGuild_ThemVao_PhanTram_PhongThu += 0.06;
						}
						if (Item_Wear[j].FLD_CuongHoaSoLuong == 8)
						{
							FLD_TrangBi_ThemVao_HP += 15;
							AoGuild_ThemVao_PhanTram_CongKich += 0.09;
							AoGuild_ThemVao_PhanTram_PhongThu += 0.09;
						}
						if (Item_Wear[j].FLD_CuongHoaSoLuong == 9)
						{
							FLD_TrangBi_ThemVao_HP += 20;
							AoGuild_ThemVao_PhanTram_CongKich += 0.12;
							AoGuild_ThemVao_PhanTram_PhongThu += 0.12;
						}
						if (Item_Wear[j].FLD_CuongHoaSoLuong == 10)
						{
							FLD_TrangBi_ThemVao_HP += 30;
							AoGuild_ThemVao_PhanTram_CongKich += 0.15;
							AoGuild_ThemVao_PhanTram_PhongThu += 0.15;
						}
					}
				}
				if (Item_Wear[j].FLD_RESIDE2 == 7)
				{
					FLD_TrangBi_ThemVao_PhongNgu += Item_Wear[j].FLD_CuongHoaSoLuong * 3;
				}
				if (Item_Wear[j].FLD_RESIDE2 == 8)
				{
					FLD_TrangBi_ThemVao_HP += Item_Wear[j].FLD_CuongHoaSoLuong * 10;
				}
				if (Item_Wear[j].FLD_RESIDE2 == 10)
				{
					if (Item_Wear[j].FLD_LEVEL == 120)
					{
						FLD_TrangBi_ThemVao_CongKich += Item_Wear[j].FLD_CuongHoaSoLuong * 3;
					}
					else if (Item_Wear[j].FLD_LEVEL == 130)
					{
						FLD_TrangBi_ThemVao_CongKich += Item_Wear[j].FLD_CuongHoaSoLuong * 5;
					}
					else if (Item_Wear[j].FLD_LEVEL == 140)
					{
						FLD_TrangBi_ThemVao_CongKich += Item_Wear[j].FLD_CuongHoaSoLuong * 7;
					}
					else if (Item_Wear[j].FLD_LEVEL == 150)
					{
						FLD_TrangBi_ThemVao_CongKich += Item_Wear[j].FLD_CuongHoaSoLuong * 9;
					}
					else if (Item_Wear[j].FLD_LEVEL == 160)
					{
						FLD_TrangBi_ThemVao_CongKich += Item_Wear[j].FLD_CuongHoaSoLuong * 11;
					}
				}
				var fLD_CuongHoaSoLuong = Item_Wear[j].FLD_CuongHoaSoLuong;
				switch (value.FLD_RESIDE2)
				{
					case 1:
						if (FLD_TrangBi_ThemVao_VuKhi_CuongHoa > 0 && Item_Wear[j].VatPham_ThuocTinh_Manh + FLD_TrangBi_ThemVao_VuKhi_CuongHoa > 15)
						{
							FLD_TrangBi_GiaTangDoiPhuong_DiThuong = Item_Wear[j].VatPham_ThuocTinh_Manh + FLD_TrangBi_ThemVao_VuKhi_CuongHoa - 10;
						}
						break;
					case 4:
						{
							var getVatPham_ID = Item_Wear[j].GetVatPham_ID;
							if (FLD_TrangBi_ThemVao_VuKhi_CuongHoa > 0)
							{
								var num10 = FLD_TrangBi_ThemVao_VuKhi_CuongHoa * 25;
								FLD_TrangBi_ThemVao_CongKich += num10;
								VuKhi_LucCongKich += num10;
								if (Item_Wear[j].VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0 && Item_Wear[j].VatPham_ThuocTinh_So_Giai_Doan > 0)
								{
									var num11 = FLD_TrangBi_ThemVao_VuKhi_CuongHoa + FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh + FLD_KetHonLeVat_ThemVaoThuocTinhThach;
									switch (Item_Wear[j].VatPham_ThuocTinh_Giai_Doan_Loai_Hinh)
									{
										case 1:
											FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu += num11 * 0.005;
											break;
										case 2:
											FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram += num11;
											break;
										case 3:
											FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += num11 * 0.01;
											break;
										case 4:
											FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += num11 * 0.5 * 0.01;
											break;
										case 5:
											FLD_TrangBi_ThemVao_MucThuongTon += num11 * 3;
											break;
										case 6:
											FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram += num11 * 0.01;
											FLD_TrangBi_ThemVao_CongKich += num11 * 3;
											break;
									}
								}
								Dictionary<int, Itimesx> dictionary = new();
								dictionary.Add(0, Item_Wear[j].ThuocTinh1);
								dictionary.Add(1, Item_Wear[j].ThuocTinh2);
								dictionary.Add(2, Item_Wear[j].ThuocTinh3);
								dictionary.Add(3, Item_Wear[j].ThuocTinh4);
								for (var k = 0; k < 4; k++)
								{
									if (dictionary[k].ThuocTinhLoaiHinh == 0)
									{
										continue;
									}
									switch (dictionary[k].ThuocTinhLoaiHinh)
									{
										case 3:
											{
												var num14 = 0.0;
												switch (fLD_CuongHoaSoLuong)
												{
													case 5:
														if (k >= 2)
														{
															goto end_IL_f095;
														}
														num14 = 1.0;
														break;
													case 6:
														num14 = 1.0;
														break;
													case 7:
														if (k >= 3)
														{
															goto end_IL_f095;
														}
														num14 = 2.0;
														break;
													case 8:
														num14 = 2.0;
														break;
													case 9:
														num14 = 2.0;
														break;
													case 10:
														num14 = 2.0;
														break;
													case 11:
														num14 = 15.0;
														break;
													case 12:
														num14 = 15.0;
														break;
													case 13:
														num14 = 1.0;
														break;
												}
												FLD_TrangBi_ThemVao_HP += (int)num14;
												break;
											}
										case 1:
											{
												var num12 = 0.0;
												switch (fLD_CuongHoaSoLuong)
												{
													case 5:
														if (k >= 2)
														{
															goto end_IL_f095;
														}
														num12 = 1.0;
														break;
													case 6:
														num12 = 1.0;
														break;
													case 7:
														if (k >= 3)
														{
															goto end_IL_f095;
														}
														num12 = 2.0;
														break;
													case 8:
														num12 = 2.0;
														break;
													case 9:
														num12 = 2.0;
														break;
													case 10:
														num12 = 2.0;
														break;
													case 11:
														num12 = 8.0;
														break;
													case 12:
														num12 = 8.0;
														break;
													case 13:
														num12 = 1.0;
														break;
												}
												FLD_TrangBi_ThemVao_CongKich += (int)num12;
												break;
											}
										case 10:
											{
												var num13 = 0.0;
												switch (fLD_CuongHoaSoLuong)
												{
													case 5:
														if (k >= 2)
														{
															goto end_IL_f095;
														}
														num13 = 1.0;
														break;
													case 6:
														num13 = 1.0;
														break;
													case 7:
														if (k >= 3)
														{
															goto end_IL_f095;
														}
														num13 = 2.0;
														break;
													case 8:
														num13 = 2.0;
														break;
													case 9:
														num13 = 2.0;
														break;
													case 10:
														num13 = 2.0;
														break;
													case 11:
														num13 = 15.0;
														break;
													case 12:
														num13 = 15.0;
														break;
													case 13:
														num13 = 1.0;
														break;
												}
												FLD_TrangBi_ThemVao_MucThuongTon += (int)num13;
												break;
											}
										case 7:
											{
												var num15 = 0.0;
												switch (fLD_CuongHoaSoLuong)
												{
													case 5:
														if (k >= 2)
														{
															goto end_IL_f095;
														}
														num15 = 1.0;
														break;
													case 6:
														num15 = 1.0;
														break;
													case 7:
														if (k >= 3)
														{
															goto end_IL_f095;
														}
														num15 = 2.0;
														break;
													case 8:
														num15 = 2.0;
														break;
													case 9:
														num15 = 2.0;
														break;
													case 10:
														num15 = 2.0;
														break;
													case 11:
														num15 = 11.0;
														break;
													case 12:
														num15 = 11.0;
														break;
													case 13:
														num15 = 1.0;
														break;
												}
												FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += num15 * World.TyLePhanTram_CongKichVoCong;
												break;
											}
										case 8:
											{
												if (dictionary[k].ThuocTinhSoLuong > 0 && fLD_CuongHoaSoLuong > 10 && fLD_CuongHoaSoLuong < 13)
												{
													FLD_TrangBi_ThemVao_KhiCong++;
												}
												break;
											}
										end_IL_f095:
											break;
									}
								}
								if (fLD_CuongHoaSoLuong >= 14)
								{
									Khoa_ChatTyLeCaCuocNhanVat = 10;
								}
								switch (fLD_CuongHoaSoLuong)
								{
									case 8:
										FLD_TrangBi_ThemVao_KhiCong++;
										break;
									case 9:
										FLD_TrangBi_ThemVao_KhiCong += 2;
										break;
									case 10:
										FLD_TrangBi_ThemVao_KhiCong += 2;
										break;
									case 11:
										FLD_TrangBi_ThemVao_KhiCong++;
										break;
								}
								dictionary.Clear();
							}
							FLD_TrangBi_ThemVao_TrungDich += 50;
							if (FLD_TrangBi_ThemVao_ThucTinh > 0)
							{
								var num16 = FLD_TrangBi_ThemVao_ThucTinh * 8;
								FLD_TrangBi_ThemVao_CongKich += num16;
								VuKhi_LucCongKich += num16;
							}
							if (getVatPham_ID != 101200001 && getVatPham_ID != 201200001 && getVatPham_ID != 301200001 && getVatPham_ID != 401200001 && getVatPham_ID != 501200001 && getVatPham_ID != 701200001 && getVatPham_ID != 801200001 && getVatPham_ID != 901200001)
							{
								if (FLD_TrangBi_ThemVao_VuKhi_CuongHoa > 0 && Item_Wear[j].VatPham_ThuocTinh_Manh + FLD_TrangBi_ThemVao_VuKhi_CuongHoa > 15)
								{
									FLD_TrangBi_GiaTang_DiThuongTrangThai = Item_Wear[j].VatPham_ThuocTinh_Manh + FLD_TrangBi_ThemVao_VuKhi_CuongHoa - 10;
								}
							}
							else
							{
								FLD_TrangBi_ThemVao_KhiCong += 3;
								FLD_TrangBi_ThemVao_PhanTramKinhNghiem += 0.5;
							}
							break;
						}
					case 2:
					case 5:
						if (FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa > 0)
						{
							if (fLD_CuongHoaSoLuong <= 6)
							{
								var num17 = FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 3;
								FLD_TrangBi_ThemVao_PhongNgu += num17;
								FLD_TrangBi_ThemVao_PhongNguNew += num17;
								if (value.FLD_RESIDE2 == 1)
								{
									YPhuc_LucPhongNgu += num17;
								}
								if (value.FLD_LEVEL < 60)
								{
									FLD_TrangBi_ThemVao_HP += FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 5;
								}
								else
								{
									FLD_TrangBi_GiamXuong_MucThuongTon += FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 5;
								}
							}
							else
							{
								if (value.FLD_RESIDE2 == 1)
								{
									var num18 = (fLD_CuongHoaSoLuong - 6) * 6;
									FLD_TrangBi_ThemVao_PhongNgu += num18;
									FLD_TrangBi_ThemVao_PhongNguNew += num18;
									YPhuc_LucPhongNgu += num18;
								}
								else
								{
									FLD_TrangBi_ThemVao_PhongNgu += FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 6;
									FLD_TrangBi_ThemVao_PhongNguNew += FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 6;
								}
								if (value.FLD_LEVEL < 60)
								{
									FLD_TrangBi_ThemVao_HP += FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 10;
								}
								else
								{
									FLD_TrangBi_GiamXuong_MucThuongTon += FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa * 10;
								}
							}
						}
						if (value.FLD_RESIDE2 == 1)
						{
							if (Item_Wear[j].VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0 && Item_Wear[j].VatPham_ThuocTinh_So_Giai_Doan > 0)
							{
								var num19 = FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh + FLD_KetHonLeVat_ThemVaoThuocTinhThach;
								switch (Item_Wear[j].VatPham_ThuocTinh_Giai_Doan_Loai_Hinh)
								{
									case 1:
										FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich += num19 * 0.01;
										break;
									case 2:
										FLD_TrangBi_ThemVao_Phan_NoKhi += num19;
										break;
									case 3:
										FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += num19 * 0.01;
										break;
									case 5:
										FLD_TrangBi_ThemVao_PhongNgu += num19 * 3;
										FLD_TrangBi_ThemVao_PhongNguNew += num19 * 3;
										break;
									case 6:
										FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram += num19 * 0.01;
										break;
								}
							}
							if (FLD_TrangBi_ThemVao_ThucTinh > 0)
							{
								var num20 = FLD_TrangBi_ThemVao_ThucTinh * 5;
								FLD_TrangBi_ThemVao_PhongNgu += num20;
								FLD_TrangBi_ThemVao_PhongNguNew += num20;
								YPhuc_LucPhongNgu += num20;
							}
						}
						if (PhanDoan_TrangPhucTinhXao(Item_Wear[j].GetVatPham_ID, Item_Wear[j].FLD_RESIDE2))
						{
							num2++;
						}
						break;
					case 6:
						if (PhanDoan_TrangPhucTinhXao(Item_Wear[j].GetVatPham_ID, Item_Wear[j].FLD_RESIDE2))
						{
							num2++;
						}
						break;
					case 8:
						if (Item_Wear[j].GetVatPham_ID == 15 && Item_Wear[j].FLD_MAGIC2 == 70000020)
						{
							Item_Wear[j].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
						else if (Item_Wear[j].GetVatPham_ID == 1000500 || Item_Wear[j].GetVatPham_ID == 1020500)
						{
							num++;
						}
						break;
					case 10:
						if (Item_Wear[j].GetVatPham_ID == 1700500 || Item_Wear[j].GetVatPham_ID == 1720500)
						{
							num++;
						}
						break;
					case 12:
					case 14:
					case 15:
						FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong * 0.01;
						FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram += Item_Wear[j].VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang * 0.01;
						FLD_TrangBi_ThemVao_PhanTramKinhNghiem += Item_Wear[j].VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang * 0.01;
						FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot += Item_Wear[j].VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot * 0.01;
						if (Item_Wear[j].GetVatPham_ID == 26900772 || Item_Wear[j].GetVatPham_ID == 16900772 || Item_Wear[j].GetVatPham_ID == 26920772 || Item_Wear[j].GetVatPham_ID == 16920772)
						{
						}
						switch (Player_Job)
						{
							case 1:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu;
								}
								break;
							case 2:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo;
								}
								break;
							case 3:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan;
								}
								break;
							case 4:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat;
								}
								break;
							case 5:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi;
								}
								break;
							case 6:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat;
								}
								break;
							case 7:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa;
								}
								break;
							case 8:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat;
								}
								break;
							case 9:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo;
								}
								break;
							case 10:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong;
								}
								break;
							case 11:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong;
								}
								break;
							case 12:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem;
								}
								break;
							case 13:
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_0 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_1 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_2 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_3 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_4 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_6 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_5 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_7 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_8 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_9 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_10 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich;
								}
								if (Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam != 0)
								{
									FLD_TrangBi_ThemVao_KhiCong_11 += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam;
								}
								break;
						}
						if (Player_Job_level < 6)
						{
							break;
						}
						foreach (var value2 in ThangThienKhiCong.Values)
						{
							if (value2.KhiCong_SoLuong > 0)
							{
								switch (value2.KhiCongID)
								{
									case 33:
										FLD_TrangBi_ThemVao_ThangThien_3NoYChiHoa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa;
										break;
									case 25:
										FLD_TrangBi_ThemVao_ThangThien_1HoThan_CuongKhi += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi;
										break;
									case 13:
										FLD_TrangBi_ThemVao_ThangThien_3HoaLong_ChiHoa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa;
										break;
									case 170:
										FLD_TrangBi_ThemVao_ThangThien_3VoTinhDaKich += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich;
										break;
									case 150:
										FLD_TrangBi_ThemVao_ThangThien_2VanVatHoiXuan += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan;
										break;
									case 58:
										FLD_TrangBi_ThemVao_ThangThien_1HoThanKhiGiap += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap;
										break;
									case 352:
										FLD_TrangBi_ThemVao_ThangThien_3MinhKinhChiThuy += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy;
										break;
									case 354:
									case 614:
										FLD_TrangBi_ThemVao_ThangThien_4VongMaiThiemHoa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa;
										break;
									case 330:
										FLD_TrangBi_ThemVao_ThangThien_1PhaGiapThuHon += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon;
										break;
									case 331:
										FLD_TrangBi_ThemVao_ThangThien_2DiThoiViTien += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien;
										break;
									case 340:
										FLD_TrangBi_ThemVao_ThangThien_1TuyetAnhXaHon += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon;
										break;
									case 341:
										FLD_TrangBi_ThemVao_ThangThien_2ThienQuanApDa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa;
										break;
									case 342:
										FLD_TrangBi_ThemVao_ThangThien_3ThienNgoaiTamThi += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi;
										break;
									case 310:
										FLD_TrangBi_ThemVao_ThangThien_1DonXuatNghichCanh += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh;
										break;
									case 311:
										FLD_TrangBi_ThemVao_ThangThien_2CungDoMatLo += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo;
										break;
									case 321:
										FLD_TrangBi_ThemVao_ThangThien_2ThienDiaDongTho += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho;
										break;
									case 322:
										FLD_TrangBi_ThemVao_ThangThien_3HoaPhuongLamTrieu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu;
										break;
									case 561:
										FLD_TrangBi_ThemVao_ThangThien_1DoatMenhLienHoan += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan;
										break;
									case 562:
										FLD_TrangBi_ThemVao_ThangThien_1DienQuangThachHoa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa;
										break;
									case 563:
										FLD_TrangBi_ThemVao_ThangThien_1TinhIchCauTinh += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh;
										break;
									case 314:
									case 324:
									case 334:
									case 565:
									case 665:
										FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong;
										break;
									case 370:
										FLD_TrangBi_ThemVao_ThangThien_1DaMaTrienThan += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan;
										break;
									case 371:
										FLD_TrangBi_ThemVao_ThangThien_2ThuanThuyThoiChu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu;
										break;
									case 327:
									case 343:
									case 353:
									case 373:
									case 613:
										FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong;
										break;
									case 326:
									case 344:
									case 374:
										FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem;
										break;
									case 380:
										FLD_TrangBi_ThemVao_ThangThien_1LucPhachHoaSon += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon;
										break;
									case 381:
										FLD_TrangBi_ThemVao_ThangThien_1TruongHong_QuanNhat += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat;
										break;
									case 382:
										FLD_TrangBi_ThemVao_ThangThien_1KimChungCuongKhi += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi;
										break;
									case 383:
										FLD_TrangBi_ThemVao_ThangThien_1VanKhiHanhTam += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam;
										break;
									case 384:
										FLD_TrangBi_ThemVao_ThangThien_1ChinhBanBoiNguyen += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen;
										break;
									case 385:
										FLD_TrangBi_ThemVao_ThangThien_1VanKhi_LieuThuong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong;
										break;
									case 386:
										FLD_TrangBi_ThemVao_ThangThien_1BachBien_ThanHanh += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh;
										break;
									case 387:
										FLD_TrangBi_ThemVao_ThangThien_1CuongPhongThienY += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY;
										break;
									case 390:
										FLD_TrangBi_ThemVao_ThangThien_1PhiHoaDiemThuy += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy;
										break;
									case 391:
										FLD_TrangBi_ThemVao_ThangThien_2TamDamAnhNguyet += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet;
										break;
									case 392:
										FLD_TrangBi_ThemVao_ThangThien_3TuDaThuCa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa;
										break;
									case 313:
									case 323:
									case 333:
									case 393:
									case 564:
									case 666:
										FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong;
										break;
									case 394:
										FLD_TrangBi_ThemVao_ThangThien_4HuyenTiChanMach += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach;
										break;
									case 700:
										FLD_TrangBi_ThemVao_ThangThien_3DiNhuKhacCuong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong;
										break;
									case 600:
										FLD_TrangBi_ThemVao_ThangThien_1HanhPhongLongVu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu;
										break;
									case 601:
										FLD_TrangBi_ThemVao_ThangThien_2ThienMaHoThe += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe;
										break;
									case 602:
										FLD_TrangBi_ThemVao_ThangThien_3NoiTucHanhTam += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam;
										break;
									case 603:
									case 701:
										FLD_TrangBi_ThemVao_ThangThien_4TruongHongQuanThien += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien;
										break;
									case 604:
									case 702:
										FLD_TrangBi_ThemVao_ThangThien_4AiHongBienDa += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa;
										break;
									case 316:
										FLD_TrangBi_ThemVao_ThangThien_1_HuyenVuLoiDien += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien;
										break;
									case 325:
										FLD_TrangBi_ThemVao_ThangThien_2_HuyenVuTroChu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu;
										break;
									case 315:
										FLD_TrangBi_ThemVao_ThangThien_3_SatNhanQuy += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy;
										break;
									case 662:
										FLD_TrangBi_ThemVao_ThangThien_1_LangKinhThoiLe += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe;
										break;
									case 663:
										FLD_TrangBi_ThemVao_ThangThien_2_SatTinhQuangPhu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu;
										break;
									case 664:
										FLD_TrangBi_ThemVao_ThangThien_3_KyQuanQuanHung += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung;
										break;
									case 679:
										FLD_TrangBi_ThemVao_ThangThien_5_LongHong_PhuThe += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe;
										break;
									case 680:
										FLD_TrangBi_ThemVao_ThangThien_5_KinhThien_DongDia += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia;
										break;
									case 681:
										FLD_TrangBi_ThemVao_ThangThien_5_DietThe_CuongVong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong;
										break;
									case 682:
										FLD_TrangBi_ThemVao_ThangThien_5_ThienLy_NhatKich += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich;
										break;
									case 683:
										FLD_TrangBi_ThemVao_ThangThien_5_HinhDi_YeuTuong += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong;
										break;
									case 684:
										FLD_TrangBi_ThemVao_ThangThien_5_NhatChieuSatThan += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan;
										break;
									case 685:
										FLD_TrangBi_ThemVao_ThangThien_5_LongTraoChiThu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu;
										break;
									case 686:
										FLD_TrangBi_ThemVao_ThangThien_5_ThienMaChiLuc += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc;
										break;
									case 687:
										FLD_TrangBi_ThemVao_ThangThien_5_KinhDao_HaiLang += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang;
										break;
									case 688:
										FLD_TrangBi_ThemVao_ThangThien_5_BatTu_ChiKhu += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu;
										break;
									case 689:
										FLD_TrangBi_ThemVao_ThangThien_5_MaHonChiLuc += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc;
										break;
									case 690:
										FLD_TrangBi_ThemVao_ThangThien_5_PhaKhongTruyTinh += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh;
										break;
									case 610:
										FLD_TrangBi_ThemVao_ThangThien_1_PhanNoDieuTiet += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet;
										break;
									case 611:
										FLD_TrangBi_ThemVao_ThangThien_2_CoDocGiaiTru += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru;
										break;
									case 612:
										FLD_TrangBi_ThemVao_ThangThien_3_ThanLucBaoHo += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo;
										break;
									case 616:
										FLD_TrangBi_ThemVao_ThangThien_5_ThiDocBaoPhat += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat;
										break;
									case 615:
									case 667:
									case 668:
									case 669:
									case 670:
									case 671:
									case 672:
									case 673:
									case 674:
									case 675:
									case 676:
									case 677:
									case 678:
										TrangBi_ThuocTinh_ThemVao_ThangThien_5_TriTan += Item_Wear[j].VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan;
										break;
								}
							}
						}
						break;
				}
			}
		}
	}

	private void HandleBaoChau()
	{
		for (var i = 0; i < 6; i++)
		{
			if (ThietBiTab3[i].GetVatPham_ID == 0)
			{
				continue;
			}
			if (ThietBiTab3[i].GetVatPham_ID == 1000001410)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.03;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.03;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.03;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.03;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 33;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001411)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.05;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.05;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.05;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 55;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001412)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.07;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.07;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.07;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.07;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 77;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001413)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.1;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.1;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.1;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 100;
				FLD_TrangBi_ThemVao_KhiCong++;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001390)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.12;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.12;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.12;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.12;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 150;
				FLD_TrangBi_ThemVao_KhiCong++;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001395)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.13;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.13;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.13;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.13;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 200;
				FLD_TrangBi_ThemVao_KhiCong++;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001400)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.14;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.14;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.14;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.14;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 250;
				FLD_TrangBi_ThemVao_KhiCong++;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001405)
			{
				FLD_DoThanThemVao_PhanTramTanCong += 0.15;
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.15;
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += 0.15;
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += 0.15;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 350;
				FLD_TrangBi_ThemVao_KhiCong += 2;
			}
			if (ThietBiTab3[i].GetVatPham_ID == 1000001414)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_LucPhongNguVoCong += 50.0;
				FLD_TrangBi_ThemVao_HP += 50;
				FLD_TrangBi_ThemVao_MP += 25;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 10;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 5;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001418)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 30;
				FLD_TrangBi_LucPhongNguVoCong += 75.0;
				FLD_TrangBi_ThemVao_HP += 100;
				FLD_TrangBi_ThemVao_MP += 50;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 10;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001422)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 40;
				FLD_TrangBi_LucPhongNguVoCong += 100.0;
				FLD_TrangBi_ThemVao_HP += 150;
				FLD_TrangBi_ThemVao_MP += 75;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 30;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 15;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001426)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 50;
				FLD_TrangBi_LucPhongNguVoCong += 150.0;
				FLD_TrangBi_ThemVao_HP += 200;
				FLD_TrangBi_ThemVao_MP += 100;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 50;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001391)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 75;
				FLD_TrangBi_LucPhongNguVoCong += 200.0;
				FLD_TrangBi_ThemVao_HP += 250;
				FLD_TrangBi_ThemVao_MP += 150;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 75;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 30;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001396)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 100;
				FLD_TrangBi_LucPhongNguVoCong += 250.0;
				FLD_TrangBi_ThemVao_HP += 300;
				FLD_TrangBi_ThemVao_MP += 200;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 100;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 40;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001401)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 125;
				FLD_TrangBi_LucPhongNguVoCong += 300.0;
				FLD_TrangBi_ThemVao_HP += 350;
				FLD_TrangBi_ThemVao_MP += 250;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 125;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 50;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001406)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 150;
				FLD_TrangBi_LucPhongNguVoCong += 350.0;
				FLD_TrangBi_ThemVao_HP += 400;
				FLD_TrangBi_ThemVao_MP += 300;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 150;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 60;
			}
			if (ThietBiTab3[i].GetVatPham_ID == 1000001415)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 10;
				FLD_TrangBi_LucPhongNguVoCong += 25.0;
				FLD_TrangBi_ThemVao_HP += 25;
				FLD_TrangBi_ThemVao_MP += 20;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 10;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 5;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001419)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 15;
				FLD_TrangBi_LucPhongNguVoCong += 50.0;
				FLD_TrangBi_ThemVao_HP += 50;
				FLD_TrangBi_ThemVao_MP += 30;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 10;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001423)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_LucPhongNguVoCong += 75.0;
				FLD_TrangBi_ThemVao_HP += 75;
				FLD_TrangBi_ThemVao_MP += 40;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 30;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 15;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001427)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 25;
				FLD_TrangBi_LucPhongNguVoCong += 100.0;
				FLD_TrangBi_ThemVao_HP += 100;
				FLD_TrangBi_ThemVao_MP += 50;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 40;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001392)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 50;
				FLD_TrangBi_LucPhongNguVoCong += 150.0;
				FLD_TrangBi_ThemVao_HP += 150;
				FLD_TrangBi_ThemVao_MP += 100;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 50;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 30;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001397)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 75;
				FLD_TrangBi_LucPhongNguVoCong += 175.0;
				FLD_TrangBi_ThemVao_HP += 200;
				FLD_TrangBi_ThemVao_MP += 150;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 60;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 40;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001402)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 100;
				FLD_TrangBi_LucPhongNguVoCong += 200.0;
				FLD_TrangBi_ThemVao_HP += 250;
				FLD_TrangBi_ThemVao_MP += 200;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 70;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 50;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001407)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 125;
				FLD_TrangBi_LucPhongNguVoCong += 250.0;
				FLD_TrangBi_ThemVao_HP += 300;
				FLD_TrangBi_ThemVao_MP += 250;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 80;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 60;
			}
			if (ThietBiTab3[i].GetVatPham_ID == 1000001416)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 5;
				FLD_TrangBi_LucPhongNguVoCong += 20.0;
				FLD_TrangBi_ThemVao_HP += 20;
				FLD_TrangBi_ThemVao_MP += 10;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 10;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 5;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001420)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 5;
				FLD_TrangBi_LucPhongNguVoCong += 30.0;
				FLD_TrangBi_ThemVao_HP += 30;
				FLD_TrangBi_ThemVao_MP += 20;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 10;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001424)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 15;
				FLD_TrangBi_LucPhongNguVoCong += 40.0;
				FLD_TrangBi_ThemVao_HP += 40;
				FLD_TrangBi_ThemVao_MP += 30;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 30;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 15;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001428)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_LucPhongNguVoCong += 50.0;
				FLD_TrangBi_ThemVao_HP += 50;
				FLD_TrangBi_ThemVao_MP += 40;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 40;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001393)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 30;
				FLD_TrangBi_LucPhongNguVoCong += 75.0;
				FLD_TrangBi_ThemVao_HP += 150;
				FLD_TrangBi_ThemVao_MP += 100;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 50;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 25;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001398)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 40;
				FLD_TrangBi_LucPhongNguVoCong += 100.0;
				FLD_TrangBi_ThemVao_HP += 200;
				FLD_TrangBi_ThemVao_MP += 150;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 60;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 30;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001403)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 50;
				FLD_TrangBi_LucPhongNguVoCong += 125.0;
				FLD_TrangBi_ThemVao_HP += 200;
				FLD_TrangBi_ThemVao_MP += 150;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 70;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 35;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001408)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 60;
				FLD_TrangBi_LucPhongNguVoCong += 150.0;
				FLD_TrangBi_ThemVao_HP += 250;
				FLD_TrangBi_ThemVao_MP += 200;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 80;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 40;
			}
			if (ThietBiTab3[i].GetVatPham_ID == 1000001417)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 5;
				FLD_TrangBi_LucPhongNguVoCong += 20.0;
				FLD_TrangBi_ThemVao_HP += 20;
				FLD_TrangBi_ThemVao_MP += 10;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 10;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 5;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001421)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 5;
				FLD_TrangBi_LucPhongNguVoCong += 30.0;
				FLD_TrangBi_ThemVao_HP += 30;
				FLD_TrangBi_ThemVao_MP += 20;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 20;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 10;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001425)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 15;
				FLD_TrangBi_LucPhongNguVoCong += 40.0;
				FLD_TrangBi_ThemVao_HP += 40;
				FLD_TrangBi_ThemVao_MP += 30;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 30;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 15;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001429)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 20;
				FLD_TrangBi_LucPhongNguVoCong += 50.0;
				FLD_TrangBi_ThemVao_HP += 50;
				FLD_TrangBi_ThemVao_MP += 40;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 40;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 20;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001394)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 25;
				FLD_TrangBi_LucPhongNguVoCong += 75.0;
				FLD_TrangBi_ThemVao_HP += 150;
				FLD_TrangBi_ThemVao_MP += 100;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 50;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 25;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001399)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 30;
				FLD_TrangBi_LucPhongNguVoCong += 100.0;
				FLD_TrangBi_ThemVao_HP += 200;
				FLD_TrangBi_ThemVao_MP += 150;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 60;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 30;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == 1000001404)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 35;
				FLD_TrangBi_LucPhongNguVoCong += 125.0;
				FLD_TrangBi_ThemVao_HP += 250;
				FLD_TrangBi_ThemVao_MP += 200;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 70;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 35;
			}
			else if (ThietBiTab3[i].GetVatPham_ID == **********)
			{
				FLD_TrangBi_ThemVao_PhongNgu += 35;
				FLD_TrangBi_LucPhongNguVoCong += 125.0;
				FLD_TrangBi_ThemVao_HP += 250;
				FLD_TrangBi_ThemVao_MP += 200;
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 70;
				FLD_TrangBi_ThemVao_VoCongNeTranh += 35;
			}
			ThietBiTab3[i].DatDuocVatPham_ThuocTinhPhuongThuc(AccountID, CharacterName);
			FLD_TrangBi_ThemVao_CongKich += (ThietBiTab3[i].Vat_Pham_Luc_Cong_Kich + ThietBiTab3[i].Vat_Pham_Luc_Cong_KichMAX) / 2;
			FLD_TrangBi_ThemVao_PhongNgu += ThietBiTab3[i].Vat_Pham_Luc_Phong_Ngu;
			FLD_TrangBi_ThemVao_PhongNguNew += ThietBiTab3[i].Vat_Pham_Luc_Phong_NguNew;
			if (i == 3)
			{
				VuKhi_LucCongKich += (ThietBiTab3[i].Vat_Pham_Luc_Cong_KichNew + ThietBiTab3[i].Vat_Pham_Luc_Cong_KichMaxNew) / 2;
			}
			YPhuc_LucPhongNgu += ThietBiTab3[i].Vat_Pham_Luc_Phong_NguNew;
			if (ThietBiTab3[i].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang > 0)
			{
				FLD_TrangBi_LucPhongNguVoCong += ThietBiTab3[i].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang;
				FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += ThietBiTab3[i].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew * (1.0 - World.TyLePhanTram_PhongNguVoCong) * 0.01;
				YPhuc_LucPhongNguVoCong_TiLePhanTram += FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram;
			}
			if (ThietBiTab3[i].VatPham_ThuocTinh_VoCong_LucCongKich > 0)
			{
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += ThietBiTab3[i].VatPham_ThuocTinh_VoCong_LucCongKich * World.TyLePhanTram_CongKichVoCong;
				if (i == 3)
				{
					VuKhiTyLePhanTram_CongKichVoCong += FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram;
				}
			}
			FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich += ThietBiTab3[i].VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich;
			FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu += ThietBiTab3[i].VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu;
			FLD_TrangBi_ThemVao_Phan_NoKhi += ThietBiTab3[i].VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang;
			FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram += ThietBiTab3[i].VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram;
			FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram += ThietBiTab3[i].VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram;
			FLD_TrangBi_ThemVao_TrungDich += ThietBiTab3[i].VatPham_ThuocTinh_TiLeChinhXac_GiaTang;
			FLD_TrangBi_ThemVao_TrungDichTiLePhanTram += ThietBiTab3[i].VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich;
			FLD_TrangBi_ThemVao_NeTranh += ThietBiTab3[i].VatPham_ThuocTinh_NeTranh_Suat_GiaTang;
			FLD_TrangBi_ThemVao_NeTranhTiLePhanTram += ThietBiTab3[i].VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh;
			FLD_TrangBi_ThemVao_MucThuongTon += ThietBiTab3[i].VatPham_ThuocTinh_ThemVao_MucThuongTon;
			FLD_TrangBi_GiamXuong_MucThuongTon += ThietBiTab3[i].VatPham_ThuocTinh_GiamXuong_MucThuongTon;
			FLD_TrangBi_ThemVao_KhiCong += ThietBiTab3[i].VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
			FLD_TrangBi_ThemVao_HP += ThietBiTab3[i].VatPham_ThuocTinh_SinhMenhLuc_GiaTang;
			FLD_TrangBi_ThemVao_MP += ThietBiTab3[i].VatPham_ThuocTinh_NoiCong_Luc_GiaTang;
			FLD_TrangBi_ThemVao_RecoveryMoney += ThietBiTab3[i].VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += ThietBiTab3[i].Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += ThietBiTab3[i].Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich;
		}
	}

	private void HandlePearlSlot16()
	{
		if (Item_Wear[16].GetVatPham_ID == 1900001)
		{
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.01;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.01;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.01;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.01;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900002)
		{
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.02;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.01;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.02;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.01;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900003)
		{
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.03;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.02;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.03;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.02;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900004)
		{
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.04;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.02;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.04;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.02;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900005)
		{
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.05;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.03;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.03;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900006)
		{
			FLD_TrangBi_ThemVao_HP += 100;
			FLD_TrangBi_ThemVao_MP += 50;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.05;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.03;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.03;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.01;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900007)
		{
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 100;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.06;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.03;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.06;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.03;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.02;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900008)
		{
			FLD_TrangBi_ThemVao_HP += 200;
			FLD_TrangBi_ThemVao_MP += 150;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.06;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.04;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.06;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.04;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.03;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900009)
		{
			FLD_TrangBi_ThemVao_HP += 250;
			FLD_TrangBi_ThemVao_MP += 200;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.07;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.04;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.07;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.04;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.04;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900010)
		{
			FLD_TrangBi_ThemVao_HP += 300;
			FLD_TrangBi_ThemVao_MP += 250;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.07;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.05;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.07;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.05;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900011)
		{
			FLD_TrangBi_ThemVao_HP += 350;
			FLD_TrangBi_ThemVao_MP += 300;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.08;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.05;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.08;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.06;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900012)
		{
			FLD_TrangBi_ThemVao_HP += 400;
			FLD_TrangBi_ThemVao_MP += 350;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.08;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.06;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.08;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.06;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.07;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900013)
		{
			FLD_TrangBi_ThemVao_HP += 450;
			FLD_TrangBi_ThemVao_MP += 400;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.09;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.06;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.09;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.06;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.08;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900014)
		{
			FLD_TrangBi_ThemVao_KhiCong++;
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 450;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.1;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.07;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.07;
			FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.09;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900015)
		{
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 150;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 150;
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 150;
		}
		else if (Item_Wear[16].GetVatPham_ID == 1900030)
		{
			if (Player_Job_level < 12)
			{
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 150;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 250;
				FLD_TrangBi_ThemVao_HP += 150;
				FLD_TrangBi_ThemVao_MP += 150;
			}
			else
			{
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 500;
				FLD_TrangBi_ThemVao_DoiQuai_CongKich += 150;
				FLD_TrangBi_ThemVao_HP += 150;
				FLD_TrangBi_ThemVao_MP += 150;
			}
		}
	}

	private void HandleMountSlot15()
	{
		if (Item_Wear[14].GetVatPham_ID == 1000001382)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000001383)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000001384)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000001385)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000002001)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000002002)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000002003)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000002004)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000002005)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		else if (Item_Wear[14].GetVatPham_ID == 1000002006)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
		}
		if (Item_Wear[15].GetVatPham_ID == 1000001184)
		{
			FLD_TrangBi_ThemVao_KhiCong++;
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 150;
			FLD_TrangBi_ThemVao_CongKich += 25;
			FLD_TrangBi_ThemVao_PhongNgu += 50;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 50;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 50;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001182)
		{
			FLD_TrangBi_ThemVao_KhiCong++;
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 150;
			FLD_TrangBi_ThemVao_CongKich += 25;
			FLD_TrangBi_ThemVao_PhongNgu += 50;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 50;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 50;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001183)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 500;
			FLD_TrangBi_ThemVao_CongKich += 100;
			FLD_TrangBi_ThemVao_PhongNgu += 200;
			FLD_TrangBi_LucPhongNguVoCong += 400.0;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 500;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 500;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.1;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
			FLD_TrangBi_ThemVao_KhiCong++;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001185)
		{
			FLD_TrangBi_ThemVao_HP += 200;
			FLD_TrangBi_ThemVao_MP += 200;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 200;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 200;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001186)
		{
			FLD_TrangBi_ThemVao_KhiCong++;
			FLD_TrangBi_ThemVao_HP += 250;
			FLD_TrangBi_ThemVao_MP += 250;
			FLD_TrangBi_ThemVao_CongKich += 100;
			FLD_TrangBi_ThemVao_PhongNgu += 100;
			FLD_TrangBi_LucPhongNguVoCong += 200.0;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.07;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.07;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001187)
		{
			FLD_TrangBi_ThemVao_HP += 100;
			FLD_TrangBi_ThemVao_MP += 100;
			FLD_TrangBi_ThemVao_CongKich += 50;
			FLD_TrangBi_ThemVao_PhongNgu += 50;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 100;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 100;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.07;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.07;
			FLD_TrangBi_ThemVao_KhiCong++;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001188)
		{
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 150;
			FLD_TrangBi_ThemVao_CongKich += 50;
			FLD_TrangBi_ThemVao_PhongNgu += 100;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 150;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 150;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.1;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
			FLD_TrangBi_ThemVao_KhiCong++;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001250)
		{
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 1000;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 1000;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001251)
		{
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 150;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 150;
			FLD_ThanThu_ThemVao_PhanTramKinhNghiem = 0.05;
			FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.05;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001263)
		{
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 100;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 100;
			FLD_ThanThu_ThemVao_PhanTramKinhNghiem = 0.05;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001264)
		{
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 100;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 100;
			FLD_ThanThu_ThemVao_PhanTramKinhNghiem = 0.05;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001276)
		{
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 250;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 250;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001301)
		{
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 100;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 100;
			FLD_TrangBi_ThemVao_HP += 100;
			FLD_TrangBi_ThemVao_MP += 100;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001302)
		{
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 150;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 150;
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 150;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001303)
		{
			FLD_TrangBi_ThemVao_HP += 250;
			FLD_TrangBi_ThemVao_MP += 250;
			FLD_TrangBi_ThemVao_CongKich += 50;
			FLD_TrangBi_ThemVao_PhongNgu += 100;
			FLD_TrangBi_LucPhongNguVoCong += 200.0;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 250;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 250;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
		}
		else if (Item_Wear[15].GetVatPham_ID == 1000001304)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 500;
			FLD_TrangBi_ThemVao_CongKich += 100;
			FLD_TrangBi_ThemVao_PhongNgu += 200;
			FLD_TrangBi_LucPhongNguVoCong += 400.0;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 500;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich += 500;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.1;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
			FLD_TrangBi_ThemVao_KhiCong++;
		}
		else if (Item_Wear[15].GetVatPham_ID != ********** && Item_Wear[15].GetVatPham_ID != ********** && Item_Wear[15].GetVatPham_ID != 0)
		{
			Item_Wear[15].DatDuocVatPham_ThuocTinhPhuongThuc(AccountID, CharacterName);
			FLD_TrangBi_ThemVao_CongKich += (Item_Wear[15].Vat_Pham_Luc_Cong_Kich + Item_Wear[15].Vat_Pham_Luc_Cong_KichMAX) / 4;
			FLD_TrangBi_ThemVao_PhongNgu += Item_Wear[15].Vat_Pham_Luc_Phong_Ngu / 2;
			FLD_TrangBi_ThemVao_PhongNguNew += Item_Wear[15].Vat_Pham_Luc_Phong_NguNew / 2;
			FLD_TrangBi_ThemVao_HP += Item_Wear[15].VatPham_ThuocTinh_SinhMenhLuc_GiaTang;
			FLD_TrangBi_ThemVao_DoiQuai_CongKich = Item_Wear[15].Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich / 5;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu = Item_Wear[15].Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu / 5;
			if (Item_Wear[15].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang > 0)
			{
				FLD_TrangBi_LucPhongNguVoCong += Item_Wear[15].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang;
				FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += Item_Wear[15].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew * (1.0 - World.TyLePhanTram_PhongNguVoCong) * 0.01;
				YPhuc_LucPhongNguVoCong_TiLePhanTram += FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram;
			}
			if (Item_Wear[15].VatPham_ThuocTinh_VoCong_LucCongKich > 0)
			{
				FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += Item_Wear[15].VatPham_ThuocTinh_VoCong_LucCongKich * World.TyLePhanTram_CongKichVoCong;
				VuKhiTyLePhanTram_CongKichVoCong += FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram;
			}
		}
	}

	private void HandleThanKhiSlot12()
	{
		if (Item_Wear[12].GetVatPham_ID == 1000002051)
		{
			FLD_TrangBi_ThemVao_HP += 100;
			FLD_TrangBi_ThemVao_MP += 50;
			FLD_TrangBi_ThemVao_CongKich += 10;
			FLD_TrangBi_ThemVao_PhongNgu += 5;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 10;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002052)
		{
			FLD_TrangBi_ThemVao_HP += 150;
			FLD_TrangBi_ThemVao_MP += 50;
			FLD_TrangBi_ThemVao_CongKich += 20;
			FLD_TrangBi_ThemVao_PhongNgu += 10;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 20;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002053)
		{
			FLD_TrangBi_ThemVao_HP += 200;
			FLD_TrangBi_ThemVao_MP += 100;
			FLD_TrangBi_ThemVao_CongKich += 30;
			FLD_TrangBi_ThemVao_PhongNgu += 15;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 40;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002054)
		{
			FLD_TrangBi_ThemVao_HP += 250;
			FLD_TrangBi_ThemVao_MP += 100;
			FLD_TrangBi_ThemVao_CongKich += 40;
			FLD_TrangBi_ThemVao_PhongNgu += 20;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 60;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002055)
		{
			FLD_TrangBi_ThemVao_HP += 300;
			FLD_TrangBi_ThemVao_MP += 150;
			FLD_TrangBi_ThemVao_CongKich += 50;
			FLD_TrangBi_ThemVao_PhongNgu += 25;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 80;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.01;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.01;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002056)
		{
			FLD_TrangBi_ThemVao_HP += 350;
			FLD_TrangBi_ThemVao_MP += 150;
			FLD_TrangBi_ThemVao_CongKich += 60;
			FLD_TrangBi_ThemVao_PhongNgu += 30;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 100;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.02;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.02;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002057)
		{
			FLD_TrangBi_ThemVao_HP += 400;
			FLD_TrangBi_ThemVao_MP += 200;
			FLD_TrangBi_ThemVao_CongKich += 70;
			FLD_TrangBi_ThemVao_PhongNgu += 35;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 120;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.03;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.03;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002058)
		{
			FLD_TrangBi_ThemVao_HP += 450;
			FLD_TrangBi_ThemVao_MP += 200;
			FLD_TrangBi_ThemVao_CongKich += 80;
			FLD_TrangBi_ThemVao_PhongNgu += 40;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 140;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.04;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.04;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002059)
		{
			FLD_TrangBi_ThemVao_HP += 500;
			FLD_TrangBi_ThemVao_MP += 250;
			FLD_TrangBi_ThemVao_CongKich += 90;
			FLD_TrangBi_ThemVao_PhongNgu += 45;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 160;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.05;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.05;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002060)
		{
			FLD_TrangBi_ThemVao_HP += 550;
			FLD_TrangBi_ThemVao_MP += 250;
			FLD_TrangBi_ThemVao_CongKich += 100;
			FLD_TrangBi_ThemVao_PhongNgu += 50;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.01;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.01;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 180;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.06;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.06;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002061)
		{
			FLD_TrangBi_ThemVao_HP += 600;
			FLD_TrangBi_ThemVao_MP += 300;
			FLD_TrangBi_ThemVao_CongKich += 110;
			FLD_TrangBi_ThemVao_PhongNgu += 55;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.01;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.01;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 200;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.07;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.07;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002062)
		{
			FLD_TrangBi_ThemVao_HP += 650;
			FLD_TrangBi_ThemVao_MP += 300;
			FLD_TrangBi_ThemVao_CongKich += 120;
			FLD_TrangBi_ThemVao_PhongNgu += 60;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.02;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.02;
			FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu += 220;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.08;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.08;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002063)
		{
			FLD_TrangBi_ThemVao_HP += 700;
			FLD_TrangBi_ThemVao_MP += 350;
			FLD_TrangBi_ThemVao_CongKich += 130;
			FLD_TrangBi_ThemVao_PhongNgu += 65;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 240;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.03;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.03;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.09;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.09;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002064)
		{
			FLD_TrangBi_ThemVao_HP += 750;
			FLD_TrangBi_ThemVao_MP += 350;
			FLD_TrangBi_ThemVao_CongKich += 140;
			FLD_TrangBi_ThemVao_PhongNgu += 70;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 260;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.04;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.04;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.1;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.1;
		}
		else if (Item_Wear[12].GetVatPham_ID == 1000002065)
		{
			FLD_TrangBi_ThemVao_HP += 800;
			FLD_TrangBi_ThemVao_MP += 400;
			FLD_TrangBi_ThemVao_CongKich += 150;
			FLD_TrangBi_ThemVao_PhongNgu += 75;
			FLD_TrangBi_ThemVao_DoiQuai_PhongNgu += 300;
			FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.05;
			FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.05;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.11;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += 0.11;
		}
	}

	private void HandleTrangSucStatCuongHoa()
	{
		if (Item_Wear[6].GetVatPham_ID != 0L || Item_Wear[9].GetVatPham_ID != 0L || Item_Wear[10].GetVatPham_ID != 0)
		{
			if (Item_Wear[6].FLD_CuongHoaSoLuong > 0)
			{
				FLD_TrangBi_DayChuyen_GiamChinhXac_DoiPhuong += Item_Wear[6].FLD_CuongHoaSoLuong * 0.1;
			}
			if (Item_Wear[9].FLD_CuongHoaSoLuong > 0)
			{
				FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong += Item_Wear[9].FLD_CuongHoaSoLuong * 0.1;
			}
			if (Item_Wear[10].FLD_CuongHoaSoLuong > 0)
			{
				FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong += Item_Wear[10].FLD_CuongHoaSoLuong * 0.1;
			}
		}
	}

	private void HandleBiThuongGiam()
	{
		if (Item_Wear[0].GetVatPham_ID != 0)
		{
			if (Item_Wear[0].FLD_CuongHoaSoLuong == 6)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 5;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 7)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 10;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 8)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 15;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 9)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 20;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 10)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 30;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 11)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 40;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 12)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 50;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 13)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 60;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 14)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 70;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 15)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 80;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong == 16)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 90;
			}
			else if (Item_Wear[0].FLD_CuongHoaSoLuong >= 17)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 100;
			}
		}
		if (Item_Wear[1].GetVatPham_ID != 0)
		{
			if (Item_Wear[1].FLD_CuongHoaSoLuong == 6)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 5;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 7)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 10;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 8)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 15;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 9)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 20;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 10)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 30;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 11)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 40;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 12)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 50;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 13)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 60;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 14)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 70;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 15)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 80;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong == 16)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 90;
			}
			else if (Item_Wear[1].FLD_CuongHoaSoLuong >= 17)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 100;
			}
		}
		if (Item_Wear[2].GetVatPham_ID != 0)
		{
			if (Item_Wear[2].FLD_CuongHoaSoLuong == 6)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 5;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 7)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 10;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 8)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 15;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 9)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 20;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 10)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 30;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 11)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 40;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 12)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 50;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 13)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 60;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 14)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 70;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 15)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 80;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong == 16)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 90;
			}
			else if (Item_Wear[2].FLD_CuongHoaSoLuong >= 17)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 100;
			}
		}
		if (Item_Wear[4].GetVatPham_ID != 0)
		{
			if (Item_Wear[4].FLD_CuongHoaSoLuong == 6)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 5;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 7)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 10;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 8)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 15;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 9)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 20;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 10)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 30;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 11)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 40;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 12)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 50;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 13)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 60;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 14)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 70;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 15)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 80;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong == 16)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 90;
			}
			else if (Item_Wear[4].FLD_CuongHoaSoLuong >= 17)
			{
				FLD_TrangBi_DoiPhuong_BiThuong_Giam += 100;
			}
		}
	}

	private void ClearStat()
	{
		FLD_TrangBi_ThemVao_HP = 0;
		FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0;
		FLD_TrangBi_ThemVao_MP = 0;
		FLD_ThemVaoTiLePhanTram_MPCaoNhat = 0;
		FLD_TrangBi_ThemVao_CongKich = 0;
		FLD_ThemVaoTiLePhanTram_CongKich = 0;
		FLD_TrangBi_ThemVao_PhongNgu = 0;
		FLD_TrangBi_ThemVao_PhongNguNew = 0;
		FLD_ThemVaoTiLePhanTram_PhongNgu = 0;
		FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0;
		FLD_TrangBi_LucPhongNguVoCong = 0;
		FLD_TrangBi_ThemVao_KhiCong = 0;
		FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0;
		FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram = 0;
		FLD_TrangBi_ThemVao_TrungDich = 0;
		FLD_TrangBi_ThemVao_NeTranh = 0;
		FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.0;
		FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.0;
		FLD_TrangBi_ThemVao_DoiQuai_PhongNgu = 0;
		FLD_TrangBi_ThemVao_DoiQuai_CongKich = 0;
		FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu = 0;
		FLD_TrangBi_ThuocTinhHoa_ThemVao_TangDame_QuaiVat = 0.0;
		AoGuild_ThemVao_PhanTram_CongKich = 0.0;
		AoGuild_ThemVao_PhanTram_PhongThu = 0.0;
		FLD_ThanThu_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_ThanThu_ThemVao_PhanTramTraiNghiem = 0.0;
		VuKhi_LucCongKich = 0;
		VuKhiTyLePhanTram_CongKichVoCong = 0.0;
		YPhuc_LucPhongNgu = 0;
		YPhuc_LucPhongNguVoCong_TiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich = 0.0;
		FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu = 0.0;
		FLD_TrangBi_ThemVao_TrungDichTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram = 0.0;
		FLD_TrangBi_GiamXuong_MucThuongTon = 0.0;
		FLD_TrangBi_ThemVao_NeTranhTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_Phan_NoKhi = 0;
		FLD_TrangBi_ThemVao_MucThuongTon = 0;
		FLD_TrangBi_ThemVao_ThangThien_3HoaLong_ChiHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1HoThan_CuongKhi = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1HoThanKhiGiap = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3NoYChiHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2VanVatHoiXuan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThienQuanApDa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2CungDoMatLo = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2TamDamAnhNguyet = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThuanThuyThoiChu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThienDiaDongTho = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThienMaHoThe = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2DiThoiViTien = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3HoaPhuongLamTrieu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3MinhKinhChiThuy = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3VoTinhDaKich = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3NoiTucHanhTam = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3ThienNgoaiTamThi = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3DiNhuKhacCuong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3TuDaThuCa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4AiHongBienDa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4VongMaiThiemHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4HuyenTiChanMach = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4TruongHongQuanThien = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1BachBien_ThanHanh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DonXuatNghichCanh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1PhiHoaDiemThuy = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1HanhPhongLongVu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1KimChungCuongKhi = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1TuyetAnhXaHon = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1CuongPhongThienY = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1LucPhachHoaSon = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1PhaGiapThuHon = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DaMaTrienThan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1VanKhiHanhTam = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1VanKhi_LieuThuong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1TruongHong_QuanNhat = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1ChinhBanBoiNguyen = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DoatMenhLienHoan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DienQuangThachHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1TinhIchCauTinh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_LongHong_PhuThe = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_KinhThien_DongDia = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_DietThe_CuongVong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_ThienLy_NhatKich = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_HinhDi_YeuTuong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_NhatChieuSatThan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_LongTraoChiThu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_ThienMaChiLuc = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_KinhDao_HaiLang = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_BatTu_ChiKhu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1_HuyenVuLoiDien = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2_HuyenVuTroChu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3_SatNhanQuy = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_MaHonChiLuc = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1_LangKinhThoiLe = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2_SatTinhQuangPhu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3_KyQuanQuanHung = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_PhaKhongTruyTinh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1_PhanNoDieuTiet = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2_CoDocGiaiTru = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3_ThanLucBaoHo = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_ThiDocBaoPhat = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_TriTan = 0.0;
		FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
		FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
		FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_LaChan = 0;
		FLD_TrangBi_LucPhongNguVoCong = 0.0;
		FLD_TrangBi_ThemVao_CongKich = 0;
		FLD_TrangBi_ThemVao_PhongNgu = 0;
		FLD_TrangBi_ThemVao_PhongNguNew = 0;
		FLD_TrangBi_ThemVao_TrungDich = 0;
		FLD_TrangBi_ThemVao_NeTranh = 0;
		FLD_TrangBi_ThemVao_KhiCong = 0;
		FLD_TrangBi_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_0 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_1 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_2 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_3 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_4 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_5 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_6 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_7 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_8 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_9 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_10 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_11 = 0.0;
		TrungCapPhuHon_PhucCuu = 0;
		TrungCapPhuHon_HapHon = 0;
		TrungCapPhuHon_KyDuyen = 0;
		TrungCapPhuHon_PhanNo = 0;
		TrungCapPhuHon_DiTinh = 0;
		TrungCapPhuHon_HoThe = 0;
		TrungCapPhuHon_HonNguyen = 0;
		TrungCapPhuHon_HonNguyen_Giap = 0;
		FLD_TrangBi_ThemVao_Tam = 0;
		FLD_TrangBi_ThemVao_The = 0;
		FLD_TrangBi_ThemVao_Luc = 0;
		FLD_TrangBi_ThemVao_Than = 0;
		FLD_TrangBi_ThemVao_ThucTinh = 0;
		FLD_TrangBi_ThemVao_HP = 0;
		FLD_TrangBi_ThemVao_RecoveryMoney = 0;
		FLD_TrangBi_ThemVao_MP = 0;
		Khoa_ChatTyLeCaCuocNhanVat = 0;
		NhanVatNeTranhSkill = 0;
		CharacterAdditionalCombatPower = 0;
		FLD_TrangBi_ThemVao_VoCongNeTranh = 0;
		FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.0;
		FLD_TrangBi_ThemVao_VoCongTrungDich = 0;
		FLD_TrangBi_GiaTangDoiPhuong_DiThuong = 0;
		FLD_TrangBi_GiaTang_DiThuongTrangThai = 0;
		GiamSatThuong_DoiPhuong = 0;
		FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong = 0.0;
		FLD_TrangBi_DayChuyen_GiamChinhXac_DoiPhuong = 0.0;
		FLD_TrangBi_DoiPhuong_BiThuong_Giam = 0;
		FLD_DoThanThemVao_PhanTramTanCong = 0.0;
	}

    }
}
