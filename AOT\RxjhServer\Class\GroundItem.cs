
using System;
using System.Timers;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class GroundItem : IDisposable
{

	private System.Timers.Timer npcyd;

	public ThreadSafeDictionary<int, Players> PlayerList;

	public long id;

	private DateTime _time;

	private Item _item;

	private byte[] _Item_Byte;

	private float _PosX;

	private float _PosY;

	private float _PosZ;

	private int _RxjhMap;

	private Players _ItemPriority;

	private int _ItemDropMapID;

	public DateTime time
	{
		get
		{
			return _time;
		}
		set
		{
			_time = value;
		}
	}

	public Item Item
	{
		get
		{
			return _item;
		}
		set
		{
			_item = value;
		}
	}

	public byte[] ItemByte
	{
		get
		{
			return _Item_Byte;
		}
		set
		{
			_Item_Byte = value;
		}
	}

	public float PosX
	{
		get
		{
			return _PosX;
		}
		set
		{
			_PosX = value;
		}
	}

	public float PosY
	{
		get
		{
			return _PosY;
		}
		set
		{
			_PosY = value;
		}
	}

	public float PosZ
	{
		get
		{
			return _PosZ;
		}
		set
		{
			_PosZ = value;
		}
	}

	public int MapID
	{
		get
		{
			return _RxjhMap;
		}
		set
		{
			_RxjhMap = value;
		}
	}

	public Players ItemPriority
	{
		get
		{
			return _ItemPriority;
		}
		set
		{
			_ItemPriority = value;
		}
	}

	public int ItemDropMapID
	{
		get
		{
			return _ItemDropMapID;
		}
		set
		{
			_ItemDropMapID = value;
		}
	}

	public GroundItem()
	{
		PlayerList = [];
	}

	~GroundItem()
	{
	}

	public void Dispose()
	{
		try
		{
			if (npcyd != null)
			{
				npcyd.Enabled = false;
				npcyd.Close();
				npcyd.Dispose();
				npcyd = null;
			}
			if (PlayerList != null)
			{
				PlayerList.Clear();
				PlayerList.Dispose();
			}
			PlayerList = null;
			Item = null;
			ItemPriority = null;

		}
		catch (Exception)
		{
		}
	}

	public GroundItem(byte[] VatPham_byte_, float x, float y, float z, int map, Players player, int VatPham_NoiRotRa)
	{
		PlayerList = [];
		ItemPriority = player;
		time = DateTime.Now;
		Item = new(VatPham_byte_);
		ItemByte = VatPham_byte_;
		id = BitConverter.ToInt64(Item.DatDuocGlobal_ID(), 0);
		PosX = x;
		PosY = y;
		PosZ = z;
		MapID = map;
		this.ItemDropMapID = VatPham_NoiRotRa;
		npcyd = new(60000.0);
		npcyd.Elapsed += VatPhamBienMat;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;

	}

	public void npcydtheout()
	{
		try
		{
			if (npcyd != null)
			{
				npcyd.Enabled = false;
				npcyd.Close();
				npcyd.Dispose();
				npcyd = null;
			}
			World.GroundItemList.Remove(id);
			GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "VatPhamBienMat errornpcydtheout ：" + BitConverter.ToInt32(Item.DatDuocGlobal_ID(), 0) + " [" + Item.GetItemName() + "]" + ex);
		}
		finally
		{
			World.GroundItemList.Remove(id);
			Dispose();
		}
	}

	public void VatPhamBienMat(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (npcyd != null)
			{
				npcyd.Enabled = false;
				npcyd.Close();
				npcyd.Dispose();
				npcyd = null;
			}
			World.GroundItemList.Remove(id);
			GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "VatPhamBienMat error !! ：" + BitConverter.ToInt64(Item.DatDuocGlobal_ID(), 0) + " [" + Item.GetItemName() + "]" + ex);
		}
		finally
		{
			World.GroundItemList.Remove(id);
			Dispose();
		}
	}

	public void GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage()
	{
		try
		{
			// Use AOI system if enabled for this map  
			if (true) // AOI system enabled
			{
				var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(MapID, PosX, PosY);
				foreach (var grid in aoiGrids)
				{
					foreach (var player in grid.GetPlayers())
					{
						if (FindPlayers(400, player))
						{
							player.ScanGroundItems();
						}
					}
				}
			}
			else
			{
				// Fallback to old system
				foreach (var value in World.allConnectedChars.Values)
				{
					if (FindPlayers(400, value))
					{
						value.ScanGroundItems();
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage error：" + ex);
		}
	}

	public void GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage()
	{
		try
		{
			if (PlayerList == null)
			{
				return;
			}
			try
			{
				foreach (var value in PlayerList.Values)
				{
					value.ScanGroundItems();
				}
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage1 error：" + ex);
			}
			PlayerList?.Clear();
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage3 error：" + ex2);
		}
	}

	public bool FindPlayers(int far_, Players player)
	{
		if (player.MapID != MapID)
		{
			return false;
		}
		var num = player.PosX - PosX;
		var num2 = player.PosY - PosY;
		bool isInRange = (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2) <= (double)far_;
		if (!isInRange)
		{
			return false;
		}
		return CanPlayerSeeItem(player);
	}

	public bool CanPlayerSeeItem(Players player)
	{
		return true;
	}

	public bool CanPlayerPickupItem(Players player)
	{
		return true;
	}

	public static GroundItem GetItme(long id)
	{
		if (World.GroundItemList.TryGetValue(id, out var value))
		{
			return value;
		}
		return null;
	}
}
