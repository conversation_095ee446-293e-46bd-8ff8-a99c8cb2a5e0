using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using HeroYulgang.Core;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.Quest;

namespace HeroYulgang.Services
{
    /// <summary>
    /// Service để reload config và template data
    /// </summary>
    public class ConfigReloadService
    {
        private readonly Logger _logger;

        public ConfigReloadService()
        {
            _logger = Logger.Instance;
        }

        /// <summary>
        /// Reload config và/hoặc templates dựa trên type
        /// </summary>
        public async Task<ConfigReloadResult> ReloadAsync(string configType)
        {
            var result = new ConfigReloadResult
            {
                Success = true,
                ReloadedItems = new List<string>(),
                Message = "Reload completed successfully"
            };

            try
            {
                _logger.Info($"Starting reload process for type: {configType}");

                switch (configType.ToLower())
                {
                    case "all":
                        await ReloadConfigAsync(result);
                        await ReloadTemplatesAsync(result);
                        QuestManager.ReloadQuestData();
                        break;

                    case "config":
                        await ReloadConfigAsync(result);
                        break;

                    case "template":
                    case "templates":
                        await ReloadTemplatesAsync(result);
                        break;

                    default:
                        result.Success = false;
                        result.Message = $"Unknown config type: {configType}. Valid types: all, config, template";
                        break;
                }

                if (result.Success)
                {
                    _logger.Info($"✓ Reload completed successfully. Items reloaded: {string.Join(", ", result.ReloadedItems)}");
                }
                else
                {
                    _logger.Error($"✗ Reload failed: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Reload failed with exception: {ex.Message}";
                _logger.Error($"✗ Reload failed with exception: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Reload configuration files
        /// </summary>
        private async Task ReloadConfigAsync(ConfigReloadResult result)
        {
            try
            {
                _logger.Info("Reloading configuration files...");

                // Reload appsettings.json through ConfigManager
                var configManager = ConfigManager.Instance;

                // Reload configuration using public method
                configManager.ReloadConfiguration();
                
                result.ReloadedItems.Add("appsettings.json");
                _logger.Info("✓ appsettings.json reloaded successfully");

                // Reload config.json if exists
                if (File.Exists("config.json"))
                {
                    // Reset Config helper to force reload
                    Config.ConfigPath = "config.json";
                    result.ReloadedItems.Add("config.json");
                    _logger.Info("✓ config.json reloaded successfully");
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message += $" Config reload error: {ex.Message}";
                _logger.Error($"Error reloading config: {ex.Message}");
            }
        }

        /// <summary>
        /// Reload database templates
        /// </summary>
        private async Task ReloadTemplatesAsync(ConfigReloadResult result)
        {
            try
            {
                _logger.Info("Reloading database templates...");

                // Reload PublicDb templates
                bool publicDbSuccess = await PublicDb.RefreshAsync();
                if (publicDbSuccess)
                {
                    result.ReloadedItems.Add("PublicDb Templates");
                    _logger.Info("✓ PublicDb templates reloaded successfully");
                }
                else
                {
                    result.Success = false;
                    result.Message += " Failed to reload PublicDb templates.";
                }

                // Reload GameDb templates if needed
                bool gameDbSuccess = await GameDb.RefreshAsync();
                if (gameDbSuccess)
                {
                    result.ReloadedItems.Add("GameDb Templates");
                    _logger.Info("✓ GameDb templates reloaded successfully");
                }
                else
                {
                    result.Success = false;
                    result.Message += " Failed to reload GameDb templates.";
                }

                // Reload BBGDb templates if needed
                bool bbgDbSuccess = await BBGDb.RefreshAsync();
                if (bbgDbSuccess)
                {
                    result.ReloadedItems.Add("BBGDb Templates");
                    _logger.Info("✓ BBGDb templates reloaded successfully");
                }
                else
                {
                    result.Success = false;
                    result.Message += " Failed to reload BBGDb templates.";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message += $" Template reload error: {ex.Message}";
                _logger.Error($"Error reloading templates: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Result của config reload operation
    /// </summary>
    public class ConfigReloadResult
    {
        public bool Success { get; set; }
        public List<string> ReloadedItems { get; set; } = new List<string>();
        public string Message { get; set; } = string.Empty;
        public DateTime ReloadTime { get; set; } = DateTime.Now;
    }
}
