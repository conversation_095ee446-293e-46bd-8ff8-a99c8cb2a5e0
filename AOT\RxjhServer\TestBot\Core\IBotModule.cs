using System;

namespace RxjhServer.TestBot.Core
{
    /// <summary>
    /// Interface cơ bản cho tất cả các module của Test Bot
    /// </summary>
    public interface IBotModule : IDisposable
    {
        /// <summary>
        /// Tên module
        /// </summary>
        string ModuleName { get; }
        
        /// <summary>
        /// Module có được bật hay không
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// Độ ưu tiên của module (số càng thấp càng ưu tiên)
        /// </summary>
        int Priority { get; }
        
        /// <summary>
        /// Khởi tạo module
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <param name="config">Bot configuration</param>
        void Initialize(Players player, BotConfig config);
        
        /// <summary>
        /// Cập nhật logic module (đư<PERSON><PERSON> gọi mỗi tick)
        /// </summary>
        void Update();
        
        /// <summary>
        /// Dọn dẹp tài nguyên
        /// </summary>
        void Cleanup();
        
        /// <summary>
        /// Kiểm tra xem module có thể chạy không
        /// </summary>
        /// <returns>True nếu có thể chạy</returns>
        bool CanExecute();
    }
    
    /// <summary>
    /// Interface cho Test Bot Core
    /// </summary>
    public interface IBotCore : IDisposable
    {
        /// <summary>
        /// Player instance
        /// </summary>
        Players Player { get; }
        
        /// <summary>
        /// Bot configuration
        /// </summary>
        BotConfig Config { get; }
        
        /// <summary>
        /// Bot có đang chạy không
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// Đăng ký module
        /// </summary>
        /// <param name="module">Module cần đăng ký</param>
        void RegisterModule(IBotModule module);
        
        /// <summary>
        /// Hủy đăng ký module
        /// </summary>
        /// <param name="moduleName">Tên module cần hủy</param>
        void UnregisterModule(string moduleName);
        
        /// <summary>
        /// Lấy module theo tên
        /// </summary>
        /// <typeparam name="T">Loại module</typeparam>
        /// <param name="moduleName">Tên module</param>
        /// <returns>Module instance hoặc null</returns>
        T GetModule<T>(string moduleName) where T : class, IBotModule;
        
        /// <summary>
        /// Bắt đầu bot
        /// </summary>
        void Start();
        
        /// <summary>
        /// Dừng bot
        /// </summary>
        void Stop();
        
        /// <summary>
        /// Cập nhật bot (main loop)
        /// </summary>
        void Update();
        
        /// <summary>
        /// Event khi bot bắt đầu
        /// </summary>
        event Action<IBotCore> OnBotStarted;
        
        /// <summary>
        /// Event khi bot dừng
        /// </summary>
        event Action<IBotCore> OnBotStopped;
        
        /// <summary>
        /// Event khi có lỗi
        /// </summary>
        event Action<IBotCore, Exception> OnError;
    }
}
