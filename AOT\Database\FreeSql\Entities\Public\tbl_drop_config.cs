using FreeSql.DatabaseModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public
{
    /// <summary>
    /// Runtime configuration for drop system
    /// </summary>
    [JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
    public partial class tbl_drop_config
    {
        /// <summary>
        /// Configuration key (primary key)
        /// </summary>
        [JsonProperty, Column(IsPrimary = true, StringLength = 50)]
        public string config_key { get; set; }

        /// <summary>
        /// Configuration value (stored as text)
        /// </summary>
        [JsonProperty, Column(StringLength = -2)]
        public string config_value { get; set; }

        /// <summary>
        /// Description of what this configuration does
        /// </summary>
        [JsonProperty, Column(StringLength = -2)]
        public string description { get; set; }

        /// <summary>
        /// Last update timestamp
        /// </summary>
        [JsonProperty]
        public DateTime updated_at { get; set; } = DateTime.Now;

        /// <summary>
        /// Get configuration value as boolean
        /// </summary>
        public bool GetBoolValue(bool defaultValue = false)
        {
            if (string.IsNullOrEmpty(config_value))
                return defaultValue;

            return bool.TryParse(config_value, out var result) ? result : defaultValue;
        }

        /// <summary>
        /// Get configuration value as integer
        /// </summary>
        public int GetIntValue(int defaultValue = 0)
        {
            if (string.IsNullOrEmpty(config_value))
                return defaultValue;

            return int.TryParse(config_value, out var result) ? result : defaultValue;
        }

        /// <summary>
        /// Get configuration value as decimal
        /// </summary>
        public decimal GetDecimalValue(decimal defaultValue = 0m)
        {
            if (string.IsNullOrEmpty(config_value))
                return defaultValue;

            return decimal.TryParse(config_value, out var result) ? result : defaultValue;
        }

        /// <summary>
        /// Get configuration value as double
        /// </summary>
        public double GetDoubleValue(double defaultValue = 0.0)
        {
            if (string.IsNullOrEmpty(config_value))
                return defaultValue;

            return double.TryParse(config_value, out var result) ? result : defaultValue;
        }

        /// <summary>
        /// Get configuration value as string
        /// </summary>
        public string GetStringValue(string defaultValue = "")
        {
            return string.IsNullOrEmpty(config_value) ? defaultValue : config_value;
        }

        /// <summary>
        /// Set configuration value from boolean
        /// </summary>
        public void SetValue(bool value)
        {
            config_value = value.ToString().ToLower();
            updated_at = DateTime.Now;
        }

        /// <summary>
        /// Set configuration value from integer
        /// </summary>
        public void SetValue(int value)
        {
            config_value = value.ToString();
            updated_at = DateTime.Now;
        }

        /// <summary>
        /// Set configuration value from decimal
        /// </summary>
        public void SetValue(decimal value)
        {
            config_value = value.ToString("F6");
            updated_at = DateTime.Now;
        }

        /// <summary>
        /// Set configuration value from double
        /// </summary>
        public void SetValue(double value)
        {
            config_value = value.ToString("F6");
            updated_at = DateTime.Now;
        }

        /// <summary>
        /// Set configuration value from string
        /// </summary>
        public void SetValue(string value)
        {
            config_value = value ?? "";
            updated_at = DateTime.Now;
        }

        /// <summary>
        /// Validate configuration value based on key
        /// </summary>
        public bool IsValidValue()
        {
            if (string.IsNullOrEmpty(config_key) || config_value == null)
                return false;

            switch (config_key.ToLower())
            {
                case "new_drop_system_enabled":
                case "debug_drop_logging":
                    return bool.TryParse(config_value, out _);

                case "global_drop_multiplier":
                case "level_gap_penalty":
                case "team_drop_bonus":
                    if (decimal.TryParse(config_value, out var decimalVal))
                        return decimalVal >= 0m && decimalVal <= 100m; // Reasonable range
                    return false;

                case "max_drops_per_kill":
                    if (int.TryParse(config_value, out var intVal))
                        return intVal >= 1 && intVal <= 50; // Reasonable range
                    return false;

                default:
                    return true; // Unknown keys are allowed
            }
        }

        /// <summary>
        /// Get display-friendly value
        /// </summary>
        public string GetDisplayValue()
        {
            if (string.IsNullOrEmpty(config_value))
                return "Not Set";

            switch (config_key?.ToLower())
            {
                case "new_drop_system_enabled":
                case "debug_drop_logging":
                    return GetBoolValue() ? "Enabled" : "Disabled";

                case "global_drop_multiplier":
                case "level_gap_penalty":
                case "team_drop_bonus":
                    var decimalVal = GetDecimalValue();
                    return $"{decimalVal:F2}x ({decimalVal * 100:F1}%)";

                case "max_drops_per_kill":
                    return $"{GetIntValue()} items";

                default:
                    return config_value;
            }
        }

        /// <summary>
        /// Create a copy of this configuration
        /// </summary>
        public tbl_drop_config Clone()
        {
            return new tbl_drop_config
            {
                config_key = this.config_key,
                config_value = this.config_value,
                description = this.description,
                updated_at = this.updated_at
            };
        }

        public override string ToString()
        {
            return $"DropConfig[{config_key}]: {GetDisplayValue()} - {description}";
        }
    }

    /// <summary>
    /// Static helper class for common drop configuration keys
    /// </summary>
    public static class DropConfigKeys
    {
        public const string NEW_DROP_SYSTEM_ENABLED = "new_drop_system_enabled";
        public const string GLOBAL_DROP_MULTIPLIER = "global_drop_multiplier";
        public const string DEBUG_DROP_LOGGING = "debug_drop_logging";
        public const string MAX_DROPS_PER_KILL = "max_drops_per_kill";
        public const string LEVEL_GAP_PENALTY = "level_gap_penalty";
        public const string TEAM_DROP_BONUS = "team_drop_bonus";

        /// <summary>
        /// Get all default configuration keys
        /// </summary>
        public static readonly Dictionary<string, (string defaultValue, string description)> DefaultConfigs = 
            new Dictionary<string, (string, string)>
            {
                { NEW_DROP_SYSTEM_ENABLED, ("false", "Enable new drop system (true/false)") },
                { GLOBAL_DROP_MULTIPLIER, ("1.0", "Global drop rate multiplier (decimal)") },
                { DEBUG_DROP_LOGGING, ("false", "Enable detailed drop logging (true/false)") },
                { MAX_DROPS_PER_KILL, ("5", "Maximum number of items that can drop from one kill") },
                { LEVEL_GAP_PENALTY, ("0.1", "Drop rate penalty per level difference") },
                { TEAM_DROP_BONUS, ("1.2", "Drop rate bonus for team kills") }
            };
    }
}
