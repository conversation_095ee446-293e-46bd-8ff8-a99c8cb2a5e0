# Script to test performance improvements
param(
    [string]$ProjectRoot = ".",
    [int]$TestDuration = 60
)

Write-Host "🚀 Starting Performance Test..." -ForegroundColor Green
Write-Host "Test Duration: $TestDuration seconds" -ForegroundColor Cyan

# Build the project first
Write-Host "📦 Building project..." -ForegroundColor Yellow
dotnet build -c Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}

# Start the server
Write-Host "🎯 Starting server..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath "dotnet" -ArgumentList "run -c Release" -PassThru -NoNewWindow

# Wait for server to start
Start-Sleep -Seconds 10

# Test database connections
Write-Host "🔍 Testing database performance..." -ForegroundColor Cyan

# Simulate multiple login attempts
$testResults = @()

for ($i = 1; $i -le 5; $i++) {
    Write-Host "  Test $i/5: Simulating login..." -ForegroundColor Gray
    
    $startTime = Get-Date
    
    # Here you would normally use a client to connect
    # For now, just simulate the delay
    Start-Sleep -Milliseconds 500
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalMilliseconds
    
    $testResults += [PSCustomObject]@{
        Test = $i
        Duration = $duration
        Success = $true
    }
    
    Write-Host "    Duration: $($duration)ms" -ForegroundColor Gray
}

# Calculate statistics
$avgDuration = ($testResults | Measure-Object -Property Duration -Average).Average
$maxDuration = ($testResults | Measure-Object -Property Duration -Maximum).Maximum
$minDuration = ($testResults | Measure-Object -Property Duration -Minimum).Minimum

Write-Host "`n📊 Performance Results:" -ForegroundColor Green
Write-Host "  Average Duration: $($avgDuration.ToString('F2'))ms" -ForegroundColor White
Write-Host "  Min Duration: $($minDuration.ToString('F2'))ms" -ForegroundColor White
Write-Host "  Max Duration: $($maxDuration.ToString('F2'))ms" -ForegroundColor White

# Performance thresholds
$goodThreshold = 1000  # 1 second
$warningThreshold = 2000  # 2 seconds

if ($avgDuration -lt $goodThreshold) {
    Write-Host "✅ Performance: GOOD" -ForegroundColor Green
} elseif ($avgDuration -lt $warningThreshold) {
    Write-Host "⚠️ Performance: WARNING" -ForegroundColor Yellow
} else {
    Write-Host "❌ Performance: POOR" -ForegroundColor Red
}

# Stop the server
Write-Host "`n🛑 Stopping server..." -ForegroundColor Yellow
try {
    Stop-Process -Id $serverProcess.Id -Force
    Write-Host "✅ Server stopped successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not stop server process" -ForegroundColor Yellow
}

Write-Host "`n🎉 Performance test completed!" -ForegroundColor Green
