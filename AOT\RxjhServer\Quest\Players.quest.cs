
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Quest;

namespace RxjhServer;

public partial class PlayersBes
{

    public ConcurrentDictionary<int, PlayerQuest> PlayerQuests = new();
    public void LoadPlayerQuests()
    {
        try
        {
            var quests = GameDb.LoadPlayerQuestAsync(CharacterName).Result;
            if (quests != null && quests.Count > 0)
            {
                foreach (var quest in quests)
                {
                    var questData = QuestClass.GetQuest(quest.quest_id);
                    if (questData == null)
                    {
                        continue;
                    }
                    PlayerQuests.TryAdd(quest.quest_id,
                    new PlayerQuest
                    {
                        QuestId = quest.quest_id,
                        CurrentStage = quest.current_stage,
                        ProgressData = quest.progress_data,
                        StartTime = quest.start_time!.Value,
                        CompleteTime = quest.complete_time,
                        ResetTime = quest.reset_time,
                        ResetType = quest.reset_type,
                        ResetIntervalDays = quest.reset_interval_days!.Value,
                        CompletionCount = quest.completion_count!.Value,
                        QuestData = questData
                    });
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi tải nhiệm vụ của người chơi: {ex.Message}");
        }
    }

    public void UpdateCharacterQuest()
    {
        using SendingClass w = new();
        // count uncompleted quests
        var count = 0;
        int[] completedQuest = [];
        int[] processingQuest = [];
        if (GuildId > 0)
        {
            (completedQuest, processingQuest) = GetAllGuildQuest(GuildId).Result;
            count += completedQuest.Length + processingQuest.Length;
        }
        foreach (var quest in PlayerQuests.Values)
        {
            // Kiểm tra nếu là quest thường đã hoàn thành thì bỏ qua, nếu là quest daily mà đã hoàn thành trong ngày thì bỏ qua
            if (quest.CompleteTime.HasValue && quest.ResetType != "none" && quest.ResetTime.HasValue && quest.ResetTime.Value > DateTime.Now)
            {
                continue;
            }
            count++;
        }
        w.Write4(count);
        foreach (var quest in PlayerQuests.Values)
        {
            if (quest.CompleteTime.HasValue && quest.ResetType != "none" && quest.ResetTime.HasValue && quest.ResetTime.Value > DateTime.Now)
            {
                continue;
            }
            w.Write2(quest.QuestId);
            w.Write2(quest.CurrentStage);
        }
        foreach (var questId in processingQuest)
        {
            w.Write2(questId);
            w.Write2(1);
        }
        w.Write4(0);
        Client?.SendPak(w, 34048, SessionID);
    }
    public void UpdateCharacterCompleteQuest()
    {
        using SendingClass w = new();
        w.Write2(1018);
        w.Write2(0);
        int[] completedQuest = [];
        if (GuildId > 0)
        {
            (completedQuest, _) = GetAllGuildQuest(GuildId).Result;
        }
        foreach (var quest in PlayerQuests.Values)
        {
            if (!quest.CompleteTime.HasValue || quest.ResetType == "none" || !quest.ResetTime.HasValue || quest.ResetTime.Value <= DateTime.Now)
            {
                continue;
            }
            w.Write2(quest.QuestId);
        }
        foreach (var questId in completedQuest)
        {
            w.Write2(questId);
        }
        Client?.SendPak(w, 0x8B00, SessionID);
    }

    public async Task SavePlayerQuests(PlayerQuest quest)
    {
        try
        {
            var questData = new tbl_player_quests
            {
                character_name = CharacterName,
                quest_id = quest.QuestId,
                quest_status = 0,
                current_stage = quest.CurrentStage,
                progress_data = quest.ProgressData,
                start_time = quest.StartTime,
                complete_time = quest.CompleteTime,
                reset_time = quest.ResetTime,
                reset_type = quest.ResetType,
                reset_interval_days = quest.ResetIntervalDays,
                completion_count = quest.CompletionCount
            };
            await GameDb.SavePlayerQuestAsync(questData);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi lưu nhiệm vụ của người chơi: {ex.Message}");
        }
    }


    public bool HasQuest(int questId)
    {
        return PlayerQuests.ContainsKey(questId);
    }

    /// <summary>
    /// Get list of active quest IDs for drop system
    /// </summary>
    /// <returns>List of active quest IDs</returns>
    public List<int> GetActiveQuestIds()
    {
        try
        {
            var activeQuests = new List<int>();

            foreach (var quest in PlayerQuests.Values)
            {
                // Skip completed quests that don't reset
                if (quest.CompleteTime.HasValue && quest.ResetType == "none")
                {
                    continue;
                }

                // Skip daily quests that are completed and still in cooldown
                if (quest.CompleteTime.HasValue && quest.ResetType != "none" &&
                    quest.ResetTime.HasValue && quest.ResetTime.Value > DateTime.Now)
                {
                    continue;
                }

                // This quest is active
                activeQuests.Add(quest.QuestId);
            }

            // Also include guild quests if player is in a guild
            if (GuildId > 0)
            {
                try
                {
                    var (completedQuest, processingQuest) = GetAllGuildQuest(GuildId).Result;
                    activeQuests.AddRange(processingQuest);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error getting guild quests: {ex.Message}");
                }
            }

            return activeQuests;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error getting active quest IDs: {ex.Message}");
            return new List<int>();
        }
    }

    public async Task<bool> AddQuest(QuestData quest, int stage)
    {
        try
        {
            var playerQuest = new PlayerQuest
            {
                QuestId = quest.QuestId,
                CurrentStage = stage,
                ProgressData = "",
                StartTime = DateTime.Now,
                CompleteTime = null,
                ResetTime = null,
                ResetType = quest.QuestType ?? "none",
                ResetIntervalDays = 0,
                CompletionCount = 0,
                QuestData = quest
            };
            PlayerQuests.TryAdd(quest.QuestId, playerQuest);
            await SavePlayerQuests(playerQuest);
            return true;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi thêm nhiệm vụ cho người chơi: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UpdateQuestAsync(PlayerQuest quest)
    {
        try
        {
            await SavePlayerQuests(quest);
            return true;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi cập nhật nhiệm vụ của người chơi: {ex.Message}");
            return false;
        }
    }

    public async Task<(int[],int[])> GetAllGuildQuest(int guildId)
    {
        try
        {
            var quests = await GameDb.GetGuildQuestsAsync(guildId);
            int[] completedQuest = [];
            int[] processingQuest = [];
            foreach (var quest in quests)
            {

                if (quest.resettime > DateTime.Now)
                {
                    completedQuest.Append(quest.questid!.Value);
                }
                else
                {
                    processingQuest.Append(quest.questid!.Value);
                }
            }
            return (completedQuest, processingQuest);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi tải nhiệm vụ guild của người chơi: {ex.Message}");
            return ([], []);
        }
    }
    
	public void DelAllTaskItem()
	{
		for (var i = 0; i < 36; i++)
		{
			NhiemVu_VatPham[i] = new X_Nhiem_Vu_Vat_Pham_Loai(new byte[8]);
		}
		GuiDi_NhiemVu_VatPham_List();
	}

	public void TaskPromptDataSending(int nhiemVuId, int thaoTacD, int nhiemVuGiaiDoanId)
	{
		HeThongNhacNho($"Nhiệm vụ {nhiemVuId} - thaotac {thaoTacD} - stage {nhiemVuGiaiDoanId}", 10, "Thiên cơ các");
		using SendingClass sendingClass = new();
		sendingClass.Write2(nhiemVuId);
		sendingClass.Write2(thaoTacD);
		sendingClass.Write2(nhiemVuGiaiDoanId);
		sendingClass.Write4(0);
		Client?.SendPak(sendingClass, 33792, SessionID);
	}

	public void ObtainQuestItems(int vatPhamViTri, int vatPhamId, int vatPhamSoLuong)
	{
		using SendingClass sendingClass = new();
		sendingClass.Write4(vatPhamViTri);
		sendingClass.Write4(0);
		sendingClass.Write4(vatPhamId);
		sendingClass.Write4(0);
		sendingClass.Write4(vatPhamSoLuong);
		Client?.SendPak(sendingClass, 33280, SessionID);
	}

	public bool ObtainQuestItems(int vatPhamId, int vatPhamSoLuong)
	{
		var num = 0;
		while (true)
		{
			if (num < NhiemVu_VatPham.Length)
			{
				if (NhiemVu_VatPham[num].VatPham_ID == vatPhamId && NhiemVu_VatPham[num].VatPhamSoLuong >= vatPhamSoLuong)
				{
					break;
				}
				num++;
				continue;
			}
			return false;
		}
		return true;
	}

	public void SetUpQuestItems(int vatPhamId, int vatPhamSoLuong)
	{
		var flag = false;
		var flag2 = false;
		for (var i = 0; i < NhiemVu_VatPham.Length; i++)
		{
			if (NhiemVu_VatPham[i].VatPham_ID == vatPhamId)
			{
				flag = true;
				flag2 = true;
				if (vatPhamSoLuong == 0)
				{
					ObtainQuestItems(i, NhiemVu_VatPham[i].VatPham_ID, 0);
					NhiemVu_VatPham[i].VatPham_byte = new byte[8];
				}
				else
				{
					NhiemVu_VatPham[i].VatPhamSoLuong += vatPhamSoLuong;
					ObtainQuestItems(i, NhiemVu_VatPham[i].VatPham_ID, NhiemVu_VatPham[i].VatPhamSoLuong);
				}
				break;
			}
		}
		if (!flag && vatPhamSoLuong > 0)
		{
			for (var j = 0; j < NhiemVu_VatPham.Length; j++)
			{
				if (NhiemVu_VatPham[j].VatPham_ID == 0)
				{
					NhiemVu_VatPham[j].VatPham_ID = vatPhamId;
					NhiemVu_VatPham[j].VatPhamSoLuong += vatPhamSoLuong;
					ObtainQuestItems(j, NhiemVu_VatPham[j].VatPham_ID, NhiemVu_VatPham[j].VatPhamSoLuong);
					flag2 = true;
					break;
				}
			}
		}
		if (!flag2)
		{
			HeThongNhacNho("Sứ mệnh bảo vật đã hoàn tất, không thể tiếp tục thu thập!");
		}
	}
    	public void TaskReminder(int nhiemVuId, int thaoTacD, int nhiemVuGiaiDoanId)
	{
		using SendingClass sendingClass = new();
		sendingClass.Write2(nhiemVuId);
		sendingClass.Write2(thaoTacD);
		sendingClass.Write2(nhiemVuGiaiDoanId);
		Client?.SendPak(sendingClass, 33792, SessionID);
	}

}

public class PlayerQuest
{
    public int QuestId { get; set; }
    public int CurrentStage { get; set; }
    public string ProgressData { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? CompleteTime { get; set; }
    public DateTime? ResetTime { get; set; }
    public string ResetType { get; set; }
    public int ResetIntervalDays { get; set; }
    public int CompletionCount { get; set; }

    public QuestData QuestData { get; set; }

    public bool IsCompleted => CompleteTime.HasValue;


}