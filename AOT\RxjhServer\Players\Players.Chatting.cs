﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    

	public void Check_Info_Player(string username)
	{
		try
		{
			var playerByName = GetPlayerByName(username);
			if (playerByName != null)
			{
				HeThongNhacNho("Công lực: [" + (int)(playerByName.TotalSkillDamage * 10.0) + "] - Phòng ngự: [" + playerByName.NhanVat_LonNhat_VoCong_PhongNgu + "]", 7, "");
				HeThongNhacNho("Tấn công: [" + playerByName.FLD_NhanVatCoBan_CongKich + "] - Phòng thủ: [" + playerByName.FLD_NhanVatCoBan_PhongNgu + "]", 7, "");
				HeT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HP: [" + playerByName.CharacterMax_HP + "] - MP: [" + playerByName.CharacterMax_MP + "] - SP: [" + playerByName.CharacterMax_SP + "]", 7, "");
				HeThongNhacNho("Né tránh: [" + playerByName.FLD_NhanVatCoBan_NeTranh + "] - Chính xác: [" + playerByName.FLD_NhanVatCoBan_TrungDich + "]", 7, "");
				HeThongNhacNho("Đột phá: [" + playerByName.NumberOfRebirths + "] - Võ huân: [" + playerByName.VoHuanGiaiDoan + "] - PVP: [" + playerByName.NhanVatThienVaAc + "]", 7, "");
				HeThongNhacNho("Giảm sát thương từ bộ trang bị: [" + playerByName.FLD_TrangBi_DoiPhuong_BiThuong_Giam + "]", 10, "");
				HeThongNhacNho("TÊN NHÂN VẬT: [" + username + "] - Cấp độ[" + playerByName.Player_Level + "]", 10, "");
			}
			else
			{
				HeThongNhacNho("Danh tính không hợp lệ hoặc không xuất hiện trong giang hồ!", 20, "Thiên cơ các");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi lệnh check info !!! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void LionRoarThread(object parObject)
	{
		try
		{
			if (FLD_RXPIONT == World.MoiLan_SuTuHong_TieuHaoNguyenBao)
			{
				var text = "Đĩ Đực";
				if (Player_Sex == 2)
				{
					text = "Đĩ Cái";
				}
				KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				KiemSoatNguyenBao_SoLuong(World.MoiLan_SuTuHong_TieuHaoNguyenBao, 0);
				Save_NguyenBaoData();
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (!value.Client.TreoMay && value.IsJoinWorld)
						{
							value.HeThongNhacNho((string)parObject, 6, text + " [" + CharacterName + "]");
							Thread.Sleep(100);
						}
					}
					return;
				}
			}
			HeThongNhacNho("Điểm không đủ, thi triển Sư Tử Hống tiêu hao " + World.MoiLan_SuTuHong_TieuHaoNguyenBao + " điểm!");
		}
		catch
		{
		}
	}

	public void CuteDogExpressionEffect(int vatPhamId, int switchOnOff, uint thoiGian, uint thoiGianHienTai)
	{
		Convert.ToDouble(DateTime.ParseExact(thoiGian.ToString(), "yyMMddHHmm", CultureInfo.CurrentCulture).AddMinutes(0L - thoiGianHienTai).ToString("yyMMddHHmm"));
		var array = Converter.HexStringToByte("AA553900010040022B0027004A010D15038203440101000000C9E3143C20060C0000000001A502BB7135F1B971400FE0F600E02000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array, 25, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(switchOnOff), 0, array, 33, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(thoiGian), 0, array, 37, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void CuteDogEmojiDataPack(int id)
	{
		var array = Converter.HexStringToByte("AA552800010040021A001600AE000830016600A800190100E00D00401700052003E07E00000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 30, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 15, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void Speak(byte[] packetData, int packetSize)
	{
		var num = 0;
		try
		{
			int num2 = packetData[39];
			int msgType = packetData[10];
			int num4 = packetData[34];
			if (num4 > 99)
			{
				num4 = 99;
			}
			var array = new byte[num4];
			num = 1;
			string text;
			try
			{
				Buffer.BlockCopy(packetData, 35, array, 0, array.Length);
				text = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "Nói lỗi 11! [" + AccountID + "]-[" + CharacterName + "]" + array.Length + "  " + ex.Message);
				return;
			}
			if (msgType == 25)
			{
				num = 2;
				switch (num2)
				{
					case 1:
						CuteDogEmojiDataPack(1);
						return;
					case 2:
						CuteDogEmojiDataPack(2);
						return;
					case 3:
						CuteDogEmojiDataPack(3);
						return;
					case 4:
						CuteDogEmojiDataPack(4);
						return;
					case 5:
						CuteDogEmojiDataPack(5);
						return;
					case 6:
						CuteDogEmojiDataPack(6);
						return;
					case 7:
						CuteDogEmojiDataPack(7);
						return;
					case 8:
						CuteDogEmojiDataPack(8);
						return;
					case 9:
						CuteDogEmojiDataPack(9);
						return;
					case 10:
						CuteDogEmojiDataPack(10);
						return;
				}
			}
			num = 3;
			var array2 = new byte[15];
			Buffer.BlockCopy(packetData, 12, array2, 0, array2.Length);
			num = 4;
			var name = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
			if (text.Length <= 0 || msgType == 100)
			{
				return;
			}
			foreach (var item in World.Kill)
			{
				num = 5;
				if (text.Replace("  ", string.Empty).IndexOf(item.Txt) == -1)
				{
					continue;
				}
				num = 6;
				if (item.Sffh == 0)
				{
					num = 7;
					text = text.Replace("  ", string.Empty).Replace(item.Txt, "我爱" + World.ServerName);
					continue;
				}
				num = 8;
				if (item.Sffh == 1)
				{
					if (Client != null)
					{
						Client.Dispose();
						LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 14]");
					}
					return;
				}
				num = 9;
				if (item.Sffh == 2)
				{
					//DBA.ExeSqlCommand($"  Insert  into  TBL_BANED  values  (  '{Client.ToString()}')", "rxjhaccount").GetAwaiter().GetResult();
					AccountDb.BanChatAccount(AccountID);
					//BanAccount(52, AccountID, "gui van ban bo loc");
					return;
				}
				if (item.Sffh != 3)
				{
					continue;
				}
				return;
			}
			num = 10;
			if (!ParseCommand(text))
			{
				SendAMessage(text, msgType, name);
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "Chat lỗi tại num:[" + num + "] [" + AccountID + "]-[" + CharacterName + "] - " + ex2.Message);
		}
	}

	public void SendAMessage(string msg, int msgType, string name)
	{
		try
		{
			if (DateTime.Now.Subtract(SendMessageTime).TotalSeconds < 1.0)
			{
				HeThongNhacNho("Đại hiệp hãy chậm lại, đừng quá vội vàng!!");
				return;
			}
			if (World.BannedList.TryGetValue(CharacterName, out var _))
			{
				HeThongNhacNho("Đại hiệp đã bị Thiên Cơ Các cấm ngôn!", 7, "Thiên cơ các");
				return;
			}
			if (msg.Contains(World.SuTuHong_Lenh))
			{
				msg = msg.Replace(World.SuTuHong_Lenh, "").TrimStart().TrimEnd();
				msgType = 204;
			}
			if (msgType != 4 && msgType != 3)
			{
				LogHelper.WriteLine(LogLevel.Debug, "[" + AccountID + "][" + CharacterName + "][" + GuildName + "][" + Player_Level + "][" + Player_Job + "](1C-2T)[" + Player_Zx + "] - (Kênh: " + World.ServerID + ") : " + msg);
			}
			SendMessageTime = DateTime.Now;
			var array = Converter.HexStringToByte("AA55A50000006600970000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			if (GMMode != 0)
			{
				array[10] = 22;
			}
			else if (FLD_RXPIONT == 100)
			{
				array[10] = 10;
			}
			else
			{
				array[10] = (byte)msgType;
			}
			if (name == null || name == "")
			{
				name = CharacterName;
			}
			var bytes = Encoding.Default.GetBytes(name);
			var bytes2 = Encoding.Default.GetBytes(msg);
			Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			var num = 0;
			switch (msgType)
			{
				case 204:
					{
						if (DateTime.Now.Subtract(LionRoarTime).TotalSeconds < 30.0)
						{
							HeThongNhacNho("Hãy chờ [" + (30 - (int)DateTime.Now.Subtract(LionRoarTime).TotalSeconds) + "] giây trước khi tái thi triển!");
							var num2 = 0;
							for (var i = 0; i < Item_In_Bag.Length; i++)
							{
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000028)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000027)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000026)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000025)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000041)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000042)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000047)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000048)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000029)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
								if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1007000030)
								{
									VatPham_GiamDi_SoLuong_DoBen(i, num2);
									if (num2 > 0)
									{
										num2 = 0;
									}
								}
							}
							break;
						}
						LionRoarTime = DateTime.Now;
						var num3 = 1;
						for (var j = 0; j < Item_In_Bag.Length; j++)
						{
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000028)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 204;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000027)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 204;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000026)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 204;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000025)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 41;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000041)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 41;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000042)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 41;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000047)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 38;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000048)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 38;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000029)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 35;
							}
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1007000030)
							{
								VatPham_GiamDi_SoLuong_DoBen(j, num3);
								if (num3 > 0)
								{
									num3 = 0;
								}
								num = 35;
							}
						}
						if (num == 0)
						{
							HeThongNhacNho("Đại hiệp không mang Sư Tử Hống trong hành trang!!");
							break;
						}
						HeThongNhacNho("Hàng chờ đang vận hành, đại hiệp hãy kiên nhẫn chờ một lát!");
						World.conn.Transmit(string.Concat("LION_ROARX|", SessionID, "|", CharacterName, "|", " (K阯h " + World.ServerID + ") " + msg, "|", Client.ToString(), "|", World.ServerID, "|", MapID + "|" + num));
						break;
					}
				case 14:
					{
						if (DateTime.Now.Subtract(LionRoarTime).TotalSeconds < 30.0)
						{
							HeThongNhacNho("Hãy chờ [" + (30 - (int)DateTime.Now.Subtract(LionRoarTime).TotalSeconds) + "] giây trước khi tái thi triển!", 20, "Thiên cơ các");
							var num4 = 0;
							for (var k = 0; k < Item_In_Bag.Length; k++)
							{
								if (BitConverter.ToInt32(Item_In_Bag[k].VatPham_ID, 0) == 1007000001)
								{
									VatPham_GiamDi_SoLuong_DoBen(k, num4);
									if (num4 > 0)
									{
										num4 = 0;
									}
								}
							}
							break;
						}
						LionRoarTime = DateTime.Now;
						var num5 = 1;
						for (var l = 0; l < Item_In_Bag.Length; l++)
						{
							if (BitConverter.ToInt32(Item_In_Bag[l].VatPham_ID, 0) == 1007000001)
							{
								VatPham_GiamDi_SoLuong_DoBen(l, num5);
								if (num5 > 0)
								{
									num5 = 0;
								}
							}
						}
						if (World.SuTuHongList.Count < World.SuTuHong_Max_SoLuong)
						{
							World.SuTuHongList.Enqueue(new X_Su_Tu_Hong_Class
							{
								FLD_INDEX = SessionID,
								UserName = CharacterName,
								TxtId = msgType,
								Txt = msg
							});
							if (World.SuTuHong_ID >= 127)
							{
								World.SuTuHong_ID = 0;
							}
							else
							{
								World.SuTuHong_ID++;
							}
							HeThongNhacNho("Hàng chờ đang vận hành, đại hiệp hãy kiên nhẫn chờ một lát!");
						}
						else
						{
							HeThongNhacNho("Hàng chờ đang vận hành, đại hiệp hãy kiên nhẫn chờ một lát!");
						}
						break;
					}
				case 0:
					if (GiaoDich != null && GiaoDich.GiaoDichBenTrong)
					{
						array[10] = 11;
						Client?.Send_Map_Data(array, array.Length);

						GiaoDich.NguoiGiaoDich.Client?.Send_Map_Data(array, array.Length);
					}
					else
					{
						Client?.Send_Map_Data(array, array.Length);
						SendCurrentRangeBroadcastData(array, array.Length);
					}
					break;
				case 1:
					Client?.Send_Map_Data(array, array.Length);
					SendAShoutMessageToBroadcastData(array, array.Length);
					break;
				case 2:
					{
						if (World.WToDoi.TryGetValue(TeamID, out var value2))
						{
							SendGroupMessageBroadcastData(array, array.Length, value2.PartyPlayers);
						}
						break;
					}
				case 3:
					if (GuildName.Length != 0)
					{
						SendGangMessage(GuildName, array, array.Length);
						World.conn.Transmit("ChatGuild|" + SessionID + "|" + CharacterName + "|" + msg + "|" + Client.ToString() + "|" + World.ServerID + "|" + GuildName);
						LogHelper.WriteLine(LogLevel.Debug, "[" + AccountID + "][" + CharacterName + "][" + GuildName + "][" + Player_Level + "][" + Player_Job + "](1C-2T)[" + Player_Zx + "] - (Kênh: " + World.ServerID + ") : " + msg);
					}
					break;
				case 4:
					{
						if (GetCharacterData(name) == null)
						{
							HeThongNhacNho("Danh tính hiệp khách không tồn tại hoặc đã thăng thiên thoát tục!");
							break;
						}

						Client?.Send_Map_Data(array, array.Length);
						var text = Converter.ToString(array);
						var characterFullServerId = SessionID;
						World.conn.Transmit("TransmissionMessage|" + characterFullServerId + "|" + CharacterName + "|" + name + " | " + msg + "|" + msgType + "|" + text);
						LogHelper.WriteLine(LogLevel.Debug, "[" + AccountID + "][" + CharacterName + "][" + GuildName + "][" + Player_Level + "][" + Player_Job + "](1C-2T)[" + Player_Zx + "] - PM:[" + name + "] (Kênh: " + World.ServerID + ") : " + msg);
						break;
					}
				case 32:
					if (World.ChatAllServer_OnOff == 0)
					{
						break;
					}
					foreach (var value3 in World.allConnectedChars.Values)
					{
						if (value3.Client != null && value3.OnOffChatAll == 1)
						{
							value3.Client.Send(array, array.Length);
						}
					}
					World.conn.Transmit("ChatAll|" + SessionID + "|" + CharacterName + "|" + msg + "|" + Client.ToString() + "|" + World.ServerID + "|" + GuildName);
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Gửi thông tin Chat lỗi ! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}


}
