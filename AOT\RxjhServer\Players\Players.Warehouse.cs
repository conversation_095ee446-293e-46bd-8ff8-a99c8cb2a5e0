﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public int GetAVacancyInAPersonalWarehouse(int iii)
	{
		var num = 0;
		while (true)
		{
			if (num < 60)
			{
				if (iii == 3)
				{
					if (BitConverter.ToInt32(PersonalWarehouse[num].VatPham_ID, 0) == 0)
					{
						return num;
					}
				}
				else if (BitConverter.ToInt32(PublicWarehouse[num].VatPham_ID, 0) == 0)
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num;
	}

	public void WarehouseAccess(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			if (GiaoDich != null && GiaoDich.GiaoDichBenTrong)
			{
				return;
			}
			if (!OpenWarehouse)
			{
				LogHelper.WriteLine(LogLevel.Debug, "Warehouse存取  BUG![" + AccountID + "]-[" + CharacterName + "]");
			}
			else
			{
				if (InTheShop || Exiting || PlayerTuVong)
				{
					return;
				}
				var unknow = new byte[4];
				var targetItem = new byte[8];
				var gold_array = new byte[8];
				var from_pos = new byte[4];
				var target_bag = new byte[4];

				var num = 1000;
				var array5 = new byte[2];
				Buffer.BlockCopy(packetData, 8, array5, 0, 2);
				if (BitConverter.ToInt16(array5, 0) <= 108)
				{
					Buffer.BlockCopy(packetData, 10, target_bag, 0, 2);
					Buffer.BlockCopy(packetData, 18, unknow, 0, 4);
					Buffer.BlockCopy(packetData, 22, gold_array, 0, 8);
					Buffer.BlockCopy(packetData, 34, targetItem, 0, 8);
					Buffer.BlockCopy(packetData, 56, from_pos, 0, 1);
				}
				else
				{
					Buffer.BlockCopy(packetData, 14, target_bag, 0, 2);
					Buffer.BlockCopy(packetData, 22, unknow, 0, 4);
					Buffer.BlockCopy(packetData, 30, gold_array, 0, 8);
					Buffer.BlockCopy(packetData, 46, targetItem, 0, 8);
					Buffer.BlockCopy(packetData, 72, from_pos, 0, 1);
				}
				var bag_case = BitConverter.ToInt32(target_bag, 0);
				var VatPham_ID = BitConverter.ToInt32(unknow, 0);
				var index_bar = BitConverter.ToInt32(from_pos, 0);
				var gold = BitConverter.ToInt64(gold_array, 0);
				HeThongNhacNho($" WarehouseAccess target_bag: {bag_case} {index_bar} {VatPham_ID}");
				if (gold < 1)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Người chơi đã gửi số tiền bị Âm, cần kiểm tra lại [" + AccountID + "]-[" + CharacterName + "]___[" + bag_case + "]___[" + VatPham_ID + "]___[" + index_bar + "]___[" + gold + "]");
				}
				else
				{
					if ((uint)(bag_case - 5) <= 1u)
					{
						num = ((NhanVatThienVaAc <= -1 && NhanVatThienVaAc > -500) ? (num + num * 2) : ((NhanVatThienVaAc <= -500 && NhanVatThienVaAc > -5000) ? (num + num * 4) : ((NhanVatThienVaAc <= -5000 && NhanVatThienVaAc > -10000) ? (num * 8) : ((NhanVatThienVaAc > -10000 || NhanVatThienVaAc <= -30000) ? num : (num * 16)))));
						if (Player_Money < num)
						{
							HeThongNhacNho("Đại hiệp cần [" + num + "] ngân lượng để lấy bảo vật từ kho ra ngoài!", 20, "Thiên cơ các");
							PurchaseItemReminder(13);
							return;
						}
						Player_Money -= num;
						UpdateMoneyAndWeight();
					}
					switch (bag_case)
					{
						case 3:
							{
								var aVacancyInAPersonalWarehouse2 = GetAVacancyInAPersonalWarehouse(3);
								if (aVacancyInAPersonalWarehouse2 != -1)
								{
									Warehouse_equipmentBarDeductedItems(VatPham_ID, index_bar, gold, aVacancyInAPersonalWarehouse2, 3);
								}
								else
								{
									PurchaseItemReminder(14);
								}
								break;
							}
						case 4:
							if (Item_In_Bag[index_bar].VatPham_KhoaLai)
							{
								var array6 = Converter.HexStringToByte("AA5556000A0093004800040000000A0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000077A755AA");
								Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array6, 4, 2);
								Client?.Send_Map_Data(array6, array6.Length);
							}
							else
							{
								var aVacancyInAPersonalWarehouse4 = GetAVacancyInAPersonalWarehouse(4);
								if (aVacancyInAPersonalWarehouse4 != -1)
								{
									Warehouse_equipmentBarDeductedItems(VatPham_ID, index_bar, gold, aVacancyInAPersonalWarehouse4, 4);
								}
								else
								{
									PurchaseItemReminder(14);
								}
							}
							break;
						case 5:
							{
								var parcelVacancy = GetParcelVacancy(this);
								if (parcelVacancy != -1)
								{
									Warehouse_equipmentColumnPlusItems(VatPham_ID, index_bar, gold, parcelVacancy, 5, num);
								}
								else
								{
									PurchaseItemReminder(14);
								}
								break;
							}
						case 6:
							{
								var parcelVacancy2 = GetParcelVacancy(this);
								if (parcelVacancy2 != -1)
								{
									Warehouse_equipmentColumnPlusItems(VatPham_ID, index_bar, gold, parcelVacancy2, 6, num);
								}
								else
								{
									PurchaseItemReminder(14);
								}
								break;
							}
						case 8:
							{
								var aVacancyInAPersonalWarehouse3 = GetAVacancyInAPersonalWarehouse(3);
								if (aVacancyInAPersonalWarehouse3 == -1)
								{
									PurchaseItemReminder(14);
								}
								break;
							}
						case 9:
							{
								var aVacancyInAPersonalWarehouse = GetAVacancyInAPersonalWarehouse(3);
								if (aVacancyInAPersonalWarehouse == -1)
								{
									PurchaseItemReminder(14);
								}
								break;
							}
					}
				}
				SaveCharacterData();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Warehouse tới gần error [" + AccountID + "][" + CharacterName + "]  " + ex.Message);
		}
	}

	public void Warehouse_equipmentColumnPlusOrMinusMoney(int vatPhamId, long soLuong, int cklx, int warehouseType)
	{
		try
		{
			var array = Converter.HexStringToByte("AA558600560395007800030000000300000000943577000000006E0000000000000000000000000000000000000000000000000000000000000000000000000000000**********000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000E76C55AA");
			Buffer.BlockCopy(BitConverter.GetBytes(warehouseType), 0, array, 10, 1);
			Buffer.BlockCopy(BitConverter.GetBytes(warehouseType), 0, array, 14, 1);
			Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 26, 8);
			Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array, 18, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array, 50, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi check kho đồ 111 !!!");
		}
	}

	public void WarehousePackageItemOperation(int packageId, int thaoTacType, int vatPhamId, long vatPhamSoLuong, Item vatPham, int position, int phiHaoTon)
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write4(thaoTacType);
			sendingClass.Write4(thaoTacType);
			sendingClass.Write8(vatPhamId);
			sendingClass.Write8(vatPhamSoLuong);
			sendingClass.Write8(phiHaoTon);
			sendingClass.Write(vatPham.GetItemGlobal_ID);
			sendingClass.Write(vatPham.GetVatPham_ID);
			sendingClass.Write8(vatPham.GetVatPhamSoLuong);
			sendingClass.Write2(1);
			sendingClass.Write(position);
			sendingClass.Write2(0);
			sendingClass.Write(3);
			sendingClass.Write2(1);
			sendingClass.Write4(vatPham.FLD_MAGIC0);
			sendingClass.Write4(vatPham.FLD_MAGIC1);
			sendingClass.Write4(vatPham.FLD_MAGIC2);
			sendingClass.Write4(vatPham.FLD_MAGIC3);
			sendingClass.Write4(vatPham.FLD_MAGIC4);
			sendingClass.Write2(vatPham.FLD_FJ_MAGIC0);
			sendingClass.Write2(vatPham.FLD_FJ_MAGIC1);
			sendingClass.Write2(vatPham.FLD_FJ_TrungCapPhuHon);
			sendingClass.Write2(vatPham.FLD_FJ_MAGIC2);
			sendingClass.Write2(vatPham.FLD_FJ_MAGIC3);
			sendingClass.Write2(vatPham.FLD_FJ_MAGIC4);
			sendingClass.Write2(vatPham.FLD_FJ_MAGIC5);
			sendingClass.Write2(0);
			sendingClass.Write4(vatPham.FLD_DAY1);
			sendingClass.Write4(vatPham.FLD_DAY2);
			sendingClass.Write2(vatPham.FLD_FJ_NJ);
			sendingClass.Write4(vatPham.FLD_FJ_LowSoul);
			sendingClass.Write2(0);
			sendingClass.Write2(vatPham.FLD_FJ_TienHoa);
			sendingClass.Write2(0);
			sendingClass.Write4(vatPham.FLD_TuLinh);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			Client?.SendPak(sendingClass, packageId, SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi check hiển thị item trong kho đồ !!!");
		}
	}

	public void Warehouse_equipmentColumnPlusItems(int vatPhamId, int position, long soLuong, int packagePosition, int warehouseType, int phiHaoTon)
	{
		var num = 0;
		var num2 = 0;
		try
		{
			if (vatPhamId == **********)
			{
				if (World.CoHayKo_Lock_RutTien_Warehouse != 0)
				{
					HeThongNhacNho("Máy chủ đã phong ấn chức năng rút ngân lượng!", 20, "Thiên cơ các");
					return;
				}
				if (Player_Money + soLuong > 9999999900L)
				{
					return;
				}
				if (warehouseType == 5)
				{
					if (PersonalWarehouseMoney < soLuong)
					{
						LogHelper.WriteLine(LogLevel.Debug, "Rút tiền số lượng bị Âm - cần kiểm tra gấp - 111 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]___[" + vatPhamId + "]___[" + position + "]___[" + soLuong + "]");
						return;
					}
					Player_Money += soLuong;
					PersonalWarehouseMoney -= soLuong;
					Warehouse_equipmentColumnPlusOrMinusMoney(vatPhamId, PersonalWarehouseMoney, 149, warehouseType);
				}
				else
				{
					if (ComprehensiveWarehouseMoney < soLuong)
					{
						LogHelper.WriteLine(LogLevel.Debug, "Rút tiền số lượng bị Âm - cần kiểm tra gấp - 222 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]___[" + vatPhamId + "]___[" + position + "]___[" + soLuong + "]");
						return;
					}
					Player_Money += soLuong;
					ComprehensiveWarehouseMoney -= soLuong;
					Warehouse_equipmentColumnPlusOrMinusMoney(vatPhamId, ComprehensiveWarehouseMoney, 149, warehouseType);
				}
			}
			else
			{
				if (soLuong < 1 || soLuong > 9999)
				{
					return;
				}
				Item xVatPhamLoai;
				if (warehouseType == 5)
				{
					xVatPhamLoai = PersonalWarehouse[position];
					for (var i = 0; i < 60; i++)
					{
						if (BitConverter.ToInt32(PersonalWarehouse[i].VatPhamSoLuong, 0) > 0 || BitConverter.ToInt32(PersonalWarehouse[i].VatPham_ID, 0) != 0)
						{
							num++;
						}
					}
				}
				else
				{
					xVatPhamLoai = PublicWarehouse[position];
					for (var j = 0; j < 60; j++)
					{
						if (BitConverter.ToInt32(PublicWarehouse[j].VatPhamSoLuong, 0) > 0 || BitConverter.ToInt32(PublicWarehouse[j].VatPham_ID, 0) != 0)
						{
							num++;
						}
					}
				}
				for (var k = 0; k < 66; k++)
				{
					if (BitConverter.ToInt32(Item_In_Bag[k].VatPhamSoLuong, 0) > 0 || BitConverter.ToInt32(Item_In_Bag[k].VatPham_ID, 0) != 0)
					{
						num2++;
					}
				}
				if (!World.ItemList.TryGetValue(BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0), out var value) && !xVatPhamLoai.VatPham_KhoaLai)
				{
					return;
				}
				var fLdSide = value.FLD_SIDE;
				if (fLdSide == 0 && soLuong > 1)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp Warehouse Truy cập tìm nạp Warehouse 33 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]  VatPhamTen称[" + xVatPhamLoai.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) + "]  SoLuong[" + soLuong + "]");
					if (warehouseType == 5)
					{
						PersonalWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else
					{
						PublicWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					return;
				}
				if (BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) < soLuong)
				{
					if (warehouseType == 5)
					{
						PersonalWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else
					{
						PublicWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp Warehouse Truy cập tìm nạp Warehouse 44 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]  VatPhamTen称[" + xVatPhamLoai.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) + "]  SoLuong[" + soLuong + "]");
					return;
				}
				if (fLdSide == 0)
				{
					soLuong = 1L;
					if ((long)BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) != 1)
					{
						return;
					}
					if (xVatPhamLoai.VatPham_TongTrongLuong + CharacterCurrentWeight >= TheTotalWeightOfTheCharacter)
					{
						PurchaseItemReminder(11);
						return;
					}
					if (vatPhamId != xVatPhamLoai.GetVatPham_ID && !xVatPhamLoai.VatPham_KhoaLai)
					{
						return;
					}
					CharacterCurrentWeight += xVatPhamLoai.VatPham_TongTrongLuong;
					var array = new byte[World.Item_Db_Byte_Length];
					var itemGlobalId = xVatPhamLoai.ItemGlobal_ID;
					Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 12, 4);
					var dst = array;
					Buffer.BlockCopy(itemGlobalId, 0, dst, 0, 8);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_ID, 0, array, 8, 4);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_ThuocTinh, 0, array, 16, World.VatPham_ThuocTinh_KichThuoc);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_byte, 72, array, 72, 1);
					Item_In_Bag[packagePosition].VatPham_byte = array;
					WarehousePackageItemOperation(37632, warehouseType, vatPhamId, soLuong, Item_In_Bag[packagePosition], packagePosition, phiHaoTon);
					if (warehouseType == 5)
					{
						if (PersonalWarehouse[position].VatPham_KhoaLai)
						{
							vatPhamId -= 20000;
						}
						WarehousePackageItemOperation(38144, warehouseType, vatPhamId, 0L, PersonalWarehouse[position], position, phiHaoTon);
						PersonalWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else
					{
						WarehousePackageItemOperation(38144, warehouseType, vatPhamId, 0L, PublicWarehouse[position], position, phiHaoTon);
						PublicWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				else if (BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) >= soLuong)
				{
					var array2 = new byte[World.Item_Db_Byte_Length];
					var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0), xVatPhamLoai.FLD_MAGIC0);
					byte[] src;
					if (characterItemType != null)
					{
						if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != ********** && BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != **********)
						{
							src = characterItemType.ItemGlobal_ID;
							packagePosition = characterItemType.VatPhamViTri;
							Buffer.BlockCopy(BitConverter.GetBytes(soLuong + BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0)), 0, array2, 12, 4);
						}
						else
						{
							src = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
							BitConverter.GetBytes(soLuong);
							Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array2, 12, 4);
						}
					}
					else
					{
						BitConverter.GetBytes(soLuong);
						src = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
						Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array2, 12, 4);
					}
					Buffer.BlockCopy(src, 0, array2, 0, 8);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_ID, 0, array2, 8, 4);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_ThuocTinh, 0, array2, 16, World.VatPham_ThuocTinh_KichThuoc);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_byte, 72, array2, 72, 1);
					if (xVatPhamLoai.VatPham_TrongLuong1Cai * soLuong + CharacterCurrentWeight >= TheTotalWeightOfTheCharacter)
					{
						PurchaseItemReminder(11);
						return;
					}
					Item_In_Bag[packagePosition].VatPham_byte = array2;
					var num3 = BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) - (int)soLuong;
					WarehousePackageItemOperation(37632, warehouseType, vatPhamId, soLuong, Item_In_Bag[packagePosition], packagePosition, phiHaoTon);
					if (warehouseType == 5)
					{
						if (num3 <= 0)
						{
							WarehousePackageItemOperation(38144, warehouseType, vatPhamId, 0L, PersonalWarehouse[position], position, phiHaoTon);
							PersonalWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
						else
						{
							PersonalWarehouse[position].VatPhamSoLuong = BitConverter.GetBytes(num3);
							WarehousePackageItemOperation(38144, warehouseType, vatPhamId, num3, PersonalWarehouse[position], position, phiHaoTon);
						}
					}
					else if (num3 <= 0)
					{
						WarehousePackageItemOperation(38144, warehouseType, vatPhamId, 0L, PublicWarehouse[position], position, phiHaoTon);
						PublicWarehouse[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else
					{
						PublicWarehouse[position].VatPhamSoLuong = BitConverter.GetBytes(num3);
						WarehousePackageItemOperation(38144, warehouseType, vatPhamId, num3, PublicWarehouse[position], position, phiHaoTon);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Warehouse lỗi !! [" + AccountID + "][" + CharacterName + "]Position[" + position + "] SL:[" + soLuong + "]PackagePosition[" + packagePosition + "]WarehouseType[" + warehouseType + "]  " + ex.Message);
		}
		UpdateMoneyAndWeight();
	}

	public void Warehouse_equipmentBarDeductedItems(int vatPhamId, int position, long soLuong, int warehousePosition, int warehouseType)
	{
		var num = 0;
		var num2 = 0;
		try
		{
			if (vatPhamId == **********)
			{
				if (World.CoHayKo_Lock_GuiTien_Warehouse != 0)
				{
					HeThongNhacNho("Máy chủ đã phong ấn chức năng gửi ngân lượng!", 20, "Thiên cơ các");
					return;
				}
				if (Player_Money < soLuong)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Lỗi khi gửi Gold vào kho bị Âm - Cần check - 111 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]___[" + vatPhamId + "]___[" + position + "]___[" + soLuong + "]");
					return;
				}
				Player_Money -= soLuong;
				if (warehouseType == 3)
				{
					PersonalWarehouseMoney += soLuong;
					Warehouse_equipmentColumnPlusOrMinusMoney(vatPhamId, PersonalWarehouseMoney, 149, warehouseType);
				}
				else
				{
					ComprehensiveWarehouseMoney += soLuong;
					Warehouse_equipmentColumnPlusOrMinusMoney(vatPhamId, ComprehensiveWarehouseMoney, 149, warehouseType);
				}
			}
			else
			{
				if (soLuong < 1 || Item_In_Bag[position].Lock_Move)
				{
					return;
				}
				var xVatPhamLoai = Item_In_Bag[position];
				if (!World.ItemList.TryGetValue(BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0), out var value))
				{
					return;
				}
				if (xVatPhamLoai.VatPham_KhoaLai)
				{
					HeThongNhacNho("Bảo vật dành cho tân thủ, không thể cất vào kho!", 10, "Thiên cơ các");
					return;
				}
				if (value.FLD_LOCK == 1)
				{
					HeThongNhacNho("Không thể cất bảo vật vào kho!", 10, "Thiên cơ các");
					return;
				}
				var fLdSide = value.FLD_SIDE;
				for (var i = 0; i < 96; i++)
				{
					if (BitConverter.ToInt32(Item_In_Bag[i].VatPhamSoLuong, 0) > 0 || BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) != 0)
					{
						num++;
					}
				}
				if (warehouseType == 3)
				{
					for (var j = 0; j < 60; j++)
					{
						if (BitConverter.ToInt32(PersonalWarehouse[j].VatPhamSoLuong, 0) > 0 || BitConverter.ToInt32(PersonalWarehouse[j].VatPham_ID, 0) != 0)
						{
							num2++;
						}
					}
				}
				else
				{
					for (var k = 0; k < 60; k++)
					{
						if (BitConverter.ToInt32(PublicWarehouse[k].VatPhamSoLuong, 0) > 0 || BitConverter.ToInt32(PublicWarehouse[k].VatPham_ID, 0) != 0)
						{
							num2++;
						}
					}
				}
				if (fLdSide == 0)
				{
					if (soLuong > 1)
					{
						LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp Warehouse Đặt mọi thứ vào Warehouse 33 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]  VatPhamTen称[" + xVatPhamLoai.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) + "]  SoLuong[" + soLuong + "]");
						var text = "===[Loai 1]===[" + position + "][" + BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) + "][" + warehouseType + "][" + xVatPhamLoai.GetItemName() + "][" + soLuong + "]";
						// logo.Log_Item_Cat_Kho_Bi_Mat(text, UserName);
						SubtractItem(position, BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0));
						return;
					}
					if (BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) == soLuong)
					{
						CharacterCurrentWeight -= xVatPhamLoai.VatPham_TongTrongLuong;
						var array = new byte[World.Item_Db_Byte_Length];
						var itemGlobalId = xVatPhamLoai.ItemGlobal_ID;
						Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 12, 4);
						var dst = array;
						Buffer.BlockCopy(itemGlobalId, 0, dst, 0, 8);
						Buffer.BlockCopy(xVatPhamLoai.VatPham_ID, 0, array, 8, 4);
						Buffer.BlockCopy(xVatPhamLoai.VatPham_ThuocTinh, 0, array, 16, World.VatPham_ThuocTinh_KichThuoc);
						Buffer.BlockCopy(xVatPhamLoai.VatPham_byte, 74, array, 74, 1);
						WarehousePackageItemOperation(37632, warehouseType, vatPhamId, soLuong, Item_In_Bag[position], position, 0);
						if (warehouseType == 3)
						{
							PersonalWarehouse[warehousePosition].VatPham_byte = array;
							WarehousePackageItemOperation(38144, warehouseType, vatPhamId, soLuong, PersonalWarehouse[warehousePosition], warehousePosition, 0);
						}
						else
						{
							PublicWarehouse[warehousePosition].VatPham_byte = array;
							WarehousePackageItemOperation(38144, warehouseType, vatPhamId, soLuong, PublicWarehouse[warehousePosition], warehousePosition, 0);
						}
						Item_In_Bag[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					else
					{
						SubtractItem(position, BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0));
						var text2 = "===[Loai 2]===[" + position + "][" + BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) + "][" + warehouseType + "][" + xVatPhamLoai.GetItemName() + "][" + soLuong + "]";
						// logo.Log_Item_Cat_Kho_Bi_Mat(text2, UserName);
					}
				}
				else
				{
					if (BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) < soLuong)
					{
						LogHelper.WriteLine(LogLevel.Debug, "Sao chép bất hợp pháp Warehouse Đặt mọi thứ vào Warehouse 22 [" + AccountID + "]-[" + CharacterName + "]___[" + warehouseType + "]  VatPhamTen称[" + xVatPhamLoai.GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) + "]  SoLuong[" + soLuong + "]");
						return;
					}
					var num3 = BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) - (int)soLuong;
					CharacterCurrentWeight -= xVatPhamLoai.VatPham_TongTrongLuong;
					var array2 = new byte[World.Item_Db_Byte_Length];
					var characterPublicwarehouseType = GetCharacterPublicwarehouseType(BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0), warehouseType, xVatPhamLoai.FLD_MAGIC0);
					byte[] src;
					if (characterPublicwarehouseType != null)
					{
						if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != ********** && BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != **********)
						{
							src = characterPublicwarehouseType.ItemGlobal_ID;
							warehousePosition = characterPublicwarehouseType.VatPhamViTri;
							Buffer.BlockCopy(BitConverter.GetBytes(soLuong + BitConverter.ToInt32(characterPublicwarehouseType.VatPhamSoLuong, 0)), 0, array2, 12, 4);
						}
						else
						{
							src = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
							BitConverter.GetBytes(soLuong);
							Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array2, 12, 4);
						}
					}
					else
					{
						BitConverter.GetBytes(soLuong);
						src = ((num3 > 0) ? BitConverter.GetBytes(RxjhClass.CreateItemSeries()) : xVatPhamLoai.ItemGlobal_ID);
						Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array2, 12, 4);
					}
					Buffer.BlockCopy(src, 0, array2, 0, 8);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_ID, 0, array2, 8, 4);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_ThuocTinh, 0, array2, 16, World.VatPham_ThuocTinh_KichThuoc);
					Buffer.BlockCopy(xVatPhamLoai.VatPham_byte, 72, array2, 72, 1);
					if (num3 <= 0)
					{
						Item_In_Bag[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						WarehousePackageItemOperation(37632, warehouseType, vatPhamId, soLuong, Item_In_Bag[position], position, 0);
					}
					else
					{
						Item_In_Bag[position].VatPhamSoLuong = BitConverter.GetBytes(num3);
						WarehousePackageItemOperation(37632, warehouseType, vatPhamId, soLuong, Item_In_Bag[position], position, 0);
					}
					if (warehouseType == 3)
					{
						PersonalWarehouse[warehousePosition].VatPham_byte = array2;
						WarehousePackageItemOperation(38144, warehouseType, vatPhamId, PersonalWarehouse[warehousePosition].GetVatPhamSoLuong, PersonalWarehouse[warehousePosition], warehousePosition, 0);
					}
					else
					{
						PublicWarehouse[warehousePosition].VatPham_byte = array2;
						WarehousePackageItemOperation(38144, warehouseType, vatPhamId, PublicWarehouse[warehousePosition].GetVatPhamSoLuong, PublicWarehouse[warehousePosition], warehousePosition, 0);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Warehouse Giảm thanh thiết bị VatPham error [" + AccountID + "][" + CharacterName + "]Position[" + position + "]SoLuong[" + soLuong + "]WarehousePosition[" + warehousePosition + "]WarehouseType[" + warehouseType + "]  " + ex.Message);
		}
		UpdateMoneyAndWeight();
	}

	public void OpenPersonalWarehouse()
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write4(5);
			sendingClass.Write4(5);
			sendingClass.Write4(8);
			for (var i = 0; i < 60; i++)
			{
				if (BitConverter.ToInt32(PersonalWarehouse[i].VatPhamSoLuong, 0) == 0)
				{
					PersonalWarehouse[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					KiemTraVatPhamHeThong("PersonalWarehouse", ref PersonalWarehouse[i]);
				}
				if (PersonalWarehouse[i].FLD_FJ_TrungCapPhuHon <= 22 && PersonalWarehouse[i].FLD_FJ_TrungCapPhuHon >= 21 && PersonalWarehouse[i].FLD_FJ_LowSoul > 0)
				{
					PersonalWarehouse[i].Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = PersonalWarehouse[i].FLD_FJ_TrungCapPhuHon - 20;
				}
				sendingClass.Write(PersonalWarehouse[i].GetByte(), 0, World.Item_Byte_Length_92);
			}
			sendingClass.Write8(PersonalWarehouseMoney);
			Client?.SendPak(sendingClass, 38400, SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Personal Warehouse error [" + AccountID + "][" + CharacterName + "]      " + ex.Message);
		}
	}

	public void OpenTheComprehensiveWarehouse()
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write4(5);
			sendingClass.Write4(5);
			sendingClass.Write4(9);
			for (var i = 0; i < 60; i++)
			{
				if (BitConverter.ToInt32(PublicWarehouse[i].VatPhamSoLuong, 0) == 0)
				{
					PublicWarehouse[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					KiemTraVatPhamHeThong("PublicWarehouse", ref PublicWarehouse[i]);
				}
				if (PublicWarehouse[i].FLD_FJ_TrungCapPhuHon <= 22 && PublicWarehouse[i].FLD_FJ_TrungCapPhuHon >= 21 && PublicWarehouse[i].FLD_FJ_LowSoul > 0)
				{
					PublicWarehouse[i].Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = PublicWarehouse[i].FLD_FJ_TrungCapPhuHon - 20;
				}
				sendingClass.Write(PublicWarehouse[i].GetByte(), 0, World.Item_Byte_Length_92);
			}
			sendingClass.Write8(ComprehensiveWarehouseMoney);
			Client?.SendPak(sendingClass, 38400, SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở Comprehensive Warehouse error [" + AccountID + "][" + CharacterName + "]      " + ex.Message);
		}
	}

}
