﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class itmeclss {

		[JsonProperty, Column(IsPrimary = true)]
		public int id { get; set; }

		[JsonProperty]
		public int? fld_type { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_reside { get; set; }

		[JsonProperty]
		public int? fld_magic0 { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_magic5 { get; set; }

		[JsonProperty]
		public int? fld_fj_nj { get; set; }

		[JsonProperty]
		public int? fld_days { get; set; }

		[JsonProperty]
		public int? fld_fj_thuctinh { get; set; }

		[JsonProperty]
		public int? fld_fj_trungcapphuhon { get; set; }

		[JsonProperty]
		public int? fld_fj_tienhoa { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_sql { get; set; }

		[JsonProperty]
		public int? fld_bd { get; set; }

	}

}
