-- =====================================================
-- TEST ATTENDANCE 14 DAY FUNCTIONALITY
-- Script để test các tính năng attendance 14 ngày
-- =====================================================

-- 1. Test function get_days_since_last_login
SELECT 
    'Testing get_days_since_last_login function' as test_name,
    get_days_since_last_login('TestPlayer') as days_since_login;

-- 2. Test function is_eligible_for_comeback_attendance
SELECT 
    'Testing is_eligible_for_comeback_attendance function' as test_name,
    is_eligible_for_comeback_attendance('TestPlayer', 14) as is_eligible;

-- 3. Test function get_active_attendance_for_player
SELECT 
    'Testing get_active_attendance_for_player function' as test_name,
    * 
FROM get_active_attendance_for_player('TestPlayer');

-- 4. <PERSON><PERSON><PERSON> tra attendance templates đã tạo
SELECT 
    'Checking attendance templates' as test_name,
    id,
    name,
    attendance_type,
    eligible_after_days,
    is_active,
    start_date,
    end_date
FROM tbl_attendance_templates
WHERE attendance_type IN ('normal', 'comeback_14day')
ORDER BY attendance_type, created_date DESC;

-- 5. Kiểm tra rewards cho attendance 14 ngày
SELECT 
    'Checking comeback 14-day rewards' as test_name,
    t.name as template_name,
    r.day_number,
    r.item_id,
    r.item_amount,
    '0x' || UPPER(TO_HEX(r.item_id)) as item_id_hex
FROM tbl_attendance_templates t
JOIN tbl_attendance_rewards r ON t.id = r.attendance_id
WHERE t.attendance_type = 'comeback_14day'
ORDER BY r.day_number;

-- 6. Tạo test data - player đã lâu không login
INSERT INTO loginrecord (userid, username, userip, loaihinh, thoigian, mac_address)
VALUES 
    ('test001', 'OldPlayer', '127.0.0.1', 'Login', CURRENT_TIMESTAMP - INTERVAL '20 days', '00:00:00:00:00:00'),
    ('test002', 'RecentPlayer', '127.0.0.1', 'Login', CURRENT_TIMESTAMP - INTERVAL '5 days', '00:00:00:00:00:00'),
    ('test003', 'VeryOldPlayer', '127.0.0.1', 'Login', CURRENT_TIMESTAMP - INTERVAL '30 days', '00:00:00:00:00:00')
ON CONFLICT DO NOTHING;

-- 7. Test eligibility cho các player test
SELECT 
    'Testing player eligibility' as test_name,
    username,
    get_days_since_last_login(username) as days_since_login,
    is_eligible_for_comeback_attendance(username, 14) as eligible_for_comeback
FROM (
    VALUES 
        ('OldPlayer'),
        ('RecentPlayer'), 
        ('VeryOldPlayer'),
        ('NonExistentPlayer')
) AS test_players(username);

-- 8. Test get_active_attendance_for_player cho các player khác nhau
SELECT 
    'Testing active attendance for different players' as test_name,
    p.username,
    a.id as attendance_id,
    a.name as attendance_name,
    a.attendance_type,
    a.eligible_after_days
FROM (
    VALUES 
        ('OldPlayer'),
        ('RecentPlayer'), 
        ('VeryOldPlayer')
) AS p(username)
LEFT JOIN LATERAL get_active_attendance_for_player(p.username) a ON true;

-- 9. Kiểm tra tất cả attendance templates và số lượng rewards
SELECT 
    'Summary of all attendance templates' as test_name,
    t.id,
    t.name,
    t.attendance_type,
    t.eligible_after_days,
    t.is_active,
    COUNT(r.id) as total_rewards,
    MIN(r.day_number) as min_day,
    MAX(r.day_number) as max_day
FROM tbl_attendance_templates t
LEFT JOIN tbl_attendance_rewards r ON t.id = r.attendance_id
GROUP BY t.id, t.name, t.attendance_type, t.eligible_after_days, t.is_active
ORDER BY t.attendance_type, t.created_date DESC;

-- 10. Test player attendance progress (nếu có data)
SELECT 
    'Checking player attendance progress' as test_name,
    pa.player_name,
    pa.attendance_id,
    t.name as attendance_name,
    t.attendance_type,
    pa.received_days,
    pa.last_received_date,
    pa.created_date
FROM tbl_player_attendance pa
JOIN tbl_attendance_templates t ON pa.attendance_id = t.id
WHERE t.attendance_type = 'comeback_14day'
ORDER BY pa.created_date DESC
LIMIT 10;

-- 11. Cleanup test data (comment out nếu muốn giữ test data)
-- DELETE FROM loginrecord WHERE userid IN ('test001', 'test002', 'test003');

-- 12. Final status check
SELECT 
    'Test completed successfully!' as status,
    CURRENT_TIMESTAMP as test_time,
    (SELECT COUNT(*) FROM tbl_attendance_templates WHERE attendance_type = 'comeback_14day') as comeback_templates,
    (SELECT COUNT(*) FROM tbl_attendance_rewards r 
     JOIN tbl_attendance_templates t ON r.attendance_id = t.id 
     WHERE t.attendance_type = 'comeback_14day') as comeback_rewards;
