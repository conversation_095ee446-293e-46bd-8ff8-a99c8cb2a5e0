-- Migration script to convert from old party system to new persistent party system
-- Run this script to migrate existing party data

-- Step 1: Create new persistent parties table
CREATE TABLE IF NOT EXISTS tbl_persistent_parties (
    party_id SERIAL PRIMARY KEY,
    party_uuid VARCHAR(36) UNIQUE NOT NULL,
    team_id INTEGER,
    server_id INTEGER NOT NULL,
    leader_name VARCHAR(50) NOT NULL,
    loot_type INTEGER DEFAULT 1,
    max_members INTEGER DEFAULT 8,
    persistence_mode INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    last_active_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    members JSONB NOT NULL DEFAULT '{}',
    metadata JSONB
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_persistent_parties_uuid ON tbl_persistent_parties(party_uuid);
CREATE INDEX IF NOT EXISTS idx_persistent_parties_team_id ON tbl_persistent_parties(team_id);
CREATE INDEX IF NOT EXISTS idx_persistent_parties_server_id ON tbl_persistent_parties(server_id);
CREATE INDEX IF NOT EXISTS idx_persistent_parties_last_active ON tbl_persistent_parties(last_active_at);
CREATE INDEX IF NOT EXISTS idx_persistent_parties_members_gin ON tbl_persistent_parties USING GIN(members);

-- Step 2: Backup existing data
CREATE TABLE IF NOT EXISTS backup_tbl_parties AS 
SELECT * FROM tbl_parties WHERE 1=0; -- Create structure only

CREATE TABLE IF NOT EXISTS backup_tbl_party_members AS 
SELECT * FROM tbl_party_members WHERE 1=0; -- Create structure only

-- Insert backup data if tables exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'tbl_parties') THEN
        INSERT INTO backup_tbl_parties SELECT * FROM tbl_parties;
        RAISE NOTICE 'Backed up % parties', (SELECT COUNT(*) FROM backup_tbl_parties);
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'tbl_party_members') THEN
        INSERT INTO backup_tbl_party_members SELECT * FROM tbl_party_members;
        RAISE NOTICE 'Backed up % party members', (SELECT COUNT(*) FROM backup_tbl_party_members);
    END IF;
END $$;

-- Step 3: Migration function
CREATE OR REPLACE FUNCTION migrate_parties_to_persistent()
RETURNS void AS $$
DECLARE
    party_record RECORD;
    member_record RECORD;
    members_json JSONB;
    online_members JSONB;
    offline_members JSONB;
    member_data JSONB;
BEGIN
    -- Check if old tables exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'tbl_parties') THEN
        RAISE NOTICE 'Old party tables not found, skipping migration';
        RETURN;
    END IF;

    RAISE NOTICE 'Starting party migration...';

    -- Loop through all active parties
    FOR party_record IN 
        SELECT * FROM tbl_parties 
        WHERE is_active = true 
        ORDER BY created_at
    LOOP
        -- Initialize JSON structures
        online_members := '[]'::jsonb;
        offline_members := '[]'::jsonb;

        -- Get all members for this party
        FOR member_record IN
            SELECT * FROM tbl_party_members 
            WHERE party_id = party_record.party_id 
            AND member_status = 1
            ORDER BY original_join_order
        LOOP
            -- Create member data
            member_data := jsonb_build_object(
                'characterName', member_record.character_name,
                'accountId', member_record.account_id,
                'joinOrder', member_record.original_join_order,
                'joinedAt', member_record.joined_at,
                'isLeader', (member_record.character_name = party_record.leader_name),
                'level', COALESCE(member_record.level, 1),
                'job', COALESCE(member_record.job, 0),
                'serverId', party_record.server_id
            );

            -- Determine if member is online or offline based on last_seen
            IF member_record.last_seen IS NULL OR 
               member_record.last_seen > NOW() - INTERVAL '5 minutes' THEN
                -- Consider as online (recently active)
                member_data := member_data || jsonb_build_object('sessionId', 0);
                online_members := online_members || member_data;
            ELSE
                -- Consider as offline
                member_data := member_data || jsonb_build_object('disconnectedAt', member_record.last_seen);
                offline_members := offline_members || member_data;
            END IF;
        END LOOP;

        -- Build complete members JSON
        members_json := jsonb_build_object(
            'online', online_members,
            'offline', offline_members
        );

        -- Skip parties with less than 2 total members
        IF jsonb_array_length(online_members) + jsonb_array_length(offline_members) < 2 THEN
            RAISE NOTICE 'Skipping party % with insufficient members', party_record.party_uuid;
            CONTINUE;
        END IF;

        -- Insert into new persistent parties table
        INSERT INTO tbl_persistent_parties (
            party_uuid,
            team_id,
            server_id,
            leader_name,
            loot_type,
            max_members,
            persistence_mode,
            created_at,
            last_active_at,
            is_active,
            members
        ) VALUES (
            COALESCE(party_record.party_uuid, gen_random_uuid()::text),
            party_record.team_id,
            party_record.server_id,
            party_record.leader_name,
            COALESCE(party_record.loot_type, 1),
            COALESCE(party_record.max_members, 8),
            1, -- Default to persistent mode
            party_record.created_at,
            COALESCE(party_record.last_active_at, party_record.created_at),
            true,
            members_json
        ) ON CONFLICT (party_uuid) DO UPDATE SET
            team_id = EXCLUDED.team_id,
            server_id = EXCLUDED.server_id,
            leader_name = EXCLUDED.leader_name,
            loot_type = EXCLUDED.loot_type,
            max_members = EXCLUDED.max_members,
            last_active_at = EXCLUDED.last_active_at,
            members = EXCLUDED.members;

        RAISE NOTICE 'Migrated party % with % total members', 
            party_record.party_uuid, 
            jsonb_array_length(online_members) + jsonb_array_length(offline_members);
    END LOOP;

    RAISE NOTICE 'Party migration completed successfully';
END;
$$ LANGUAGE plpgsql;

-- Step 4: Run migration
SELECT migrate_parties_to_persistent();

-- Step 5: Verification queries
DO $$
DECLARE
    old_count INTEGER;
    new_count INTEGER;
BEGIN
    -- Count old parties
    SELECT COUNT(*) INTO old_count FROM tbl_parties WHERE is_active = true;
    
    -- Count new parties
    SELECT COUNT(*) INTO new_count FROM tbl_persistent_parties WHERE is_active = true;
    
    RAISE NOTICE 'Migration verification:';
    RAISE NOTICE 'Old active parties: %', old_count;
    RAISE NOTICE 'New persistent parties: %', new_count;
    
    IF new_count >= old_count THEN
        RAISE NOTICE 'Migration appears successful!';
    ELSE
        RAISE WARNING 'Migration may have issues - fewer new parties than old ones';
    END IF;
END $$;

-- Step 6: Show sample of migrated data
SELECT 
    party_uuid,
    server_id,
    leader_name,
    jsonb_array_length(members->'online') as online_count,
    jsonb_array_length(members->'offline') as offline_count,
    created_at,
    last_active_at
FROM tbl_persistent_parties 
WHERE is_active = true 
ORDER BY created_at DESC 
LIMIT 10;

-- Step 7: Cleanup function (run manually after verification)
CREATE OR REPLACE FUNCTION cleanup_old_party_tables()
RETURNS void AS $$
BEGIN
    RAISE NOTICE 'This function will drop old party tables';
    RAISE NOTICE 'Make sure to verify migration first!';
    RAISE NOTICE 'Uncomment the DROP statements below to proceed';
    
    -- Uncomment these lines after verifying migration
    -- DROP TABLE IF EXISTS tbl_parties CASCADE;
    -- DROP TABLE IF EXISTS tbl_party_members CASCADE;
    -- RAISE NOTICE 'Old party tables dropped successfully';
END;
$$ LANGUAGE plpgsql;

-- To cleanup old tables after verification, run:
-- SELECT cleanup_old_party_tables();
