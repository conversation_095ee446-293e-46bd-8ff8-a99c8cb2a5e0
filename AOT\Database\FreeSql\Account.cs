

using System;
using System.Threading.Tasks;
using FreeSql;
using HeroYulgang.Core;
using HeroYulgang.Database.FreeSql.Entities.Account;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using static HeroYulgang.Database.FreeSql.Services;

namespace HeroYulgang.Database.FreeSql;

public static class AccountDb
{
    private static IFreeSql? _freeSql;

    public static bool InitializeAsync()
    {
        try
        {
            if (_freeSql != null) return true;

            var connectionString = ConfigManager.Instance.PogresSettings.AccountDb;
            // Console.WriteLine("AccountDb connection string: " + connectionString);
            // Create FreeSql instance
            _freeSql = new FreeSqlBuilder()
                .UseConnectionString(DataType.PostgreSQL, connectionString)
                //.UseAutoSyncStructure(true)
                .UseAdoConnectionPool(true)
                // .UseNoneCommandParameter(true)
                .Build();

            Console.WriteLine("✓ AccountDb initialized successfully");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Failed to initialize AccountDb: {ex.Message}");
            return false;
        }
    }

    public static void RefreshCheckLogin()
    {
        try
        {
            _freeSql?.Update<account>().Set(a => a.fld_checklogin, false).Set(a => a.fld_checkip, null).Where(a => a.fld_checklogin == true).ExecuteAffrows();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Failed to refresh check login: {ex.Message}");
        }
    }

    public static void BanChatAccount(string accountid)
    {
        try
        {
            _freeSql.Insert(new banned
            {
                fld_banedip = accountid
            })
            .ExecuteAffrows();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Banning Account " + ex.Message);
        }
    }

    public static void UnBanChatAccount(string accountid)
    {
        try
        {
            _freeSql.Delete<banned>()
            .Where(a => a.fld_banedip == accountid)
            .ExecuteAffrows();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Unbanning Account " + ex.Message);
        }
    }

    public async static void UpdateRemoveUserLogin(string accountid)
    {
        try
        {
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_online == 0)
            .Set(a => a.fld_checklogin == false)
            .Set(a => a.fld_checkip == null)
            .Set(a => a.fld_lanip == null)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Updating User Login " + ex.Message);
        }
    }

    public async static void BanAnAccount(string accountid, string reason, int hour)
    {
        try
        {
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_ghichu == reason)
            .Set(a => a.fld_zt == hour)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Banning Account " + ex.Message);
        }
    }
    public async static void UnbanAnAccount(string accountid)
    {
        try
        {
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_ghichu == null)
            .Set(a => a.fld_zt == 0)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Unbanning Account " + ex.Message);
        }
    }

    public static int number = 0;

    public async static Task<account> FindAccount(string accountid)
    {       
        // using var tracker = Core.Performance.PerformanceMonitor.TrackOperation("AccountDb.FindAccount");
        try
        {
            var account = await _freeSql
            .Select<account>()
            .Where(a => a.fld_id == accountid)
            .FirstAsync();
            return account;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error FindAccount " + ex.Message);
            return null;
        }
    }

    public async static Task<bool> UpdateVipTime(string accountid, int vip, DateTime viptime)
    {
        try
        {
            var result = await _freeSql
            .Update<account>()
            .Set(a => a.fld_vip == vip)
            .Set(a => a.fld_viptim == viptime)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Updating Vip Time " + ex.Message);
            return false;
        }
    }
    /// <summary>
    /// Update account RX points, coin and safe word using PostgreSQL function
    /// Cập nhật điểm RX, coin và mật khẩu an toàn tài khoản sử dụng function PostgreSQL
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="rxPoint">RX Points</param>
    /// <param name="rxPointX">RX Points X</param>
    /// <param name="coin">Coin amount</param>
    /// <param name="safeWord">Safe word</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> UpdateRxPointAsync(string accountId, int rxPoint, int rxPointX, int coin, string safeWord)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (string.IsNullOrEmpty(accountId))
            {
                Logger.Instance.Error("Account ID is null or empty");
                return false;
            }

            // Use FreeSql ORM Update method instead of stored procedure for native AOT compatibility
            // Sử dụng phương thức Update của FreeSql ORM thay vì stored procedure để tương thích với native AOT
            // Equivalent to: UPDATE account SET fld_rxpiont = ?, fld_rxpiontx = ?, fld_coin = ?, fld_safeword = ? WHERE fld_id = ?
            var result = await _freeSql.Update<account>()
                .Set(a => a.fld_rxpiont, rxPoint)
                .Set(a => a.fld_rxpiontx, rxPointX)
                .Set(a => a.fld_coin, coin)
                .Set(a => a.fld_safeword, safeWord ?? string.Empty)
                .Where(a => a.fld_id == accountId)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Debug($"✓ Account RX points updated successfully for {accountId} - Rows affected: {result}");
                return true;
            }
            else
            {
                Logger.Instance.Warning($"No account found to update RX points for {accountId}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error updating account RX points for {accountId}: {ex.Message}");
            return false;
        }
    }

}
