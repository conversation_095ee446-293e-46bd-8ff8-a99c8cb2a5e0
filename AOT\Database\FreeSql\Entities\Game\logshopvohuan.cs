﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class logshopvohuan {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('logshopvohuan_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string userid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string username { get; set; }

		[JsonProperty]
		public int? maitem { get; set; }

		[JsonProperty]
		public int? iditem { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tenitem { get; set; }

		[JsonProperty]
		public int? magic1 { get; set; }

		[JsonProperty]
		public int? magic2 { get; set; }

		[JsonProperty]
		public int? magic3 { get; set; }

		[JsonProperty]
		public int? magic4 { get; set; }

		[JsonProperty]
		public int? magic5 { get; set; }

		[JsonProperty]
		public int? soluong { get; set; }

		[JsonProperty]
		public int? giatien { get; set; }

		[JsonProperty]
		public DateTime? thoigian { get; set; }

		[JsonProperty]
		public int? vohuanconlai { get; set; }

		[JsonProperty]
		public int? co_hay_khong_mo_ra { get; set; }

		[JsonProperty]
		public int? da_su_dung { get; set; }

		[JsonProperty]
		public int? mua_id { get; set; }

		[JsonProperty]
		public int? thanh_cong { get; set; }

	}

}
