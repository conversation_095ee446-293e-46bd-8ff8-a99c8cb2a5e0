﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_party_members {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_party_members_member_id_seq'::regclass)")]
		public int member_id { get; set; }

		[JsonProperty]
		public int party_id { get; set; }

		[JsonProperty, Column(StringLength = 50, IsNullable = false)]
		public string character_name { get; set; }

		[JsonProperty, Column(StringLength = 50, IsNullable = false)]
		public string account_id { get; set; }

		[JsonProperty]
		public int current_server_id { get; set; }

		[JsonProperty]
		public int original_join_order { get; set; }

		[JsonProperty]
		public int? rejoin_order { get; set; }

		[JsonProperty]
		public int? member_status { get; set; } = 1;

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime joined_at { get; set; }

		[JsonProperty]
		public DateTime? rejoined_at { get; set; }

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime last_seen_at { get; set; }

		[JsonProperty]
		public bool? is_current_leader { get; set; } = false;

	}

}
