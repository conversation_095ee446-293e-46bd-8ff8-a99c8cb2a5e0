using System;
using System.Collections;

namespace RxjhServer
{
    public class HatchItemClass
    {
        private int _ID;
        private int _FLD_PID;
        private string _FLD_NAME;
        private int _FLD_PIDX;
        private string _FLD_NAMEX;
        private int _FLD_Number;
        private int _FLD_PP;
        private int _FLD_MAGIC0;
        private int _FLD_MAGIC1;
        private int _FLD_MAGIC2;
        private int _FLD_MAGIC3;
        private int _FLD_MAGIC4;
        private int _FLD_LowSoul;
        private int _FLD_MedSoul;
        private int _FLD_Quality;
        private int _FLD_Lock;
        private int _FLD_ExpiryDate;
        private int _FLD_Announce;


        public static HatchItemClass Get_ItemHatch(int PID)
        {
            int total_rate = 0;
            int get_rate = 0;
            foreach (HatchItemClass List_Item in World.List_HatchItem.Values)
            {
                if (List_Item.FLD_PID == PID)
                {
                    total_rate += List_Item.FLD_PP;
                }
            }

            int random_rate = new Random(World.GetRandomSeed()).Next(0, total_rate + 1);
            foreach(HatchItemClass Get_Item in World.List_HatchItem.Values)
            {
                if (Get_Item.FLD_PID == PID)
                {
                    get_rate += Get_Item.FLD_PP;
                    if (get_rate >= random_rate)
                    {
                        return Get_Item;
                    }
                }
            }
            return null;
        }

        public int ID
        {
            get { return _ID; }
            set { _ID = value; }
        }

        public int FLD_PID
        {
            get { return _FLD_PID; }
            set { _FLD_PID = value; }
        }

        public string FLD_NAME
        {
            get { return _FLD_NAME; }
            set { _FLD_NAME = value; }
        }

        public int FLD_PIDX
        {
            get { return _FLD_PIDX; }
            set { _FLD_PIDX = value; }
        }

        public string FLD_NAMEX
        {
            get { return _FLD_NAMEX; }
            set { _FLD_NAMEX = value; }
        }

        public int FLD_Number
        {
            get { return _FLD_Number; }
            set { _FLD_Number = value; }
        }
        public int FLD_PP
        {
            get { return _FLD_PP; }
            set { _FLD_PP = value; }
        }
        public int FLD_MAGIC0
        {
            get { return _FLD_MAGIC0; }
            set { _FLD_MAGIC0 = value; }
        }

        public int FLD_MAGIC1
        {
            get { return _FLD_MAGIC1; }
            set { _FLD_MAGIC1 = value; }
        }

        public int FLD_MAGIC2
        {
            get { return _FLD_MAGIC2; }
            set { _FLD_MAGIC2 = value; }
        }

        public int FLD_MAGIC3
        {
            get { return _FLD_MAGIC3; }
            set { _FLD_MAGIC3 = value; }
        }

        public int FLD_MAGIC4
        {
            get { return _FLD_MAGIC4; }
            set { _FLD_MAGIC4 = value; }
        }

        public int FLD_LowSoul
        {
            get { return _FLD_LowSoul; }
            set { _FLD_LowSoul = value; }
        }
        public int FLD_MedSoul
        {
            get { return _FLD_MedSoul; }
            set { _FLD_MedSoul = value; }
        }
        public int FLD_Quality
        {
            get { return _FLD_Quality; }
            set { _FLD_Quality = value; }
        }

        public int FLD_Lock
        {
            get { return _FLD_Lock; }
            set { _FLD_Lock = value; }
        }
        public int FLD_ExpiryDate
        {
            get { return _FLD_ExpiryDate; }
            set { _FLD_ExpiryDate = value; }
        }

        public int FLD_Announce
        {
            get { return _FLD_Announce; }
            set { _FLD_Announce = value; }
        }
    }
}
