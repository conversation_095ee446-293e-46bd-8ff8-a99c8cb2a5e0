using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Timers;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.AOI;
using static RxjhServer.ReverserInfo;

namespace RxjhServer.NpcManager
{
    public enum NPCBehaviorType
    {
        Idle,           // Standing still
        Moving,         // Random movement around spawn
        Attacking,      // Chasing and attacking player
        Returning,       // Returning to spawn point,
    }

    public class NPCBehaviorState
    {
        public NPCBehaviorType CurrentBehavior { get; set; } = NPCBehaviorType.Idle;
        public DateTime BehaviorStartTime { get; set; } = DateTime.Now;
        public DateTime LastAttackTime { get; set; } = DateTime.MinValue;
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
        public DateTime LastAOIUpdate { get; set; } = DateTime.Now;
        public Players TargetPlayer { get; set; }
        public float TargetX { get; set; }
        public float TargetY { get; set; }
        public bool IsMoving { get; set; }

        public bool CanAttack(int attackSpeedMs = 2500)
        {
            return (DateTime.Now - LastAttackTime).TotalMilliseconds >= attackSpeedMs;
        }

        public bool CanChangeBehavior(int minDurationMs = 1000)
        {
            // Allow faster behavior changes when attacking for better responsiveness
            if (CurrentBehavior == NPCBehaviorType.Attacking)
            {
                return (DateTime.Now - BehaviorStartTime).TotalMilliseconds >= Math.Min(minDurationMs, 200);
            }
            return (DateTime.Now - BehaviorStartTime).TotalMilliseconds >= minDurationMs;
        }

        public void UpdateBehavior(NPCBehaviorType newBehavior, Players target = null)
        {
            if (CurrentBehavior != newBehavior)
            {
                // If switching to attacking behavior, reset attack timer for immediate attack
                if (newBehavior == NPCBehaviorType.Attacking && CurrentBehavior != NPCBehaviorType.Attacking)
                {
                    LastAttackTime = DateTime.MinValue;
                }

                CurrentBehavior = newBehavior;
                BehaviorStartTime = DateTime.Now;
                TargetPlayer = target;
            }
        }
    }

    public class NPCUpdateInfo
    {
        public NpcClass NPC { get; set; }
        public DateTime NextActionTime { get; set; }
        public DateTime NextRespawnTime { get; set; } = DateTime.MaxValue;
        public int MoveInterval { get; set; } = 5000;
        public int RespawnInterval { get; set; } = 10000;
        public int ErrorCount { get; set; } = 0;
    }

    /// <summary>
    /// Simplified NPC Manager - Clean and easy to extend
    /// Combines timer management and behavior logic
    /// </summary>
    public static class NpcManager
    {
        // Configuration - easily adjustable
        public static int ATTACK_RANGE = 10;
        public static int CHASE_RANGE = 80;
        public static int ACTIVITY_AREA = 120;
        public static int RETURN_TO_SPAWN_RANGE = 160;
        public static int ATTACK_SPEED_MS = 1000; // Reduced from 2500ms to 1000ms for faster attacks

        // Boss settings
        public static int BOSS_CHASE_RANGE = 200;
        public static int BOSS_ATTACK_RANGE = 50;
        public static int BOSS_ACTIVITY_AREA = 256;

        // Internal state
        private static readonly System.Timers.Timer _globalTimer = new(100);
        private static readonly ConcurrentDictionary<int, NPCUpdateInfo> _npcUpdates = new();
        private static readonly ConcurrentDictionary<int, NPCBehaviorState> _npcStates = new();
        private static bool _initialized = false;

        static NpcManager()
        {
            Initialize();
        }

        private static void Initialize()
        {
            if (_initialized) return;
            
            _globalTimer.Elapsed += ProcessAllNPCs;
            _globalTimer.AutoReset = true;
            _globalTimer.Enabled = true;
            _initialized = true;
            
            LogHelper.WriteLine(LogLevel.Info, "NpcManager initialized successfully");
        }

        /// <summary>
        /// Register NPC for management - This is the main entry point
        /// </summary>
        public static void RegisterNPC(NpcClass npc, int moveInterval = 5000)
        {
            if (!_initialized) Initialize();

            try
            {
                var now = DateTime.Now;
                var updateInfo = new NPCUpdateInfo
                {
                    NPC = npc,
                    MoveInterval = moveInterval,
                    NextActionTime = now.AddMilliseconds(RNG.Next(1000, moveInterval)),
                    RespawnInterval = Math.Max(npc.FLD_NEWTIME * 1000, 10000)
                };

                _npcUpdates.AddOrUpdate(npc.NPC_SessionID, updateInfo, (key, existing) => updateInfo);
                _npcStates.GetOrAdd(npc.NPC_SessionID, _ => new NPCBehaviorState());
                
                // LogHelper.WriteLine(LogLevel.Debug, $"Registered NPC: {npc.Name} (ID: {npc.NPC_SessionID})");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error registering NPC {npc.Name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Unregister NPC from management
        /// </summary>
        public static void UnregisterNPC(int npcSessionID)
        {
            _npcUpdates.TryRemove(npcSessionID, out _);
            _npcStates.TryRemove(npcSessionID, out _);
        }

        /// <summary>
        /// Enable respawn for dead NPC
        /// </summary>
        public static void EnableRespawn(int npcSessionID, int respawnInterval = 10000)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var info))
            {
                info.NextRespawnTime = DateTime.Now.AddMilliseconds(respawnInterval);
                info.RespawnInterval = respawnInterval;
            }
        }

        /// <summary>
        /// Main processing loop - called every 100ms
        /// </summary>
        private static void ProcessAllNPCs(object sender, ElapsedEventArgs e)
        {
            try
            {
                var now = DateTime.Now;
                
                foreach (var kvp in _npcUpdates)
                {
                    var npcInfo = kvp.Value;
                    var npc = npcInfo.NPC;
                    
                    try
                    {
                        if (npc.NPC_Removed) continue;

                        // Handle respawn first
                        if (now >= npcInfo.NextRespawnTime)
                        {
                            ProcessRespawn(npc, npcInfo);
                            continue;
                        }

                        // Process behavior for living NPCs
                        if (!npc.NPCDeath)
                        {
                            // Update NPC position every tick (for smooth movement)
                            UpdateNPCPosition(npc, now);
                            
                            // Process behavior decisions (less frequent)
                            if (now >= npcInfo.NextActionTime)
                            {
                                ProcessNPCBehavior(npc, npcInfo, now);
                            }
                        }

                        npcInfo.ErrorCount = 0; // Reset error count on success
                    }
                    catch (Exception ex)
                    {
                        npcInfo.ErrorCount++;
                        LogHelper.WriteLine(LogLevel.Debug, $"Error processing NPC {npc.NPC_SessionID}: {ex.Message}");

                        // Remove problematic NPCs after 20 consecutive errors
                        if (npcInfo.ErrorCount > 20)
                        {
                            LogHelper.WriteLine(LogLevel.Error, $"Removing problematic NPC {npc.NPC_SessionID} after {npcInfo.ErrorCount} errors");
                            _npcUpdates.TryRemove(kvp.Key, out _);
                            _npcStates.TryRemove(kvp.Key, out _);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Critical error in NpcManager: {ex.Message}");
            }
        }

        /// <summary>
        /// Process NPC behavior logic
        /// </summary>
        private static void ProcessNPCBehavior(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            // Skip non-combat NPCs and special NPCs
            if (npc.IsNpc != 0 || npc.FLD_AT <= 0 || IsSpecialNPC(npc))
            {
                npcInfo.NextActionTime = now.AddMilliseconds(5000);
                return;
            }

            var state = _npcStates.GetOrAdd(npc.NPC_SessionID, _ => new NPCBehaviorState());
            var targetPlayer = FindTargetPlayer(npc);
            var newBehavior = DetermineBehavior(npc, state, targetPlayer);

            // Execute behavior if changed or if it's time to act
            if (state.CurrentBehavior != newBehavior || state.CanChangeBehavior())
            {
                state.UpdateBehavior(newBehavior, targetPlayer);
                ExecuteBehavior(npc, state, newBehavior, targetPlayer);
            }

            // next Action time được set ở vị trí khác
            // var nextInterval = newBehavior switch
            // {
            //     NPCBehaviorType.Attacking => targetPlayer != null ? 300 : 1000, // Much faster response when attacking
            //     NPCBehaviorType.Returning => 1000, // Faster return behavior
            //     NPCBehaviorType.Moving => 6000,
            //     _ => targetPlayer != null ? 1000 : RNG.Next(3000, 8000) // Faster response when target exists
            // };

            // npcInfo.NextMoveTime = now.AddMilliseconds(nextInterval + RNG.Next(-200, 200));
        }

        /// <summary>
        /// Determine NPC behavior based on current situation
        /// </summary>
        private static NPCBehaviorType DetermineBehavior(NpcClass npc, NPCBehaviorState state, Players targetPlayer)
        {
            var distanceFromSpawn = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_cs_X, npc.Rxjh_cs_Y);

            // Priority 1: Return to spawn if too far
            if (distanceFromSpawn > RETURN_TO_SPAWN_RANGE)
            {
                return NPCBehaviorType.Returning;
            }

            // Priority 2: Attack valid target
            if (targetPlayer != null && targetPlayer.NhanVat_HP > 0 && !targetPlayer.PlayerTuVong)
            {
                var distanceToPlayer = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);
                var chaseRange = npc.FLD_BOSS == 1 ? BOSS_CHASE_RANGE : CHASE_RANGE;
                var activityArea = npc.FLD_BOSS == 1 ? BOSS_ACTIVITY_AREA : ACTIVITY_AREA;

                if (distanceToPlayer <= chaseRange && distanceFromSpawn <= activityArea)
                {
                    return NPCBehaviorType.Attacking;
                }
            }

            // Priority 3: Random movement or idle
            if (state.CurrentBehavior == NPCBehaviorType.Idle && 
                (DateTime.Now - state.BehaviorStartTime).TotalMilliseconds > 5000)
            {
                return NPCBehaviorType.Moving;
            }
            else if (state.CurrentBehavior == NPCBehaviorType.Moving && 
                     (DateTime.Now - state.BehaviorStartTime).TotalMilliseconds > 8000)
            {
                return NPCBehaviorType.Idle;
            }

            return state.CurrentBehavior;
        }

        /// <summary>
        /// Execute the determined behavior
        /// </summary>
        private static void ExecuteBehavior(NpcClass npc, NPCBehaviorState state, NPCBehaviorType behavior, Players targetPlayer)
        {
            // Đưa nẽtActionTime vào đây
            // Nhưng lại có vấn đề với thời gian tiếp theo
            // Ví dụ trường hợp npc đang idle, hoặc đang chạy về vị trí gốc
            // Nếu có player chạy qua thì sao ???
            
            switch (behavior)
            {
                case NPCBehaviorType.Idle:
                    npc.AutomaticMoveEnabled = false;
                    npc.AutomaticAttackEnabled = false;
                    state.IsMoving = false;
                    SetNextActionTime(npc, DateTime.Now.AddMilliseconds(2000));
                    break;

                case NPCBehaviorType.Moving:
                    if (!state.IsMoving)
                    {
                        var moveRange = 50;
                        var random = new Random(npc.NPC_SessionID + DateTime.Now.Millisecond);
                        var angle = random.NextDouble() * Math.PI * 2;
                        state.TargetX = npc.Rxjh_cs_X + (float)(Math.Cos(angle) * moveRange);
                        state.TargetY = npc.Rxjh_cs_Y + (float)(Math.Sin(angle) * moveRange);
                        state.IsMoving = true;
                        state.LastUpdateTime = DateTime.Now; // Start movement timer
                        npc.SendMovingData(state.TargetX, state.TargetY, 55, 2);
                    }
                    npc.AutomaticMoveEnabled = true;
                    npc.AutomaticAttackEnabled = false;
                    // Moving sẽ có cập nhật 100ms liên tục, có thể sử dụng nó để tìm player và attack
                    // Tạm thời set 1000
                    SetNextActionTime(npc, DateTime.Now.AddMilliseconds(1000));
                    break;
                // Base Attack cho NOrmal Monster
                case NPCBehaviorType.Attacking:
                    // Attack sẽ có cập nhật liên tục. để scan player, nhưng monster sẽ có attack cooldown
                    // Trong 1 khoảng thời gian sẽ  không được tấn công nhưng vẫn có thể di chuyển
                    // Next Action Time sẽ set ở Npc, chưa được xử lý tại đây, tương lai sẽ chuyển hết về đây
                    if (targetPlayer != null)
                    {
                        var distanceToPlayer = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);
                        var attackRange = npc.FLD_BOSS == 1 ? BOSS_ATTACK_RANGE : ATTACK_RANGE;

                        if (distanceToPlayer <= attackRange)
                        {
                            npc.AutomaticMoveEnabled = false;
                            npc.AutomaticAttackEnabled = true;
                            state.IsMoving = false;

                            // Check if this is the first time entering attack range or if enough time has passed
                            bool isFirstTimeInRange = state.LastAttackTime == DateTime.MinValue;
                            bool canAttackNow = state.CanAttack(ATTACK_SPEED_MS);

                            if (isFirstTimeInRange || canAttackNow)
                            {
                                npc.NPC_Attack(targetPlayer);
                                state.LastAttackTime = DateTime.Now;
                            }
                        }
                        else
                        {
                            var (attackX, attackY) = CalculateOptimalAttackPosition(npc, targetPlayer);
                            // Chase player
                            state.TargetX = attackX;
                            state.TargetY = attackY;
                            state.IsMoving = true;
                            state.LastUpdateTime = DateTime.Now; // Start chase timer
                            npc.SendMovingData(attackX, attackY, 55, 2);
                            npc.AutomaticMoveEnabled = true;
                            npc.AutomaticAttackEnabled = true;
                        }
                    }
                    break;

                case NPCBehaviorType.Returning:
                    // Khi trở về vị trí cũ, vì nó vẫn là di chuyển nên mỗi 100ms sẽ cập nhật một lần
                    // Khi cập nhật có thể sẽ scan lại player trong tầm hoạt động để tấn công tiếp ?
                    state.TargetX = npc.Rxjh_cs_X;
                    state.TargetY = npc.Rxjh_cs_Y;
                    state.IsMoving = true;
                    state.LastUpdateTime = DateTime.Now; // Start return timer
                    npc.SendMovingData(npc.Rxjh_cs_X, npc.Rxjh_cs_Y, 55, 2);
                    npc.AutomaticMoveEnabled = true;
                    npc.AutomaticAttackEnabled = false;
                    state.TargetPlayer = null;
                    SetNextActionTime(npc, DateTime.Now.AddMilliseconds(2000));
                    break;
            }
        }

        /// <summary>
        /// Find target player for NPC
        /// </summary>
        private static Players FindTargetPlayer(NpcClass npc)
        {
            try
            {
                var chaseRange = npc.FLD_BOSS == 1 ? BOSS_CHASE_RANGE : CHASE_RANGE;
                
                // Check if NPC has existing target
                if (npc.PlayerWid > 0 && World.allConnectedChars.TryGetValue(npc.PlayerWid, out var existingTarget))
                {
                    if (existingTarget.NhanVat_HP > 0 && !existingTarget.PlayerTuVong && existingTarget.GMMode != 8)
                    {
                        var distance = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, existingTarget.PosX, existingTarget.PosY);
                        if (distance <= chaseRange * 1.5) // Allow some extra chase distance for existing target
                        {
                            return existingTarget;
                        }
                    }
                }

                // Find new target for aggressive NPCs
                if (npc.FLD_AUTO == 1)
                {
                    return npc.GetClosestPlayer(chaseRange);
                }

                // For passive NPCs, check if they have been attacked
                if (npc.FLD_AUTO == 0 && npc.GetNearbyPlayersCount() > 0)
                {
                    return npc.GetClosestPlayer(chaseRange);
                }

                return null;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"Error finding target for NPC {npc.NPC_SessionID}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Update NPC position based on movement target - called every tick
        /// </summary>
        private static void UpdateNPCPosition(NpcClass npc, DateTime now)
        {
            try
            {
                var state = _npcStates.GetOrAdd(npc.NPC_SessionID, _ => new NPCBehaviorState());
                
                // Only update position if NPC is supposed to be moving
                if (!state.IsMoving || state.CurrentBehavior == NPCBehaviorType.Idle)
                    return;

                var deltaTime = (float)(now - state.LastUpdateTime).TotalSeconds;
                if (deltaTime <= 0) return;

                state.LastUpdateTime = now;

                var targetX = state.TargetX;
                var targetY = state.TargetY;
                
                // Calculate distance to target
                var distanceToTarget = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetX, targetY);
                
                // If close enough to target, stop moving
                if (distanceToTarget <= 3f)
                {
                    npc.Rxjh_X = targetX;
                    npc.Rxjh_Y = targetY;
                    state.IsMoving = false;
                    
                    // Update AOI position
                    try
                    {
                        AOISystem.Instance.UpdateNPCPosition(npc, npc.Rxjh_X, npc.Rxjh_Y);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"Error updating NPC AOI position: {ex.Message}");
                    }
                    return;
                }

                // Calculate movement step based on speed and time
                var speed = GetMovementSpeed(state.CurrentBehavior);
                var moveDistance = speed * deltaTime;
                
                if (moveDistance > distanceToTarget)
                    moveDistance = (float)distanceToTarget;

                // Calculate direction vector
                var directionX = (targetX - npc.Rxjh_X) / distanceToTarget;
                var directionY = (targetY - npc.Rxjh_Y) / distanceToTarget;

                // Update NPC position
                npc.Rxjh_X += (float)(directionX * moveDistance);
                npc.Rxjh_Y += (float)(directionY * moveDistance);

                // Update AOI position periodically (not every tick)
                if ((now - state.LastAOIUpdate).TotalMilliseconds > 200) // Every 200ms
                {
                    try
                    {
                        AOISystem.Instance.UpdateNPCPosition(npc, npc.Rxjh_X, npc.Rxjh_Y);
                        state.LastAOIUpdate = now;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"Error updating NPC AOI position during movement: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC position for {npc.NPC_SessionID}: {ex.Message}");
            }
        }
        // Phương thức để set NextActionTime
        public static void SetNextActionTime(NpcClass npc, DateTime nextActionTime)
        {
            if (_npcUpdates.TryGetValue(npc.NPC_SessionID, out var npcInfo))
            {
                npcInfo.NextActionTime = nextActionTime;
            }
        }

        /// <summary>
        /// Get movement speed based on behavior type
        /// </summary>
        private static float GetMovementSpeed(NPCBehaviorType behavior)
        {
            return behavior switch
            {
                NPCBehaviorType.Idle => 0f,
                NPCBehaviorType.Moving => 30f,      // Random movement speed
                NPCBehaviorType.Attacking => 100f,   // Fast speed when chasing/attacking  
                NPCBehaviorType.Returning => 50f,    // Speed when returning to spawn
                _ => 30f
            };
        }

        /// <summary>
        /// Process NPC respawn
        /// </summary>
        private static void ProcessRespawn(NpcClass npc, NPCUpdateInfo npcInfo)
        {
            try
            {
                npc.ProcessAutomaticRespawn();
                npcInfo.NextRespawnTime = DateTime.MaxValue;
                npcInfo.NextActionTime = DateTime.Now.AddMilliseconds(RNG.Next(1000, 3000));
                
                // Reset behavior state
                if (_npcStates.TryGetValue(npc.NPC_SessionID, out var state))
                {
                    state.CurrentBehavior = NPCBehaviorType.Idle;
                    state.BehaviorStartTime = DateTime.Now;
                    state.TargetPlayer = null;
                    state.IsMoving = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in respawn for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }
         private static (float targetX, float targetY) CalculateOptimalAttackPosition(NpcClass npc, Players targetPlayer)
        {
            // Get all NPCs near the target player to avoid clustering
            var nearbyNPCs = GetNearbyNPCs(targetPlayer, ATTACK_RANGE * 3);

            // Calculate preferred angle based on NPC's current position relative to player
            var currentAngle = Math.Atan2(npc.Rxjh_Y - targetPlayer.PosY, npc.Rxjh_X - targetPlayer.PosX);

            // Try to find an optimal position around the player
            var bestAngle = FindBestPositionAngle(targetPlayer, nearbyNPCs, currentAngle, npc.NPC_SessionID);

            // Dynamic attack distance based on number of nearby NPCs
            var baseAttackDistance = ATTACK_RANGE * 0.5f;
            var npcCount = nearbyNPCs.Count;

            // Increase distance when more NPCs are around to spread them out
            var dynamicDistance = baseAttackDistance;
            if (npcCount > 3)
            {
                dynamicDistance = baseAttackDistance + (npcCount - 3) * 5f; // Add 5 pixels per extra NPC
                dynamicDistance = Math.Min(dynamicDistance, ATTACK_RANGE * 1.0f); // Cap at 1.5x attack range
            }

            var targetX = targetPlayer.PosX + (float)(Math.Cos(bestAngle) * dynamicDistance);
            var targetY = targetPlayer.PosY + (float)(Math.Sin(bestAngle) * dynamicDistance);

            return (targetX, targetY);
        }
        private static List<NpcClass> GetNearbyNPCs(Players targetPlayer, float range)
        {
            var nearbyNPCs = new List<NpcClass>();

            try
            {
                //var listNpc = targetPlayer.GetNpcsInRangeZone((int)range);
                var listNpc = targetPlayer.NearbyNpcs.Values;
                foreach (var npc in listNpc)
                {
                    if (npc.NPCDeath || npc.FLD_AT <= 0) continue;

                    var distance = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);
                    if (distance <= range)
                    {
                        nearbyNPCs.Add(npc);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting nearby NPCs: {ex.Message}");
            }

            return nearbyNPCs;
        }




        /// <summary>
        /// Find the best angle around a target to minimize clustering using random 360-degree positioning
        /// </summary>
        private static double FindBestPositionAngle(Players targetPlayer, List<NpcClass> nearbyNPCs, double preferredAngle, int excludeNpcId, float centerX = 0, float centerY = 0)
        {
            if (centerX == 0 && centerY == 0)
            {
                centerX = targetPlayer.PosX;
                centerY = targetPlayer.PosY;
            }

            var random = new Random(excludeNpcId + DateTime.Now.Millisecond);
            var bestAngle = preferredAngle;
            var minCrowding = float.MaxValue;

            // Test multiple random angles to find the best position
            var testAngles = 16; // Number of random angles to test

            for (int i = 0; i < testAngles; i++)
            {
                // Generate completely random angle (0 to 2π)
                var angle = random.NextDouble() * 2 * Math.PI;

                // Calculate position at this angle
                var testX = centerX + (float)(Math.Cos(angle) * ATTACK_RANGE * 0.8f);
                var testY = centerY + (float)(Math.Sin(angle) * ATTACK_RANGE * 0.8f);

                // Calculate crowding score (how many NPCs are near this position)
                var crowdingScore = 0f;
                foreach (var nearbyNpc in nearbyNPCs)
                {
                    if (nearbyNpc.NPC_SessionID == excludeNpcId) continue;

                    var distance = CalculateDistance(testX, testY, nearbyNpc.Rxjh_X, nearbyNpc.Rxjh_Y);
                    if (distance < ATTACK_RANGE)
                    {
                        // Exponential penalty for closer NPCs to strongly discourage clustering
                        var proximityFactor = (ATTACK_RANGE - distance) / ATTACK_RANGE;
                        crowdingScore += (float)(proximityFactor * proximityFactor * 2); // Squared penalty
                    }
                }

                // Small preference for angles closer to the preferred angle (but much less weight than crowding)
                var angleDifference = Math.Abs(angle - preferredAngle);
                if (angleDifference > Math.PI) angleDifference = 2 * Math.PI - angleDifference;
                var anglePreferenceScore = (float)angleDifference / (float)Math.PI * 0.1f; // Very low weight

                var totalScore = crowdingScore + anglePreferenceScore;

                if (totalScore < minCrowding)
                {
                    minCrowding = totalScore;
                    bestAngle = angle;
                }
            }

            // If all tested positions are crowded, fall back to completely random angle
            if (minCrowding > 1.0f)
            {
                bestAngle = random.NextDouble() * 2 * Math.PI;
            }

            return bestAngle;
        }

        /// <summary>
        /// Handle counter-attack when monster is hit
        /// </summary>
        public static void HandleCounterAttack(NpcClass npc, Players attacker)
        {
            try
            {
                if (npc == null || attacker == null || npc.NPCDeath || attacker.NhanVat_HP <= 0) return;

                var state = _npcStates.GetOrAdd(npc.NPC_SessionID, _ => new NPCBehaviorState());

                // Add attacker to target list
                npc.AddPlayerTarget(attacker);

                // Immediately switch to attacking behavior
                state.UpdateBehavior(NPCBehaviorType.Attacking, attacker);
                ExecuteBehavior(npc, state, NPCBehaviorType.Attacking, attacker);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in counter attack for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        // Utility methods
        private static double CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var dx = x1 - x2;
            var dy = y1 - y2;
            return Math.Sqrt(dx * dx + dy * dy);
        }

        private static bool IsSpecialNPC(NpcClass npc)
        {
            return npc.FLD_PID == 16431 || npc.FLD_PID == 16430 || npc.FLD_PID == 16435;
        }


        // Configuration methods
        public static void SetAttackRange(int range) => ATTACK_RANGE = Math.Max(1, Math.Min(range, 1000));
        public static void SetChaseRange(int range) => CHASE_RANGE = Math.Max(1, Math.Min(range, 1000));
        public static void SetActivityArea(int area) => ACTIVITY_AREA = Math.Max(1, Math.Min(area, 2000));
        public static void ResetToDefaults()
        {
            ATTACK_RANGE = 10;
            CHASE_RANGE = 80;
            ACTIVITY_AREA = 120;
            RETURN_TO_SPAWN_RANGE = 160;
            ATTACK_SPEED_MS = 1000; // Updated to match new default
        }

        // Statistics
        public static (int RegisteredNPCs, bool IsRunning) GetStatistics()
        {
            return (_npcUpdates.Count, _globalTimer.Enabled);
        }

        // Cleanup
        public static void Shutdown()
        {
            try
            {
                _globalTimer.Stop();
                _globalTimer.Dispose();
                _npcUpdates.Clear();
                _npcStates.Clear();
                _initialized = false;
                LogHelper.WriteLine(LogLevel.Info, "NpcManager shutdown completed");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error during NpcManager shutdown: {ex.Message}");
            }
        }
    }
}