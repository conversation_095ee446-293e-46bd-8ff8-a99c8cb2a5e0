﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class PlayersBes : X_<PERSON>hi_Cong_Thuoc_Tinh
{
	public Players Find_CharacterFullServerID(int UserSessionID)
	{
		Players result;
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.SessionID == UserSessionID)
				{
					return value;
				}
			}
			result = null;
		}
		catch
		{
			result = null;
		}
		return result;
	}

	public Players GetCharacterData(int int_109)
	{
		if (World.allConnectedChars.TryGetValue(int_109, out var value))
		{
			return value;
		}
		return null;
	}

	public Players GetPlayerByName(string UserName)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.CharacterName.ToLower() == UserName.ToLower())
				{
					return value;
				}
			}
			return null;
		}
		catch
		{
			return null;
		}
	}

	public Players GetCharacterData(string string_11)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.CharacterName == string_11)
				{
					return value;
				}
			}
			return null;
		}
		catch
		{
			return null;
		}
	}
	public Item FindItemByItemID(int int_109)
	{
		var num = 0;
		while (true)
		{
			if (num < 96)
			{
				if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == int_109)
				{
					return Item_In_Bag[num];
				}
				num++;
				continue;
			}
			return null;
		}

	}

	public X_Nhiem_Vu_Vat_Pham_Loai FindQuestItemByItemID(int int_109)
	{
		var num = 0;
		while (true)
		{
			if (num < 36)
			{
				if (NhiemVu_VatPham[num].VatPham_ID == int_109)
				{
					break;
				}
				num++;
				continue;
			}
			return null;
		}
		return NhiemVu_VatPham[num];
	}

	public int GetEmptySlotEventBag()
	{
		var num = 0;
		while (true)
		{
			if (num < 36)
			{
				if (EventBag[num].GetVatPham_ID == 0)
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num;
	}

	public int GetParcelVacancy(Players players_0)
	{
		var num = 0;
		while (true)
		{
			if (num < 96)
			{
				if (BitConverter.ToInt32(players_0.Item_In_Bag[num].VatPham_ID, 0) == 0)
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num;
	}

	public int getnumberofempty()
	{
		var num = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 0)
			{
				num++;
			}
		}
		return num;
	}

	public int GetParcelVacancyPosition()
	{
		var num = 0;
		while (true)
		{
			if (num < 96)
			{
				if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0)
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num;
	}

	public int GetPacket_VatPhamViTri(int PID)
	{
		var num = 0;
		while (true)
		{
			if (num < 96)
			{
				if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == PID)
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num;
	}

	public Item DatDuocVatPhamLoaiHinh(long id, int magic = 0)
	{
		var equipmentBarPackage = Item_In_Bag;
		var array = equipmentBarPackage;
		foreach (var x_Vat_Pham_Loai in array)
		{
			if (x_Vat_Pham_Loai.GetVatPham_ID == id)
			{
				return x_Vat_Pham_Loai;
			}
		}
		return null;
	}

	public List<int> GetParcelVacancyPositionGroup(int int_109)
	{
		var num = 0;
		List<int> list = new();
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 0)
			{
				num++;
				list.Add(i);
				if (num >= int_109)
				{
					break;
				}
			}
		}
		return list;
	}

	public int GetParcelVacancyNumber()
	{
		var num = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 0)
			{
				num++;
			}
		}
		return num;
	}

	public int GetParcelVacancyNumber_Test(int PID)
	{
		var num = 0;
		for (var i = 0; i < 96; i++)
		{
			if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 0)
			{
				num++;
			}
		}
		return num;
	}

	public Item GetCharacterPublicwarehouseType(int int_109, int int_110)
	{
		if (int_110 == 3)
		{
			var personalWarehouse = PersonalWarehouse;
			var array = personalWarehouse;
			foreach (var x_Vat_Pham_Loai in array)
			{
				if (BitConverter.ToInt32(x_Vat_Pham_Loai.VatPham_ID, 0) == int_109)
				{
					return x_Vat_Pham_Loai;
				}
			}
		}
		else
		{
			var publicWarehouse = PublicWarehouse;
			var array2 = publicWarehouse;
			foreach (var x_Vat_Pham_Loai2 in array2)
			{
				if (BitConverter.ToInt32(x_Vat_Pham_Loai2.VatPham_ID, 0) == int_109)
				{
					return x_Vat_Pham_Loai2;
				}
			}
		}
		return null;
	}

	public Item GetCharacterItemsGlobal_ID(Players players_0, long long_5)
	{
		var equipmentBarPackage = players_0.Item_In_Bag;
		var num = 0;
		Item x_Vat_Pham_Loai;
		while (true)
		{
			if (num < equipmentBarPackage.Length)
			{
				x_Vat_Pham_Loai = equipmentBarPackage[num];
				if (x_Vat_Pham_Loai.GetItemGlobal_ID == long_5)
				{
					break;
				}
				num++;
				continue;
			}
			return null;
		}
		return x_Vat_Pham_Loai;
	}

	public Item GetCharacterItemType_int(int int_109)
	{
		var equipmentBarPackage = Item_In_Bag;
		var num = 0;
		Item x_Vat_Pham_Loai;
		while (true)
		{
			if (num < equipmentBarPackage.Length)
			{
				x_Vat_Pham_Loai = equipmentBarPackage[num];
				if (x_Vat_Pham_Loai.GetVatPham_ID == int_109)
				{
					break;
				}
				num++;
				continue;
			}
			return null;
		}
		return x_Vat_Pham_Loai;
	}
	public Item GetEventBagItemByItemId(int int_109, int int_110)
	{
		var equipmentBarPackage = EventBag;
		var num = 0;
		Item x_Vat_Pham_Loai;
		while (true)
		{
			if (num < equipmentBarPackage.Length)
			{
				x_Vat_Pham_Loai = equipmentBarPackage[num];
				if (x_Vat_Pham_Loai.GetVatPham_ID == int_109 && x_Vat_Pham_Loai.FLD_MAGIC0 == int_110)
				{
					break;
				}
				num++;
				continue;
			}
			return null;
		}
		return x_Vat_Pham_Loai;
	}

	public Item GetCharacterBagItemByItemId(int int_109, int int_110)
	{
		var equipmentBarPackage = Item_In_Bag;
		var num = 0;
		Item x_Vat_Pham_Loai;
		while (true)
		{
			if (num < equipmentBarPackage.Length)
			{
				x_Vat_Pham_Loai = equipmentBarPackage[num];
				if (x_Vat_Pham_Loai.GetVatPham_ID == int_109 && x_Vat_Pham_Loai.FLD_MAGIC0 == int_110)
				{
					break;
				}
				num++;
				continue;
			}
			return null;
		}
		return x_Vat_Pham_Loai;
	}

	public Item GetCharacterPublicwarehouseType(int int_109, int int_110, int int_111)
	{
		if (int_110 == 3)
		{
			var personalWarehouse = PersonalWarehouse;
			var array = personalWarehouse;
			foreach (var x_Vat_Pham_Loai in array)
			{
				if (BitConverter.ToInt32(x_Vat_Pham_Loai.VatPham_ID, 0) == int_109 && x_Vat_Pham_Loai.FLD_MAGIC0 == int_111)
				{
					return x_Vat_Pham_Loai;
				}
			}
		}
		else
		{
			var publicWarehouse = PublicWarehouse;
			var array2 = publicWarehouse;
			foreach (var x_Vat_Pham_Loai2 in array2)
			{
				if (BitConverter.ToInt32(x_Vat_Pham_Loai2.VatPham_ID, 0) == int_109 && x_Vat_Pham_Loai2.FLD_MAGIC0 == int_111)
				{
					return x_Vat_Pham_Loai2;
				}
			}
		}
		return null;
	}

	public void Update_CuongKhi_TrangBi(int index)
	{
		var item = Item_Wear[index];
		if (item.GetVatPham_ID != 0)
		{
			if (item.VatPham_ThuocTinh_Manh + (int)item.VatPham_ThuocTinh_ThemVao_CuongHoa == 14)
				FLD_CuongKhi_TrangBi += (int)(item.Vat_Pham_Luc_Phong_Ngu * 0.1);
			else if (item.VatPham_ThuocTinh_Manh + (int)item.VatPham_ThuocTinh_ThemVao_CuongHoa > 14)
				FLD_CuongKhi_TrangBi += (int)(item.Vat_Pham_Luc_Phong_Ngu * 0.2);
		}
	}
	public void UpdateCharacterData(Players players_0)
	{
		try
		{
			var updatedCharacterData = GetUpdatedCharacterData(players_0);
			if (updatedCharacterData != null && players_0.IsJoinWorld)
			{
				Client.SendPak(updatedCharacterData, 25600, players_0.SessionID);
			}
			if (players_0.CuaHangCaNhan != null && players_0.IsJoinWorld)
			{
				UpdateCuaHangCaNhanData(players_0);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Update Character Data 12345 error" + Client.PlayerSessionID + "|" + Client.ToString() + "      " + ex);
		}
	}

	public void UpdateCuaHangCaNhanData(Players players_0)
	{
		if (players_0.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa)
		{
			SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write4(players_0.SessionID);
			sendingClass.Write4(players_0.SessionID);
			sendingClass.Write2((byte)players_0.CuaHangCaNhan.StoreName.Length);
			sendingClass.Write(players_0.CuaHangCaNhan.StoreName, 0, players_0.CuaHangCaNhan.StoreName.Length);
			if (Client != null)
			{
				Client.SendPak(sendingClass, 51712, SessionID);
			}
		}
	}

	public void UpdateBroadcastCharacterData()
	{
		try
		{
			var updatedCharacterData = GetUpdatedCharacterData(Client.Player);
			if (updatedCharacterData != null)
			{
				SendCurrentRangeBroadcastData(updatedCharacterData, 25600, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Update Broadcast Character Data error" + Client.PlayerSessionID + "|" + Client.ToString() + "      " + ex);
		}
	}
	public SendingClass GetUpdatedCharacterDataOld(Players Play)
	{
		SendingClass sendingClass = new();
		try
		{
			if (Play == null)
			{
				return null;
			}
			if (Play.Client == null)
			{
				return null;
			}
			sendingClass.Write4(1);
			sendingClass.Write4(Play.SessionID);
			if (World.tmc_flag && Play.MapID == 801)
			{
				sendingClass.WriteName(" ");
			}
			else
			{
				sendingClass.WriteName(Play.CharacterName);
			}
			sendingClass.Write4(Play.GuildId);
			sendingClass.WriteName(Play.GuildName);
			sendingClass.Write(Play.GangCharacterLevel);
			if (Play.GangBadge != null)
			{
				sendingClass.Write2(World.ServerGroupID);
			}
			else
			{
				sendingClass.Write2(0);
			}
			sendingClass.Write(Play.Player_Zx);
			sendingClass.Write(Play.Player_Level);
			sendingClass.Write(Play.Player_Job_level);
			sendingClass.Write(Play.Player_Job);
			sendingClass.Write(1);
			sendingClass.Write2(Play.NewCharacterTemplate.MauToc);
			sendingClass.Write2(Play.NewCharacterTemplate.KieuToc);
			sendingClass.Write2(0);
			sendingClass.Write4(0);

			sendingClass.Write2(Play.NewCharacterTemplate.KhuonMat);
			sendingClass.Write1(Play.NewCharacterTemplate.AmThanh);
			sendingClass.Write1(Play.NewCharacterTemplate.GioiTinh);
			sendingClass.Write(Play.PosX);
			sendingClass.Write(Play.PosZ);
			sendingClass.Write(Play.PosY);
			sendingClass.Write4(Play.MapID);

			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[0].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[1].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[2].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[4].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[3].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[5].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[9].VatPham_ID, 0));
			sendingClass.Write4(0);
			sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[10].VatPham_ID, 0));
			sendingClass.Write4(0);
			if (Play.MapID != 9001 && Play.MapID != 9101 && Play.MapID != 9201)
			{
				sendingClass.Write(Play.Item_Wear[3].FLD_CuongHoaSoLuong);
			}
			else
			{
				sendingClass.Write(0);
			}
			if (Play.MapID == 801 && World.tmc_flag)
			{
				if (Play.TheLucChien_PhePhai == "CHINH_PHAI")
				{
					if (Play.Player_Sex == 1)
					{
						sendingClass.Write4(World.Hieu_Ung_Ao_Choang_Chinh_Nam);
					}
					else
					{
						sendingClass.Write4(World.Hieu_Ung_Ao_Choang_Chinh_Nu);
					}
				}
				else if (Play.Player_Sex == 1)
				{
					sendingClass.Write4(World.Hieu_Ung_Ao_Choang_Ta_Nam);
				}
				else
				{
					sendingClass.Write4(World.Hieu_Ung_Ao_Choang_Ta_Nu);
				}
			}
			else
			{
				sendingClass.Write4(BitConverter.ToInt32(Play.Item_Wear[11].VatPham_ID, 0));
			}
			sendingClass.Write4(0);
			var num = ConfigClass.GetConfig(Play.Config, Play.MapID);
			if (Play.AppendStatusList != null && Play.AppendStatusList.ContainsKey(700014))
			{
				num += 8;
			}
			if (Play.tracking_status)
			{
				sendingClass.Write(num + 1);
			}
			else if (Play.tracking_status_id1 == 0)
			{
				sendingClass.Write(num);
			}
			else
			{
				sendingClass.Write(num);
			}
			if (Play.Player_Job == 7)
			{
				if (Play.AppendStatusList != null)
				{
					if (Play.AppendStatusList.ContainsKey(900401))
					{
						sendingClass.Write(18);
					}
					else if (Play.AppendStatusList.ContainsKey(900402))
					{
						sendingClass.Write(34);
					}
					else if (Play.AppendStatusList.ContainsKey(900403))
					{
						sendingClass.Write(66);
					}
					else if (Play.Config.VoHuanSwitchOnOff == 90 && Play.TitleDrug.ContainsKey(1008001478) && (!World.tmc_flag || Play.MapID != 801))
					{
						if (Play.MapID != 801)
						{
							sendingClass.Write(8);
						}
						else
						{
							sendingClass.Write(0);
						}
					}
					else if (Play.Config.VoHuanSwitchOnOff != 0 && (!World.tmc_flag || Play.MapID != 801))
					{
						sendingClass.Write(1);
					}
					else
					{
						sendingClass.Write(0);
					}
				}
				else if (Play.Config.VoHuanSwitchOnOff == 90 && Play.TitleDrug.ContainsKey(1008001478) && (!World.tmc_flag || Play.MapID != 801))
				{
					if (Play.Config.ChuyenDoiToc_OnOff != 1 && Play.Config.ChuyenDoiToc_OnOff != 0)
					{
						sendingClass.Write(136);
					}
					else
					{
						sendingClass.Write(8);
					}
				}
				else if (Play.Config.VoHuanSwitchOnOff != 0 && (!World.tmc_flag || Play.MapID != 801))
				{
					if (Play.Config.ChuyenDoiToc_OnOff != 1 && Play.Config.ChuyenDoiToc_OnOff != 0)
					{
						sendingClass.Write(129);
					}
					else
					{
						sendingClass.Write(1);
					}
				}
				else if (Play.Config.VoHuanSwitchOnOff == 0 && (!World.tmc_flag || Play.MapID != 801))
				{
					if (Play.Config.ChuyenDoiToc_OnOff != 1 && Play.Config.ChuyenDoiToc_OnOff != 0)
					{
						sendingClass.Write(128);
					}
					else
					{
						sendingClass.Write(0);
					}
				}
				else
				{
					sendingClass.Write(0);
				}
			}
			else if (Play.Config.VoHuanSwitchOnOff == 90 && Play.TitleDrug.ContainsKey(1008001478) && (!World.tmc_flag || Play.MapID != 801))
			{
				if (Play.Config.ChuyenDoiToc_OnOff != 1 && Play.Config.ChuyenDoiToc_OnOff != 0)
				{
					sendingClass.Write(136);
				}
				else if (Play.MapID != 801)
				{
					sendingClass.Write(8);
				}
				else
				{
					sendingClass.Write(0);
				}
			}
			else if (Play.Config.VoHuanSwitchOnOff != 0 && (!World.tmc_flag || Play.MapID != 801) && Play.MapID != 40101)
			{
				if (Play.Config.ChuyenDoiToc_OnOff != 1 && Play.Config.ChuyenDoiToc_OnOff != 0)
				{
					sendingClass.Write(129);
				}
				else
				{
					sendingClass.Write(1);
				}
			}
			else if (Play.Config.VoHuanSwitchOnOff == 0 && (!World.tmc_flag || Play.MapID != 801))
			{
				if (Play.Config.ChuyenDoiToc_OnOff != 1 && Play.Config.ChuyenDoiToc_OnOff != 0)
				{
					sendingClass.Write(128);
				}
				else
				{
					sendingClass.Write(0);
				}
			}
			else
			{
				sendingClass.Write(0);
			}
			sendingClass.Write2(0);
			sendingClass.Write(Play.PosX);
			sendingClass.Write(Play.PosZ);
			sendingClass.Write(Play.PosY);
			sendingClass.Write4(0);
			if (Play.CharacterBeast != null)
			{
				if (Play.CharacterBeast.CuoiThu == 1)
				{
					sendingClass.Write4(3);
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			else
			{
				sendingClass.Write4(255);
			}
			sendingClass.Write(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write2(0);
			sendingClass.Write2(0);
			sendingClass.Write4(Play.Player_WuXun);
			if (Play.MapID == 801)
			{
				sendingClass.Write4(0);
			}
			else
			{
				sendingClass.Write4(Play.NhanVatThienVaAc);
			}
			sendingClass.Write2(0);
			if (Play.CharacterPKMode != 0 && Play.MapID != 801 && Play.GMMode != 8)
			{
				sendingClass.Write2(Play.CharacterPKMode);
			}
			else
			{
				sendingClass.Write2(0);
			}
			if (Play.AppendStatusList != null)
			{
				if (Play.AppendStatusList.ContainsKey(1008000183))
				{
					sendingClass.Write2(2);
				}
				else if (Play.AppendStatusList.ContainsKey(1008000156))
				{
					sendingClass.Write2(1);
				}
				else if (Play.AppendStatusList.ContainsKey(1008000195))
				{
					sendingClass.Write2(4);
				}
				else if (Play.AppendStatusList.ContainsKey(1008000187))
				{
					sendingClass.Write2(3);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			else
			{
				sendingClass.Write2(0);
			}
			sendingClass.Write2(Play.HinhThuc_TangHinh);
			if (Play.AppendStatusList != null)
			{
				if (Play.AppendStatusList.ContainsKey(1008000188))
				{
					sendingClass.Write2(1);
				}
				else if (Play.AppendStatusList.ContainsKey(1008000252))
				{
					sendingClass.Write2(20);
				}
				else if (Play.AppendStatusList.ContainsKey(1008000245))
				{
					sendingClass.Write2(8);
				}
				else if (Play.AppendStatusList.ContainsKey(1008000232))
				{
					sendingClass.Write2(6);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			else
			{
				sendingClass.Write2(0);
			}
			sendingClass.Write(Play.CharacterNameTemplate, 0, 16);
			sendingClass.Write(Play.CharacterNameTemplate, 16, 16);
			sendingClass.Write(Play.CharacterNameTemplate, 32, 16);
			sendingClass.Write(0);
			if (Play.Config.RauQua_SwitchOnOff == 1)
			{
				if (Play.AppendStatusList != null)
				{
					if (!Play.AppendStatusList.ContainsKey(1008000240) && !Play.AppendStatusList.ContainsKey(1008000241) && !Play.AppendStatusList.ContainsKey(1008000242))
					{
						if (!Play.AppendStatusList.ContainsKey(1008000250) && !Play.AppendStatusList.ContainsKey(1008000251))
						{
							if (!Play.AppendStatusList.ContainsKey(1008000304) && !Play.AppendStatusList.ContainsKey(1008000305))
							{
								if (!Play.AppendStatusList.ContainsKey(1008000306) && !Play.AppendStatusList.ContainsKey(1008000307))
								{
									if (!Play.AppendStatusList.ContainsKey(1008000325) && !Play.AppendStatusList.ContainsKey(1008000326))
									{
										if (!Play.AppendStatusList.ContainsKey(1008001111) && !Play.AppendStatusList.ContainsKey(1008001112) && !Play.AppendStatusList.ContainsKey(1008001113) && !Play.AppendStatusList.ContainsKey(1008001114))
										{
											sendingClass.Write(0);
										}
										else
										{
											sendingClass.Write(5);
										}
									}
									else
									{
										sendingClass.Write(4);
									}
								}
								else
								{
									sendingClass.Write(3);
								}
							}
							else
							{
								sendingClass.Write(3);
							}
						}
						else
						{
							sendingClass.Write(2);
						}
					}
					else
					{
						sendingClass.Write(1);
					}
				}
				else
				{
					sendingClass.Write(0);
				}
			}
			else
			{
				sendingClass.Write(0);
			}
			sendingClass.Write2(0);
			sendingClass.Write(0);
			if (Play.FLD_Couple.Length != 0)
			{
				sendingClass.Write(1);
				sendingClass.WriteName(Play.FLD_Couple);
				if (Play.WhetherMarried == 1)
				{
					sendingClass.Write2(Play.FLD_loveDegreeLevel);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			else
			{
				sendingClass.Write(0);
				sendingClass.WriteName(string.Empty);
				sendingClass.Write2(0);
			}
			sendingClass.Write(0);
			sendingClass.Write2(0);
			if (Play.MapID != 9001 && Play.MapID != 9101 && Play.MapID != 9201)
			{
				sendingClass.Write(Play.Item_Wear[3].FLD_FJ_TienHoa);
				sendingClass.Write(Play.Item_Wear[3].FLDThuocTinhLoaiHinh);
				sendingClass.Write(Play.Item_Wear[3].FLDThuocTinhSoLuong);
			}
			else
			{
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
			}
			if (Play.NhanVat_BatTu == 1)
			{
				sendingClass.Write(8);
			}
			else
			{
				sendingClass.Write(0);
			}
			sendingClass.Write2(0);
			if (Play.MapID != 9001 && Play.MapID != 9101 && Play.MapID != 9201)
			{
				sendingClass.Write4(Play.VoHuanGiaiDoan);
				sendingClass.Write2(Play.FLD_HonorID);
			}
			else
			{
				sendingClass.Write4(0);
				sendingClass.Write2(0);
			}
			if (Play.FLD_PVP_Piont != 0)
			{
			}
			if (Play.MapID == 801)
			{
				sendingClass.Write(0);
			}
			else if (Play.MapID != 9001 && Play.MapID != 9101 && Play.MapID != 9201)
			{
				if (Play.MapID == 7301)
				{
					if (Play.GangCharacterLevel == 6)
					{
						sendingClass.Write(1);
					}
					else
					{
						sendingClass.Write(0);
					}
				}
				else
				{
					sendingClass.Write(TitleRanking);
				}
			}
			else
			{
				sendingClass.Write(0);
			}
			sendingClass.Write2(Play.MartialTitleType);
			if (Play.CharacterBeast != null)
			{
				if (Play.MapID != 9001 && Play.MapID != 9101 && Play.MapID != 9201)
				{
					sendingClass.Write(1);
					sendingClass.Write2(Play.CharacterBeastFullServiceID);
					sendingClass.WriteString(Play.CharacterBeast.Name, 16);
					sendingClass.Write4(0);
					sendingClass.Write1(Play.CharacterBeast.FLD_LEVEL);
					sendingClass.Write1(Play.CharacterBeast.FLD_JOB_LEVEL);
					sendingClass.Write1(Play.CharacterBeast.FLD_JOB);
					sendingClass.Write1(Play.CharacterBeast.Bs);
					sendingClass.Write1(0);
					if (Play.CharacterBeast.VoCongMoi[0, 3] != null)
					{
						sendingClass.Write4(Play.CharacterBeast.VoCongMoi[0, 3].FLD_PID);
					}
					else if (Play.CharacterBeast.VoCongMoi[0, 2] != null)
					{
						sendingClass.Write4(Play.CharacterBeast.VoCongMoi[0, 2].FLD_PID);
					}
					else if (Play.CharacterBeast.VoCongMoi[0, 1] != null)
					{
						sendingClass.Write4(Play.CharacterBeast.VoCongMoi[0, 1].FLD_PID);
					}
					else
					{
						sendingClass.Write4(0);
					}
					sendingClass.Write2(3);
					for (var i = 0; i < 5; i++)
					{
						sendingClass.Write4((int)Play.CharacterBeast.ThuCungVaTrangBi[i].GetVatPham_ID);
					}
				}
				else
				{
					sendingClass.Write2(1);
					for (var j = 0; j < 13; j++)
					{
						sendingClass.Write4(0);
					}
				}
			}
			else
			{
				sendingClass.Write2(1);
				for (var k = 0; k < 13; k++)
				{
					sendingClass.Write4(0);
				}
			}
			sendingClass.Write1(0);
			sendingClass.Write4(0);
			if (Play.KiemTraLietNhatViemViemTrangThai() && Play.KiemTraDocXaXuatDongTrangThai() && Play.KiemTraAiHongBienDaTrangThai() && KiemTraTriTanTrangThai())
			{
				sendingClass.Write2(71);
			}
			else if (Play.KiemTraDocXaXuatDongTrangThai() && Play.KiemTraAiHongBienDaTrangThai() && KiemTraTriTanTrangThai())
			{
				sendingClass.Write2(69);
			}
			else if (Play.KiemTraLietNhatViemViemTrangThai() && Play.KiemTraDocXaXuatDongTrangThai() && Play.KiemTraAiHongBienDaTrangThai())
			{
				sendingClass.Write2(7);
			}
			else if (Play.KiemTraDocXaXuatDongTrangThai() && KiemTraTriTanTrangThai())
			{
				sendingClass.Write2(66);
			}
			else if (Play.KiemTraAiHongBienDaTrangThai() && KiemTraTriTanTrangThai())
			{
				sendingClass.Write2(68);
			}
			else if (Play.KiemTraLietNhatViemViemTrangThai() && KiemTraTriTanTrangThai())
			{
				sendingClass.Write2(65);
			}
			else if (Play.KiemTraLietNhatViemViemTrangThai() && Play.KiemTraDocXaXuatDongTrangThai())
			{
				sendingClass.Write2(3);
			}
			else if (Play.KiemTraLietNhatViemViemTrangThai() && Play.KiemTraAiHongBienDaTrangThai())
			{
				sendingClass.Write2(5);
			}
			else if (Play.KiemTraDocXaXuatDongTrangThai() && Play.KiemTraAiHongBienDaTrangThai())
			{
				sendingClass.Write2(6);
			}
			else if (Play.KiemTraTriTanTrangThai())
			{
				sendingClass.Write2(64);
			}
			else if (Play.KiemTraLietNhatViemViemTrangThai())
			{
				sendingClass.Write2(1);
			}
			else if (Play.KiemTraDocXaXuatDongTrangThai())
			{
				sendingClass.Write2(2);
			}
			else if (Play.KiemTraAiHongBienDaTrangThai())
			{
				sendingClass.Write2(4);
			}
			else if (Play.DCH_StackC == 4000)
			{
				sendingClass.Write2(32);
			}
			else
			{
				sendingClass.Write2(0);
			}
			sendingClass.Write1(0);
			sendingClass.Write2(0);
			if (Play.Item_Wear[0].FLD_CuongHoaSoLuong >= 15 && Play.Item_Wear[1].FLD_CuongHoaSoLuong >= 15 && Play.Item_Wear[2].FLD_CuongHoaSoLuong >= 15 && Play.Item_Wear[4].FLD_CuongHoaSoLuong >= 15 && Play.Item_Wear[5].FLD_CuongHoaSoLuong >= 15)
			{
				sendingClass.Write(1);
			}
			else
			{
				sendingClass.Write(0);
			}
			sendingClass.Write4(0);
			sendingClass.Write4(uint.MaxValue);
			if (Play.Item_Wear[3].FLD_TuLinh != 0)
			{
				sendingClass.Write2(Play.Item_Wear[3].FLD_TuLinh);
			}
			else
			{
				sendingClass.Write2(0);
			}
			if (Play.Item_Wear[0].FLD_TuLinh != 0)
			{
				sendingClass.Write2(Play.Item_Wear[0].FLD_TuLinh);
			}
			else
			{
				sendingClass.Write2(0);
			}
			sendingClass.Write(0);
			sendingClass.Write(Play.Player_Zx);
			sendingClass.Write4((int)Play.Item_Wear[15].GetVatPham_ID);
			sendingClass.Write4(0);
			sendingClass.Write4(Play.Item_Wear[15].FLD_MAGIC1);
			sendingClass.Write4(Play.Item_Wear[15].FLD_MAGIC2);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			if (Play.CharacterBeast != null)
			{
				if (Play.CharacterBeast.ThuBay == 1)
				{
					sendingClass.Write(1);
				}
				else
				{
					sendingClass.Write(0);
				}
			}
			else
			{
				sendingClass.Write(0);
			}
			if (Play.MapID != 801)
			{
				sendingClass.Write4(Play.Player_Bien_Hinh_ID);
			}
			else
			{
				sendingClass.Write4(0);
			}
			sendingClass.Write4(0);
			return sendingClass;
		}
		catch (Exception ex)
		{
			var array = new string[8]
			{
				"Nhận dữ liệu nhân vật được cập nhật - lỗi !! ",
				Client.PlayerSessionID.ToString(),
				" - ",
				Client.ToString(),
				" - ",
				Converter.ToString(sendingClass.ToArray3()),
				" - ",
				null
			};
			array[7] = ex.ToString();
			LogHelper.WriteLine(LogLevel.Error, string.Concat(array));
			return null;
		}
	}

	public SendingClass GetUpdatedCharacterData(Players players, float posX, float posY, float MapId, int sessionID)
	{
		SendingClass packetDataClass = new();
		try
		{
			if (players == null)
				return null;
			if (players.Client == null)
				return null;
			var name = players.GMMode == 8
				? "GM"
				: players.MapID == 7001
					? "Ðôìi ðiòch"
					: players.Client.TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 1
						? players.CharacterName + "[Auto]"
						: players.CharacterName;
			var guildName = players.GMMode == 8 || players.MapID == 7001
				? "GM"
				: players.GuildName.Replace(" ", string.Empty);
			var gangBadge = players.GangBadge != null ? World.ServerGroupID : 0;
			var posZ = players.PosZ;
			//var posZ = players.PET_BAY == 0 ? players.Player_Zx : 270f;
			var isNotSpecialMap = !CheckUserOnMap(players, new[] { 9001, 9101, 9201 });
			var weapon_enhanced = isNotSpecialMap ? players.Item_Wear[3].FLD_CuongHoaSoLuong : 0;

			var costume = BitConverter.ToInt32(players.Item_Wear[11].VatPham_ID, 0);
			var walking_id = ConfigClass.GetConfig(players.Config, players.MapID) +
							 (players.AppendStatusList != null && players.AppendStatusList.ContainsKey(700014) ? 8 : 0);
			var vohuanEffect = players.Player_Job == 7
				? players.AppendStatusList != null
					? players.AppendStatusList.ContainsKey(900401)
						? 18
						: players.AppendStatusList.ContainsKey(900402)
							? 34
							: players.AppendStatusList.ContainsKey(900403)
								? 66
								: players.Config.VoHuanSwitchOnOff == 90 &&
								  (players.TitleDrug.ContainsKey(1008001478) ||
								   players.TitleDrug.ContainsKey(1008001479))
									? 8
									: players.Config.VoHuanSwitchOnOff != 0
										? 1
										: 0
					: players.Config.VoHuanSwitchOnOff == 90 &&
					  (players.TitleDrug.ContainsKey(1008001478) || players.TitleDrug.ContainsKey(1008001479))
						? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0 ? 136 : 8
						: players.Config.VoHuanSwitchOnOff != 0
							? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0
								? 129
								: 1
							: players.Config.VoHuanSwitchOnOff == 0
								? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0
									? 128
									: 0
								: 0
				: players.Config.VoHuanSwitchOnOff == 90 &&
				  (players.TitleDrug.ContainsKey(1008001478) || players.TitleDrug.ContainsKey(1008001479))
					? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0 ? 136 : 8
					: players.Config.VoHuanSwitchOnOff != 0
						? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0 ? 129 : 1
						: players.Config.VoHuanSwitchOnOff == 0
							? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0
								? 128
								: 0
							: 0;
			var camsu_status = players.AppendStatusList.ContainsKey(1008001624) ? 31 : players.CamSu_TrangThai;
			var mounting = players.CharacterBeast != null ? players.CharacterBeast.CuoiThu == 1 ? 3 : 0 : 255;
			var thien_ac = players.MapID == 801 && Player_Zx != players.Player_Zx
				? -11111
				: players.NhanVatThienVaAc;
			var pk_mode = players.CharacterPKMode != 0 && players.MapID != 801 && players.GMMode != 8
				? players.CharacterPKMode
				: 0;
			var stealthMode = players.Player_Job != 6 || players.MapID != 801 ? players.HinhThuc_TangHinh : 0;
			var prefix_buff_name = players.AppendStatusList != null
				? players.AppendStatusList.ContainsKey(1008000188)
					? 1
					: players.AppendStatusList.ContainsKey(1008000252)
						? 20
						: players.AppendStatusList.ContainsKey(1008000909)
							? 32
							: players.AppendStatusList.ContainsKey(1008000914)
								? 64
								: players.AppendStatusList.ContainsKey(1008000245)
									? 8
									: players.AppendStatusList.ContainsKey(1008000232)
										? 6
										: 0
				: 0;
			var weapon_costume = players.Config.RauQua_SwitchOnOff == 1
				? players.AppendStatusList != null
					? players.AppendStatusList.ContainsKey(1008000240) ||
					  players.AppendStatusList.ContainsKey(1008000241) ||
					  players.AppendStatusList.ContainsKey(1008000242)
						? 1
						: players.AppendStatusList.ContainsKey(1008000250) ||
						  players.AppendStatusList.ContainsKey(1008000251)
							? 2
							: players.AppendStatusList.ContainsKey(1008000304) ||
							  players.AppendStatusList.ContainsKey(1008000305) ||
							  players.AppendStatusList.ContainsKey(1008000306) ||
							  players.AppendStatusList.ContainsKey(1008000307)
								? 3
								: players.AppendStatusList.ContainsKey(1008000325) ||
								  players.AppendStatusList.ContainsKey(1008000326)
									? 4
									: players.AppendStatusList.ContainsKey(1008001350) ||
									  players.AppendStatusList.ContainsKey(1008001351) ||
									  players.AppendStatusList.ContainsKey(1008001111) ||
									  players.AppendStatusList.ContainsKey(1008001112)
										? 5
										: players.AppendStatusList.ContainsKey(1008002385) ||
										  players.AppendStatusList.ContainsKey(1008002386) ||
										  players.AppendStatusList.ContainsKey(1008002410)
											? 6
											: 0
					: 0
				: 0;
			// Couple
			var is_couple = players.FLD_Couple.Length != 0 ? 1 : 0;
			var couple_name = players.FLD_Couple.Length != 0
				? players.GMMode == 8 || players.MapID == 7001 ? " " : players.FLD_Couple
				: string.Empty;
			var couple_level = players.FLD_Couple.Length != 0 ? players.FLD_loveDegreeLevel : 0;

			// Thuoc tinh vu khi
			var weapon_attr_name = isNotSpecialMap ? players.Item_Wear[3].FLD_FJ_TienHoa : 0;
			var weapon_attribute = isNotSpecialMap ? players.Item_Wear[3].FLDThuocTinhLoaiHinh : 0;
			var weapon_attr_enhanced = isNotSpecialMap
				? players.Player_VoDich ? players.Item_Wear[3].FLDThuocTinhSoLuong : 0
				: 0;
			var mark_zx = CheckUserOnMap(players, new[] { 801, 7001 }) && Player_Zx != players.Player_Zx
				? 1
				: CheckUserOnMap(players, new[] { 801, 7001 })
					? 1
					: 0;

			// Vo HUan giai doan + xep hang thanh danh
			var isAllowedmap = CheckUserOnMap(players, new[] { 801, 9001, 9101, 9201 });
			var vohuan_giaidoan = !isAllowedmap ? players.VoHuanGiaiDoan : 0;
			var honor_ranking = !isAllowedmap ? players.FLD_HonorID : 0;

			var honor_id = !isAllowedmap
				? players.get_HonorID(players.CharacterName, 1) != 0 || GetAddState(players.HonorID_)
					? 1
					: players.get_HonorID(players.CharacterName, 2) != 0 || GetAddState(players.HonorID_)
						? 11
						: players.get_HonorID(players.CharacterName, 4) != 0
							? 2
							: players.get_HonorID(players.CharacterName, 5) != 0
								? 31
								: players.get_HonorID(players.CharacterName, 3) != 0
									? 51
									: TitleRanking
				: 0;
			honor_id = players.MapID == 7301 ? players.GangCharacterLevel == 6 ? 1 : 0 : honor_id;


			packetDataClass.Write4(1);
			packetDataClass.Write4(sessionID);
			packetDataClass.WriteName(name);
			packetDataClass.Write4(players.GuildId);
			packetDataClass.WriteName(guildName);
			packetDataClass.Write(players.GangCharacterLevel);
			packetDataClass.Write2(gangBadge);
			packetDataClass.Write(players.Player_Zx); //39
			packetDataClass.Write(players.Player_Level); // 3a
			packetDataClass.Write(players.Player_Job_level); //3b
			packetDataClass.Write(players.Player_Job); //3c
			packetDataClass.Write(0); //3d old = 0
			packetDataClass.Write2(players.NewCharacterTemplate.MauToc);
			packetDataClass.Write2(players.NewCharacterTemplate.KieuToc);
			packetDataClass.Write2(players.NewCharacterTemplate.KhuonMat);
			packetDataClass.Write2(0);
			packetDataClass.Write2(0);
			packetDataClass.Write2(0);
			packetDataClass.Write(players.NewCharacterTemplate.AmThanh); //4a
			packetDataClass.Write(players.NewCharacterTemplate.GioiTinh); //4b
			packetDataClass.Write(posX);
			packetDataClass.Write(posZ);
			packetDataClass.Write(posY);
			packetDataClass.Write4(MapID);

			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[0].VatPham_ID, 0)); // 5c
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[1].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[2].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[4].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[3].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[5].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[9].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[10].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write(weapon_enhanced);
			packetDataClass.Write4(costume); //DD -E0 Wrong 
			packetDataClass.Write4(0);
			packetDataClass.Write(walking_id);
			packetDataClass.Write(vohuanEffect);
			packetDataClass.Write2(camsu_status); //A7-A8
			packetDataClass.Write(posX); // a9-AC
			packetDataClass.Write(posZ);
			packetDataClass.Write(posY); // B1-B4
			packetDataClass.Write4(0);
			// Cuoi thu B9-BC //correct
			packetDataClass.Write4(mounting);

			packetDataClass.Write(0);
			// C1 C4 Vat pham 13
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[13].VatPham_ID, 0));
			packetDataClass.Write4(0); // C5 -8
			packetDataClass.Write4(players.GangDoorClothesColor); // C9
			packetDataClass.Write4(players.Player_WuXun); //CA-CD
														  // CB-CE
			packetDataClass.Write4(thien_ac);
			packetDataClass.Write2(players.NhanVoHuan_MoiNgay);
			packetDataClass.Write2(pk_mode); // PkMode D4 d5
											 //packetDataClass.Write2(0);
			packetDataClass.Write4(stealthMode);
			packetDataClass.Write2(prefix_buff_name); //DE-DF


			//packetDataClass.Write2(0);
			packetDataClass.Write(players.CharacterNameTemplate, 0, 16);
			packetDataClass.Write(players.CharacterNameTemplate, 16, 16);
			packetDataClass.Write(players.CharacterNameTemplate, 32, 16);

			packetDataClass.Write1(0);
			packetDataClass.Write2(weapon_costume); // 112-115
													////////////////////////////OK//////////////////////////////
			packetDataClass.Write2(0);
			packetDataClass.Write(is_couple);
			packetDataClass.WriteName(couple_name);
			packetDataClass.Write2(couple_level);
			packetDataClass.Write2(0); //117-125
			packetDataClass.Write1(0); //117-125
			packetDataClass.Write(weapon_attr_name);
			packetDataClass.Write(weapon_attribute);
			packetDataClass.Write(weapon_attr_enhanced);

			//// Mark 1
			packetDataClass.Write1(mark_zx);
			packetDataClass.Write2(0);
			packetDataClass.Write4(vohuan_giaidoan);
			packetDataClass.Write2(honor_ranking);
			packetDataClass.Write(honor_id);

			packetDataClass.Write2(players.MartialTitleType); // Original

			if (players.CharacterBeast != null && isNotSpecialMap)
			{
				packetDataClass.Write(1);
				packetDataClass.Write2(players.CharacterBeastFullServiceID);
				packetDataClass.WriteName(players.GMMode == 8 ? " " : players.CharacterBeast.Name);
				packetDataClass.Write4(0);
				packetDataClass.Write(0);
				packetDataClass.Write(players.CharacterBeast.FLD_LEVEL);
				packetDataClass.Write(players.CharacterBeast.FLD_JOB_LEVEL);
				packetDataClass.Write(players.CharacterBeast.FLD_JOB);
				packetDataClass.Write(players.CharacterBeast.Bs);
				packetDataClass.Write(0);
				var fldPid = 0;
				var writeValue = 0;
				for (var i = 3; i >= 1; i--)
					if (players.CharacterBeast.VoCongMoi[0, i] != null)
					{
						fldPid = players.CharacterBeast.VoCongMoi[0, i].FLD_PID;
						writeValue = 1;
						break;
					}

				packetDataClass.Write4(fldPid);
				packetDataClass.Write2(writeValue);
				for (var i = 0; i < 5; i++)
					packetDataClass.Write4((int)players.CharacterBeast.ThuCungVaTrangBi[i].GetVatPham_ID);
				packetDataClass.Write4(256);
			}
			else
			{
				packetDataClass.Write2(0);
				for (var k = 0; k < 14; k++)
					packetDataClass.Write4(0);
			}

			packetDataClass.Write2(0);
			packetDataClass.Write2(ThangThien4TrangThai());
			packetDataClass.Write2(0);

			if (players.Item_Wear[3].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[0].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[5].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[1].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[2].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[4].FLD_CuongHoaSoLuong >= 17)
				packetDataClass.Write2(1);
			else
				packetDataClass.Write2(0); //  

			//
			packetDataClass.Write2(0);
			// packetDataClass.Write2(isNotSpecialMap && players.EquippedInTheEquipmentSlot[3].FLD_CuongHoaSoLuong >= World.Gioi_Hang_Cuong_hoa_trang_bi_vu_khi ? 1 : 0);
			packetDataClass.Write(0);
			packetDataClass.Write4(uint.MaxValue);
			packetDataClass.Write2(
				players.Item_Wear[3].FLD_TuLinh != 0 ? players.Item_Wear[3].FLD_TuLinh : 0);
			packetDataClass.Write2(
				players.Item_Wear[0].FLD_TuLinh != 0 ? players.Item_Wear[0].FLD_TuLinh : 0);
			packetDataClass.Write(0);
			packetDataClass.Write(players.Player_Zx);
			packetDataClass.Write4((int)players.Item_Wear[15].GetVatPham_ID);
			packetDataClass.Write4(0);
			packetDataClass.Write4(players.Item_Wear[15].FLD_MAGIC0);
			packetDataClass.Write4(players.Item_Wear[15].FLD_MAGIC1);
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write(new byte[32], 0, 32);
			//
			packetDataClass.Write(0);
			//packetDataClass.Write(players.CharacterBeast != null && players.PET_BAY == 1 ? players.PET_BAY : 0);
			packetDataClass.Write4(Player_Bien_Hinh_ID); //  1600101
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			return packetDataClass;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GetUpdatedCharacterData Error" + ex.StackTrace);
			return null;
		}
	}

	public SendingClass GetUpdatedCharacterData(Players players)
	{
		SendingClass packetDataClass = new();
		try
		{
			if (players == null)
				return null;
			if (players.Client == null)
				return null;
			var name = players.GMMode == 8
				? "GM"
				: players.MapID == 7001
					? "Ðôìi ðiòch"
					: players.Client.TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 1
						? players.CharacterName + "[Auto]"
						: players.CharacterName;
			var guildName = players.GMMode == 8 || players.MapID == 7001
				? "GM"
				: players.GuildName.Replace(" ", string.Empty);
			var gangBadge = players.GangBadge != null ? World.ServerGroupID : 0;
			var posZ = players.PosZ;
			//var posZ = players.PET_BAY == 0 ? players.Player_Zx : 270f;
			var isNotSpecialMap = !CheckUserOnMap(players, new[] { 9001, 9101, 9201 });
			var weapon_enhanced = isNotSpecialMap ? players.Item_Wear[3].FLD_CuongHoaSoLuong : 0;

			var costume = BitConverter.ToInt32(players.Item_Wear[11].VatPham_ID, 0);
			var walking_id = ConfigClass.GetConfig(players.Config, players.MapID) +
							 (players.AppendStatusList != null && players.AppendStatusList.ContainsKey(700014) ? 8 : 0);
			var vohuanEffect = players.Player_Job == 7
				? players.AppendStatusList != null
					? players.AppendStatusList.ContainsKey(900401)
						? 18
						: players.AppendStatusList.ContainsKey(900402)
							? 34
							: players.AppendStatusList.ContainsKey(900403)
								? 66
								: players.Config.VoHuanSwitchOnOff == 90 &&
								  (players.TitleDrug.ContainsKey(1008001478) ||
								   players.TitleDrug.ContainsKey(1008001479))
									? 8
									: players.Config.VoHuanSwitchOnOff != 0
										? 1
										: 0
					: players.Config.VoHuanSwitchOnOff == 90 &&
					  (players.TitleDrug.ContainsKey(1008001478) || players.TitleDrug.ContainsKey(1008001479))
						? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0 ? 136 : 8
						: players.Config.VoHuanSwitchOnOff != 0
							? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0
								? 129
								: 1
							: players.Config.VoHuanSwitchOnOff == 0
								? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0
									? 128
									: 0
								: 0
				: players.Config.VoHuanSwitchOnOff == 90 &&
				  (players.TitleDrug.ContainsKey(1008001478) || players.TitleDrug.ContainsKey(1008001479))
					? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0 ? 136 : 8
					: players.Config.VoHuanSwitchOnOff != 0
						? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0 ? 129 : 1
						: players.Config.VoHuanSwitchOnOff == 0
							? players.Config.ChuyenDoiToc_OnOff != 1 && players.Config.ChuyenDoiToc_OnOff != 0
								? 128
								: 0
							: 0;
			var camsu_status = players.AppendStatusList.ContainsKey(1008001624) ? 31 : players.CamSu_TrangThai;
			var mounting = players.CharacterBeast != null ? players.CharacterBeast.CuoiThu == 1 ? 3 : 0 : 255;
			var thien_ac = players.MapID == 801 && Player_Zx != players.Player_Zx
				? -11111
				: players.NhanVatThienVaAc;
			var pk_mode = players.CharacterPKMode != 0 && players.MapID != 801 && players.GMMode != 8
				? players.CharacterPKMode
				: 0;
			var stealthMode = players.Player_Job != 6 || players.MapID != 801 ? players.HinhThuc_TangHinh : 0;
			var prefix_buff_name = players.AppendStatusList != null
				? players.AppendStatusList.ContainsKey(1008000188)
					? 1
					: players.AppendStatusList.ContainsKey(1008000252)
						? 20
						: players.AppendStatusList.ContainsKey(1008000909)
							? 32
							: players.AppendStatusList.ContainsKey(1008000914)
								? 64
								: players.AppendStatusList.ContainsKey(1008000245)
									? 8
									: players.AppendStatusList.ContainsKey(1008000232)
										? 6
										: 0
				: 0;
			var weapon_costume = players.Config.RauQua_SwitchOnOff == 1
				? players.AppendStatusList != null
					? players.AppendStatusList.ContainsKey(1008000240) ||
					  players.AppendStatusList.ContainsKey(1008000241) ||
					  players.AppendStatusList.ContainsKey(1008000242)
						? 1
						: players.AppendStatusList.ContainsKey(1008000250) ||
						  players.AppendStatusList.ContainsKey(1008000251)
							? 2
							: players.AppendStatusList.ContainsKey(1008000304) ||
							  players.AppendStatusList.ContainsKey(1008000305) ||
							  players.AppendStatusList.ContainsKey(1008000306) ||
							  players.AppendStatusList.ContainsKey(1008000307)
								? 3
								: players.AppendStatusList.ContainsKey(1008000325) ||
								  players.AppendStatusList.ContainsKey(1008000326)
									? 4
									: players.AppendStatusList.ContainsKey(1008001350) ||
									  players.AppendStatusList.ContainsKey(1008001351) ||
									  players.AppendStatusList.ContainsKey(1008001111) ||
									  players.AppendStatusList.ContainsKey(1008001112)
										? 5
										: players.AppendStatusList.ContainsKey(1008002385) ||
										  players.AppendStatusList.ContainsKey(1008002386) ||
										  players.AppendStatusList.ContainsKey(1008002410)
											? 6
											: 0
					: 0
				: 0;
			// Couple
			var is_couple = players.FLD_Couple.Length != 0 ? 1 : 0;
			var couple_name = players.FLD_Couple.Length != 0
				? players.GMMode == 8 || players.MapID == 7001 ? " " : players.FLD_Couple
				: string.Empty;
			var couple_level = players.FLD_Couple.Length != 0 ? players.FLD_loveDegreeLevel : 0;

			// Thuoc tinh vu khi
			var weapon_attr_name = isNotSpecialMap ? players.Item_Wear[3].FLD_FJ_TienHoa : 0;
			var weapon_attribute = isNotSpecialMap ? players.Item_Wear[3].FLDThuocTinhLoaiHinh : 0;
			var weapon_attr_enhanced = isNotSpecialMap
				? players.Player_VoDich ? players.Item_Wear[3].FLDThuocTinhSoLuong : 0
				: 0;
			var mark_zx = CheckUserOnMap(players, new[] { 801, 7001 }) && Player_Zx != players.Player_Zx
				? 1
				: CheckUserOnMap(players, new[] { 801, 7001 })
					? 1
					: 0;

			// Vo HUan giai doan + xep hang thanh danh
			var isAllowedmap = CheckUserOnMap(players, new[] { 801, 9001, 9101, 9201 });
			var vohuan_giaidoan = !isAllowedmap ? players.VoHuanGiaiDoan : 0;
			var honor_ranking = !isAllowedmap ? players.FLD_HonorID : 0;

			var honor_id = !isAllowedmap
				? players.get_HonorID(players.CharacterName, 1) != 0 || GetAddState(players.HonorID_)
					? 1
					: players.get_HonorID(players.CharacterName, 2) != 0 || GetAddState(players.HonorID_)
						? 11
						: players.get_HonorID(players.CharacterName, 4) != 0
							? 2
							: players.get_HonorID(players.CharacterName, 5) != 0
								? 31
								: players.get_HonorID(players.CharacterName, 3) != 0
									? 51
									: TitleRanking
				: 0;
			honor_id = players.MapID == 7301 ? players.GangCharacterLevel == 6 ? 1 : 0 : honor_id;


			packetDataClass.Write4(1);
			packetDataClass.Write4(players.SessionID);
			packetDataClass.WriteName(name);
			packetDataClass.Write4(players.GuildId);
			packetDataClass.WriteName(guildName);
			packetDataClass.Write(players.GangCharacterLevel);
			packetDataClass.Write2(gangBadge);
			packetDataClass.Write(players.Player_Zx); //39
			packetDataClass.Write(players.Player_Level); // 3a
			packetDataClass.Write(players.Player_Job_level); //3b
			packetDataClass.Write(players.Player_Job); //3c
			packetDataClass.Write(0); //3d old = 0
			packetDataClass.Write2(players.NewCharacterTemplate.MauToc);
			packetDataClass.Write2(players.NewCharacterTemplate.KieuToc);
			packetDataClass.Write2(players.NewCharacterTemplate.KhuonMat);
			packetDataClass.Write2(0);
			packetDataClass.Write2(0);
			packetDataClass.Write2(0);
			packetDataClass.Write(players.NewCharacterTemplate.AmThanh); //4a
			packetDataClass.Write(players.NewCharacterTemplate.GioiTinh); //4b
			packetDataClass.Write(players.PosX);
			packetDataClass.Write(posZ);
			packetDataClass.Write(players.PosY);
			packetDataClass.Write4(players.MapID);

			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[0].VatPham_ID, 0)); // 5c
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[1].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[2].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[4].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[3].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[5].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[9].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[10].VatPham_ID, 0));
			packetDataClass.Write4(0);
			packetDataClass.Write(weapon_enhanced);
			packetDataClass.Write4(costume); //DD -E0 Wrong 
			packetDataClass.Write4(0);
			packetDataClass.Write(walking_id);
			packetDataClass.Write(vohuanEffect);
			packetDataClass.Write2(camsu_status); //A7-A8
			packetDataClass.Write(players.PosX); // a9-AC
			packetDataClass.Write(posZ);
			packetDataClass.Write(players.PosY); // B1-B4
			packetDataClass.Write4(0);
			// Cuoi thu B9-BC //correct
			packetDataClass.Write4(mounting);

			packetDataClass.Write(0);
			// C1 C4 Vat pham 13
			packetDataClass.Write4(BitConverter.ToInt32(players.Item_Wear[13].VatPham_ID, 0));
			packetDataClass.Write4(0); // C5 -8
			packetDataClass.Write4(players.GangDoorClothesColor); // C9
			packetDataClass.Write4(players.Player_WuXun); //CA-CD
														  // CB-CE
			packetDataClass.Write4(thien_ac);
			packetDataClass.Write2(players.NhanVoHuan_MoiNgay);
			packetDataClass.Write2(pk_mode); // PkMode D4 d5
											 //packetDataClass.Write2(0);
			packetDataClass.Write4(stealthMode);
			packetDataClass.Write2(prefix_buff_name); //DE-DF


			//packetDataClass.Write2(0);
			packetDataClass.Write(players.CharacterNameTemplate, 0, 16);
			packetDataClass.Write(players.CharacterNameTemplate, 16, 16);
			packetDataClass.Write(players.CharacterNameTemplate, 32, 16);

			packetDataClass.Write1(0);
			packetDataClass.Write2(weapon_costume); // 112-115
													////////////////////////////OK//////////////////////////////
			packetDataClass.Write2(0);
			packetDataClass.Write(is_couple);
			packetDataClass.WriteName(couple_name);
			packetDataClass.Write2(couple_level);
			packetDataClass.Write2(0); //117-125
			packetDataClass.Write1(0); //117-125
			packetDataClass.Write(weapon_attr_name);
			packetDataClass.Write(weapon_attribute);
			packetDataClass.Write(weapon_attr_enhanced);

			//// Mark 1
			packetDataClass.Write1(mark_zx);
			packetDataClass.Write2(0);
			packetDataClass.Write4(vohuan_giaidoan);
			packetDataClass.Write2(honor_ranking);
			packetDataClass.Write(honor_id);

			packetDataClass.Write2(players.MartialTitleType); // Original

			if (players.CharacterBeast != null && isNotSpecialMap)
			{
				packetDataClass.Write(1);
				packetDataClass.Write2(players.CharacterBeastFullServiceID);
				packetDataClass.WriteName(players.GMMode == 8 ? " " : players.CharacterBeast.Name);
				packetDataClass.Write4(0);
				packetDataClass.Write(0);
				packetDataClass.Write(players.CharacterBeast.FLD_LEVEL);
				packetDataClass.Write(players.CharacterBeast.FLD_JOB_LEVEL);
				packetDataClass.Write(players.CharacterBeast.FLD_JOB);
				packetDataClass.Write(players.CharacterBeast.Bs);
				packetDataClass.Write(0);
				var fldPid = 0;
				var writeValue = 0;
				for (var i = 3; i >= 1; i--)
					if (players.CharacterBeast.VoCongMoi[0, i] != null)
					{
						fldPid = players.CharacterBeast.VoCongMoi[0, i].FLD_PID;
						writeValue = 1;
						break;
					}

				packetDataClass.Write4(fldPid);
				packetDataClass.Write2(writeValue);
				for (var i = 0; i < 5; i++)
					packetDataClass.Write4((int)players.CharacterBeast.ThuCungVaTrangBi[i].GetVatPham_ID);
				packetDataClass.Write4(256);
			}
			else
			{
				packetDataClass.Write2(0);
				for (var k = 0; k < 14; k++)
					packetDataClass.Write4(0);
			}

			packetDataClass.Write2(0);
			packetDataClass.Write2(ThangThien4TrangThai());
			packetDataClass.Write2(0);

			if (players.Item_Wear[3].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[0].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[5].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[1].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[2].FLD_CuongHoaSoLuong >= 17 &&
				players.Item_Wear[4].FLD_CuongHoaSoLuong >= 17)
				packetDataClass.Write2(1);
			else
				packetDataClass.Write2(0); //  

			//
			packetDataClass.Write2(0);
			// packetDataClass.Write2(isNotSpecialMap && players.EquippedInTheEquipmentSlot[3].FLD_CuongHoaSoLuong >= World.Gioi_Hang_Cuong_hoa_trang_bi_vu_khi ? 1 : 0);
			packetDataClass.Write(0);
			packetDataClass.Write4(uint.MaxValue);
			packetDataClass.Write2(
				players.Item_Wear[3].FLD_TuLinh != 0 ? players.Item_Wear[3].FLD_TuLinh : 0);
			packetDataClass.Write2(
				players.Item_Wear[0].FLD_TuLinh != 0 ? players.Item_Wear[0].FLD_TuLinh : 0);
			packetDataClass.Write(0);
			packetDataClass.Write(players.Player_Zx);
			packetDataClass.Write4((int)players.Item_Wear[15].GetVatPham_ID);
			packetDataClass.Write4(0);
			packetDataClass.Write4(players.Item_Wear[15].FLD_MAGIC0);
			packetDataClass.Write4(players.Item_Wear[15].FLD_MAGIC1);
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write(new byte[32], 0, 32);
			//
			packetDataClass.Write(0);
			//packetDataClass.Write(players.CharacterBeast != null && players.PET_BAY == 1 ? players.PET_BAY : 0);
			packetDataClass.Write4(Player_Bien_Hinh_ID); //  1600101
			packetDataClass.Write4(0);
			packetDataClass.Write4(0);
			packetDataClass.Write4((int)players.Item_Wear[17].GetVatPham_ID);
			packetDataClass.Write4(0);
			return packetDataClass;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GetUpdatedCharacterData Error" + ex.StackTrace);
			return null;
		}
	}


	public void UpdateVoHuanEffect()
	{
		NhanVat_WX_BUFF_SinhMenh = 0;
		NhanVat_WX_BUFF_CongKich = 0;
		NhanVat_WX_BUFF_PhongNgu = 0;
		Player_CLVC_VoHuan = 0.0;
		Player_ULPT_VoHuan = 0;
		NhanVat_WX_BUFF_NeTranh = 0;
		NhanVat_WX_BUFF_NoiCong = 0;
		NhanVat_WX_BUFF_TrungDich = 0;
		NhanVat_WX_BUFF_KhiCong = 0;
		CharactersAddMartialArtsPower = 0;
		NhanVatThemVaoNeTranhSkill = 0;
		if (Config.VoHuanSwitchOnOff != 0)
		{
			switch (VoHuanGiaiDoan)
			{
				case 2:
					NhanVat_WX_BUFF_SinhMenh = 50;
					NhanVat_WX_BUFF_NoiCong = 50;
					NhanVat_WX_BUFF_CongKich = 15;
					NhanVat_WX_BUFF_PhongNgu = 15;
					break;
				case 3:
					NhanVat_WX_BUFF_SinhMenh = 100;
					NhanVat_WX_BUFF_NoiCong = 100;
					NhanVat_WX_BUFF_CongKich = 30;
					NhanVat_WX_BUFF_PhongNgu = 30;
					break;
				case 4:
					NhanVat_WX_BUFF_SinhMenh = 150;
					NhanVat_WX_BUFF_NoiCong = 150;
					NhanVat_WX_BUFF_CongKich = 60;
					NhanVat_WX_BUFF_PhongNgu = 60;
					break;
				case 5:
					NhanVat_WX_BUFF_SinhMenh = 250;
					NhanVat_WX_BUFF_NoiCong = 250;
					NhanVat_WX_BUFF_CongKich = 100;
					NhanVat_WX_BUFF_PhongNgu = 100;
					Player_CLVC_VoHuan = 0.01;
					Player_ULPT_VoHuan = 100;
					break;
				case 6:
					NhanVat_WX_BUFF_SinhMenh = 500;
					NhanVat_WX_BUFF_NoiCong = 500;
					NhanVat_WX_BUFF_CongKich = 150;
					NhanVat_WX_BUFF_PhongNgu = 120;
					Player_CLVC_VoHuan = 0.02;
					Player_ULPT_VoHuan = 200;
					break;
				case 7:
					NhanVat_WX_BUFF_SinhMenh = 700;
					NhanVat_WX_BUFF_NoiCong = 700;
					NhanVat_WX_BUFF_CongKich = 200;
					NhanVat_WX_BUFF_PhongNgu = 150;
					Player_CLVC_VoHuan = 0.03;
					Player_ULPT_VoHuan = 300;
					NhanVat_WX_BUFF_KhiCong = 1;
					break;
				case 8:
					NhanVat_WX_BUFF_SinhMenh = 900;
					NhanVat_WX_BUFF_NoiCong = 900;
					NhanVat_WX_BUFF_CongKich = 250;
					NhanVat_WX_BUFF_PhongNgu = 200;
					Player_CLVC_VoHuan = 0.04;
					Player_ULPT_VoHuan = 400;
					NhanVat_WX_BUFF_KhiCong = 1;
					if (TitleDrug.ContainsKey(**********))
					{
						CharactersAddMartialArtsPower += 5;
						NhanVatThemVaoNeTranhSkill += 5;
					}
					else if (TitleDrug.ContainsKey(**********))
					{
						CharactersAddMartialArtsPower += 5;
						NhanVatThemVaoNeTranhSkill += 5;
					}
					break;
				case 9:
					NhanVat_WX_BUFF_SinhMenh = 1200;
					NhanVat_WX_BUFF_NoiCong = 1200;
					NhanVat_WX_BUFF_CongKich = 300;
					NhanVat_WX_BUFF_PhongNgu = 250;
					Player_CLVC_VoHuan = 0.05;
					Player_ULPT_VoHuan = 500;
					NhanVat_WX_BUFF_KhiCong = 2;
					if (TitleDrug.ContainsKey(**********))
					{
						CharactersAddMartialArtsPower += 7;
						NhanVatThemVaoNeTranhSkill += 7;
					}
					else if (TitleDrug.ContainsKey(**********))
					{
						CharactersAddMartialArtsPower += 7;
						NhanVatThemVaoNeTranhSkill += 7;
					}
					break;
			}
		}
		World.FindPlayerBySession(SessionID)?.CapNhat_HP_MP_SP();
	}

	public void UpdateMartialArtsAndStatus()
	{
		try
		{
			var flag = false;
			var flag2 = false;
			if ((Player_Zx == 1 && TitleDrug.ContainsKey(**********)) || (Player_Zx == 2 && TitleDrug.ContainsKey(**********)))
			{
				flag = true;
			}
			if ((Player_Zx == 1 && TitleDrug.ContainsKey(**********)) || (Player_Zx == 2 && TitleDrug.ContainsKey(**********)))
			{
				flag2 = true;
			}
			if (Player_WuXun > World.Wxlever[8] && flag2)
			{
				VoHuanGiaiDoan = 9;
			}
			else if (Player_WuXun > World.Wxlever[7] && flag)
			{
				VoHuanGiaiDoan = 8;
			}
			else if (Player_WuXun > World.Wxlever[6])
			{
				VoHuanGiaiDoan = 7;
			}
			else if (Player_WuXun > World.Wxlever[5])
			{
				VoHuanGiaiDoan = 6;
			}
			else if (Player_WuXun > World.Wxlever[4])
			{
				VoHuanGiaiDoan = 5;
			}
			else if (Player_WuXun > World.Wxlever[3])
			{
				VoHuanGiaiDoan = 4;
			}
			else if (Player_WuXun > World.Wxlever[2])
			{
				VoHuanGiaiDoan = 3;
			}
			else if (Player_WuXun > World.Wxlever[1])
			{
				VoHuanGiaiDoan = 2;
			}
			else if (Player_WuXun > World.Wxlever[0])
			{
				VoHuanGiaiDoan = 1;
			}
			UpdateVoHuanEffect();
			TinhToanNhanVat_VoHuanGiaDoan_VinhDu();
			SendingClass sendingClass = new();
			sendingClass.Write2(Player_Level);
			sendingClass.Write2(FLD_Tam + FLD_TrangBi_ThemVao_Tam);
			sendingClass.Write2(FLD_Luc + FLD_TrangBi_ThemVao_Luc);
			sendingClass.Write2(FLD_The + FLD_TrangBi_ThemVao_The);
			sendingClass.Write2(FLD_Than + FLD_TrangBi_ThemVao_Than);
			sendingClass.Write2(0);
			sendingClass.Write4(FLD_NhanVatCoBan_CongKich);
			sendingClass.Write4(FLD_NhanVatCoBan_PhongNgu);
			sendingClass.Write2(FLD_NhanVatCoBan_TrungDich);
			sendingClass.Write2(FLD_NhanVatCoBan_NeTranh);
			sendingClass.Write2(Player_Qigong_point);
			for (var i = 0; i < 15; i++)
			{
				if (i < 12)
				{
					sendingClass.Write2(KhiCong[i].KhiCongID);
					if (KhiCong[i].KhiCongID != 0)
					{
						if (KhiCong[i].KhiCong_SoLuong != 0)
						{
							var maxKhiCong_Tren1KhiCong = World.MaxKhiCong_Tren1KhiCong;
							var num = KhiCong[i].KhiCong_SoLuong + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + (int)NhanGiaTri_TangRieng_CuaKhiCong(i) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
							if (num > maxKhiCong_Tren1KhiCong)
							{
								num = maxKhiCong_Tren1KhiCong;
							}
							sendingClass.Write2(num);
						}
						else
						{
							sendingClass.Write2(0);
						}
					}
					else
					{
						sendingClass.Write2(0);
					}
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			sendingClass.Write2(0);
			for (var j = 0; j < 32; j++)
			{
				if (VoCongMoi[0, j] != null)
				{
					sendingClass.Write4(VoCongMoi[0, j].FLD_PID);
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			for (var k = 0; k < 28; k++)
			{
				if (VoCongMoi[1, k] != null)
				{
					sendingClass.Write4(VoCongMoi[1, k].FLD_PID);
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			sendingClass.Write4(0);
			for (var l = 0; l < 15; l++)
			{
				if (VoCongMoi[2, l] != null)
				{
					sendingClass.Write4(VoCongMoi[2, l].FLD_PID);
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			for (var m = 0; m < 32; m++)
			{
				if (VoCongMoi[0, m] != null)
				{
					sendingClass.Write2(VoCongMoi[0, m].VoCong_DangCap);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			for (var n = 0; n < 28; n++)
			{
				if (VoCongMoi[1, n] != null)
				{
					sendingClass.Write2(VoCongMoi[1, n].VoCong_DangCap);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			for (var num2 = 0; num2 < 16; num2++)
			{
				if (VoCongMoi[2, num2] != null)
				{
					sendingClass.Write2(VoCongMoi[2, num2].VoCong_DangCap);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			for (var num3 = 0; num3 < 32; num3++)
			{
				if (VoCongMoi[0, num3] != null)
				{
					sendingClass.Write2(VoCongMoi[0, num3].FLD_VoCongToiCaoDangCap);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			for (var num4 = 0; num4 < 28; num4++)
			{
				if (VoCongMoi[1, num4] != null)
				{
					sendingClass.Write2(VoCongMoi[1, num4].FLD_VoCongToiCaoDangCap);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			for (var num5 = 0; num5 < 16; num5++)
			{
				if (VoCongMoi[2, num5] != null)
				{
					sendingClass.Write2(VoCongMoi[2, num5].FLD_VoCongToiCaoDangCap);
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			if (VoCongMoi[2, 16] != null)
			{
				sendingClass.Write4(VoCongMoi[2, 16].FLD_PID);
			}
			else
			{
				sendingClass.Write4(0);
			}
			if (VoCongMoi[2, 17] != null)
			{
				sendingClass.Write4(VoCongMoi[2, 17].FLD_PID);
			}
			else
			{
				sendingClass.Write4(1);
			}
			sendingClass.Write4(NhanVat_LonNhat_VoCong_PhongNgu);
			sendingClass.Write4(Player_WuXun);
			sendingClass.Write4(NhanVatThienVaAc);
			sendingClass.Write4(Player_Job);
			sendingClass.Write4(100);
			sendingClass.Write2(1);
			if (ThangThienKhiCong != null && ThangThienKhiCong.Count > 0)
			{
				foreach (var value2 in ThangThienKhiCong.Values)
				{
					sendingClass.Write2(value2.KhiCongID);
					if (value2.KhiCong_SoLuong != 0)
					{
						var maxKhiCong_Tren1KhiCong2 = World.MaxKhiCong_Tren1KhiCong;
						var num6 = value2.KhiCong_SoLuong + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + (int)NhanGiaTri_TangRieng_CuaKhiCong(value2.KhiCongID) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC;
						if (num6 > maxKhiCong_Tren1KhiCong2)
						{
							num6 = maxKhiCong_Tren1KhiCong2;
						}
						sendingClass.Write2(num6);
					}
					else
					{
						sendingClass.Write2(0);
					}
				}
				for (var num7 = 0; num7 < 18 - ThangThienKhiCong.Count; num7++)
				{
					sendingClass.Write4(0);
				}
			}
			else
			{
				for (var num8 = 0; num8 < 18; num8++)
				{
					sendingClass.Write4(0);
				}
			}
			sendingClass.Write2(0);
			for (var num9 = 0; num9 < 32; num9++)
			{
				if (VoCongMoi[3, num9] != null)
				{
					sendingClass.Write4(VoCongMoi[3, num9].FLD_PID);
					sendingClass.Write4(VoCongMoi[3, num9].VoCong_DangCap);
				}
				else
				{
					sendingClass.Write4(0);
					sendingClass.Write4(0);
				}
			}
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(ThangThienVoCong_DiemSo);
			for (var num10 = 0; num10 < 15; num10++)
			{
				if (num10 < 12)
				{
					if (KhiCong[num10].KhiCongID != 0)
					{
						sendingClass.Write((byte)KhiCong[num10].KhiCong_SoLuong);
					}
					else
					{
						sendingClass.Write(0);
					}
				}
				else
				{
					sendingClass.Write(0);
				}
			}
			if (ThangThienKhiCong != null && ThangThienKhiCong.Count > 0)
			{
				foreach (var value3 in ThangThienKhiCong.Values)
				{
					sendingClass.Write((byte)value3.KhiCong_SoLuong);
				}
				for (var num11 = 0; num11 < 20 - ThangThienKhiCong.Count; num11++)
				{
					sendingClass.Write(0);
				}
			}
			else
			{
				for (var num12 = 0; num12 < 20; num12++)
				{
					sendingClass.Write(0);
				}
			}
			sendingClass.Write(0);
			sendingClass.Write4(100); //Met moi
			sendingClass.Write4(200); // Ativity Remain time
			sendingClass.Write4((int)(TotalSkillDamage * 10.0));
			sendingClass.Write4(Player_VoHoang); // Võ hoàng

			sendingClass.Write2(NhanVat_ThemVao_PVPChienLuc);
			sendingClass.Write2(NhanVoHuan_MoiNgay);
			sendingClass.Write4(MatDi_VoHuan);
			sendingClass.Write4(FLD_NhanVat_ThemVao_TanCong_QuaiVat);
			sendingClass.Write4(FLD_NhanVat_ThemVao_PhongThu_QuaiVat);
			sendingClass.Write4(MagicEvade);
			if (Player_Job == 13)
			{
				for (var num13 = 5; num13 < 23; num13++)
				{
					if (VoCongMoi[1, num13] != null)
					{
						sendingClass.Write4(VoCongMoi[1, num13].FLD_PID);
						sendingClass.Write4(VoCongMoi[1, num13].VoCong_DangCap);
						sendingClass.Write4(VoCongMoi[1, num13].FLD_VoCongToiCaoDangCap);
					}
					else
					{
						sendingClass.Write4(0);
						sendingClass.Write4(0);
						sendingClass.Write4(0);
					}
				}
				sendingClass.Write2(ThanNuVoCongDiemSo);
			}
			else
			{
				for (var num14 = 5; num14 < 23; num14++)
				{
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
				}
				sendingClass.Write2(0);
			}
			sendingClass.Write2(0);
			for (var num15 = 2001; num15 < 2028; num15++)
			{

				if (PhanKhiCong != null && PhanKhiCong.TryGetValue(num15, out var value))
				{
					if (value.KhiCong_SoLuong > 0)
					{
						var maxKhiCong_Tren1KhiCong3 = World.MaxKhiCong_Tren1KhiCong;
						var num16 = value.KhiCong_SoLuong + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + (int)NhanGiaTri_TangRieng_CuaKhiCong(value.KhiCongID) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
						if (num16 > maxKhiCong_Tren1KhiCong3)
						{
							num16 = maxKhiCong_Tren1KhiCong3;
						}
						sendingClass.Write2(value.KhiCongID);
						sendingClass.Write2(num16);
					}
					else
					{
						sendingClass.Write2(0);
						sendingClass.Write2(0);
					}
				}
				else
				{
					sendingClass.Write2(0);
					sendingClass.Write2(0);
				}
			}
			sendingClass.Write2(Player_Anti_Qigong_point);
			if (Client != null)
			{
				Client.SendPak(sendingClass, 27392, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Cập Nhật Võ Công Và Trạng Thái error !! [" + AccountID + "]-[" + CharacterName + "] " + ex.Message);
		}
	}

}

