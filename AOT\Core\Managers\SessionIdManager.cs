using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using HeroYulgang.Helpers;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Managers
{
    /// <summary>
    /// Quản lý SessionID cho tất cả các entity trong game
    /// Thread-safe và hỗ trợ release/reuse SessionID
    /// Mỗi channel có tối đa 1000 player với SessionID bắt đầu từ ServerID * 1000
    /// </summary>
    public sealed class SessionIdManager
    {
        private static readonly Lazy<SessionIdManager> _instance = new(() => new SessionIdManager());
        public static SessionIdManager Instance => _instance.Value;

        // Phân chia SessionID ranges - Player range được tính động dựa trên ServerID
        private readonly int PLAYER_MIN;
        private readonly int PLAYER_MAX;

        private const int NPC_MIN = 1;
        private const int NPC_MAX = 999;
        private const int NPC_MONSTER_MIN = 10001;
        private const int NPC_MONSTER_MAX = 39999;
        private const int PET_MIN = 40000;
        private const int PET_MAX = 65535;

        // Thread-safe pools cho từng loại entity
        private readonly ConcurrentQueue<int> _playerPool = new();

        private readonly ConcurrentQueue<int> _npcPool = new();
        private readonly ConcurrentQueue<int> _npcMonsterPool = new();
        private readonly ConcurrentQueue<int> _petPool = new();

        // Tracking allocated SessionIDs
        private readonly ConcurrentDictionary<int, SessionIdType> _allocatedSessions = new();

        // Locks for pool initialization
        private readonly object _playerPoolLock = new();
        private readonly object _npcMonsterPoolLock = new();
        private readonly object _petPoolLock = new();

        private readonly object _npcPoolLock = new();

        // Pool initialization flags
        private volatile bool _playerPoolInitialized = false;
        private volatile bool _npcMonsterPoolInitialized = false;
        private volatile bool _petPoolInitialized = false;
        private volatile bool _npcPoolInitialized = false;

        private SessionIdManager()
        {
            // Tính toán player range dựa trên ServerID
            // Mỗi channel có tối đa 1000 player slots
            var serverId = ConfigManager.Instance.ServerSettings.ServerId;
            PLAYER_MIN = serverId * 1000;
            PLAYER_MAX = PLAYER_MIN + 999; // 1000 slots per channel

            LogHelper.WriteLine(LogLevel.Info, $"SessionIdManager initialized for ServerID {serverId}");
            LogHelper.WriteLine(LogLevel.Info, $"Player SessionID range: {PLAYER_MIN} - {PLAYER_MAX} (1000 slots)");
        }

        /// <summary>
        /// Loại SessionID
        /// </summary>
        public enum SessionIdType
        {
            Player,
            NpcMonster,
            Pet,
            Npc
        }

        /// <summary>
        /// Khởi tạo pool cho Player SessionIDs
        /// </summary>
        private void InitializePlayerPool()
        {
            if (_playerPoolInitialized) return;

            lock (_playerPoolLock)
            {
                if (_playerPoolInitialized) return;

                for (int i = PLAYER_MIN; i <= PLAYER_MAX; i++)
                {
                    _playerPool.Enqueue(i);
                }
                _playerPoolInitialized = true;
                LogHelper.WriteLine(LogLevel.Info, $"Player SessionID pool initialized: {PLAYER_MIN}-{PLAYER_MAX} ({PLAYER_MAX - PLAYER_MIN + 1} slots)");
            }
        }

        /// <summary>
        /// Khởi tạo pool cho NPC/Monster SessionIDs
        /// </summary>
        private void InitializeNpcMonsterPool()
        {
            if (_npcMonsterPoolInitialized) return;

            lock (_npcMonsterPoolLock)
            {
                if (_npcMonsterPoolInitialized) return;

                for (int i = NPC_MONSTER_MIN; i <= NPC_MONSTER_MAX; i++)
                {
                    _npcMonsterPool.Enqueue(i);
                }
                _npcMonsterPoolInitialized = true;
                LogHelper.WriteLine(LogLevel.Info, $"NPC/Monster SessionID pool initialized: {NPC_MONSTER_MIN}-{NPC_MONSTER_MAX} ({NPC_MONSTER_MAX - NPC_MONSTER_MIN + 1} slots)");
            }
        }

        private void InitializeNpcPool()
        {
            if (_npcPoolInitialized) return;

            lock (_npcPoolLock)
            {
                if (_npcPoolInitialized) return;

                for (int i = NPC_MIN; i <= NPC_MAX; i++)
                {
                    _npcPool.Enqueue(i);
                }
                _npcPoolInitialized = true;
                LogHelper.WriteLine(LogLevel.Info, $"NPC SessionID pool initialized: {NPC_MIN}-{NPC_MAX} ({NPC_MAX - NPC_MIN + 1} slots)");
            }
        }

        /// <summary>
        /// Khởi tạo pool cho Pet SessionIDs
        /// </summary>
        private void InitializePetPool()
        {
            if (_petPoolInitialized) return;

            lock (_petPoolLock)
            {
                if (_petPoolInitialized) return;

                for (int i = PET_MIN; i <= PET_MAX; i++)
                {
                    _petPool.Enqueue(i);
                }
                _petPoolInitialized = true;
                LogHelper.WriteLine(LogLevel.Info, $"Pet SessionID pool initialized: {PET_MIN}-{PET_MAX} ({PET_MAX - PET_MIN + 1} slots)");
            }
        }

        /// <summary>
        /// Lấy SessionID cho Player (bao gồm cả offline player)
        /// </summary>
        /// <returns>SessionID hoặc -1 nếu pool đã hết</returns>
        public int AllocatePlayerSessionId()
        {
            InitializePlayerPool();

            if (_playerPool.TryDequeue(out int sessionId))
            {
                _allocatedSessions[sessionId] = SessionIdType.Player;
                LogHelper.WriteLine(LogLevel.Debug, $"Allocated Player SessionID: {sessionId}");
                return sessionId;
            }

            LogHelper.WriteLine(LogLevel.Warning, "Player SessionID pool exhausted!");
            return -1;
        }

        // Dictionary để lưu trữ SessionID counter cho mỗi map
        private static readonly ConcurrentDictionary<int, int> _mapSessionCounters = new();
        private const int MAX_SESSIONS_PER_MAP = 20000;

        /// <summary>
        /// Lấy SessionID cho NPC/Monster theo map cụ thể
        /// Mỗi map có riêng 20k SessionID từ 10001-30000
        /// </summary>
        /// <param name="mapId">ID của map để cấp phát SessionID</param>
        /// <returns>SessionID hoặc -1 nếu pool đã hết</returns>
        public int AllocateNpcMonsterSessionId(int mapId)
        {
            // Lấy counter hiện tại cho map này và tăng lên 1, bắt đầu từ NPC_MONSTER_MIN (10001)
            int sessionId = _mapSessionCounters.AddOrUpdate(mapId, NPC_MONSTER_MIN, (key, oldValue) =>
            {
                int nextId = oldValue + 1;
                // Reset về NPC_MONSTER_MIN nếu vượt quá giới hạn
                return nextId > (NPC_MONSTER_MIN + MAX_SESSIONS_PER_MAP - 1) ? NPC_MONSTER_MIN : nextId;
            });

            // Kiểm tra xem SessionID này đã được sử dụng chưa trong map này
            // Sử dụng tuple key để kiểm tra unique trong map
            if (_allocatedSessions.ContainsKey(sessionId))
            {
                // Tìm SessionID khả dụng tiếp theo trong range 1-30000
                for (int i = NPC_MONSTER_MIN; i <= (NPC_MONSTER_MIN + MAX_SESSIONS_PER_MAP - 1); i++)
                {
                    if (!_allocatedSessions.ContainsKey(i))
                    {
                        sessionId = i;
                        _mapSessionCounters[mapId] = i;
                        break;
                    }
                }

                if (_allocatedSessions.ContainsKey(sessionId))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"NPC/Monster SessionID pool exhausted for Map: {mapId}!");
                    return -1;
                }
            }

            _allocatedSessions[sessionId] = SessionIdType.NpcMonster;
            //LogHelper.WriteLine(LogLevel.Debug, $"Allocated NPC/Monster SessionID: {sessionId} for Map: {mapId}");
            return sessionId;
        }

        /// <summary>
        /// Lấy SessionID cho Pet
        /// </summary>
        /// <returns>SessionID hoặc -1 nếu pool đã hết</returns>
        public int AllocatePetSessionId()
        {
            InitializePetPool();

            if (_petPool.TryDequeue(out int sessionId))
            {
                _allocatedSessions[sessionId] = SessionIdType.Pet;
                LogHelper.WriteLine(LogLevel.Debug, $"Allocated Pet SessionID: {sessionId}");
                return sessionId;
            }

            LogHelper.WriteLine(LogLevel.Warning, "Pet SessionID pool exhausted!");
            return -1;
        }

        public int AllocateNpcSessionId()
        {
            InitializeNpcPool();

            if (_npcPool.TryDequeue(out int sessionId))
            {
                _allocatedSessions[sessionId] = SessionIdType.Npc;
                LogHelper.WriteLine(LogLevel.Debug, $"Allocated NPC SessionID: {sessionId}");
                return sessionId;
            }

            LogHelper.WriteLine(LogLevel.Warning, "NPC SessionID pool exhausted!");
            return -1;
        }

        /// <summary>
        /// Giải phóng SessionID để tái sử dụng
        /// </summary>
        /// <param name="sessionId">SessionID cần giải phóng</param>
        /// <returns>true nếu giải phóng thành công</returns>
        public bool ReleaseSessionId(int sessionId)
        {
            if (!_allocatedSessions.TryRemove(sessionId, out SessionIdType type))
            {
                LogHelper.WriteLine(LogLevel.Warning, $"Attempted to release unallocated SessionID: {sessionId}");
                return false;
            }

            switch (type)
            {
                case SessionIdType.Player:
                    _playerPool.Enqueue(sessionId);
                    LogHelper.WriteLine(LogLevel.Debug, $"Released Player SessionID: {sessionId}");
                    break;

                case SessionIdType.NpcMonster:
                    // SessionID cho NPC/Monster giờ đây là từ 1-30000, không cần xử lý đặc biệt
                    LogHelper.WriteLine(LogLevel.Debug, $"Released NPC/Monster SessionID: {sessionId}");
                    break;

                case SessionIdType.Pet:
                    _petPool.Enqueue(sessionId);
                    LogHelper.WriteLine(LogLevel.Debug, $"Released Pet SessionID: {sessionId}");
                    break;
                case SessionIdType.Npc:
                    _npcPool.Enqueue(sessionId);
                    LogHelper.WriteLine(LogLevel.Debug, $"Released NPC SessionID: {sessionId}");
                    break;

                default:
                    LogHelper.WriteLine(LogLevel.Error, $"Unknown SessionID type for SessionID: {sessionId}");
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Kiểm tra SessionID có được allocated không
        /// </summary>
        /// <param name="sessionId">SessionID cần kiểm tra</param>
        /// <returns>true nếu SessionID đang được sử dụng</returns>
        public bool IsSessionIdAllocated(int sessionId)
        {
            return _allocatedSessions.ContainsKey(sessionId);
        }

        /// <summary>
        /// Lấy loại của SessionID
        /// </summary>
        /// <param name="sessionId">SessionID cần kiểm tra</param>
        /// <returns>Loại SessionID hoặc null nếu không tìm thấy</returns>
        public SessionIdType? GetSessionIdType(int sessionId)
        {
            return _allocatedSessions.TryGetValue(sessionId, out SessionIdType type) ? type : null;
        }

        /// <summary>
        /// Lấy thống kê về pool
        /// </summary>
        /// <returns>Thông tin thống kê</returns>
        public SessionIdPoolStats GetPoolStats()
        {
            return new SessionIdPoolStats
            {
                PlayerPoolAvailable = _playerPool.Count,
                PlayerPoolTotal = PLAYER_MAX - PLAYER_MIN + 1,
                NpcMonsterPoolAvailable = _npcMonsterPool.Count,
                NpcMonsterPoolTotal = NPC_MONSTER_MAX - NPC_MONSTER_MIN + 1,
                PetPoolAvailable = _petPool.Count,
                PetPoolTotal = PET_MAX - PET_MIN + 1,
                NpcPoolAvailable = _npcPool.Count,
                NpcPoolTotal = NPC_MAX - NPC_MIN + 1,
                TotalAllocated = _allocatedSessions.Count
            };
        }

        /// <summary>
        /// Lấy thông tin về player SessionID range cho channel hiện tại
        /// </summary>
        /// <returns>Tuple chứa (min, max) của player SessionID range</returns>
        public (int Min, int Max) GetPlayerSessionIdRange()
        {
            return (PLAYER_MIN, PLAYER_MAX);
        }

        /// <summary>
        /// Kiểm tra tính hợp lệ của SessionID range
        /// </summary>
        /// <param name="sessionId">SessionID cần kiểm tra</param>
        /// <returns>true nếu SessionID nằm trong range hợp lệ</returns>
        public static bool IsValidSessionId(int sessionId)
        {
            var instance = Instance;
            return sessionId >= instance.PLAYER_MIN && sessionId <= PET_MAX;
        }

        /// <summary>
        /// Xác định loại SessionID dựa trên range
        /// </summary>
        /// <param name="sessionId">SessionID cần xác định</param>
        /// <returns>Loại SessionID hoặc null nếu không hợp lệ</returns>
        public static SessionIdType? DetermineSessionIdType(int sessionId)
        {
            var instance = Instance;
            if (sessionId >= NPC_MIN && sessionId <= NPC_MAX)
                return SessionIdType.Npc;
            else if (sessionId >= instance.PLAYER_MIN && sessionId <= instance.PLAYER_MAX)
                return SessionIdType.Player;
            else if (sessionId >= NPC_MONSTER_MIN && sessionId <= (NPC_MONSTER_MIN + MAX_SESSIONS_PER_MAP - 1))
                return SessionIdType.NpcMonster; // NPC/Monster SessionID từ 10001-30000 cho mỗi map
            else if (sessionId >= PET_MIN && sessionId <= PET_MAX)
                return SessionIdType.Pet;
            else
                return null;
        }

        /// <summary>
        /// Lấy thống kê SessionID đã sử dụng cho map cụ thể
        /// </summary>
        /// <param name="mapId">ID của map</param>
        /// <returns>Số SessionID đã sử dụng cho map này</returns>
        public static int GetUsedSessionCountForMap(int mapId)
        {
            return _mapSessionCounters.TryGetValue(mapId, out int count) ? count : 0;
        }

        /// <summary>
        /// Reset SessionID counter cho map cụ thể
        /// </summary>
        /// <param name="mapId">ID của map cần reset</param>
        public static void ResetMapSessionCounter(int mapId)
        {
            _mapSessionCounters.TryRemove(mapId, out _);
            LogHelper.WriteLine(LogLevel.Info, $"Reset SessionID counter for Map: {mapId}");
        }
    }

    /// <summary>
    /// Thống kê về SessionID pools
    /// </summary>
    public class SessionIdPoolStats
    {
        public int PlayerPoolAvailable { get; set; }
        public int PlayerPoolTotal { get; set; }
        public int NpcMonsterPoolAvailable { get; set; }
        public int NpcMonsterPoolTotal { get; set; }
        public int PetPoolAvailable { get; set; }
        public int PetPoolTotal { get; set; }
        public int NpcPoolAvailable { get; set; }
        public int NpcPoolTotal { get; set; }
        public int TotalAllocated { get; set; }

        public int PlayerPoolUsed => PlayerPoolTotal - PlayerPoolAvailable;
        public int NpcMonsterPoolUsed => NpcMonsterPoolTotal - NpcMonsterPoolAvailable;
        public int PetPoolUsed => PetPoolTotal - PetPoolAvailable;

        public int NpcPoolUsed => NpcPoolTotal - NpcPoolAvailable;

        public override string ToString()
        {
            return $"SessionID Pool Stats:\n" +
                   $"  Player: {PlayerPoolUsed}/{PlayerPoolTotal} used ({PlayerPoolAvailable} available)\n" +
                   $"  NPC/Monster: {NpcMonsterPoolUsed}/{NpcMonsterPoolTotal} used ({NpcMonsterPoolAvailable} available)\n" +
                   $"  Pet: {PetPoolUsed}/{PetPoolTotal} used ({PetPoolAvailable} available)\n" +
                   $"  NPC: {NpcPoolUsed}/{NpcPoolTotal} used ({NpcPoolAvailable} available)\n" +
                   $"  Total Allocated: {TotalAllocated}";
        }
    }
}
