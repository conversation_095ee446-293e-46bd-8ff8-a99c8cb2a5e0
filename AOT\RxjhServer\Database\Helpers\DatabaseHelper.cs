using System;
using System.Data;
using HeroYulgang.Helpers;
using Microsoft.Data.SqlClient;

namespace RxjhServer.Database.Helpers
{
    /// <summary>
    /// Helper class để xử lý database operations an toàn
    /// </summary>
    public static class DatabaseHelper
    {
      

        /// <summary>
        /// Safely read item data with DBNull handling
        /// </summary>
        public static void SafeInitializeItemData(object item, DataRow row)
        {
            if (item == null || row == null) return;

            try
            {
                // Sử dụng reflection để set properties an toàn
                var itemType = item.GetType();
                
                foreach (DataColumn column in row.Table.Columns)
                {
                    var property = itemType.GetProperty(column.ColumnName);
                    if (property != null && property.CanWrite)
                    {
                        var value = row[column.ColumnName];
                        if (value != DBNull.Value)
                        {
                            try
                            {
                                var convertedValue = Convert.ChangeType(value, property.PropertyType);
                                property.SetValue(item, convertedValue);
                            }
                            catch (Exception conversionEx)
                            {
                                LogHelper.WriteLine(LogLevel.Warning, 
                                    $"Không thể convert {column.ColumnName}: {conversionEx.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"Lỗi SafeInitializeItemData: {ex.Message}");
            }
        }


        /// <summary>
        /// Safely check if DataTable has data
        /// </summary>
        public static bool HasData(DataTable dataTable)
        {
            return dataTable != null && dataTable.Rows.Count > 0;
        }

        /// <summary>
        /// Safely dispose DataTable
        /// </summary>
        public static void SafeDispose(DataTable dataTable)
        {
            try
            {
                dataTable?.Dispose();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Warning, 
                    $"Lỗi khi dispose DataTable: {ex.Message}");
            }
        }

        /// <summary>
        /// Batch process database operations to reduce connection overhead
        /// </summary>
        public static void BatchProcessCharacters(System.Collections.Generic.List<Players> players, 
            System.Action<Players> processAction)
        {
            if (players == null || players.Count == 0) return;

            const int batchSize = 10;
            
            for (int i = 0; i < players.Count; i += batchSize)
            {
                var batch = players.GetRange(i, Math.Min(batchSize, players.Count - i));
                
                System.Threading.Tasks.Parallel.ForEach(batch, player =>
                {
                    try
                    {
                        processAction(player);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Error, 
                            $"Lỗi khi xử lý player {player.CharacterName}: {ex.Message}");
                    }
                });
                
                // Small delay between batches to prevent overwhelming database
                System.Threading.Thread.Sleep(10);
            }
        }
    }
}
