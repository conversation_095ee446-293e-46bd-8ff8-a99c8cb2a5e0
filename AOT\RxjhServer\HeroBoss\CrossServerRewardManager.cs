using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;

namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Quản lý phân phối reward cho Boss Cross Server
    /// </summary>
    public class CrossServerRewardManager
    {
        private static CrossServerRewardManager _instance;
        private static readonly object _lock = new();

        public static CrossServerRewardManager Instance
        {
            get
            {
                if (_instance is null)
                {
                    lock (_lock)
                    {
                        _instance ??= new CrossServerRewardManager();
                    }
                }
                return _instance;
            }
        }

        private CrossServerRewardManager()
        {
            LogHelper.WriteLine(LogLevel.Info, "CrossServerRewardManager initialized");
        }

        /// <summary>
        /// Tính toán và phân phối reward cho tất cả contributors
        /// </summary>
        public async Task DistributeRewards(int bossId, List<CrossServerDamageContribute> allContributions)
        {
            try
            {
                if (allContributions == null || allContributions.Count == 0)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"No contributions found for boss {bossId}");
                    return;
                }

                LogHelper.WriteLine(LogLevel.Info, $"Distributing rewards for boss {bossId} to {allContributions.Count} contributors");

                // Tính toán reward cho từng contributor
                var rewards = CalculateRewards(bossId, allContributions);

                // Nhóm rewards theo server
                var serverGroups = rewards.GroupBy(r => r.ServerId);

                // Gửi rewards đến từng server
                foreach (var serverGroup in serverGroups)
                {
                    var serverId = serverGroup.Key;
                    var serverRewards = serverGroup.ToList();

                    if (serverId == World.ServerID)
                    {
                        // Phân phối reward cho local players
                        await DistributeLocalRewardsInternal(bossId, serverRewards);
                    }
                    else
                    {
                        // Gửi reward info đến server khác
                        await SendRewardsToRemoteServer(bossId, serverId, serverRewards);
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Completed reward distribution for boss {bossId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error distributing rewards for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Tính toán rewards cho tất cả contributors
        /// </summary>
        private List<CrossServerRewardInfo> CalculateRewards(int bossId, List<CrossServerDamageContribute> contributions)
        {
            var rewards = new List<CrossServerRewardInfo>();
            var totalDamage = contributions.Sum(c => c.Damage);

            // Sắp xếp theo damage giảm dần
            var sortedContributions = contributions.OrderByDescending(c => c.Damage).ToList();

            for (int i = 0; i < sortedContributions.Count; i++)
            {
                var contribution = sortedContributions[i];
                var damagePercentage = totalDamage > 0 ? (double)contribution.Damage / totalDamage * 100 : 0;
                var rank = i + 1;

                var reward = new CrossServerRewardInfo
                {
                    BossId = bossId,
                    ServerId = contribution.ServerId,
                    SessionId = contribution.SessionId,
                    PlayerName = contribution.PlayerName,
                    TotalDamage = contribution.Damage,
                    AttackCount = contribution.AttackCount,
                    DamagePercentage = damagePercentage,
                    RewardPoints = CalculateRewardPoints(contribution.Damage, damagePercentage, rank),
                    HasSpecialReward = ShouldGetSpecialReward(rank, damagePercentage),
                    SpecialRewardItem = GetSpecialRewardItem(rank, damagePercentage)
                };

                rewards.Add(reward);
            }

            return rewards;
        }

        /// <summary>
        /// Tính toán reward points dựa trên damage, percentage và rank
        /// </summary>
        private int CalculateRewardPoints(long damage, double damagePercentage, int rank)
        {
            // Base points từ damage
            var basePoints = (int)(damage / 1000); // 1 point per 1000 damage

            // Rank bonus
            var rankBonus = rank switch
            {
                1 => 200,  // Top 1
                2 => 150,  // Top 2
                3 => 100,  // Top 3
                <= 5 => 75,   // Top 5
                <= 10 => 50,  // Top 10
                <= 20 => 25,  // Top 20
                _ => 10    // Participation
            };

            // Damage percentage bonus
            var percentageBonus = damagePercentage switch
            {
                >= 30 => 100,
                >= 20 => 75,
                >= 10 => 50,
                >= 5 => 25,
                >= 1 => 10,
                _ => 5
            };

            return Math.Max(basePoints + rankBonus + percentageBonus, 10); // Minimum 10 points
        }

        /// <summary>
        /// Kiểm tra có nên nhận special reward không
        /// </summary>
        private bool ShouldGetSpecialReward(int rank, double damagePercentage)
        {
            // Top 3 hoặc damage >= 10% có cơ hội nhận special reward
            return rank <= 3 || damagePercentage >= 10;
        }

        /// <summary>
        /// Lấy special reward item dựa trên rank và damage percentage
        /// </summary>
        private string GetSpecialRewardItem(int rank, double damagePercentage)
        {
            if (rank == 1 && damagePercentage >= 20)
            {
                return "LegendaryBossReward"; // Top 1 với high damage
            }
            else if (rank <= 2 && damagePercentage >= 15)
            {
                return "EpicBossReward"; // Top 2 với good damage
            }
            else if (rank <= 3 && damagePercentage >= 10)
            {
                return "RareBossReward"; // Top 3 với decent damage
            }
            else if (rank <= 5)
            {
                return "UncommonBossReward"; // Top 5
            }

            return null; // No special reward
        }

        /// <summary>
        /// Phân phối reward cho local players (public method)
        /// </summary>
        public async Task DistributeLocalRewards(int bossId, List<CrossServerRewardInfo> rewards)
        {
            await DistributeLocalRewardsInternal(bossId, rewards);
        }

        /// <summary>
        /// Phân phối reward cho local players (internal implementation)
        /// </summary>
        private async Task DistributeLocalRewardsInternal(int bossId, List<CrossServerRewardInfo> rewards)
        {
            try
            {
                foreach (var reward in rewards)
                {
                    var player = World.FindPlayerBySession(reward.SessionId);
                    if (player != null)
                    {
                        // Gửi reward points
                        await GiveRewardPoints(player, reward.RewardPoints);

                        // Gửi special reward nếu có
                        if (reward.HasSpecialReward && !string.IsNullOrEmpty(reward.SpecialRewardItem))
                        {
                            await GiveSpecialReward(player, reward.SpecialRewardItem);
                        }

                        // Thông báo cho player
                        player.HeThongNhacNho($"Bạn đã nhận được {reward.RewardPoints} điểm từ Boss Cross-Server! Rank: #{rewards.IndexOf(reward) + 1}");
                        
                        if (reward.HasSpecialReward)
                        {
                            player.HeThongNhacNho($"Bạn đã nhận được phần thưởng đặc biệt: {reward.SpecialRewardItem}!");
                        }

                        LogHelper.WriteLine(LogLevel.Info, $"Distributed reward to {player.CharacterName}: {reward.RewardPoints} points");
                    }
                    else
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"Player with session {reward.SessionId} not found for reward distribution");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error distributing local rewards: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi reward info đến server khác
        /// </summary>
        private async Task SendRewardsToRemoteServer(int bossId, int serverId, List<CrossServerRewardInfo> rewards)
        {
            try
            {
                var message = CrossServerBossProtocol.CreateRewardDistributionMessage(bossId, serverId, rewards);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Info, $"Sent {rewards.Count} rewards to server {serverId} for boss {bossId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error sending rewards to server {serverId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi reward points cho player
        /// </summary>
        private async Task GiveRewardPoints(Players player, int points)
        {
            try
            {
                // TODO: Implement actual reward points system
                // Có thể là experience, gold, special currency, etc.
                
                // Ví dụ: Thêm experience
                //player.AddExperience(points * 100); // 100 exp per point
                
                //// Hoặc thêm gold
                //player.AddGold(points * 1000); // 1000 gold per point

                LogHelper.WriteLine(LogLevel.Debug, $"Gave {points} reward points to {player.CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error giving reward points to {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi special reward cho player
        /// </summary>
        private async Task GiveSpecialReward(Players player, string rewardItem)
        {
            try
            {
                // TODO: Implement actual special reward system
                // Có thể gửi item qua mail system
                
                var itemId = GetItemIdFromRewardName(rewardItem);
                if (itemId > 0)
                {
                    // Gửi item qua mail
                 //   var item = new Item(itemId, 1); // 1 quantity
                    var description = $"Phần thưởng đặc biệt từ Boss Cross-Server: {rewardItem}";
                    var byteDesc = System.Text.Encoding.GetEncoding(1252).GetBytes(description);
                    
                    //World.SendItemMail("[Boss Reward]", player.CharacterName, byteDesc, 0, item, 30);
                    //World.SendMailCodNotificationByAdmin(player.WorldID, player.SessionID);
                }

                LogHelper.WriteLine(LogLevel.Debug, $"Gave special reward {rewardItem} to {player.CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error giving special reward to {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy item ID từ tên reward
        /// </summary>
        private int GetItemIdFromRewardName(string rewardName)
        {
            return rewardName switch
            {
                "LegendaryBossReward" => 10001, // Example item ID
                "EpicBossReward" => 10002,
                "RareBossReward" => 10003,
                "UncommonBossReward" => 10004,
                _ => 0
            };
        }
    }
}
