-- =====================================================
-- CUMULATIVE REWARD SYSTEM DATABASE TABLES
-- =====================================================

-- 1. PUBLIC DATABASE - CumulativeReward Templates và Items
-- =====================================================

-- Bảng cumulative reward templates (lưu trong Public DB)
CREATE TABLE IF NOT EXISTS tbl_cumulative_reward_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    month INTEGER NOT NULL,
    year INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    -- 10 mốc cash requirement
    milestone_1_cash INTEGER NOT NULL DEFAULT 5000,
    milestone_2_cash INTEGER NOT NULL DEFAULT 10000,
    milestone_3_cash INTEGER NOT NULL DEFAULT 20000,
    milestone_4_cash INTEGER NOT NULL DEFAULT 35000,
    milestone_5_cash INTEGER NOT NULL DEFAULT 50000,
    milestone_6_cash INTEGER NOT NULL DEFAULT 75000,
    milestone_7_cash INTEGER NOT NULL DEFAULT 100000,
    milestone_8_cash INTEGER NOT NULL DEFAULT 150000,
    milestone_9_cash INTEGER NOT NULL DEFAULT 200000,
    milestone_10_cash INTEGER NOT NULL DEFAULT 300000,
    UNIQUE(month, year)
);

-- Bảng cumulative reward items (lưu trong Public DB)
-- Mỗi milestone có thể có tối đa 6 phần thưởng
CREATE TABLE IF NOT EXISTS tbl_cumulative_reward_items (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL,
    milestone_number INTEGER NOT NULL CHECK (milestone_number >= 1 AND milestone_number <= 10),
    item_slot INTEGER NOT NULL CHECK (item_slot >= 1 AND item_slot <= 6), -- Tối đa 6 phần thưởng mỗi mốc
    item_id INTEGER NOT NULL,
    item_amount INTEGER NOT NULL DEFAULT 1,
    FOREIGN KEY (template_id) REFERENCES tbl_cumulative_reward_templates(id) ON DELETE CASCADE,
    UNIQUE(template_id, milestone_number, item_slot)
);

-- =====================================================
-- 2. GAME DATABASE - Player Progress
-- =====================================================

-- Bảng player cumulative reward log (lưu trong Game DB)
CREATE TABLE IF NOT EXISTS tbl_player_cumulative_reward_log (
    id SERIAL PRIMARY KEY,
    player_name VARCHAR(255) NOT NULL,
    template_id INTEGER NOT NULL,
    milestone_number INTEGER NOT NULL CHECK (milestone_number >= 1 AND milestone_number <= 10),
    received_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cash_spent_at_time INTEGER NOT NULL, -- Số cash đã spend khi nhận thưởng
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(player_name, template_id, milestone_number)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Index cho tìm kiếm template active
CREATE INDEX IF NOT EXISTS idx_cumulative_reward_templates_active 
ON tbl_cumulative_reward_templates(is_active, month, year);

-- Index cho tìm kiếm items theo template
CREATE INDEX IF NOT EXISTS idx_cumulative_reward_items_template 
ON tbl_cumulative_reward_items(template_id, milestone_number);

-- Index cho tìm kiếm log theo player
CREATE INDEX IF NOT EXISTS idx_player_cumulative_reward_log_player 
ON tbl_player_cumulative_reward_log(player_name, template_id);

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Sample template cho tháng hiện tại
INSERT INTO tbl_cumulative_reward_templates (
    name, month, year, is_active, start_date, end_date,
    milestone_1_cash, milestone_2_cash, milestone_3_cash, milestone_4_cash, milestone_5_cash,
    milestone_6_cash, milestone_7_cash, milestone_8_cash, milestone_9_cash, milestone_10_cash
) VALUES (
    'Cumulative Reward December 2024', 12, 2024, true, 
    '2024-12-01 00:00:00', '2024-12-31 23:59:59',
    5000, 10000, 20000, 35000, 50000, 75000, 100000, 150000, 200000, 300000
) ON CONFLICT (month, year) DO NOTHING;

-- Sample rewards cho milestone 1 (5000 cash)
INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) 
SELECT id, 1, 1, 34604, 1 FROM tbl_cumulative_reward_templates WHERE month = 12 AND year = 2024;

INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) 
SELECT id, 1, 2, 34605, 5 FROM tbl_cumulative_reward_templates WHERE month = 12 AND year = 2024;

-- Sample rewards cho milestone 2 (10000 cash)
INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) 
SELECT id, 2, 1, 34606, 1 FROM tbl_cumulative_reward_templates WHERE month = 12 AND year = 2024;

INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) 
SELECT id, 2, 2, 34607, 3 FROM tbl_cumulative_reward_templates WHERE month = 12 AND year = 2024;

INSERT INTO tbl_cumulative_reward_items (template_id, milestone_number, item_slot, item_id, item_amount) 
SELECT id, 2, 3, 34608, 10 FROM tbl_cumulative_reward_templates WHERE month = 12 AND year = 2024;
