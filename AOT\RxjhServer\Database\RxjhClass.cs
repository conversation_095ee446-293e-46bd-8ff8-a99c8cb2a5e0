
using HeroYulgang.Database.FreeSql;

namespace RxjhServer.Database
{
    /// <summary>
    /// Lớ<PERSON> tương thích với RxjhClass cũ, sử dụng Entity Framework Core
    /// </summary>
    public static class RxjhClass
    {

    
        /// <summary>
        /// Ghi lại thông tin về các vật phẩm
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="toUserId">ID người nhận</param>
        /// <param name="toUserName">Tên người nhận</param>
        /// <param name="globalId">ID toàn cục của vật phẩm</param>
        /// <param name="itemId">ID vật phẩm</param>
        /// <param name="itemName">Tê<PERSON> vật phẩm</param>
        /// <param name="itemCount"><PERSON><PERSON> lượng vật phẩm</param>
        /// <param name="itemProperties">Thu<PERSON><PERSON> tính vật phẩm</param>
        /// <param name="money">Số tiền</param>
        /// <param name="type">Loại hình</param>
        public static async void ItemRecord(string userId, string userName, string toUserId, string toUserName, double globalId, int itemId, string itemName, int itemCount, string itemProperties, int money, string type)
        {
           await GameDb.ItemRecord(userId, userName, toUserId, toUserName, globalId, itemId, itemName, itemCount, itemProperties, money, type);
        }

        public static long CreateItemSeries()
        {
            return GameDb.CreateItemSeries();
        }

        public static int CapNhat_ThienMaThanCung_TinTuc(string ChiemLinhMonPhai, string ChiemLinhNgay, int CuaThanh_CuongHoa_Level)
        {
            return -1;
        }
    }
}
