﻿using HeroYulgang.Database.FreeSql.Extensions;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections;

namespace RxjhServer
{
    public partial class PlayersBes : X_Khi_Cong_Thuoc_Tinh
    {
		

	public void SaveMemberData()
	{
		if (AccountDb.UpdateVipTime(AccountID, 1, FLD_VIPTIM).GetAwaiter().GetResult())
		{
			LogHelper.WriteLine(LogLevel.Info, "SaveID hội viên SoLieu success[" + AccountID + "]-[" + CharacterName + "]");
		}
		else
		{
			LogHelper.WriteLine(LogLevel.Error, "SaveID hội viên SoLieu error[" + AccountID + "]-[" + CharacterName + "]");
		}
	}

	public void SaveCharacterData()
	{
		try
		{
			if (CharacterName.Length != 0)
			{
				SaveCharacterDataAsync().GetAwaiter().GetResult();
				SavePersonalWarehouseAsync().GetAwaiter().GetResult();
				SaveComprehensiveWarehouseAsync().GetAwaiter().GetResult();
				if (CharacterBeast != null)
				{
					CharacterBeast.SaveSoLieu();
				}
				ArchiveTime = true;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "LỖI NGHIÊM TRỌNG - LƯU THÔNG TIN NHÂN VẬT BỊ LỖI !! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SaveGangData()
	{
		try
		{
			if (CharacterName.Length != 0 && GuildId != 0)
			{
				GameDb.UpdateGuildMemberLevel(GuildName, CharacterName, Player_Level).Wait();
				//StringBuilder stringBuilder = new();
				// stringBuilder.AppendFormat("UPDATE  TBL_XWWL_GuildMember  SET  FLD_LEVEL   =  @zw   WHERE  FLD_NAME  =   @Username");
				// var sqlParameter_ = new SqlParameter[2]
				// {
				// 	SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, Player_Level),
				// 	SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, CharacterName)
				// };
				// if (DBA.ExeSqlCommand(stringBuilder.ToString(), sqlParameter_).GetAwaiter().GetResult() == -1)
				// {
				// 	LogHelper.WriteLine(LogLevel.Info, "Save nhân vâ\u0323t Gang Level SoLieu error 11 [" + AccountID + "]-[" + CharacterName + "]");
				// }
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save nhân vâ\u0323t Gang Level SoLieu error 22 [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

		/// <summary>
	/// Save character data using new PostgreSQL/FreeSql system
	/// Lưu dữ liệu nhân vật sử dụng hệ thống PostgreSQL/FreeSql mới
	/// </summary>
	public async Task<bool> SaveCharacterDataAsync()
	{
		if (string.IsNullOrEmpty(CharacterName))
		{
			return false;
		}

		try
		{
			LogHelper.WriteLine(LogLevel.Info, $"Lưu trữ dữ liệu nhân vật {AccountID} {CharacterName} (FreeSql)");

			// Convert current player data to model
			var characterData = this.ToCharacterDataModel();

			// Save using GameDb FreeSql system
			var result = await GameDb.SaveCharacterDataAsync(characterData);

			if (result)
			{
				// Save mentoring data if applicable
				if (MasterData.TID != -1)
				{
					await SaveMentoringDataAsync();
				}

				LogHelper.WriteLine(LogLevel.Info, $"✓ Character data saved successfully for {CharacterName}");
				return true;
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to save character data for {CharacterName}");
				return false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Save Character Data error[{AccountID}]-[{CharacterName}]: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Save personal warehouse data using new PostgreSQL/FreeSql system
	/// Lưu dữ liệu kho cá nhân sử dụng hệ thống PostgreSQL/FreeSql mới
	/// </summary>
	public async Task<bool> SavePersonalWarehouseAsync()
	{
		if (string.IsNullOrEmpty(CharacterName) || Client?.TreoMay == true)
		{
			return false;
		}

		try
		{
			LogHelper.WriteLine(LogLevel.Info, $"Lưu trữ kho cá nhân {AccountID} {CharacterName} (FreeSql)");

			// Save using GameDb FreeSql system
			var result = await GameDb.UpdatePersonalWarehouseAsync(
				AccountID,
				CharacterName,
				PersonalWarehouseMoney.ToString(),
				GetPersonalWarehousebyte()
			);

			if (result)
			{
				LogHelper.WriteLine(LogLevel.Info, $"✓ Personal warehouse saved successfully for {CharacterName}");
				return true;
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to save personal warehouse for {CharacterName}");
				return false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Save Personal Warehouse error[{AccountID}]-[{CharacterName}]: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Save comprehensive warehouse data using new PostgreSQL/FreeSql system
	/// Lưu dữ liệu kho tổng hợp sử dụng hệ thống PostgreSQL/FreeSql mới
	/// </summary>
	public async Task<bool> SaveComprehensiveWarehouseAsync()
	{
		if (string.IsNullOrEmpty(CharacterName) || Client?.TreoMay == true)
		{
			return false;
		}

		try
		{
			LogHelper.WriteLine(LogLevel.Info, $"Lưu trữ kho tổng hợp {AccountID} {CharacterName} (FreeSql)");

			// Save using GameDb FreeSql system
			var result = await GameDb.UpdateComprehensiveWarehouseAsync(
				AccountID,
				ComprehensiveWarehouseMoney.ToString(),
				GetComprehensiveWarehousebyte(),
				GetComprehensiveWarehouseProductbyte(),
				ComprehensiveWarehouseEquipmentDataVersion
			);

			if (result)
			{
				LogHelper.WriteLine(LogLevel.Info, $"✓ Comprehensive warehouse saved successfully for {CharacterName}");
				return true;
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to save comprehensive warehouse for {CharacterName}");
				return false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Save Comprehensive Warehouse error[{AccountID}]-[{CharacterName}]: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Save account RX points data using new PostgreSQL/FreeSql system
	/// Lưu dữ liệu điểm RX tài khoản sử dụng hệ thống PostgreSQL/FreeSql mới
	/// </summary>
	public async Task<bool> SaveRxPointDataAsync()
	{
		try
		{
			LogHelper.WriteLine(LogLevel.Info, $"Lưu trữ điểm RX {AccountID} {CharacterName} (FreeSql)");

			// Save using AccountDb FreeSql system
			var result = await AccountDb.UpdateRxPointAsync(
				AccountID,
				FLD_RXPIONT,
				FLD_RXPIONTX,
				FLD_Coin,
				GameSecurityCode
			);

			if (result)
			{
				LogHelper.WriteLine(LogLevel.Info, $"✓ RX points saved successfully for {CharacterName}");
				return true;
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to save RX points for {CharacterName}");
				return false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Save RX Points error[{AccountID}]-[{CharacterName}]: {ex.Message}");
			return false;
		}
	}


	public void Save_NguyenBaoData()
	{
		try
		{
			SaveRxPointDataAsync().GetAwaiter().GetResult();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "SaveID NguyenBao SoLieu error[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	/// <summary>
	/// Save mentoring/master-student data using new PostgreSQL/FreeSql system
	/// Lưu dữ liệu sư đồ/thầy trò sử dụng hệ thống PostgreSQL/FreeSql mới
	/// </summary>
	public async Task<bool> SaveMentoringDataAsync()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "PlayersBes Save Mentoring Data Async()");
		}

		try
		{
			LogHelper.WriteLine(LogLevel.Info, $"Lưu trữ dữ liệu sư đồ {AccountID} {CharacterName} (FreeSql)");

			// Save using GameDb FreeSql system
			var result = await HeroYulgang.Database.FreeSql.GameDb.UpdateMentoringDataAsync(
				CharacterName,
				MasterData.TLEVEL,
				MasterData.STLEVEL,
				MasterData.STYHD,
				MasterData.STWG1,
				MasterData.STWG2,
				MasterData.STWG3
			);

			if (result)
			{
				LogHelper.WriteLine(LogLevel.Info, $"✓ Mentoring data saved successfully for {CharacterName}");
				return true;
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to save mentoring data for {CharacterName}");
				return false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Save Mentoring Data error[{AccountID}]-[{CharacterName}]: {ex.Message}");
			return false;
		}
	}

	public byte[] GetFLD_ITEMNSZCodesbyte()
	{
		var array = new byte[World.Item_Db_Byte_Length * 6];
		try
		{
			for (var i = 0; i < 6; i++)
			{
				try
				{
				}
				catch
				{
					var src = new byte[World.Item_Db_Byte_Length];
					System.Buffer.BlockCopy(src, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Bảo tồn số liệu phạm sai lầm GetFLD_ITEMCodesbyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return array;
	}

        
	public byte[] GetTimeMedicinebyte()
	{
		var array = new byte[320];
		try
		{
			var num = 0;
			foreach (var value in TimeMedicine.Values)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.DuocPhamID), 0, array, num * 8, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes((long)value.ThoiGian), 0, array, num * 8 + 4, 4);
				num++;
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 111 GetTimeMedicinebyte [" + AccountID + "]-[" + CharacterName + "][" + TimeMedicine.Count + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetNhiemVubyte()
	{
		SendingClass sendingClass = new();
		try
		{
			if (QuestList == null)
			{
				return new byte[400];
			}
			foreach (var value in QuestList.Values)
			{
				sendingClass.Write2(value.NhiemVuID);
				sendingClass.Write2(value.NhiemVuGiaiDoanID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 333 GetNhiemVubyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return sendingClass.ToArray3();
	}

	public byte[] GetNhiemVuFinishbyte()
	{
		SendingClass sendingClass = new();
		try
		{
			if (QuestList == null)
			{
				return new byte[400];
			}
			foreach (var item in CompletedQuestList)
			{
				sendingClass.Write2(item.Value.nhiemvuid);
				var value = int.Parse(item.Value.nhiemvudate.ToString("MMddyyyy"));
				sendingClass.Write4(value);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 333 GetNhiemVubyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return sendingClass.ToArray3();
	}

	public byte[] GetThoLinhPhubyte()
	{
		var array = new byte[990];
		try
		{
			foreach (DictionaryEntry item in ThoLinhPhu_ToaDo)
			{
				var x_Toa_Do_Class = (X_Toa_Do_Class)item.Value;
				var num = (int)item.Key;
				if (num >= 10)
				{
					System.Buffer.BlockCopy(Encoding.Default.GetBytes(x_Toa_Do_Class.Rxjh_name), 0, array, (num - 10) * 32, Encoding.Default.GetBytes(x_Toa_Do_Class.Rxjh_name).Length);
					System.Buffer.BlockCopy(BitConverter.GetBytes(x_Toa_Do_Class.Rxjh_Map), 0, array, (num - 10) * 32 + 15, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(x_Toa_Do_Class.Rxjh_X), 0, array, (num - 10) * 32 + 19, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(x_Toa_Do_Class.Rxjh_Y), 0, array, (num - 10) * 32 + 23, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(x_Toa_Do_Class.Rxjh_Z), 0, array, (num - 10) * 32 + 27, 4);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 444 GetThoLinhPhubyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetTitleDrugbyte()
	{
		var array = new byte[320];
		try
		{
			var num = 0;
			foreach (var value in TitleDrug.Values)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.DuocPhamID), 0, array, num * 8, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes((long)value.ThoiGian), 0, array, num * 8 + 4, 4);
				num++;
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save Pill TTTP Loi !!![" + AccountID + "]-[" + CharacterName + "][" + TitleDrug.Count + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetPersonalMedicinebyte()
	{
		var num = 0;
		var array = new byte[480];
		try
		{
			if (AppendStatusList != null)
			{
				foreach (var value in AppendStatusList.Values)
				{
					if (value.FLD_RESIDE1 == 1)
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_PID), 0, array, num * 8, 4);
						System.Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_sj), 0, array, num * 8 + 4, 4);
						num++;
					}
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Mất Pill [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetPersonalMedicineNewbyte()
	{
		var num = 0;
		var array = new byte[240];
		try
		{
			if (AppendStatusNewList != null)
			{
				foreach (var value in AppendStatusNewList.Values)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_PID), 0, array, num * 16, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_sj), 0, array, num * 16 + 4, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.SoLuong), 0, array, num * 16 + 8, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.SoLuongLoaiHinh), 0, array, num * 16 + 12, 4);
					num++;
				}
				return array;
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 666 GetPersonalMedicineNewbyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetPersonalWarehousebyte()
	{
		var array = new byte[4560];
		try
		{
			for (var i = 0; i < 60; i++)
			{
				try
				{
					System.Buffer.BlockCopy(PersonalWarehouse[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 777 GetPersonalWarehousebyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetComprehensiveWarehousebyte()
	{
		var array = new byte[4560];
		try
		{
			for (var i = 0; i < 60; i++)
			{
				try
				{
					System.Buffer.BlockCopy(PublicWarehouse[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 888 GetComprehensiveWarehousebyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetComprehensiveWarehouseProductbyte()
	{
		var array = new byte[16];
		try
		{
			if (PublicDrugs != null)
			{
				var num = 0;
				foreach (var value in PublicDrugs.Values)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.DuocPhamID), 0, array, num * 8, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.ThoiGian), 0, array, num * 8 + 4, 4);
					num++;
				}
				return array;
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 999 GetComprehensiveWarehouseProductbyte[" + AccountID + "]-[" + CharacterName + "][" + PublicDrugs.Count + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetWgCodesbyte()
	{
		var array = new byte[100];
		try
		{
			for (var i = 0; i < 12; i++)
			{
				byte[] src;
				try
				{
					src = KhiCong[i].KhiCong_byte;
				}
				catch
				{
					src = new byte[2];
				}
				System.Buffer.BlockCopy(src, 0, array, i, 1);
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 000 GetWgCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetWEARITEMCodesbyte()
	{
		var array = new byte[5000];
		try
		{
			for (var i = 0; i < 16; i++)
			{
				try
				{
					System.Buffer.BlockCopy(Item_Wear[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			for (var j = 0; j < 15; j++)
			{
				try
				{
					System.Buffer.BlockCopy(Sub_Wear[j].VatPham_byte, 0, array, (j + 16) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, (j + 16) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			for (var k = 0; k < 6; k++)
			{
				try
				{
					System.Buffer.BlockCopy(ThietBiTab3[k].VatPham_byte, 0, array, (k + 31) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, (k + 31) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			try
			{
				System.Buffer.BlockCopy(Item_Wear[16].VatPham_byte, 0, array, (46) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
			}
			catch
			{
				System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, (46) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
			}
			try
			{
				System.Buffer.BlockCopy(Item_Wear[17].VatPham_byte, 0, array, (47) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
			}
			catch
			{
				System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, (47) * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 11 GetWEARITEMCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetFLD_ITEMCodesbyte()
	{
		var array = new byte[8000];
		try
		{
			for (var i = 0; i < 96; i++)
			{
				try
				{
					System.Buffer.BlockCopy(Item_In_Bag[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 22 GetFLD_ITEMCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetFLD_ITEM_NTCbyte()
	{
		var array = new byte[600];
		try
		{
			LogHelper.WriteLine(LogLevel.Info, "Save  GetFLD ITEM NTCbyte[" + AccountID + "]-[" + CharacterName + "]");
			for (var i = 0; i < 6; i++)
			{
				try
				{
					Buffer.BlockCopy(Item_NTC[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save So Lieu error  GetFLD ITEM NTCbyte[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetFLD_FASHION_ITEMCodesbyte()
	{
		var array = new byte[4620];
		try
		{
			for (var i = 0; i < 60; i++)
			{
				try
				{
					System.Buffer.BlockCopy(AoChang_HanhLy[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 33 GetFLD_FASHION_ITEMCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetEventBagCodesbyte()
	{
		var array = new byte[1824];
		try
		{
			for (var i = 0; i < 24; i++)
			{
				try
				{
					System.Buffer.BlockCopy(EventBag[i].VatPham_byte, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[World.Item_Db_Byte_Length], 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save SoLieu error GetFLD PINKBAG ITEMCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetQuestITEMCodesbyte()
	{
		var array = new byte[288];
		try
		{
			for (var i = 0; i < 36; i++)
			{
				try
				{
					System.Buffer.BlockCopy(NhiemVu_VatPham[i].VatPham_byte, 0, array, i * 8, 8);
				}
				catch
				{
					System.Buffer.BlockCopy(new byte[8], 0, array, i * 8, 8);
				}
			}
			return array;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 44 GetQuestITEMCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			return array;
		}
	}

	public byte[] GetFLD_KONGFUCodesbyte()
	{
		SendingClass sendingClass = new();
		try
		{
			for (var i = 0; i < 3; i++)
			{
				for (var j = 0; j < 32; j++)
				{
					if (VoCongMoi[i, j] != null)
					{
						sendingClass.Write4(VoCongMoi[i, j].FLD_PID);
						sendingClass.Write4(VoCongMoi[i, j].VoCong_DangCap);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 55 GetFLD_KONGFUCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return sendingClass.ToArray3();
	}

	public byte[] GetThangThienVoCongCodesbyte()
	{
		SendingClass sendingClass = new();
		try
		{
			for (var i = 0; i < 32; i++)
			{
				if (VoCongMoi[3, i] != null)
				{
					sendingClass.Write4(VoCongMoi[3, i].FLD_PID);
					sendingClass.Write4(VoCongMoi[3, i].VoCong_DangCap);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 66 GetThangThienVoCongCodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return sendingClass.ToArray3();
	}

	public byte[] GetThangThienKhiCongCodesbyte()
	{
		SendingClass sendingClass = new();
		try
		{
			foreach (var value in ThangThienKhiCong.Values)
			{
				sendingClass.Write2(value.KhiCongID);
				sendingClass.Write2(value.KhiCong_SoLuong);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 77 GetThangThienKhiCongodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return sendingClass.ToArray3();
	}

	public byte[] GetPhanKhiCongCodesbyte()
	{
		SendingClass sendingClass = new();
		try
		{
			if (PhanKhiCong != null)
			{
				foreach (var value in PhanKhiCong.Values)
				{
					sendingClass.Write2(value.KhiCongID);
					sendingClass.Write2(value.KhiCong_SoLuong);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save của SoLieu error 77 GetPhanKhiCongodesbyte [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
		return sendingClass.ToArray3();
	}

    }
}
