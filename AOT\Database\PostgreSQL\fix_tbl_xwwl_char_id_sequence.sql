-- Fix tbl_xwwl_char ID auto-increment issue
-- <PERSON><PERSON><PERSON><PERSON> <PERSON>hục vấn đề tự động tăng ID cho bảng tbl_xwwl_char

-- Step 1: Create sequence for tbl_xwwl_char.id
-- Bước 1: Tạo sequence cho tbl_xwwl_char.id
CREATE SEQUENCE IF NOT EXISTS tbl_xwwl_char_id_seq;

-- Step 2: Set the sequence to start from the next available ID
-- Bước 2: Đặt sequence bắt đầu từ ID tiếp theo có sẵn
SELECT setval('tbl_xwwl_char_id_seq', COALESCE((SELECT MAX(id) FROM tbl_xwwl_char), 0) + 1, false);

-- Step 3: Set the default value for id column to use the sequence
-- Bước 3: Đặt giá trị mặc định cho cột id sử dụng sequence
ALTER TABLE tbl_xwwl_char 
ALTER COLUMN id SET DEFAULT nextval('tbl_xwwl_char_id_seq');

-- Step 4: Make sure the sequence is owned by the table column
-- Bước 4: Đ<PERSON><PERSON> bảo sequence thuộc về cột của bảng
ALTER SEQUENCE tbl_xwwl_char_id_seq OWNED BY tbl_xwwl_char.id;

-- Verify the changes
-- Kiểm tra các thay đổi
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default 
FROM information_schema.columns 
WHERE table_name = 'tbl_xwwl_char' AND column_name = 'id';

-- Check sequence info
-- Kiểm tra thông tin sequence
SELECT 
    schemaname,
    sequencename,
    last_value,
    start_value,
    increment_by,
    is_called
FROM pg_sequences 
WHERE sequencename = 'tbl_xwwl_char_id_seq';
