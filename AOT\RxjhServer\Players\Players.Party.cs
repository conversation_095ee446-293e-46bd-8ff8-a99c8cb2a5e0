﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
	public void GroupTeleport(byte[] packetData, int length)
	{
		try
		{
			int num = packetData[10];
			int num2 = packetData[11];
			var nhanVatToaDoBanDo = MapID;
			var array = new byte[2];
			Buffer.BlockCopy(packetData, 14, array, 0, 2);
			var array2 = new byte[14];
			Buffer.BlockCopy(packetData, 26, array2, 0, 14);
			var text = Converter.smethod_8(array2).Replace("\0", string.Empty).Trim();
			int num3 = BitConverter.ToInt16(array, 0);
			if (BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) != ********** || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 0 || 1 == 0)
			{
				return;
			}
			var players = World.KiemTra_Ten_NguoiChoi(text);
			if (players != null)
			{
				switch (players.MapID)
				{
					case 801:
						HeThongNhacNho("Đối phương đang ở bản đồ Thế Lực Chiến, không thể sử dụng!");
						return;
					case 1001:
						HeThongNhacNho("Đối phương đang ở bản đồ Đại Ma Đầu, không thể sử dụng!");
						return;
					case 32001:
						HeThongNhacNho("Đối phương đang ở bản đồ Công Thành Chiến, không thể sử dụng!");
						return;
					case 40101:
						HeThongNhacNho("Bản đồ này không cho phép sử dụng bảo vật hay bí thuật!!");
						return;
				}
				if (players.CharacterName == CharacterName)
				{
					NhacNho_Move_Party(3, num3, **********);
				}
				else if (players.TeamID != TeamID)
				{
					NhacNho_Move_Party(6, num3, **********);
				}
				else
				{
					Mobile(players.PosX, players.PosY, players.PosZ, players.MapID, 0);
					if (nhanVatToaDoBanDo == MapID)
					{
						NhacNho_Move_Party(7, num3, **********);
					}
					else
					{
						NhacNho_Move_Party(1, num3, **********);
					}
				}
			}
			else
			{
				NhacNho_Move_Party(2, num3, **********);
			}
			VatPham_GiamDi_SoLuong_DoBen(num3, 1);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Sử dụng Bùa Party phạm sai lầm! [" + AccountID + "]-[" + CharacterName + "] " + ex.Message);
		}
	}

	public void LeaveTheTeam(byte[] data, int length)
	{
		if (World.WToDoi.TryGetValue(TeamID, out var value))
		{
			value.LeaveParty(this, 0);
		}
	}

	public void CaptainManagement(byte[] data, int length)
	{
		try
		{
			var array = new byte[14];
			Buffer.BlockCopy(data, 14, array, 0, 14);
			var characterName = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
			var action = data[10];
			int characterSession = BitConverter.ToInt16(data, 12);
			HeThongNhacNho($"CaptainManagement {this.SessionID} [{action}] [{characterName}] [{characterSession}]");
			if (!World.WToDoi.TryGetValue(TeamID, out var team))
			{
				return;
			}
			switch (action)
			{
				case 1:
					{
						var char1 = GetCharacterData(characterSession);
						if (char1 != null)
						{
							team.UyQuyen_DoiTruong(this, char1);
							break;
						}
						break;
					}
				case 2:
					{
						var char1 = GetCharacterData(characterSession);
						if (char1 != null)
						{
							team.LeaveParty(char1, 2);
							break;
						}
						break;
					}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "DoiTruong quản lý error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}
	
	public void AbortTeamRequest(byte[] data, int length)
	{
		try
		{
			var array = Converter.HexStringToByte("AA5512000B003300040001000100000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
			if (!World.WToDoi.TryGetValue(TeamID, out var value))
			{
				return;
			}
			if (value.InvitedPlayer != null)
			{
				value.InvitedPlayer.TeamID = 0;
				value.InvitedPlayer.TeamingStage = 0;
				value.InvitedPlayer.Client?.Send_Map_Data(array, array.Length);
				value.InvitedPlayer = null;
			}
			if (value.TotalMember <= 1)
			{
				value.Dispose();
				World.WToDoi.TryRemove(TeamID, out _);
				TeamID = 0;
				TeamingStage = 0;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tôi hủy bỏ ToDoi error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}
	private readonly object _teamLock = new object();
	public void ProcessTeamRequest(byte[] data, int length)
	{
		lock (_teamLock)
		{
			var num = 0;
			try
			{
				var type = BitConverter.ToInt16(data, 12);
				var sessionId = BitConverter.ToInt16(data, 14);
				HandleTeamRequest(sessionId, type);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "Bên kia đã huỷ ToDoi error![" + AccountID + "]-[" + CharacterName + "]" + num + "|" + ex.Message);
			}
		}

	}

	// request type enum, 2 = deny, 1 = accept
	public enum TeamRequestType
	{
		Deny = 2,
		Accept = 1
	}

	public void HandleTeamRequest(int playerId, int type)
	{
		if ((GetCharacterData(playerId) == null || playerId == 2) && World.WToDoi.TryGetValue(TeamID, out var value))
		{
			playerId = value.InvitedPlayer.SessionID;
			type = 1;
		}
		HeThongNhacNho("type: " + type);
		switch (type)
		{
			case (int)TeamRequestType.Deny:
				{
					var array3 = Converter.HexStringToByte("AA5512000B003300040001000100000000000000000055AA");
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
					Client?.Send_Map_Data(array3, array3.Length);
					TeamingStage = 0;
					if (!World.WToDoi.TryGetValue(TeamID, out var value4))
					{
						break;
					}
					var characterData2 = GetCharacterData(playerId);
					if (characterData2 != null)
					{
						if (value4.Captain.Client != null)
						{
							Buffer.BlockCopy(BitConverter.GetBytes(value4.Captain.SessionID), 0, array3, 4, 2);
							value4.Captain.Client.Send_Map_Data(array3, array3.Length);
						}
						value4.InvitedPlayer = null;
						if (value4.TotalMember <= 1)
						{
							value4.Captain.TeamID = 0;
							value4.Captain.TeamingStage = 0;
							value4.Dispose();
							World.WToDoi.TryRemove(TeamID, out _);
							characterData2.TeamID = 0;
							TeamID = 0;
							characterData2.TeamingStage = 0;
							TeamingStage = 0;
						}
						else
						{
							TeamingStage = 0;
							TeamID = 0;
						}
					}
					if (TeamingStage == 2)
					{
						value4.LeaveParty(this, 3);
					}
					break;
				}
			case (int)TeamRequestType.Accept:
				{
					if (!World.WToDoi.TryGetValue(TeamID, out var team)) break;
					Players inviter = GetCharacterData(playerId);
					if (inviter == null) break;

					// (A) ĐÃ là thành viên: chuẩn hoá + (migrate key nếu session đổi) rồi thoát
					int oldKey = -1;
					foreach (var player in team.PartyPlayers.Values)
					{
						if (player != null && player.AccountID == this.AccountID)
						{
							oldKey = player.SessionID;
							break;
						}
					}
					if (oldKey != -1)
					{
						if (oldKey != SessionID)
						{
							team.PartyPlayers.TryRemove(oldKey, out _);
							team.PartyPlayers.TryAdd(SessionID, this);
						}
						this.TeamID = team.PartyID;
						this.TeamingStage = 2;
						break;
					}

					// (B) CHƯA là thành viên & PT FULL: Đồng ý = TỪ CHỐI (chỉ áp cho người thứ 9)
					if (team.TotalMember >= World.Gioi_han_so_nguoi_vao_party)
					{
						if (team.InvitedPlayer != null && team.InvitedPlayer.AccountID == this.AccountID)
							team.InvitedPlayer = null;
						this.TeamingStage = 0;
						this.TeamID = 0;
						SendTeamData(CharacterName, 2, 13, 0, 1);           // đóng popup ở client mình
						if (inviter != null)
							inviter.SendTeamData(this.CharacterName, 2, 2, 0, 1); // báo người mời: coi như từ chối
						HeThongNhacNho("Số hiệp khách đã đầy, một đội tối đa [" + World.Gioi_han_so_nguoi_vao_party + "] người!");
						break;
					}

					// (C) Còn slot -> Accept bình thường
					team.InvitedPlayer = null;
					if (!team.PartyPlayers.ContainsKey(SessionID))
					{
						team.PartyPlayers.TryAdd(SessionID, this);
						team.ThamGiaThanhVienNhom_NhacNho(this);
					}
					this.TeamID = team.PartyID;
					this.TeamingStage = 2;
					if (inviter != null && inviter.TeamingStage < 2)
						inviter.TeamingStage = 2;

					break;
				}
		}
	}

	public void SendTeamData(string characterName, int type, int type2, int partyItem, int mark)
	{
		var array = Converter.HexStringToByte("AA551E0013013100180001000100010000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(type2), 0, array, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(partyItem), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 16, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(mark), 0, array, 18, 2);
		var bytes = Encoding.Default.GetBytes(characterName);
		Buffer.BlockCopy(bytes, 0, array, 20, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void SendRequestTeam(byte[] data, int length)
	{
		try
		{
			var targetByte = new byte[4];
			Buffer.BlockCopy(data, 14, targetByte, 0, 2);
			var targetId = BitConverter.ToInt32(targetByte, 0);
			HandleSendTeam(targetId);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi pt sài [Ctrl V] 111 - [" + AccountID + "]-[" + CharacterName + "]-[" + ex.Message + "]/[" + ex.StackTrace);
		}
	}

	public void HandleSendTeam(int targetId)
	{
		var array2 = Converter.HexStringToByte("AA5512000B003300040001000100000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
		var characterData = GetCharacterData(targetId);
		if (GMMode != 0 && World.ON_OFF_Kick_Mem_Ban_888 != 0)
		{
			if (characterData.Client != null)
			{
				World.ToanCucNhacNho("SERVER", 7, " KICK NHÂN VẬT [" + characterData.CharacterName + "] KHỎI KÊNH !!");
				HeThongNhacNho("Trục xuất ID: [" + characterData.AccountID + "] [" + characterData.CharacterName + "] Cấp: [" + characterData.Player_Level + "] Nghề: [" + characterData.Player_Job + "]", 10, "Thiên cơ các");
				characterData.BanAccount(5555, characterData.AccountID, "Admin Kick");
				characterData.Client.Dispose();
			}

			Client?.Send_Map_Data(array2, array2.Length);
		}
		else if (!PlayerTuVong && NhanVat_HP > 0)
		{
			if (InTheShop || OpenWarehouse)
			{
				HeThongNhacNho("Đại hiệp đang ở cửa hàng hoặc kho, không thể hành động!");
				Client?.Send_Map_Data(array2, array2.Length);
				return;
			}
			if (Exiting)
			{
				HeThongNhacNho("Đại hiệp đang rời giang hồ, không thể tiếp tục!");
				Client?.Send_Map_Data(array2, array2.Length);
				return;
			}
			var characterData2 = GetCharacterData(targetId);
			if (characterData2 == null)
			{
				HeThongNhacNho("Không tìm thấy đối phương trong giang hồ!");
				Client?.Send_Map_Data(array2, array2.Length);
			}
			else if (!FindPlayers(500, characterData2))
			{
				HeThongNhacNho("Đối phương ở quá xa, không thể liên kết!");
				Client?.Send_Map_Data(array2, array2.Length);
			}
			else if (characterData2.TeamID != 0)
			{
				HeThongNhacNho("Đối phương đã có tổ đội, không thể mời thêm!");
				Client?.Send_Map_Data(array2, array2.Length);
			}
			else if (!characterData2.PlayerTuVong && characterData2.NhanVat_HP > 0)
			{
				if (characterData2.Exiting)
				{
					HeThongNhacNho("Đối phương đang rời giang hồ, không thể liên lạc!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				if (characterData2.InTheShop)
				{
					HeThongNhacNho("Đối phương [" + characterData2?.ToString() + "] đang ở cửa hàng!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				if (characterData2.OpenWarehouse)
				{
					HeThongNhacNho("Đối phương [" + characterData2?.ToString() + "] đang ở kho!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				if (characterData2.CuaHangCaNhan != null && characterData2.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa)
				{
					HeThongNhacNho("Đối phương [" + characterData2?.ToString() + "] đang mở sạp hàng cá nhân!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				if (characterData2.CharacterName == CharacterName)
				{
					HeThongNhacNho("Danh tính [" + characterData2?.ToString() + "] không chính xác hoặc không xuất hiện!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				if (characterData2.Config.ToDoi == 0)
				{
					HeThongNhacNho("Đối phương [" + characterData2?.ToString() + "] đã từ chối tổ đội!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				if (TeamID == 0 && Math.Abs(Player_Level - characterData2.Player_Level) > World.HanChe_CapDo_Nhom)
				{
					HeThongNhacNho("Đẳng cấp chênh lệch vượt [" + World.HanChe_CapDo_Nhom + "] cấp, không thể lập đội!");
					Client?.Send_Map_Data(array2, array2.Length);
					return;
				}
				PartyClass team = null;
				if (World.WToDoi != null && !World.WToDoi.TryGetValue(TeamID, out team))
				{
					if (characterData2 != null)
					{
						team = new PartyClass(this);
						if (team != null)
						{
							if (CharacterName != characterData2.CharacterName)
							{
								if (CharacterPKMode == 0)
								{
									if (characterData2.CharacterPKMode == 0)
									{
										team.InvitedPlayer = characterData2;
										TeamID = World.AddTeam(team);
										characterData2.TeamID = TeamID;
										team.PartyID = TeamID;
										characterData2.TeamingStage = 1;
										TeamingStage = 1;
										characterData2.SendTeamData(CharacterName, 1, 1, team.RuleDistribution, 0);
										SendTeamData(CharacterName, 1, 1, team.RuleDistribution, 1);

									}
									else
									{
										HeThongNhacNho("Đối phương đang trong trạng thái PK, không thể sử dụng bí lệnh này!!", 20, "Thiên cơ các");
										Client?.Send_Map_Data(array2, array2.Length);
									}
								}
								else
								{
									HeThongNhacNho("Đại hiệp đang trong trạng thái PK, không thể sử dụng bí lệnh này!!", 20, "Thiên cơ các");
									Client?.Send_Map_Data(array2, array2.Length);
								}
							}
							else
							{
								HeThongNhacNho("Giá trị này hiện vô nghĩa, đại hiệp hãy thử lại sau 111!");
								Client?.Send_Map_Data(array2, array2.Length);
							}
						}
						else
						{
							HeThongNhacNho("Danh tính mời phải là duy nhất trong giang hồ!");
							Client?.Send_Map_Data(array2, array2.Length);
						}
					}
					else
					{
						HeThongNhacNho("Hiệp khách này đã không còn tồn tại trong giang hồ!");
						Client?.Send_Map_Data(array2, array2.Length);
					}
				}
				else if (team.Captain.SessionID != SessionID)
				{
					HeThongNhacNho("Chỉ đội trưởng mới có quyền mời người vào đội!");
					Client?.Send_Map_Data(array2, array2.Length);
				}
				else if (Math.Abs(team.PartyLevel - characterData2.Player_Level) > World.HanChe_CapDo_Nhom)
				{
					HeThongNhacNho("Đẳng cấp tổ đội hiện tại là [" + team.PartyLevel + "] cấp!", 20, "Thiên cơ các");
					Client?.Send_Map_Data(array2, array2.Length);
				}
				else if (team.TotalMember < World.Gioi_han_so_nguoi_vao_party && team.TotalMember != 1)
				{
					team.InvitedPlayer = characterData2;
					characterData2.TeamID = TeamID;
					characterData2.TeamingStage = 1;
					characterData2.SendTeamData(CharacterName, 1, 1, team.RuleDistribution, 0);
					SendTeamData(CharacterName, 1, 1, team.RuleDistribution, 1);
				}
			}
			else
			{
				HeThongNhacNho("Trạng thái đối phương không thể trả lời!", 20, "Thiên cơ các");
				Client?.Send_Map_Data(array2, array2.Length);
			}
		}
		else
		{
			HeThongNhacNho("Trạng thái không thể mời nhóm!", 20, "Thiên cơ các");
			Client?.Send_Map_Data(array2, array2.Length);
		}
	}

	public void SendTeam(string player)
	{
		try
		{
			var array = Converter.HexStringToByte("AA5512000B003300040001000100000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (!PlayerTuVong && NhanVat_HP > 0)
			{
				if (InTheShop || OpenWarehouse)
				{
					SendTeamData(CharacterName, 2, 13, 0, 1);
					HeThongNhacNho("Bạn [" + player + "] đang trong cửa hàng hoặc kho.");
					Client?.Send_Map_Data(array, array.Length);
					return;
				}
				if (Exiting)
				{
					SendTeamData(CharacterName, 2, 2, 0, 1);
					HeThongNhacNho("Bạn [" + player + "] đang thoát trò chơi.");
					Client?.Send_Map_Data(array, array.Length);
					return;
				}
				var playerByName = GetPlayerByName(player);
				if (playerByName == null)
				{
					HeThongNhacNho("Không tìm thấy người chơi [" + player + "], vui lòng viết đúng ký tự viết hoa.");
					Client?.Send_Map_Data(array, array.Length);
				}
				else if (playerByName.TeamID != 0)
				{
					HeThongNhacNho("Đối phương [" + player + "] đã có tổ đội.");
					Client?.Send_Map_Data(array, array.Length);
				}
				else if (!playerByName.PlayerTuVong && playerByName.NhanVat_HP > 0)
				{
					if (playerByName.Exiting)
					{
						SendTeamData(CharacterName, 2, 2, 0, 1);
						HeThongNhacNho("Đối phương [" + playerByName?.ToString() + "] đang rời giang hồ!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					if (playerByName.InTheShop)
					{
						SendTeamData(CharacterName, 2, 13, 0, 1);
						HeThongNhacNho("Đối phương [" + playerByName?.ToString() + "] đang ở cửa hàng!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					if (playerByName.OpenWarehouse)
					{
						SendTeamData(CharacterName, 2, 13, 0, 1);
						HeThongNhacNho("Đối phương [" + playerByName?.ToString() + "] đang ở kho!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					if (playerByName.CuaHangCaNhan != null && playerByName.CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa)
					{
						SendTeamData(CharacterName, 2, 13, 0, 1);
						HeThongNhacNho("Đối phương [" + playerByName?.ToString() + "] đang mở sạp hàng cá nhân!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					if (playerByName.CharacterName == CharacterName)
					{
						SendTeamData(CharacterName, 2, 13, 0, 1);
						HeThongNhacNho("Danh tính [" + playerByName?.ToString() + "] không chính xác hoặc không xuất hiện!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					if (playerByName.Config.ToDoi == 0)
					{
						SendTeamData(CharacterName, 2, 13, 0, 1);
						HeThongNhacNho("Đối phương [" + playerByName?.ToString() + "] đã từ chối tổ đội!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					if (TeamID == 0 && Math.Abs(Player_Level - playerByName.Player_Level) > World.HanChe_CapDo_Nhom)
					{
						SendTeamData(CharacterName, 2, 6, 0, 1);
						HeThongNhacNho("Đẳng cấp chênh lệch vượt [" + World.HanChe_CapDo_Nhom + "] cấp, không thể lập đội!");
						Client?.Send_Map_Data(array, array.Length);
						return;
					}
					PartyClass value = null;
					if (World.WToDoi != null && !World.WToDoi.TryGetValue(TeamID, out value))
					{
						value = new PartyClass(this);
						if (value != null)
						{
							if (CharacterName != playerByName.CharacterName)
							{
								if (CharacterPKMode == 0)
								{
									if (playerByName.CharacterPKMode == 0)
									{
										time_party = DateTime.Now;
										value.InvitedPlayer = playerByName;
										Thread.Sleep(10);
										TeamID = World.AddTeam(value);
										Thread.Sleep(10);
										playerByName.TeamID = TeamID;
										value.PartyID = TeamID;
										playerByName.TeamingStage = 1;
										TeamingStage = 1;
										playerByName.SendTeamData(CharacterName, 1, 1, value.RuleDistribution, 0);
										SendTeamData(CharacterName, 1, 1, value.RuleDistribution, 1);
									}
									else
									{
										HeThongNhacNho("Đối phương đang trong trạng thái PK, không thể sử dụng bí lệnh này!!", 20, "Thiên cơ các");
										Client?.Send_Map_Data(array, array.Length);
									}
								}
								else
								{
									HeThongNhacNho("Đại hiệp đang trong trạng thái PK, không thể sử dụng bí lệnh này!!", 20, "Thiên cơ các");
									Client?.Send_Map_Data(array, array.Length);
								}
							}
							else
							{
								HeThongNhacNho("Giá trị này hiện vô nghĩa, đại hiệp hãy thử lại sau 222!");
								Client?.Send_Map_Data(array, array.Length);
							}
						}
						else
						{
							HeThongNhacNho("Thiên Cơ Các đang gặp trục trặc, hãy thử lại sau vài khắc!");
							Client?.Send_Map_Data(array, array.Length);
						}
					}
					else if (Math.Abs(value.PartyLevel - playerByName.Player_Level) > World.HanChe_CapDo_Nhom)
					{
						SendTeamData(CharacterName, 2, 6, 0, 1);
						HeThongNhacNho("Đẳng cấp tổ đội hiện tại là [" + value.PartyLevel + "] cấp!", 20, "Thiên cơ các");
						Client?.Send_Map_Data(array, array.Length);
					}
					else if (value.TotalMember < World.Gioi_han_so_nguoi_vao_party && value.TotalMember != 1)
					{
						value.InvitedPlayer = playerByName;
						playerByName.TeamID = TeamID;
						playerByName.TeamingStage = 1;
						playerByName.SendTeamData(CharacterName, 1, 1, value.RuleDistribution, 0);
						SendTeamData(CharacterName, 1, 1, value.RuleDistribution, 1);
					}
				}
				else
				{
					SendTeamData(CharacterName, 2, 13, 0, 1);
					HeThongNhacNho("Đối phương đang đếm số tử thi, hãy bảo họ rời giang hồ rồi trở lại!", 20, "Thiên cơ các");
					Client?.Send_Map_Data(array, array.Length);
				}
			}
			else
			{
				SendTeamData(CharacterName, 2, 13, 0, 1);
				HeThongNhacNho("Đại hiệp đang đếm số tử thi, hãy hồi sinh để tiếp tục!", 20, "Thiên cơ các");
				Client?.Send_Map_Data(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi sài lệnh [ ! pt ] 222 - [" + AccountID + "]-[" + CharacterName + "]-[" + ex.Message + "]/[" + ex.StackTrace);
		}
	}
	public void ShowPlayers()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				LogHelper.WriteLine(0, "Players chỉ NguoiChoi");
			}
			if (!World.WToDoi.TryGetValue(TeamID, out var value))
			{
				return;
			}

			// Check if this is a single-member restored party
			using SendingClass sendingClass = new();
			sendingClass.Write(1);
			sendingClass.Write(value.PartyLevel); // party level
			sendingClass.Write(0);

			sendingClass.Write(value.TotalMember);
			foreach (var value2 in value.PartyPlayers.Values)
			{
				var disconnect = !value2.IsJoinWorld;
				sendingClass.Write2(value2.CharacterName == value.CaptainName ? 1 : 0);
				sendingClass.Write2(value2.SessionID);
				sendingClass.Write4(disconnect ? 0 : value2.NhanVat_HP);
				sendingClass.Write4(disconnect ? 0 : value2.NhanVat_MP);
				sendingClass.Write4(value2.CharacterMax_HP);
				sendingClass.Write4(value2.CharacterMax_MP);
				sendingClass.WriteString(value2.CharacterName, 14);
				sendingClass.Write2(0);
				sendingClass.Write(1); // Độ khó phó bản
				sendingClass.Write(0);
				sendingClass.Write(value2.Player_Job);
				sendingClass.Write(0);
				sendingClass.Write4(-1);

			}
			var sessionID = 9000;
			foreach (var value3 in value.PlayerOfflineList.Values)
			{
				sendingClass.Write2(0);
				sendingClass.Write2(sessionID++);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.WriteString(value3.CharacterName, 14);
				sendingClass.Write2(0);
				sendingClass.Write(1); // Độ khó phó bản
				sendingClass.Write(0);
				sendingClass.Write(value3.Job);
				sendingClass.Write(0);
				sendingClass.Write(-1);
				sendingClass.Write(-1);
				sendingClass.Write(0xC1);
				sendingClass.Write(1);
			}
			for (var i = 0; i < value.PartyPlayers.Count; i++)
			{
				Client?.SendPak(sendingClass, 30720, SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "LỗI Show Players() Phạm sai lầm tại num: [" + 1 + "]-[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void UyQuyen_DoiTruong_NhacNho(Players playe, Players toPlaye)
	{
		try
		{
			var array = Converter.HexStringToByte("AA553200000029002400010001000000C8CE0000000000000000000000000100D1E0D00000000000000000000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array, 14, 2);
			var bytes = Encoding.Default.GetBytes(playe.CharacterName);
			Buffer.BlockCopy(bytes, 0, array, 16, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(toPlaye.SessionID), 0, array, 30, 2);
			var bytes2 = Encoding.Default.GetBytes(toPlaye.CharacterName);
			Buffer.BlockCopy(bytes2, 0, array, 32, bytes2.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "bổ nhiệm Doi Truong Nhac Nho error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SendPartyDisbandMessage()
	{
		var array = Converter.HexStringToByte("AA55120000003700040003000200000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void this_RoiKhoi_ToDoi_NhacNho()
	{
		var array = Converter.HexStringToByte("AA5510000000370002000500000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void RoiKhoi_ToDoi_NhacNho(Players playe)
	{
		var array = Converter.HexStringToByte("AA5525000A0037001700020001000B000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array, 14, 2);
		var bytes = Encoding.Default.GetBytes(playe.CharacterName);
		Buffer.BlockCopy(bytes, 0, array, 16, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void GiaNhap_ToDoi_NhacNho(Players playe)
	{
		var array = Converter.HexStringToByte("AA5525000100350017000100010002000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array, 14, 2);
		var bytes = Encoding.Default.GetBytes(playe.CharacterName);
		Buffer.BlockCopy(bytes, 0, array, 16, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}
	
	public void TimKiem_ToDoi(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var num = 0;
			SendingClass sendingClass = new();
			sendingClass.Write1(World.WToDoi.Count);
			sendingClass.Write1(0);
			foreach (var value in World.WToDoi.Values)
			{
				sendingClass.Write4(value.PartyID);
				sendingClass.Write2(0);
				sendingClass.Write(Player_Zx);
				sendingClass.Write(Player_Level);
				sendingClass.WriteString(value.Captain.CharacterName, 15);
				sendingClass.Write(value.TotalMember);
				sendingClass.Write4(0);
				sendingClass.Write4(value.Captain.MapID);
				for (num = 0; num < 10; num++)
				{
					sendingClass.Write8(0L);
				}
			}

			Client?.SendPak(sendingClass, 280, SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "lỗi tìm PT![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public async void KiemTraToDoi(byte[] data, int length)
	{
		try
		{
			PacketModification(data, length);
			int num = BitConverter.ToInt16(data, 4);
			int num2 = BitConverter.ToInt16(data, 10);
			var array = new byte[4];
			Buffer.BlockCopy(data, 14, array, 0, 2);
			// var num3 = BitConverter.ToInt32(array, 0);
			var dst = Converter.HexStringToByte("AA5512000B003300040001000100000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, dst, 4, 2);
			if (GetCharacterData(num) == null)
			{
				return;
			}
			foreach (var value in World.WToDoi.Values)
			{
				var characterData = GetCharacterData(num);
				var userName = characterData.CharacterName;
				var playerLevel = characterData.Player_Level;
				var @string = string.Format("[{0}] cấp độ [{1}] xin vào tổ đội, bấm [!invite " + CharacterName + "] để mời tổ đội !! ", userName, playerLevel);
				if (value.PartyID == num2 && value.Captain.Client != null)
				{
					value.Captain.HeThongNhacNho(@string, 2, "Thiên cơ các");
					await SendMailInviteParty("\n Thư xin yêu cầu gia nhập Nhóm \n Nếu bạn đồng ý cho [" + CharacterName + "] tham gia. \n \n Gõ lệnh: !invite " + CharacterName + " \n \n Thông tin: \n Nhân vật: " + CharacterName + "\n Level: " + Player_Level + "\n Môn Phái: " + GuildName + " ", value.Captain.CharacterName);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kiểm tra Tổ Đội lỗi !! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public async Task SendMailInviteParty(string noiDungMsg, string nguoiNhan)
	{
		try
		{
			if (await GameDb.CheckCharacterExists(nguoiNhan))
			{
				var characterData = GetCharacterData(nguoiNhan);
				if (characterData != null)
				{
					X_Nguoi_Truyen_Thu_Loai xNguoiTruyenThuLoai = new();
					xNguoiTruyenThuLoai.TruyenThuID = (int)RxjhClass.CreateItemSeries();
					xNguoiTruyenThuLoai.TruyenThuNguoiGui = "Thư Yêu Cầu";
					xNguoiTruyenThuLoai.TruyenThuNoiDung = noiDungMsg;
					xNguoiTruyenThuLoai.TruyenThuThoiGian = DateTime.Now;
					xNguoiTruyenThuLoai.CoPhaiLaNPC = 0;
					xNguoiTruyenThuLoai.DaXemHayChua = 0;
					characterData.DanhSach_TruyenThu.Add(xNguoiTruyenThuLoai.TruyenThuID, xNguoiTruyenThuLoai);
					characterData.GetAllMails();
					characterData.ThereIsANewBookReminder_2();
				}
				var text = "AA551000010000B4000100000000000000005DA355AA";
				var array = Converter.HexStringToByte(text);
				Client.Send(array, array.Length);
			}
			else
			{
				HeThongNhacNho("Danh tính hiệp khách không tồn tại trong giang hồ!", 22, DateTime.Now.Hour + ":" + DateTime.Now.Minute);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thư Yêu Cầu lỗi !! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}
	
	public Players ToDoi_PhanPhoi(Players play)
	{
		var i = 0;
		try
		{
			if (!World.WToDoi.TryGetValue(play.TeamID, out var value))
			{
				return play;
			}
			if (value.RuleDistribution == 1)
			{
				for (; i < value.PartyPlayers.Count; i++)
				{
					if (value.PhanBoHienTai >= value.PartyPlayers.Count)
					{
						value.PhanBoHienTai = 0;
					}
					var players = value.ThanhVien_DatDuoc_TuongUng(value.PhanBoHienTai);
					value.PhanBoHienTai++;
					if (players != null && play.FindPlayers(1000, players))
					{
						return players;
					}
				}
				return play;
			}
			if (value.RuleDistribution == 2)
			{
				for (; i < value.PartyPlayers.Count; i++)
				{
					var key = RNG.Next(0, value.PartyPlayers.Count + 1);
					var players2 = value.ThanhVien_DatDuoc_TuongUng(key);
					if (players2 != null && play.FindPlayers(1000, players2))
					{
						return players2;
					}
				}
			}
			else if (value.RuleDistribution == 3)
			{
				var players3 = value.ThanhVien_DatDuoc_TuongUng(value.PhanBoHienTai);
				value.PhanBoHienTai++;
				if (players3 != null && play.FindPlayers(1000, players3))
				{
					return players3;
				}
			}
			else if (value.RuleDistribution == 4)
			{
				for (; i < value.PartyPlayers.Count; i++)
				{
					var key2 = 0;
					var players4 = value.ThanhVien_DatDuoc_TuongUng(key2);
					if (players4 != null && play.FindPlayers(1000, players4))
					{
						return players4;
					}
				}
				return play;
			}
			return play;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi PhanPhoi error [" + play.CharacterName + "] Chu kỳ [" + i + "]" + ex);
			return null;
		}
	}
}
