﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
	public void ChangeDoorService(byte[] packetData, int packetSize)
	{
		PacketReader packetReader = new(packetData, packetSize, bool_0: false);
		packetReader.Seek(10, SeekOrigin.Begin);
		var num = packetReader.ReadInt32();
		packetReader.ReadInt16();
		GangServiceWords = packetReader.ReadInt16();
		GangDoorClothesColor = packetReader.ReadInt16();
		var array = Converter.HexStringToByte("AA551E0045049100100002000000020000003D000000000000000000000000003C9455AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		var array2 = Converter.HexStringToByte("AA551E00450442151000B8530000010001001C800000002D310********00000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(GuildId), 0, array2, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(GangServiceWords), 0, array2, 16, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(GangDoorClothesColor), 0, array2, 18, 2);
		Client?.Send_Map_Data(array, array.Length);
		Client?.Send_Map_Data(array2, array2.Length);
		SendCurrentRangeBroadcastData(array2, array2.Length);
		OpenWarehouse = false;
		var gangServiceWords = GangServiceWords;
		var gangDoorClothesColor = GangDoorClothesColor;
		GameDb.UpdateGuildCostume(num, gangServiceWords, gangDoorClothesColor);
	}

	public async Task ApplyForDoorBadge(byte[] packetData, int packetSize)
	{
		var array = new byte[770];
		PacketReader packetReader = new(packetData, packetSize, bool_0: false);
		packetReader.Seek(10, SeekOrigin.Begin);
		var num = packetReader.ReadInt32();
		Buffer.BlockCopy(packetData, 18, array, 0, 770);
		await GameDb.SetGuildEmblem(GuildId, array);
		SendingClass w = new();
		w.Write4(num); // GuildID
		w.Write4(0); // member ???
		w.Write(array);
		Client?.SendPak(w, 60160, SessionID);
	}

	public async Task GetTheDoorBadge(byte[] packetData, int packetSize)
	{
		PacketReader packetReader = new(packetData, packetSize, bool_0: false);
		packetReader.Seek(10, SeekOrigin.Begin);
		var num = packetReader.ReadInt32();
		var theDoorBadge = await GameDb.GetGuildEmblem(num);
		SendingClass w = new();
		w.Write4(num); // GuildID
		w.Write4(0); // member ???
		w.Write(theDoorBadge);
		Client?.SendPak(w, 60160, SessionID);
	}

	public async void AssignAPosition(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 10, array, 0, 2);
			var num = BitConverter.ToInt32(array, 0);
			var array2 = Converter.HexStringToByte("AA551E000A00EF0010000********00000000000000000000000000000000000000055AA");
			var array3 = new byte[14];
			Buffer.BlockCopy(packetData, 12, array3, 0, 14);
			var characterData = GetCharacterData(Encoding.Default.GetString(array3).Replace("\0", string.Empty));
			if (characterData != null)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array2, 10, 1);
				if (num == 6)
				{
					GangCharacterLevel = 5;
					await GameDb.ChangeGuildMaster(characterData.CharacterName, CharacterName, GuildName);
					//RxjhClass.ChuyenBangChu_ChucVi(characterData.CharacterName, CharacterName, GuildName);
				}
				//RxjhClass.BangPhaiAssignAPosition(num, characterData.CharacterName);
				await GameDb.UpdateGuildPosition(GuildName, characterData.CharacterName, num);
				characterData.GangCharacterLevel = num;
				await characterData.ChaGang();
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array2, 10, 1);
			}

			Client?.Send_Map_Data(array2, array2.Length);
		}
		catch
		{
		}
	}

	public async Task GiaNhapBangPhai(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 27, array, 0, 1);
			switch (BitConverter.ToInt32(array, 0))
			{
				case 1:
					{
						var array10 = new byte[4];
						Buffer.BlockCopy(packetData, 10, array10, 0, 2);
						var characterData5 = GetCharacterData(BitConverter.ToInt32(array10, 0));
						if (characterData5 == null)
						{
							break;
						}
						var array11 = Converter.HexStringToByte("AA5530002A03E80022003103400000000000000000000000000000010AC0B600000000000000000000000000000000000000000055AA");
						var bytes8 = Encoding.Default.GetBytes(GuildName);
						Buffer.BlockCopy(bytes8, 0, array11, 12, bytes8.Length);
						var bytes9 = Encoding.Default.GetBytes(characterData5.CharacterName);
						Buffer.BlockCopy(bytes9, 0, array11, 29, bytes9.Length);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array11, 4, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(characterData5.SessionID), 0, array11, 10, 2);
						var dataTable2 = await GameDb.TotalMemberInGuild(GuildName);
						var num2 = 70;
						if (dataTable2 >= num2)
						{
							HeThongNhacNho("Bang hội của đại hiệp đã đủ [" + num2 + "] hiệp khách, không thể chiêu mộ thêm!!", 3, "Thiên cơ các");
							characterData5.HeThongNhacNho("Bang hội [" + GuildName + "] của [" + CharacterName + "] đã đủ [" + num2 + "] người", 3, "Thiên cơ các");
							break;
						}
						if (characterData5.CuaHangCaNhan != null || characterData5.Client == null)
						{
							HeThongNhacNho("Hiệp khách [" + characterData5.CharacterName + "] đang trong NPC, không thể gia nhập bang hội!!", 3, "Thiên cơ các");
							break;
						}
						if (characterData5.Player_Level < 35 && characterData5.Player_Job_level < 2)
						{
							HeThongNhacNho("Hiệp khách [" + characterData5.CharacterName + "] chưa thăng cấp nhị cảnh, không thể gia nhập bang hội!!", 3, "Thiên cơ các");
							break;
						}
						if (dataTable2 >= num2)
						{
							Buffer.BlockCopy(BitConverter.GetBytes(18), 0, array11, 28, 1);
						}
						else if (characterData5.CuaHangCaNhan != null)
						{
							Buffer.BlockCopy(BitConverter.GetBytes(16), 0, array11, 28, 1);
						}
						else if (characterData5.Player_Level >= 35 && characterData5.Player_Job_level >= 2)
						{
							if (characterData5.GuildId != 0)
							{
								Buffer.BlockCopy(BitConverter.GetBytes(17), 0, array11, 28, 1);
							}
							else
							{
								characterData5.Client?.Send_Map_Data(array11, array11.Length);
							}
						}
						else if (Player_Zx == characterData5.Player_Zx)
						{
							if (characterData5.GuildId != 0)
							{
								Buffer.BlockCopy(BitConverter.GetBytes(17), 0, array11, 28, 1);
							}
							else
							{
								characterData5.Client?.Send_Map_Data(array11, array11.Length);
							}
						}
						else
						{
							Buffer.BlockCopy(BitConverter.GetBytes(16), 0, array11, 28, 1);
						}
						Client?.Send_Map_Data(array11, array11.Length);
						break;
					}
				case 2:
					{
						var array4 = new byte[4];
						Buffer.BlockCopy(packetData, 10, array4, 0, 2);
						var num = BitConverter.ToInt32(array4, 0);
						var array5 = new byte[4];
						Buffer.BlockCopy(packetData, 28, array5, 0, 1);
						switch (BitConverter.ToInt32(array5, 0))
						{
							case 21:
								{
									var array7 = Converter.HexStringToByte("AA5530002A03E80022002A034400000000000000000000000000000215C0B600000000000000000000000000000000000000000055AA");
									var bytes3 = Encoding.Default.GetBytes(CharacterName);
									Buffer.BlockCopy(bytes3, 0, array7, 29, bytes3.Length);
									Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 4, 2);
									var characterData3 = GetCharacterData(num);
									if (characterData3 != null)
									{
										var bytes4 = Encoding.Default.GetBytes(characterData3.GuildName);
										Buffer.BlockCopy(bytes4, 0, array7, 12, bytes4.Length);
										Buffer.BlockCopy(BitConverter.GetBytes(characterData3.SessionID), 0, array7, 10, 2);
										characterData3.Client?.Send_Map_Data(array7, array7.Length);
										GameDb.PlayerJoinGuild(CharacterName, characterData3.GuildName, Player_Level, Player_Job).GetAwaiter().GetResult();
									}

									Client?.Send_Map_Data(array7, array7.Length);
									ReadGangData();
									ChaGang().GetAwaiter().GetResult();
									LoadCharacterWearItem();
									PlayerLeaveMap(MapID);
									GetTheReviewRangePlayers();
									break;
								}
							case 22:
								{
									var array8 = Converter.HexStringToByte("AA5530000A00E80022000B00CC00000000000000000000000000000216617300000000000000000000000000000000000000000055AA");
									var bytes5 = Encoding.Default.GetBytes(CharacterName);
									Buffer.BlockCopy(bytes5, 0, array8, 29, bytes5.Length);
									Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array8, 4, 2);
									var characterData4 = GetCharacterData(num);
									if (characterData4 != null)
									{
										var bytes6 = Encoding.Default.GetBytes(characterData4.GuildName);
										Buffer.BlockCopy(bytes6, 0, array8, 12, bytes6.Length);
										Buffer.BlockCopy(BitConverter.GetBytes(characterData4.SessionID), 0, array8, 10, 2);
										characterData4.Client?.Send_Map_Data(array8, array8.Length);
									}

									Client?.Send_Map_Data(array8, array8.Length);
									break;
								}
							case 23:
								{
									var array6 = Converter.HexStringToByte("AA5530000A00E80022000B000000000000000000000000000000000217BDD600000000000000000000000000000000000000000055AA");
									var bytes2 = Encoding.Default.GetBytes(CharacterName);
									Buffer.BlockCopy(bytes2, 0, array6, 29, bytes2.Length);
									Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array6, 4, 2);
									var characterData2 = GetCharacterData(num);
									if (characterData2 != null)
									{
										Buffer.BlockCopy(BitConverter.GetBytes(characterData2.SessionID), 0, array6, 10, 2);
										characterData2.Client?.Send_Map_Data(array6, array6.Length);
									}

									Client?.Send_Map_Data(array6, array6.Length);
									break;
								}
						}
						break;
					}
				case 3:
					{
						if (MapID == 42001)
						{
							HeThongNhacNho("Tại bản đồ này, đại hiệp không thể rời bang phái!");
							break;
						}
						var array9 = Converter.HexStringToByte("AA5530000B00E80022000000000000000000000000000000000000031F000000000000000000000000000000000000000000000055AA");
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array9, 4, 2);
						Client?.Send_Map_Data(array9, array9.Length);
						var bytes7 = Encoding.Default.GetBytes(CharacterName);
						Buffer.BlockCopy(bytes7, 0, array9, 29, bytes7.Length);
						var gangName = GuildName;
						if (GangCharacterLevel == 6)
						{
							GameDb.DisbandGuild(GuildName).GetAwaiter().GetResult();
							//DBA.ExeSqlCommand($"DELETE  FROM  TBL_XWWL_Guild  WHERE  G_Master  =  '{CharacterName}'").GetAwaiter().GetResult();
							foreach (var value in World.allConnectedChars.Values)
							{
								if (value.GuildName == gangName)
								{
									GameDb.ExpelMember(gangName, value.CharacterName).GetAwaiter().GetResult();
									value.ReadGangData();
									Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array9, 27, 1);
									SendGangMessage(gangName, array9, array9.Length);
									value.LoadCharacterWearItem();
									value.PlayerLeaveMap(value.MapID);
									value.GetTheReviewRangePlayers();
								}
							}
							//DBA.ExeSqlCommand($"DELETE  FROM  TBL_XWWL_GuildMember  WHERE  G_Name  =  '{gangName}'").GetAwaiter().GetResult();
						}
						else
						{
							GameDb.ExpelMember(gangName, CharacterName).GetAwaiter().GetResult();
							ReadGangData();
							Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array9, 27, 1);
							SendGangMessage(gangName, array9, array9.Length);
							LoadCharacterWearItem();
							PlayerLeaveMap(MapID);
							GetTheReviewRangePlayers();
						}
						break;
					}
				case 4:
					{
						var array2 = new byte[14];
						Buffer.BlockCopy(packetData, 29, array2, 0, 14);
						var text = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
						var dataTable = await GameDb.FindGuild(GuildName);
						if (dataTable == null)
						{
							break;
						}
						if (MapID == 42001)
						{
							HeThongNhacNho("Tại bản đồ này, đại hiệp không thể rời bang phái!");
						}
						else
						{
							var text2 = dataTable.g_master.ToString();
							if (!(text == text2) && GameDb.ExpelMember(GuildName, text).Result)
							{
								var array3 = Converter.HexStringToByte("AA5530000A00E800220000000000000000000000000000000000000421617300000000000000000000000000000000000000000055AA");
								var bytes = Encoding.Default.GetBytes(text);
								Buffer.BlockCopy(bytes, 0, array3, 29, bytes.Length);
								SendGangMessage(GuildName, array3, array3.Length);
								var characterData = GetCharacterData(text);
								if (characterData != null)
								{
									characterData.ReadGangData();
									characterData.LoadCharacterWearItem();
									characterData.PlayerLeaveMap(MapID);
									characterData.GetTheReviewRangePlayers();
								}
							}
						}
						break;
					}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Gia Nhap Bang Phai error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void TaoBangPhaiXacNhan(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = Converter.HexStringToByte("AA5520000000E40012003********000000000000000000000000300000000000000000055AA");
			var array2 = new byte[15];
			Buffer.BlockCopy(packetData, 10, array2, 0, 15);
			var s = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
			var bytes = Encoding.Default.GetBytes(s);
			Buffer.BlockCopy(bytes, 0, array, 10, bytes.Length);
			if (Player_Level < 35)
			{
				HeThongNhacNho("Chưa đạt cấp 35, đại hiệp không thể lập bang phái!");
				return;
			}
			if (Player_Money < ********)
			{
				HeThongNhacNho("Không đủ 10 triệu ngân lượng, đại hiệp không thể lập bang phái!");
				return;
			}
			if (GameDb.CheckGuildNameExists(s).Result)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 26, 1);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(99), 0, array, 26, 1);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TaoMoi_BangPhai_XacNhan error!:[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void TaoBangPhai(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = Converter.HexStringToByte("AA5520000000E60012003********000000000000000000000000********0000000000055AA");
			var array2 = new byte[15];
			Buffer.BlockCopy(packetData, 10, array2, 0, 15);
			var s = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
			if (Player_Level < 35)
			{
				HeThongNhacNho("Chưa đạt cấp 35, đại hiệp không thể lập bang phái!");
				return;
			}
			if (Player_Money < ********)
			{
				HeThongNhacNho("Không đủ 10 triệu ngân lượng, đại hiệp không thể lập bang phái!");
				return;
			}
			var bytes = Encoding.Default.GetBytes(s);
			Buffer.BlockCopy(bytes, 0, array, 10, bytes.Length);
			// switch (RxjhClass.TaoMoi_BangPhai(CharacterName, s, Player_Level))
			// {
			// 	case 0:
			// 		Player_Money -= ********L;
			// 		UpdateMoneyAndWeight();
			// 		ReadGangData();
			// 		KhoiTaoChungToiDa_TrangBiVatPham();
			// 		Buffer.BlockCopy(BitConverter.GetBytes(99), 0, array, 26, 1);
			// 		break;
			// 	case 1:
			// 		Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 26, 1);
			// 		break;
			// 	case 2:
			// 		HeThongNhacNho("Số liệu sai lệch, đại hiệp hãy nhập lại một lần nữa!");
			// 		Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 27, 1);
			// 		break;
			// }
			if (GameDb.CreateGuild(CharacterName, s, Player_Level).Result)
			{
				Player_Money -= ********L;
				UpdateMoneyAndWeight();
				ReadGangData();
				LoadCharacterWearItem();
				Buffer.BlockCopy(BitConverter.GetBytes(99), 0, array, 26, 1);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 26, 1);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
			ChaGang().GetAwaiter().GetResult();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "TaoMoi_BangPhai error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public async void ModifyGangAnnouncement(byte[] packetData, int packetSize)
	{
		PacketModification(packetData, packetSize);
		var array = new byte[50];
		Buffer.BlockCopy(packetData, 14, array, 0, 50);
		var text = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
		if (text.IndexOf("测试", 0) != -1)
		{
			text = text.Remove(0, 2);
		}
		else if (text.IndexOf("'") != -1 || text.IndexOf(";") != -1 || text.IndexOf("--") != -1)
		{
			return;
		}
		if (GangCharacterLevel == 6)
		{
			// var query = $"UPDATE TBL_XWWL_Guild SET G_Notice = '{text}' WHERE G_Name = '{GuildName}'";
			// DBA.ExeSqlCommand(query).GetAwaiter().GetResult();
			await GameDb.UpdateGuildNotice(GuildName, text);
			await ChaGang();
		}
		else
		{
			HeThongNhacNho("Đại hiệp không phải bang chủ, không thể sửa thông tri bang phái!");
		}
	}

	public async Task ChaGang()
	{
		var num = 0;
		if (GuildId == 0)
		{
			return;
		}
		try
		{
			var array = Converter.hexStringToByte2("AA55C200C802E900B400565E260000000000000000000000000000000000020000000********30000006500000000000000000000000000000000000000000000000000000000000000000000000000000000000000");
			var array2 = Converter.HexStringToByte("000000000000A44E55AA");
			var dataTable = await GameDb.FindGuild(GuildName);
			if (dataTable == null)
			{
				return;
			}

			var guildLevel = (int)dataTable.leve;
			var guildPoint = (int)dataTable.thanhdanh;
			var point = 0;
			var win = 0;
			var loss = 0;
			var money = 0L;
			var hoa = 0;
			try
			{
				point = dataTable.bangphaivohuan ?? 0;
				win = dataTable.thang ?? 0;
				loss = dataTable.thua ?? 0;
				hoa =dataTable.hoa ?? 0;
				money = dataTable.monphaitaisan ;
				MonPhai_LienMinh_MinhChu = dataTable.lienminh_minhchu.ToString();
				ThongBao_CongThanh = int.Parse(dataTable.thongbao_congthanh.ToString());
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, ex.StackTrace+"Tra BangPhai Mới error: [" + AccountID + "]-[" + CharacterName + "] [" + ex.Message + "]");
			}
			var array3 = new byte[100];
			var bytes3 = Encoding.Default.GetBytes(dataTable.g_notice.ToString());
			var bytes = BitConverter.GetBytes(GuildId);
			var bytes2 = Encoding.Default.GetBytes(GuildName);
			Buffer.BlockCopy(bytes, 0, array, 10, bytes.Length);
			Buffer.BlockCopy(bytes2, 0, array, 14, bytes2.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(guildLevel), 0, array, 30, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(guildPoint), 0, array, 34, 2);
			if (GangBadge != null)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(World.ServerGroupID), 0, array, 42, 2);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 42, 2);
			}
			Buffer.BlockCopy(BitConverter.GetBytes(point), 0, array, 62, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(win), 0, array, 66, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(loss), 0, array, 70, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(money), 0, array, 74, 8);
			var dataTable2 = await GameDb.FindGuildMembers(GuildName);
			var totalMember = dataTable2.Count;
			if (totalMember > 100)
			{
				totalMember = 100;
			}
			Buffer.BlockCopy(BitConverter.GetBytes(totalMember), 0, array, 38, 2);
			var response = new byte[totalMember * 46 + array.Length + array2.Length + array3.Length];
			Buffer.BlockCopy(array, 0, response, 0, array.Length);
			Buffer.BlockCopy(array2, 0, response, response.Length - array2.Length, array2.Length);
			for (var i = 0; i < totalMember; i++)
			{
				var memberByteName = Encoding.Default.GetBytes(dataTable2[i].fld_name.ToString());
				var memberName = Encoding.Default.GetString(memberByteName);
				var num3 = int.Parse(dataTable2[i].leve.ToString());
				if (CharacterName == memberName)
				{
					GangCharacterLevel = num3;
				}
				Buffer.BlockCopy(memberByteName, 0, response, array.Length + i * 46, memberByteName.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, response, array.Length + 18 + i * 46, 2);
				try
				{
					var online = GameDb.IsCharacterOnline(memberName).Result;
					if (online > 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(1), 0, response, array.Length + 20 + i * 46, 2);
						// HeThongNhacNho($"Char {memberName} Job { dataTable2[i].fld_job} ");
						Buffer.BlockCopy(BitConverter.GetBytes(dataTable2[i].fld_job), 0, response, array.Length + 24 + i * 46, 2); // job
					}
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "kiểm tra BangPhai hội viên Sai lầm: [" + AccountID + "]-[" + CharacterName + "]" + ex2.Message);
				}
				Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(dataTable2[i].fld_level.ToString())), 0, response, array.Length + 16 + i * 46, 2);
			}
			Buffer.BlockCopy(bytes3, 0, response, array.Length + totalMember * 46, bytes3.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(58 + totalMember * 46 + array3.Length + 8), 0, response, 2, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(52 + totalMember * 46 + array3.Length), 0, response, 8, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, response, 4, 2);
			Client?.SendMultiplePackage(response, response.Length);
			UpdateMartialArtsAndStatus();
		}
		catch (Exception ex3)
		{
			LogHelper.WriteLine(LogLevel.Error, "kiểm tra BangPhai error: Num: [" + num + "] - [" + AccountID + "]-[" + CharacterName + "]" + ex3.Message);
		}
	}
	
	public void GangTeleport(byte[] packetData, int packetSize)
	{
		try
		{
			if (GuildId == 0 || MapID == 801 || MapID == 2501)
			{
				return;
			}
			if (World.WhetherTheCurrentLineIsSilver == 1)
			{
				HeThongNhacNho("Mời đại hiệp đến Quảng Trường Tiền Tệ để dịch chuyển về Huyền Bột Phái!", 10, "Thiên cơ các");
				return;
			}
			var array = new byte[2];
			Buffer.BlockCopy(packetData, 14, array, 0, 2);
			int num = BitConverter.ToInt16(array, 0);
			if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != **********)
			{
				return;
			}
			var array2 = new byte[14];
			Buffer.BlockCopy(packetData, 26, array2, 0, 14);
			var text = Encoding.Default.GetString(array2).Replace("\0", string.Empty).Trim();
			if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0)
			{
				return;
			}
			var players = World.KiemTra_Ten_NguoiChoi(text);
			if (players != null)
			{
				var array3 = World.BanDoKhoa_Chat.Split(';');
				if (array3.Length >= 1)
				{
					for (var i = 0; i < array3.Length; i++)
					{
						if (int.Parse(array3[i]) == players.MapID)
						{
							GangTeleporterPrompt(8, num, **********);
							return;
						}
					}
				}
				if (players.MapID != 42001 && players.MapID != 9001 && players.MapID != 9101 && players.MapID != 9201)
				{
					if (players.MapID == 30000 || players.MapID == 30100 || players.MapID == 30200 || players.MapID == 30300)
					{
						if (RemainingTimeOfTrainingMap <= 0)
						{
							GangTeleporterPrompt(8, num, **********);
							HeThongNhacNho("Dịch chuyển thất bại, thời gian hoạt động của bản đồ đã cạn!");
							return;
						}
						FBtime = DateTime.Now;
						HeThongNhacNho("Thời gian bắt đầu tính, sau [" + RemainingTimeOfTrainingMap + "] khắc sẽ tự động truyền tống!");
					}
					if (players.CharacterName == CharacterName)
					{
						GangTeleporterPrompt(3, num, **********);
						return;
					}
					if (players.GuildId != GuildId)
					{
						GangTeleporterPrompt(6, num, **********);
						return;
					}
					GangTeleporterPrompt(1, num, **********);
					VatPham_GiamDi_SoLuong_DoBen(num, 1);
					Mobile(players.PosX, players.PosY, players.PosZ, players.MapID, 0);
				}
				else
				{
					HeThongNhacNho("Nơi đây không cho phép sử dụng bảo vật hay bí thuật!");
				}
			}
			else
			{
				GangTeleporterPrompt(2, num, **********);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BangPhai Gửi error![" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}
}
