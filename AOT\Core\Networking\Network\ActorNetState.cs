using Akka.Actor;
using Akka.IO;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer;
using RxjhServer.HelperTools;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Networking.Core;
using HeroYulgang.Core;
using HeroYulgang.Core.NetCore;
using HeroYulgang.Core.Managers;

namespace HeroYulgang.Core.Networking.Network
{
    /// <summary>
    /// Cấu hình cho một batch queue
    /// </summary>
    public class BatchConfig
    {
        public int CompressionOpcode { get; set; }
        public int TimeoutMs { get; set; }
        public HashSet<int> Opcodes { get; set; } = new HashSet<int>();
    }

    /// <summary>
    /// Queue cho một batch cụ thể
    /// </summary>
    public class BatchQueue
    {
        public List<(SendingClass packet, int opcode, int sessionID)> Packets { get; set; } = new List<(SendingClass, int, int)>();
        public Timer Timer { get; set; }
        public volatile bool TimerActive = false;
        public BatchConfig Config { get; set; }
    }

    /// <summary>
    /// Lớp ActorNetState thay thế cho NetState, sử dụng Akka.NET để gửi packet
    /// Hỗ trợ cả ConnectionID (network layer) và PlayerSessionID (game layer)
    /// </summary>
    public class ActorNetState
    {
        private readonly IActorRef _connection;
        private readonly int _sessionId; // Có thể là ConnectionID hoặc PlayerSessionID tùy context
        private readonly IPEndPoint _remoteEndPoint;
        private bool _running = true;
        private bool _treoMay = false;
        private bool _online = false;

        private bool _login = false;

        // Thêm property riêng cho PlayerSessionID
        private int? _playerSessionId = null;

        public IActorRef Connection => _connection;
        public bool Login
        {
            get => _login;
            set => _login = value;
        }
        public bool Online
        {
            get => _online;
            set => _online = value;
        }
        public byte[] g_cur_key { get; set; }
        private Players? _player;

        public bool VersionVerification { get; set; } = false;

        // Multiple Batch packet system
        private readonly object _batchLock = new object();

        // Batch configuration for different compression opcodes
        private readonly Dictionary<int, BatchConfig> _batchConfigs = new Dictionary<int, BatchConfig>
        {
            { 576, new BatchConfig { CompressionOpcode = 576, TimeoutMs = 100, Opcodes = [29696] } }, // 6fps  packet di chuyển của quúai 116,
            { 587, new BatchConfig { CompressionOpcode = 587, TimeoutMs = 25, Opcodes = [31488,26624,26368] } }, // 40fps packet 104, 103,123
            { 588, new BatchConfig { CompressionOpcode = 588, TimeoutMs = 16, Opcodes = [25600,40960] } } /// 60fps paket 100
        };

        // Batch queues for each compression opcode
        private readonly Dictionary<int, BatchQueue> _batchQueues = new Dictionary<int, BatchQueue>();

        /// <summary>
        /// Khởi tạo một ActorNetState mới
        /// </summary>
        /// <param name="connection">Tham chiếu đến actor kết nối</param>
        /// <param name="sessionId">ID phiên kết nối</param>
        /// <param name="remoteEndPoint">Địa chỉ IP và port của client</param>
        public ActorNetState(IActorRef connection, int sessionId, IPEndPoint remoteEndPoint)
        {
            _connection = connection;
            _sessionId = sessionId;
            _remoteEndPoint = remoteEndPoint;

            // Khởi tạo batch queues cho mỗi compression opcode
            foreach (var config in _batchConfigs)
            {
                _batchQueues[config.Key] = new BatchQueue
                {
                    Config = config.Value
                };
            }
        }

        public bool ThoatGame { get; private set; }

        /// <summary>
        /// Lấy hoặc đặt trạng thái treo máy
        /// </summary>
        public bool TreoMay
        {
            get => _treoMay;
            set => _treoMay = value;
        }

        /// <summary>
        /// Lấy ConnectionID (network layer) hoặc PlayerSessionID (game layer) tùy context
        /// DEPRECATED: Sử dụng ConnectionID hoặc PlayerSessionID thay thế
        /// </summary>
        [Obsolete("Use ConnectionID for network operations, PlayerSessionID for game operations")]
        public int SessionID
        {
            get => _sessionId;
        }

        /// <summary>
        /// Lấy ConnectionID cho network operations
        /// </summary>
        public int ConnectionID => _sessionId;

        /// <summary>
        /// Lấy hoặc đặt PlayerSessionID cho game operations
        /// </summary>
        public int? PlayerSessionID
        {
            get => _playerSessionId;
            set => _playerSessionId = value;
        }

        /// <summary>
        /// Kiểm tra xem có PlayerSessionID không
        /// </summary>
        public bool HasPlayerSession => _playerSessionId.HasValue;

        /// <summary>
        /// Lấy hoặc đặt tham chiếu đến đối tượng Player
        /// </summary>
        public Players? Player
        {
            get => _player;
            set => _player = value;
        }

        /// <summary>
        /// Kiểm tra xem kết nối có đang chạy không
        /// </summary>
        public bool Running => _running;

        public bool BindAccount { get; internal set; }

        /// <summary>
        /// Gửi dữ liệu đến client
        /// </summary>
        /// <param name="data">Dữ liệu cần gửi</param>
        /// <param name="length">Độ dài dữ liệu</param>
        public void Send(byte[] data, int length)
        {
            try
            {
                if (!_running) return;

                // Check networking configuration to decide routing
                var networkingConfig = ConfigManager.Instance.NetworkingSettings;

                if (networkingConfig.UseNetCoreServer)
                {
                    // Route to NetCoreServer
                    SendViaNetCoreServer(data, length);
                }
                else
                {
                    // Route to Akka.net (original implementation)
                    SendViaAkka(data, length);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Send data via Akka.net (original implementation)
        /// </summary>
        private void SendViaAkka(byte[] data, int length)
        {
            try
            {
                // Check if ActorSystem is available
                try
                {
                    var actorSystem = NetworkingSystem.Instance.ActorSystem;
                    if (actorSystem == null)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"ActorSystem chưa được khởi tạo cho client {_sessionId}");
                        return;
                    }
                }
                catch (InvalidOperationException)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"ActorSystem chưa được khởi tạo cho client {_sessionId}");
                    return;
                }

                // Mã hóa dữ liệu trước khi gửi
                var encryptedData = Crypto.EncryptPacket(data);

                // Gửi dữ liệu thông qua actor
                var tcpManager = NetworkingSystem.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                tcpManager.Tell(new SendPacket(_connection, encryptedData));
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu qua Akka đến client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Send data via NetCoreServer
        /// </summary>
        private void SendViaNetCoreServer(byte[] data, int length)
        {
            try
            {
                // Find the corresponding GameSession for this ActorNetState
                var gameSession = FindGameSessionBySessionId(_sessionId);
                if (gameSession != null)
                {
                    // Use GameSession to send (it handles encryption internally)
                    gameSession.SendPacket(data, length);

                    // Ghi log nếu cần
                    if (World.Debug > 0)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"[NetCore] Gửi dữ liệu đến client {_sessionId}: {Converter.ToString(data)}");
                    }
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Không tìm thấy GameSession cho SessionID {_sessionId}");

                    // Only fallback to Akka if Akka networking is enabled
                    var config = ConfigManager.Instance.NetworkingSettings;
                    if (config.UseAkkaNetworking)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"Fallback to Akka for SessionID {_sessionId}");
                        SendViaAkka(data, length);
                    }
                    else
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"GameSession not found for SessionID {_sessionId} and Akka networking is disabled");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu qua NetCore đến client {_sessionId}: {ex.Message}");

                // Only fallback to Akka if Akka networking is enabled
                var config = ConfigManager.Instance.NetworkingSettings;
                if (config.UseAkkaNetworking)
                {
                    try
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"Fallback to Akka after NetCore error for SessionID {_sessionId}");
                        SendViaAkka(data, length);
                    }
                    catch (Exception fallbackEx)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Fallback to Akka cũng thất bại: {fallbackEx.Message}");
                    }
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, $"NetCore send failed for SessionID {_sessionId} and Akka networking is disabled");
                }
            }
        }

        public GameSession? FindGameSessionByAccountId(string accountId)
        {
            try
            {
                // Access NetCoreServerWrapper to find the session
                var port = ConfigManager.Instance.ServerSettings.GameServerPort;
                var serverWrapper = HeroYulgang.Core.NetCore.NetCoreServerWrapper.GetInstance(port);
                return serverWrapper?.FindSessionByAccountId(accountId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error finding GameSession for AccountId {accountId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Find GameSession by SessionId for NetCoreServer routing
        /// </summary>
        private GameSession? FindGameSessionBySessionId(int sessionId)
        {
            try
            {
                // Access NetCoreServerWrapper to find the session
                // Get the correct port from ConfigManager
                var port = ConfigManager.Instance.ServerSettings.GameServerPort;
                var serverWrapper = NetCoreServerWrapper.GetInstance(port);
                return serverWrapper?.FindSessionById(sessionId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error finding GameSession for SessionID {sessionId}: {ex.Message}");
                return null;
            }
        }

        public void SendSinglePackage(byte[] toSendBuff, int len)
        {
            var array = new byte[BitConverter.ToInt16(toSendBuff, 9) + 7];
            System.Buffer.BlockCopy(toSendBuff, 5, array, 0, array.Length);
            SendSinglePackage_PackageTransmit(array, array.Length);
        }

        private void SendSinglePackage_PackageTransmit(byte[] toSendBuff, int length)
        {
            var array = new byte[length + 15];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(length + 9), 0, array, 2, 2);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 5, length);
            array[array.Length - 2] = 85;
            array[array.Length - 1] = 170;
            Send(array, array.Length);
        }

        public void Send_Map_Data(byte[] byte_0, int int_1)
        {
            try
            {
                var array = new byte[int_1 + 2];
                array[0] = 170;
                array[1] = 85;
                Buffer.BlockCopy(BitConverter.GetBytes(int_1 - 4), 0, array, 2, 2);
                Buffer.BlockCopy(byte_0, 4, array, 4, 2);
                Buffer.BlockCopy(byte_0, 6, array, 8, int_1 - 8);
                array[^2] = 85;
                array[^1] = 170;
                Send(array, array.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Send_Map_Data Error {_sessionId}: {ex.Message}");
            }
        }
        public void SendPak(SendingClass pak, int opcode, int sessionID, bool bypass = false)
        {
            try
            {
                // Tìm batch queue phù hợp cho opcode này
                int? targetCompressionOpcode = FindBatchQueueForOpcode(opcode);

                if (targetCompressionOpcode.HasValue && !bypass)
                {
                    AddToBatch(pak, opcode, sessionID, targetCompressionOpcode.Value);
                }
                else
                {
                    // Gửi packet bình thường
                    var array = pak.ToArray2(opcode, sessionID);
                    SendMultiplePackageEncryptionByPass(array, array.Length, 1);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendPak error for session {_sessionId}: {ex.Message}");
            }
        }

        private void SendMultiplePackageEncryptionByPass(byte[] toSendBuff, int length, int xl)
        {
            try
            {
                var array = new byte[toSendBuff.Length + 6];
                array[0] = 170;
                array[1] = 85;
                Buffer.BlockCopy(BitConverter.GetBytes(toSendBuff.Length), 0, array, 2, 2);
                Buffer.BlockCopy(toSendBuff, 0, array, 4, toSendBuff.Length);
                array[^2] = 85;
                array[^1] = 170;
                Send(array, array.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_sessionId}: {ex.Message}");
                ///LogHelper.WriteLine(LogLevel.Error, "Send()_SendMultiplePackageEncryption" + WorldId + "|" + ex.Message);
            }
        }
        private void SendMultiplePackage_PackageTransmit(byte[] toSendBuff, int length, int int_0)
        {
            var array = new byte[length + 16];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(length + 10), 0, array, 2, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(int_0), 0, array, 4, 2);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 6, length);
            array[array.Length - 2] = 85;
            array[array.Length - 1] = 170;
            Send_Map_Data(array, array.Length);
        }

        public void SendMultiplePackage(byte[] toSendBuff, int len)
        {
            Send_Map_Data(toSendBuff, len);
        }
        /// <summary>
        /// Đóng kết nối và giải phóng tài nguyên
        /// </summary>
        public void Dispose()
        {
            _running = false;

            try
            {
                // Flush và dispose tất cả batch queues trước khi đóng kết nối
                FlushBatchedPackets();

                // Dispose tất cả batch timers và packets
                lock (_batchLock)
                {
                    foreach (var queue in _batchQueues.Values)
                    {
                        queue.Timer?.Dispose();
                        foreach (var packet in queue.Packets)
                        {
                            packet.packet?.Dispose();
                        }
                        queue.Packets.Clear();
                    }
                }

                Player?.Logout(); // World.allconnectedChars đã có dispose trong đây
                // Only notify TcpManagerActor if Akka networking is enabled
                var config = ConfigManager.Instance.NetworkingSettings;
                if (config.UseAkkaNetworking)
                {
                    try
                    {
                        var tcpManager = NetworkingSystem.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                        tcpManager.Tell(new CloseConnection(_connection));
                    }
                    catch (Exception akkaEx)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng kết nối Akka cho client {_sessionId}: {akkaEx.Message}");
                    }
                }
                // For NetCore, the GameSession will handle cleanup automatically
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng kết nối client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Chuyển đổi thành chuỗi
        /// </summary>
        public override string ToString()
        {
            return _remoteEndPoint.Address.ToString();
        }
        public int GetPort()
        {
            return _remoteEndPoint.Port;
        }

        internal void DisposedOffline()
        {
            Dispose();
        }

        internal async void OffAttack()
        {
            // Sử dụng PlayerSessionID để tìm player trong World.allConnectedChars
            Players players = null;

            if (HasPlayerSession)
            {
                players = World.FindPlayerBySession(_playerSessionId.Value);
            }
            else
            {
                // Fallback: thử tìm bằng ConnectionID thông qua mapping
                players = World.FindPlayerByConnectionId(_sessionId);
            }

            if (players == null)
            {
                LogHelper.WriteLine(LogLevel.Warning, $"Không tìm thấy player cho ConnectionID: {_sessionId}, PlayerSessionID: {_playerSessionId}");
                return;
            }

            try
            {
                // Thiết lập trạng thái offline attack
                ThoatGame = true;
                TreoMay = true;

                // Thiết lập auto settings cho player (giữ nguyên trạng thái hiện tại, không load lại)
                players.Auto_Train_Track = DateTime.Now.AddMilliseconds(-12000.0);
                players.Auto_TreoMay_TheoDoi_ThoiGian = 0;
                players.TuDongTiepTe = 1;
                players.ChucNang_Auto_ThucHien = 1;
                players.Offline_TreoMay_Mode_ON_OFF = 1;
                players.Offline_TreoMay_ToaDo_X = (int)players.PosX;
                players.Offline_TreoMay_ToaDo_Y = (int)players.PosY;
                players.Offline_TreoMay_BanDo = players.MapID;

                // Tìm skill cao nhất để sử dụng cho offline attack
                int skillId = await GetHighestSkillIdOptimized(players);
                if (players.VoCong_DanhLanCuoi_Khi_OffLine == 0)
                {
                    players.OfflineTreoMaySkill_ID = skillId;
                }
                else
                {
                    players.OfflineTreoMaySkill_ID = players.VoCong_DanhLanCuoi_Khi_OffLine;
                }

                // Xử lý logic job đặc biệt (Thích khách)
                if (players.Player_Job == 4)
                {
                    skillId = 400001;
                    players.CurrentlyActiveSkill_ID = 400001;
                    players.OfflineTreoMaySkill_ID = 0;
                }

                // Tạo OfflineActorNetState để thay thế connection hiện tại
                var offlineNetState = HeroYulgang.Core.Networking.Utils.NetworkFactory.CreateOfflineActorNetState(
                    _playerSessionId ?? _sessionId, // Sử dụng PlayerSessionID nếu có, nếu không thì ConnectionID
                    _remoteEndPoint
                );

                // Chuyển player sang offline netstate
                
                offlineNetState.Player = players;
                offlineNetState.TreoMay = true;
                offlineNetState.ThoatGame = true;
                players.Client.TreoMay = true;

                // Cập nhật player client reference sang offline netstate
                players.Client = offlineNetState;

                // Cập nhật counter
                if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 1)
                {
                    World.TreoMay_Offline++;
                }
                else if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 0)
                {
                    World.OffLine_SoLuong++;
                }

                // Ngắt kết nối TCP hiện tại mà không làm player logout khỏi World
                CloseConnectionOnly();

                LogHelper.WriteLine(LogLevel.Info, $"Player {players.CharacterName} đã chuyển sang chế độ OffAttack - ConnectionID: {_sessionId}, PlayerSessionID: {_playerSessionId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi chuyển player sang OffAttack - ConnectionID: {_sessionId}, PlayerSessionID: {_playerSessionId}: {ex.Message}");
                LogHelper.WriteLine(LogLevel.Error, $"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Tìm skill cao nhất một cách tối ưu (async version)
        /// </summary>
        private static async Task<int> GetHighestSkillIdOptimized(Players player)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var skill = player.FindHighestLevelVoCong();
                    return skill?.FLD_PID ?? 0;
                });
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Chỉ đóng kết nối TCP mà không làm player logout
        /// </summary>
        private void CloseConnectionOnly()
        {
            try
            {
                _running = false;

                // Chỉ đóng kết nối TCP, không gọi Player.Logout()
                var config = ConfigManager.Instance.NetworkingSettings;
                if (config.UseAkkaNetworking)
                {
                    try
                    {
                        var tcpManager = NetworkingSystem.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                        tcpManager.Tell(new CloseConnection(_connection));
                        LogHelper.WriteLine(LogLevel.Debug, $"Đã đóng kết nối TCP cho ConnectionID: {_sessionId}");
                    }
                    catch (Exception akkaEx)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng kết nối Akka cho ConnectionID {_sessionId}: {akkaEx.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng kết nối TCP cho ConnectionID {_sessionId}: {ex.Message}");
            }
        }

        #region Multiple Batch Packet System

        /// <summary>
        /// Tìm batch queue phù hợp cho opcode
        /// </summary>
        /// <param name="opcode">Opcode cần tìm</param>
        /// <returns>Compression opcode của batch queue hoặc null nếu không tìm thấy</returns>
        private int? FindBatchQueueForOpcode(int opcode)
        {
            foreach (var config in _batchConfigs)
            {
                if (config.Value.Opcodes.Contains(opcode))
                {
                    return config.Key;
                }
            }
            return null;
        }

        /// <summary>
        /// Thêm packet vào batch queue cụ thể
        /// </summary>
        /// <param name="packet">Packet cần batch</param>
        /// <param name="opcode">Opcode của packet</param>
        /// <param name="sessionID">Session ID</param>
        /// <param name="compressionOpcode">Compression opcode của batch queue</param>
        private void AddToBatch(SendingClass packet, int opcode, int sessionID, int compressionOpcode)
        {
            lock (_batchLock)
            {
                if (!_batchQueues.TryGetValue(compressionOpcode, out var batchQueue))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Batch queue not found for compression opcode {compressionOpcode}");
                    return;
                }

                // Tạo bản sao của packet để tránh dispose issues
                var packetCopy = ClonePacket(packet);
                batchQueue.Packets.Add((packetCopy, opcode, sessionID));

                // Nếu timer chưa active, khởi động timer
                if (!batchQueue.TimerActive)
                {
                    batchQueue.TimerActive = true;
                    batchQueue.Timer = new Timer(
                        state => ProcessBatch(compressionOpcode),
                        null,
                        batchQueue.Config.TimeoutMs,
                        Timeout.Infinite
                    );
                }
            }
        }

        /// <summary>
        /// Tạo bản sao của SendingClass packet
        /// </summary>
        /// <param name="original">Packet gốc</param>
        /// <returns>Bản sao của packet</returns>
        private SendingClass ClonePacket(SendingClass original)
        {
            var clone = new SendingClass();
            var originalData = original.ToArray3(); // Lấy raw data
            clone.Write(originalData);
            return clone;
        }

        /// <summary>
        /// Xử lý batch packets khi timer timeout cho một compression opcode cụ thể
        /// </summary>
        /// <param name="compressionOpcode">Compression opcode của batch queue</param>
        private void ProcessBatch(int compressionOpcode)
        {
            List<(SendingClass packet, int opcode, int sessionID)> packetsToSend;
            BatchQueue batchQueue;

            lock (_batchLock)
            {
                if (!_batchQueues.TryGetValue(compressionOpcode, out batchQueue))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Batch queue not found for compression opcode {compressionOpcode}");
                    return;
                }

                if (batchQueue.Packets.Count == 0)
                {
                    batchQueue.TimerActive = false;
                    return;
                }

                // Tạo bản sao danh sách để xử lý
                packetsToSend = [.. batchQueue.Packets];
                batchQueue.Packets.Clear();
                batchQueue.TimerActive = false;
            }

            try
            {
                if (packetsToSend.Count == 1)
                {
                    // Chỉ có 1 packet, gửi bình thường
                    var singlePacket = packetsToSend[0];
                    var array = singlePacket.packet.ToArray2(singlePacket.opcode, singlePacket.sessionID);
                    SendMultiplePackageEncryptionByPass(array, array.Length, 1);
                }
                else
                {
                    // Có nhiều packet, gửi bằng compression với opcode tương ứng
                    var packetTuples = packetsToSend.Select(p => (p.opcode, p.packet, p.sessionID)).ToArray();
                    SendCompressedPacket(packetTuples, compressionOpcode);
                }

                // Dispose các packet copies
                foreach (var packet in packetsToSend)
                {
                    packet.packet?.Dispose();
                }

                if (World.Debug > 0)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Processed batch with {packetsToSend.Count} packets using compression opcode {compressionOpcode}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing batch packets for session {_sessionId}, compression opcode {compressionOpcode}: {ex.Message}");

                // Dispose các packet copies trong trường hợp lỗi
                foreach (var packet in packetsToSend)
                {
                    packet.packet?.Dispose();
                }
            }
        }

        /// <summary>
        /// Flush tất cả batched packets ngay lập tức cho tất cả queues
        /// </summary>
        public void FlushBatchedPackets()
        {
            lock (_batchLock)
            {
                foreach (var queue in _batchQueues.Values)
                {
                    if (queue.TimerActive)
                    {
                        queue.Timer?.Dispose();
                        queue.TimerActive = false;
                    }
                }

                // Process tất cả queues có packets
                foreach (var kvp in _batchQueues)
                {
                    if (kvp.Value.Packets.Count > 0)
                    {
                        ProcessBatch(kvp.Key);
                    }
                }
            }
        }

        /// <summary>
        /// Flush batched packets cho một compression opcode cụ thể
        /// </summary>
        /// <param name="compressionOpcode">Compression opcode của batch queue</param>
        public void FlushBatchedPackets(int compressionOpcode)
        {
            lock (_batchLock)
            {
                if (_batchQueues.TryGetValue(compressionOpcode, out var queue))
                {
                    if (queue.TimerActive)
                    {
                        queue.Timer?.Dispose();
                        queue.TimerActive = false;
                    }

                    if (queue.Packets.Count > 0)
                    {
                        ProcessBatch(compressionOpcode);
                    }
                }
            }
        }

        /// <summary>
        /// Thêm opcode vào batch queue cụ thể
        /// </summary>
        /// <param name="opcode">Opcode cần thêm</param>
        /// <param name="compressionOpcode">Compression opcode của batch queue</param>
        public void AddBatchableOpcode(int opcode, int compressionOpcode)
        {
            lock (_batchLock)
            {
                if (_batchConfigs.TryGetValue(compressionOpcode, out var config))
                {
                    config.Opcodes.Add(opcode);
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Batch config not found for compression opcode {compressionOpcode}");
                }
            }
        }

        /// <summary>
        /// Xóa opcode khỏi tất cả batch queues
        /// </summary>
        /// <param name="opcode">Opcode cần xóa</param>
        public void RemoveBatchableOpcode(int opcode)
        {
            lock (_batchLock)
            {
                foreach (var config in _batchConfigs.Values)
                {
                    config.Opcodes.Remove(opcode);
                }
            }
        }

        /// <summary>
        /// Xóa opcode khỏi batch queue cụ thể
        /// </summary>
        /// <param name="opcode">Opcode cần xóa</param>
        /// <param name="compressionOpcode">Compression opcode của batch queue</param>
        public void RemoveBatchableOpcode(int opcode, int compressionOpcode)
        {
            lock (_batchLock)
            {
                if (_batchConfigs.TryGetValue(compressionOpcode, out var config))
                {
                    config.Opcodes.Remove(opcode);
                }
            }
        }

        /// <summary>
        /// Kiểm tra xem opcode có thể batch không
        /// </summary>
        /// <param name="opcode">Opcode cần kiểm tra</param>
        /// <returns>True nếu có thể batch</returns>
        public bool IsBatchableOpcode(int opcode)
        {
            return FindBatchQueueForOpcode(opcode).HasValue;
        }

        /// <summary>
        /// Lấy số lượng packets đang chờ batch cho tất cả queues
        /// </summary>
        /// <returns>Tổng số lượng packets trong tất cả batch queues</returns>
        public int GetBatchedPacketCount()
        {
            lock (_batchLock)
            {
                return _batchQueues.Values.Sum(q => q.Packets.Count);
            }
        }

        /// <summary>
        /// Lấy số lượng packets đang chờ batch cho một compression opcode cụ thể
        /// </summary>
        /// <param name="compressionOpcode">Compression opcode của batch queue</param>
        /// <returns>Số lượng packets trong batch queue</returns>
        public int GetBatchedPacketCount(int compressionOpcode)
        {
            lock (_batchLock)
            {
                if (_batchQueues.TryGetValue(compressionOpcode, out var queue))
                {
                    return queue.Packets.Count;
                }
                return 0;
            }
        }

        /// <summary>
        /// Lấy thông tin về tất cả batch queues
        /// </summary>
        /// <returns>Dictionary với compression opcode và số lượng packets</returns>
        public Dictionary<int, int> GetBatchQueueStatus()
        {
            lock (_batchLock)
            {
                return _batchQueues.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.Packets.Count
                );
            }
        }

        #endregion

        #region New Packet Compression System

        /// <summary>
        /// Check if opcode is a compressed packet opcode
        /// These opcodes indicate the packet contains compressed data
        /// Based on: if ( word_58EBE54 >= 576 && word_58EBE54 <= 638 ) || word_58EBE54 == 192
        /// </summary>
        /// <param name="opcode">Opcode to check</param>
        /// <returns>True if this is a compressed packet opcode</returns>
        private bool IsCompressedPacketOpcode(int opcode)
        {
            return (opcode >= 576 && opcode <= 638) || opcode == 192;
        }

        /// <summary>
        /// New LZ77-like compression algorithm based on reverse engineering
        /// </summary>
        /// <param name="data">Data to compress</param>
        /// <returns>Compressed data</returns>
        private byte[] CompressLZ77(byte[] data)
        {
            if (data == null || data.Length == 0)
                return new byte[0];

            var output = new List<byte>();
            int inputPos = 0;

            while (inputPos < data.Length)
            {
                // Look for back references
                int bestLength = 0;
                int bestOffset = 0;

                // Search for matches in previous data (up to 8191 bytes back)
                int searchStart = Math.Max(0, inputPos - 8191);

                for (int offset = 1; offset <= Math.Min(inputPos - searchStart, 8191); offset++)
                {
                    if (inputPos - offset < 0) continue;

                    int matchLength = 0;
                    int maxLength = Math.Min(data.Length - inputPos, 264); // Max length with extended encoding

                    while (matchLength < maxLength &&
                           inputPos + matchLength < data.Length &&
                           data[inputPos + matchLength] == data[inputPos - offset + matchLength])
                    {
                        matchLength++;
                    }

                    if (matchLength > bestLength && matchLength >= 3) // Minimum match length
                    {
                        bestLength = matchLength;
                        bestOffset = offset;
                    }
                }

                if (bestLength >= 3)
                {
                    // Encode back reference
                    int length = bestLength - 2; // Subtract 2 as per algorithm
                    int offset = bestOffset - 1; // Subtract 1 as per algorithm

                    if (length >= 7)
                    {
                        // Extended length encoding
                        byte controlByte = (byte)((7 << 5) | ((offset >> 8) & 0x1F));
                        output.Add(controlByte);
                        output.Add((byte)(length - 7));
                        output.Add((byte)(offset & 0xFF));
                    }
                    else
                    {
                        // Normal encoding
                        byte controlByte = (byte)((length << 5) | ((offset >> 8) & 0x1F));
                        output.Add(controlByte);
                        output.Add((byte)(offset & 0xFF));
                    }

                    inputPos += bestLength;
                }
                else
                {
                    // Find literal run
                    int literalStart = inputPos;
                    int literalLength = 0;

                    // Look ahead to find optimal literal length
                    while (inputPos < data.Length && literalLength < 32)
                    {
                        // Check if we should start a back reference instead
                        if (literalLength >= 1) // Allow at least 1 literal
                        {
                            bool foundMatch = false;
                            for (int offset = 1; offset <= Math.Min(inputPos - searchStart, 8191); offset++)
                            {
                                if (inputPos - offset < 0) continue;
                                int matchLength = 0;
                                while (matchLength < 10 &&
                                       inputPos + matchLength < data.Length &&
                                       data[inputPos + matchLength] == data[inputPos - offset + matchLength])
                                {
                                    matchLength++;
                                }
                                if (matchLength >= 3)
                                {
                                    foundMatch = true;
                                    break;
                                }
                            }
                            if (foundMatch) break;
                        }

                        literalLength++;
                        inputPos++;
                    }

                    // Encode literals
                    byte controlByte = (byte)(literalLength - 1); // 0-31 range
                    output.Add(controlByte);

                    for (int i = 0; i < literalLength; i++)
                    {
                        output.Add(data[literalStart + i]);
                    }
                }
            }

            return output.ToArray();
        }

        /// <summary>
        /// Create compressed packet following the exact structure from existing SendMultiplePackageEncryption
        /// </summary>
        /// <param name="packets">List of individual packets to compress</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <returns>Complete compressed packet with proper headers</returns>
        private byte[] CreateCompressedPacket(List<byte[]> packets, int compressionOpcode = 587)
        {
            if (packets == null || packets.Count == 0)
                return new byte[0];

            // Concatenate all packets
            var combinedData = new List<byte>();
            foreach (var packet in packets)
            {
                combinedData.AddRange(packet);
            }

            var combinedArray = combinedData.ToArray();

            // Compress the combined data
            var compressedData = CompressLZ77(combinedArray);

            var result = new byte[compressedData.Length + 8];

            // Compression opcode (little endian)
            Buffer.BlockCopy(BitConverter.GetBytes((ushort)compressionOpcode), 0, result, 0, 2);

            // Compressed length + 4 (little endian)
            Buffer.BlockCopy(BitConverter.GetBytes((ushort)(compressedData.Length + 4)), 0, result, 2, 2);

            // Compressed length (little endian)
            Buffer.BlockCopy(BitConverter.GetBytes((ushort)compressedData.Length), 0, result, 4, 2);

            // Original length (little endian)
            Buffer.BlockCopy(BitConverter.GetBytes((ushort)combinedArray.Length), 0, result, 6, 2);

            // Compressed data
            Buffer.BlockCopy(compressedData, 0, result, 8, compressedData.Length);

            return result;
        }

        /// <summary>
        /// New packet compression function that can handle single or multiple packets
        /// Replaces the old SendDuopak3 function
        /// </summary>
        /// <param name="packets">Array of packets to compress (can have any opcodes)</param>
        /// <param name="compressionOpcode">Opcode for the compressed packet (587, 588, etc.)</param>
        /// <returns>Compressed packet data with specified compression opcode</returns>
        public byte[] CompressPackets(byte[][] packets, int compressionOpcode = 587)
        {
            try
            {
                if (packets == null || packets.Length == 0)
                    return new byte[0];

                // Validate compression opcode
                if (!IsCompressedPacketOpcode(compressionOpcode))
                {
                    LogHelper.WriteLine(LogLevel.Warning,
                        $"Invalid compression opcode {compressionOpcode}. Using default 587.");
                    compressionOpcode = 587;
                }

                // Convert to List for easier handling
                var packetList = new List<byte[]>(packets);

                // Create compressed packet with specified compression opcode
                return CreateCompressedPacket(packetList, compressionOpcode);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Compression error in session {_sessionId}: {ex.Message}");
                // Return first packet as fallback
                return packets.Length > 0 ? packets[0] : new byte[0];
            }
        }

        public void SendCompressedPacket((int, SendingClass, int)[] packetList, int compressionOpcode = 587)
        {
            try
            {
                if (packetList == null || packetList.Length == 0)
                    return;

                var packetBytes = new List<byte[]>();
                foreach (var packet in packetList)
                {
                    var bytes = packet.Item2.ToArray2(packet.Item1, packet.Item3);
                    packetBytes.Add(bytes);
                }

                var compressedData = CompressPackets([.. packetBytes], compressionOpcode);

                if (compressedData.Length > 0)
                {
                    SendMultiplePackage_PackageTransmit(compressedData, compressedData.Length, packetList.Length);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendCompressedPacket error in session {_sessionId}: {ex.Message}");
            }
        }

    
        #endregion
    }
}
