-- =====================================================
-- ATTENDANCE SYSTEM DATABASE TABLES
-- =====================================================

-- 1. PUBLIC DATABASE - Attendance Templates và Rewards
-- =====================================================

-- Bảng attendance templates (lưu trong Public DB)
CREATE TABLE IF NOT EXISTS tbl_attendance_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    month INTEGER NOT NULL,
    year INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    UNIQUE(month, year)
);

-- Bảng attendance rewards (lưu trong Public DB)
CREATE TABLE IF NOT EXISTS tbl_attendance_rewards (
    id SERIAL PRIMARY KEY,
    attendance_id INTEGER NOT NULL,
    day_number INTEGER NOT NULL CHECK (day_number >= 1 AND day_number <= 28),
    item_id INTEGER NOT NULL,
    item_amount INTEGER NOT NULL DEFAULT 1,
    FOREIGN KEY (attendance_id) REFERENCES tbl_attendance_templates(id) ON DELETE CASCADE,
    UNIQUE(attendance_id, day_number)
);

-- Index cho performance
CREATE INDEX IF NOT EXISTS idx_attendance_templates_active ON tbl_attendance_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_attendance_templates_date ON tbl_attendance_templates(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_attendance_rewards_attendance_id ON tbl_attendance_rewards(attendance_id);
CREATE INDEX IF NOT EXISTS idx_attendance_rewards_day ON tbl_attendance_rewards(attendance_id, day_number);

-- =====================================================
-- 2. GAME DATABASE - Player Attendance Data
-- =====================================================

-- Bảng player attendance progress (lưu trong Game DB)
CREATE TABLE IF NOT EXISTS tbl_player_attendance (
    id SERIAL PRIMARY KEY,
    player_name VARCHAR(255) NOT NULL,
    attendance_id INTEGER NOT NULL,
    received_days VARCHAR(255) DEFAULT '', -- String format: "1,2,3,5,7" 
    last_received_date TIMESTAMP NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(player_name, attendance_id)
);

-- Index cho performance
CREATE INDEX IF NOT EXISTS idx_player_attendance_player ON tbl_player_attendance(player_name);
CREATE INDEX IF NOT EXISTS idx_player_attendance_template ON tbl_player_attendance(attendance_id);
CREATE INDEX IF NOT EXISTS idx_player_attendance_player_template ON tbl_player_attendance(player_name, attendance_id);

-- =====================================================
-- SAMPLE DATA - Attendance Template cho tháng hiện tại
-- =====================================================

-- Tạo attendance template mẫu cho tháng hiện tại
INSERT INTO tbl_attendance_templates (name, month, year, is_active, start_date, end_date) 
VALUES (
    'Điểm danh tháng ' || EXTRACT(MONTH FROM CURRENT_DATE) || '/' || EXTRACT(YEAR FROM CURRENT_DATE),
    EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER,
    EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER,
    TRUE,
    DATE_TRUNC('month', CURRENT_DATE),
    DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'
) ON CONFLICT (month, year) DO NOTHING;

-- Tạo 28 phần thưởng mẫu cho attendance template vừa tạo
DO $$
DECLARE
    template_id INTEGER;
    day_num INTEGER;
BEGIN
    -- Lấy ID của template vừa tạo
    SELECT id INTO template_id 
    FROM tbl_attendance_templates 
    WHERE month = EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER 
    AND year = EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER;
    
    -- Tạo 28 phần thưởng mẫu
    FOR day_num IN 1..28 LOOP
        INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)
        VALUES (
            template_id,
            day_num,
            CASE 
                WHEN day_num % 7 = 0 THEN 1008003456 -- Ngày thứ 7: Item đặc biệt
                WHEN day_num % 5 = 0 THEN 1008003455 -- Ngày thứ 5: Item tốt
                ELSE 1008003454 -- Ngày thường: Item cơ bản
            END,
            CASE 
                WHEN day_num % 7 = 0 THEN 5 -- Ngày thứ 7: 5 items
                WHEN day_num % 5 = 0 THEN 3 -- Ngày thứ 5: 3 items  
                ELSE 1 -- Ngày thường: 1 item
            END
        ) ON CONFLICT (attendance_id, day_number) DO NOTHING;
    END LOOP;
END $$;

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function để lấy attendance đang active
CREATE OR REPLACE FUNCTION get_active_attendance()
RETURNS TABLE(
    id INTEGER,
    name VARCHAR(255),
    month INTEGER,
    year INTEGER,
    start_date TIMESTAMP,
    end_date TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT t.id, t.name, t.month, t.year, t.start_date, t.end_date
    FROM tbl_attendance_templates t
    WHERE t.is_active = TRUE
    AND CURRENT_DATE BETWEEN t.start_date::DATE AND t.end_date::DATE
    ORDER BY t.created_date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function để check player đã nhận thưởng ngày nào
CREATE OR REPLACE FUNCTION check_player_received_day(
    p_player_name VARCHAR(255),
    p_attendance_id INTEGER,
    p_day_number INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
    received_days_str VARCHAR(255);
    day_exists BOOLEAN := FALSE;
BEGIN
    -- Lấy chuỗi received_days
    SELECT received_days INTO received_days_str
    FROM tbl_player_attendance
    WHERE player_name = p_player_name AND attendance_id = p_attendance_id;
    
    -- Nếu không tìm thấy record thì chưa nhận
    IF received_days_str IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check xem day_number có trong chuỗi không
    IF received_days_str = '' THEN
        RETURN FALSE;
    END IF;
    
    -- Check bằng cách tìm trong chuỗi
    IF (',' || received_days_str || ',') LIKE ('%,' || p_day_number || ',%') THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
