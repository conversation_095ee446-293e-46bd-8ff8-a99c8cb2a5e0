using System;
using System.Threading.Tasks;
using HeroYulgang.Services;
using HeroYulgang.Core.Networking.Core;
using HeroYulgang.Core.NetCore;

namespace HeroYulgang.Core
{
    /// <summary>
    /// Manages networking implementation switching between Akka.net and NetCoreServer
    /// Provides unified interface for both networking systems
    /// </summary>
    public class NetworkingManager
    {
        private static NetworkingManager? _instance;
        private bool _isInitialized = false;
        private bool _isStarted = false;
        private NetworkingType _currentNetworkingType;

        public static NetworkingManager Instance => _instance ??= new NetworkingManager();

        public bool IsInitialized => _isInitialized;
        public bool IsStarted => _isStarted;
        public NetworkingType CurrentNetworkingType => _currentNetworkingType;

        private NetworkingManager()
        {
            // Private constructor for singleton pattern
        }

        /// <summary>
        /// Initialize networking system based on configuration
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                if (_isInitialized)
                {
                    Logger.Instance.Warning("NetworkingManager already initialized");
                    return true;
                }

                var config = ConfigManager.Instance.NetworkingSettings;
                
                // Validate configuration
                if (config.UseNetCoreServer && config.UseAkkaNetworking)
                {
                    Logger.Instance.Warning("Both networking systems enabled, defaulting to Akka.net");
                    config.UseAkkaNetworking = true;
                    config.UseNetCoreServer = false;
                }
                else if (!config.UseNetCoreServer && !config.UseAkkaNetworking)
                {
                    Logger.Instance.Warning("No networking system enabled, defaulting to Akka.net");
                    config.UseAkkaNetworking = true;
                }

                // Initialize the selected networking system
                if (config.UseNetCoreServer)
                {
                    Logger.Instance.Info("Initializing NetCoreServer networking...");
                    _currentNetworkingType = NetworkingType.NetCoreServer;
                    await InitializeNetCoreServerAsync();
                }
                else
                {
                    Logger.Instance.Info("Initializing Akka.net networking...");
                    _currentNetworkingType = NetworkingType.AkkaNetworking;
                    await InitializeAkkaNetworkingAsync();
                }

                _isInitialized = true;
                Logger.Instance.Info($"NetworkingManager initialized with {_currentNetworkingType}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to initialize NetworkingManager: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Start the networking system
        /// </summary>
        public async Task<bool> StartAsync()
        {
            try
            {
                if (!_isInitialized)
                {
                    Logger.Instance.Error("NetworkingManager not initialized. Call InitializeAsync first.");
                    return false;
                }

                if (_isStarted)
                {
                    Logger.Instance.Warning("NetworkingManager already started");
                    return true;
                }

                bool success = false;
                if (_currentNetworkingType == NetworkingType.NetCoreServer)
                {
                    success = await StartNetCoreServerAsync();
                }
                else
                {
                    success = await StartAkkaNetworkingAsync();
                }

                if (success)
                {
                    _isStarted = true;
                    Logger.Instance.Info($"NetworkingManager started with {_currentNetworkingType}");
                }

                return success;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to start NetworkingManager: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Stop the networking system
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                if (!_isStarted)
                {
                    Logger.Instance.Warning("NetworkingManager not started");
                    return;
                }

                if (_currentNetworkingType == NetworkingType.NetCoreServer)
                {
                    await StopNetCoreServerAsync();
                }
                else
                {
                    await StopAkkaNetworkingAsync();
                }

                _isStarted = false;
                Logger.Instance.Info($"NetworkingManager stopped");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error stopping NetworkingManager: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset NetworkingManager state for restart scenarios
        /// </summary>
        public async Task ResetAsync()
        {
            try
            {
                Logger.Instance.Info("Resetting NetworkingManager for restart...");

                // Stop if running
                if (_isStarted)
                {
                    await StopAsync();
                }

                // Reset initialization state
                _isInitialized = false;
                _isStarted = false;

                // Wait for complete cleanup
                await Task.Delay(1000);

                Logger.Instance.Info("NetworkingManager reset completed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error resetting NetworkingManager: {ex.Message}");
            }
        }

        #region Private Methods

        private async Task InitializeNetCoreServerAsync()
        {
            var port = ConfigManager.Instance.ServerSettings.GameServerPort;
            var serverInstance = NetCoreServerWrapper.GetInstance(port);
            serverInstance.InitializeAsync(port);
            await Task.CompletedTask; // NetCoreServer initialization is synchronous
        }

        private async Task InitializeAkkaNetworkingAsync()
        {
            await NetworkingSystem.Instance.InitializeAsync();
        }

        private async Task<bool> StartNetCoreServerAsync()
        {
            var port = ConfigManager.Instance.ServerSettings.GameServerPort;
            var serverInstance = NetCoreServerWrapper.GetInstance(port);
            bool success = serverInstance.StartServer();
            await Task.CompletedTask; // NetCoreServer start is synchronous
            return success;
        }

        private async Task<bool> StartAkkaNetworkingAsync()
        {
            try
            {
                NetworkingSystem.Instance.TcpManagerActor.Tell(new StartServer(), Akka.Actor.ActorRefs.NoSender);
                await Task.CompletedTask; // Akka start is fire-and-forget
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to start Akka networking: {ex.Message}");
                return false;
            }
        }

        private async Task StopNetCoreServerAsync()
        {
            var port = ConfigManager.Instance.ServerSettings.GameServerPort;
            var serverInstance = NetCoreServerWrapper.GetInstance(port);
            serverInstance.StopServer();
            await Task.CompletedTask; // NetCoreServer stop is synchronous
        }

        private async Task StopAkkaNetworkingAsync()
        {
            NetworkingSystem.Instance.Shutdown();
            await Task.CompletedTask; // Akka shutdown is synchronous
        }

        #endregion
    }

    /// <summary>
    /// Enum for networking implementation types
    /// </summary>
    public enum NetworkingType
    {
        AkkaNetworking,
        NetCoreServer
    }
}
