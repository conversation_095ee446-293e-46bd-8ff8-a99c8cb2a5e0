﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class backup_tbl_parties {

		[JsonProperty]
		public int? party_id { get; set; }

		[JsonProperty, Column(StringLength = 36)]
		public string party_uuid { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string original_leader_name { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string current_leader_name { get; set; }

		[JsonProperty]
		public int? current_server_id { get; set; }

		[JsonProperty]
		public int? max_members { get; set; }

		[JsonProperty]
		public int? current_active_members { get; set; }

		[JsonProperty]
		public int? loot_type { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

		[JsonProperty]
		public DateTime? last_active_at { get; set; }

		[JsonProperty]
		public bool? is_active { get; set; }

	}

}
