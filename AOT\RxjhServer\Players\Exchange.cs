

using System;
using RxjhServer.Database;

namespace RxjhServer
{
    public partial class Players
    {
        public int GoldCoin
        {
            get
            {
                var item = FindItemByItemID(World.CoinCurrency);
                return item?.GetVatPhamSoLuong ?? 0;
            }
        }

        public int SilverCoin
        {
            get
            {
                var item = FindItemByItemID(1000001503);
                return item?.GetVatPhamSoLuong ?? 0;
            }
        }

        public int BronzeCoin
        {
            get
            {
                var item = FindItemByItemID(1000001504);
                return item?.GetVatPhamSoLuong ?? 0;
            }
        }
        public void Exchange_Coin(byte[] data, int length)
        {
            PacketModification(data, length);
            int TienVang = BitConverter.ToInt32(data, 10);  // Số lượng vàng còn lại sau khi đổi
            int TienBac = BitConverter.ToInt32(data, 14);   // Số lượng bạc còn lại sau khi đổi
            int TienDong = BitConverter.ToInt32(data, 18);  // Số lượng đồng còn lại sau khi đổi

            if (!base.PlayerTuVong && base.NhanVat_HP > 0L && (CuaHangCaNhan == null || !CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa) && (GiaoDich == null || GiaoDich.NguoiGiaoDich == null))
            {
                // Lấy item hiện tại trong túi
                Item VatPhamVang = FindItemByItemID(1000001502);  // Vàng
                Item VatPhamBac = FindItemByItemID(1000001503);   // Bạc
                Item VatPhamDong = FindItemByItemID(1000001504);  // Đồng

                // Kiểm tra và xử lý đổi vàng thành bạc
                if (VatPhamVang != null && TienVang < VatPhamVang.GetVatPhamSoLuong)
                {
                    int soVangCanDoi = VatPhamVang.GetVatPhamSoLuong - TienVang;
                    int soBacNhanDuoc = soVangCanDoi * 50; // 1 vàng = 50 bạc

                    // Cập nhật số lượng vàng còn lại
                    if (TienVang > 0)
                    {
                        VatPhamVang.VatPhamSoLuong = BitConverter.GetBytes(TienVang);
                    }
                    else
                    {
                        // Xóa item vàng nếu không còn
                        Item_In_Bag[VatPhamVang.VatPhamViTri].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                    }

                    // Thêm bạc vào túi
                    if (VatPhamBac != null)
                    {
                        // Nếu đã có bạc, cộng thêm
                        int soBacHienTai = VatPhamBac.GetVatPhamSoLuong;
                        VatPhamBac.VatPhamSoLuong = BitConverter.GetBytes(soBacHienTai + soBacNhanDuoc);
                    }
                    else
                    {
                        // Nếu chưa có bạc, tạo mới
                        var emptySlot = GetParcelVacancy(this);
                        if (emptySlot != -1)
                        {
                            AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()),
                                    BitConverter.GetBytes(1000001503),
                                    emptySlot,
                                    BitConverter.GetBytes(soBacNhanDuoc),
                                    new byte[World.VatPham_ThuocTinh_KichThuoc]);
                        }
                        else
                        {
                            HeThongNhacNho("Túi đồ không đủ chỗ trống!", 20, "Hệ Thống");
                            return;
                        }
                    }

                    HeThongNhacNho($"Đã đổi {soVangCanDoi} vàng thành {soBacNhanDuoc} bạc!", 10, "Đổi Tiền");
                }

                // Kiểm tra và xử lý đổi bạc thành đồng
                if (VatPhamBac != null && TienBac < VatPhamBac.GetVatPhamSoLuong)
                {
                    int soBacCanDoi = VatPhamBac.GetVatPhamSoLuong - TienBac;
                    int soDongNhanDuoc = soBacCanDoi * 50; // 1 bạc = 50 đồng

                    // Cập nhật số lượng bạc còn lại
                    if (TienBac > 0)
                    {
                        VatPhamBac.VatPhamSoLuong = BitConverter.GetBytes(TienBac);
                    }
                    else
                    {
                        // Xóa item bạc nếu không còn
                        Item_In_Bag[VatPhamBac.VatPhamViTri].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                    }

                    // Thêm đồng vào túi
                    if (VatPhamDong != null)
                    {
                        // Nếu đã có đồng, cộng thêm
                        int soDongHienTai = VatPhamDong.GetVatPhamSoLuong;
                        VatPhamDong.VatPhamSoLuong = BitConverter.GetBytes(soDongHienTai + soDongNhanDuoc);
                    }
                    else
                    {
                        // Nếu chưa có đồng, tạo mới
                        var emptySlot = GetParcelVacancy(this);
                        if (emptySlot != -1)
                        {
                            AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()),
                                    BitConverter.GetBytes(1000001504),
                                    emptySlot,
                                    BitConverter.GetBytes(soDongNhanDuoc),
                                    new byte[World.VatPham_ThuocTinh_KichThuoc]);
                        }
                        else
                        {
                            HeThongNhacNho("Túi đồ không đủ chỗ trống!", 20, "Hệ Thống");
                            return;
                        }
                    }

                    HeThongNhacNho($"Đã đổi {soBacCanDoi} bạc thành {soDongNhanDuoc} đồng!", 10, "Đổi Tiền");
                }

                Init_Item_In_Bag();
            }
            else
            {
                HeThongNhacNho("Thao Tác không hộp lệ!", 20, "Hệ Thống");
            }
        }
    }
}