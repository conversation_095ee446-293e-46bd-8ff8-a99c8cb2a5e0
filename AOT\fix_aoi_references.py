#!/usr/bin/env python3
import os
import re

# File patterns to fix
files_to_fix = [
    "RxjhServer/NpcManager/NPCBehaviorManager.cs",
    "RxjhServer/NpcManager/NpcCommand.cs", 
    "RxjhServer/Players/Players.FindPlayers.cs",
    "RxjhServer/Players/Players.Lobby.cs",
    "RxjhServer/Players/Players.Map.cs",
    "RxjhServer/Players/Players.Moving.cs",
    "RxjhServer/Players/Players.Npc.cs",
    "RxjhServer/World.cs",
    "RxjhServer/Class/GroundItem.cs",
    "RxjhServer/Neo/Command.cs",
    "RxjhServer/Neo/NpcClass.cs"
]

# Replacements to apply
replacements = [
    # Replace old AOI class references
    (r'AOIManager\.Instance\.', 'AOISystem.Instance.'),
    (r'AOIConfiguration\.Instance\.', '// AOIConfiguration.Instance. '),
    (r'AOIUpdateService\.Instance\.', '// AOIUpdateService.Instance. '),
    
    # Replace removed methods
    (r'\.ForceUpdateAOI\(\)', '.UpdateAOI()'),
    (r'\.UpdateAOIImmediate\(\)', '.UpdateAOI()'),
    (r'\.UpdateVisibilityWithOverlap\(\)', '.UpdateAOI()'),
    (r'\.SynchronizeMapTransitionAOI\(\)', '.UpdateAOI()'),
    
    # Replace AOIConfiguration checks
    (r'AOIConfiguration\.Instance\.ShouldUseAOI\([^)]+\)', 'true'),
    (r'AOIConfiguration\.Instance\.EnableDebugLogging', 'false'),
    
    # Fix method calls
    (r'\.GetGridCoordinates\(', '.GetGridCoords('),
    (r'\.GetAOIGrids\(', '.GetNearbyGrids('),
]

def fix_file(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {filepath}")
        else:
            print(f"No changes needed: {filepath}")
            
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")

def main():
    base_dir = "E:/YulgangDev/YulgangHero/yulgang-hero/AOT/"
    
    for file_pattern in files_to_fix:
        filepath = os.path.join(base_dir, file_pattern).replace('/', os.sep)
        if os.path.exists(filepath):
            fix_file(filepath)
        else:
            print(f"File not found: {filepath}")

if __name__ == "__main__":
    main()