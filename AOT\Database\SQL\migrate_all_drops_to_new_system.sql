-- =====================================================
-- MIGRATION SCRIPT: tbl_xwwl_drop -> tbl_new_drops
-- Migrate toàn bộ drop data từ hệ thống cũ sang hệ thống mới
-- Tất cả drops sẽ được convert thành level_range format
--
-- DROP LOGIC CŨ:
-- - Random từ 1-8000
-- - Drop nếu random <= FLD_PP
-- - Drop rate thực = FLD_PP / 8000
--
-- VÍ DỤ:
-- - FLD_PP = 700  → Rate = 700/8000 = 0.0875 (8.75%)
-- - FLD_PP = 2000 → Rate = 2000/8000 = 0.25 (25%)
-- - FLD_PP = 400  → Rate = 400/8000 = 0.05 (5%)
-- =====================================================

-- Bước 1: Backup dữ liệu hiện tại
DO $$
BEGIN
    -- Tạo backup table với timestamp
    EXECUTE 'CREATE TABLE IF NOT EXISTS tbl_xwwl_drop_backup_' || 
            to_char(now(), 'YYYYMMDD_HH24MISS') || 
            ' AS SELECT * FROM tbl_xwwl_drop';
    
    RAISE NOTICE 'Backup table created successfully';
END $$;

-- Bước 2: Xóa dữ liệu cũ trong tbl_new_drops nếu có (optional)
-- Uncomment dòng dưới nếu muốn xóa dữ liệu cũ
-- DELETE FROM tbl_new_drops WHERE source_type = 'level_range';

-- Bước 3: Migration chính - Convert tất cả drops
INSERT INTO tbl_new_drops (
    source_type,
    source_value, 
    item_id,
    drop_rate,
    quantity_min,
    quantity_max,
    magic0,
    magic1, 
    magic2,
    magic3,
    magic4,
    expire_days,
    is_active,
    priority,
    created_at,
    updated_at
)
SELECT 
    'level_range' as source_type,
    
    -- Tạo source_value từ fld_level1 và fld_level2
    CASE 
        WHEN fld_level1 IS NULL OR fld_level1 < 1 THEN '1'
        ELSE fld_level1::text
    END || '-' || 
    CASE 
        WHEN fld_level2 IS NULL OR fld_level2 < fld_level1 THEN 
            CASE 
                WHEN fld_level1 IS NULL OR fld_level1 < 1 THEN '100'
                ELSE GREATEST(fld_level1, 100)::text
            END
        WHEN fld_level2 > 300 THEN '300'
        ELSE fld_level2::text
    END as source_value,
    
    fld_pid as item_id,
    
    -- Convert FLD_PP thành decimal rate
    -- Logic cũ: random(1-8000), drop nếu random >= FLD_PP
    -- Rate thực = FLD_PP / 8000
    CASE
        WHEN fld_pp IS NULL OR fld_pp <= 0 THEN 0.000001
        WHEN fld_pp >= 8000 THEN 1.000000
        ELSE ROUND(CAST(fld_pp AS DECIMAL(10,6)) / 8000.0, 6)
    END as drop_rate,
    
    1 as quantity_min,
    1 as quantity_max,
    
    COALESCE(fld_magic0, 0) as magic0,
    COALESCE(fld_magic1, 0) as magic1, 
    COALESCE(fld_magic2, 0) as magic2,
    COALESCE(fld_magic3, 0) as magic3,
    COALESCE(fld_magic4, 0) as magic4,
    
    COALESCE(fld_days, 0) as expire_days,
    
    true as is_active,
    
    -- Set priority dựa trên drop rate
    CASE 
        WHEN fld_pp >= 4000 THEN 120  -- High drop rate items (50%+)
        WHEN fld_pp >= 1600 THEN 110  -- Medium drop rate items (20%+) 
        WHEN fld_pp >= 400 THEN 100   -- Normal drop rate items (5%+)
        ELSE 90                       -- Low drop rate items (<5%)
    END as priority,
    
    NOW() as created_at,
    NOW() as updated_at

FROM tbl_xwwl_drop 

WHERE 
    -- Chỉ migrate những record hợp lệ
    fld_pid IS NOT NULL 
    AND fld_pid > 0
    AND fld_pp IS NOT NULL 
    AND fld_pp > 0
    AND (fld_level1 IS NULL OR fld_level1 >= 1)
    AND (fld_level2 IS NULL OR fld_level2 <= 300)
    AND (fld_level1 IS NULL OR fld_level2 IS NULL OR fld_level2 >= fld_level1)

-- Tránh duplicate entries
ON CONFLICT (source_type, source_value, item_id) DO UPDATE SET
    drop_rate = EXCLUDED.drop_rate,
    magic0 = EXCLUDED.magic0,
    magic1 = EXCLUDED.magic1,
    magic2 = EXCLUDED.magic2,
    magic3 = EXCLUDED.magic3,
    magic4 = EXCLUDED.magic4,
    expire_days = EXCLUDED.expire_days,
    priority = EXCLUDED.priority,
    updated_at = NOW();

-- Bước 4: Migration summary và validation
DO $$
DECLARE
    old_count INTEGER;
    new_count INTEGER;
    invalid_count INTEGER;
BEGIN
    -- Đếm records trong bảng cũ
    SELECT COUNT(*) INTO old_count 
    FROM tbl_xwwl_drop 
    WHERE fld_pid IS NOT NULL AND fld_pid > 0 AND fld_pp IS NOT NULL AND fld_pp > 0;
    
    -- Đếm records đã migrate
    SELECT COUNT(*) INTO new_count 
    FROM tbl_new_drops 
    WHERE source_type = 'level_range';
    
    -- Đếm records không hợp lệ
    SELECT COUNT(*) INTO invalid_count
    FROM tbl_xwwl_drop 
    WHERE fld_pid IS NULL OR fld_pid <= 0 OR fld_pp IS NULL OR fld_pp <= 0;
    
    RAISE NOTICE '=== MIGRATION SUMMARY ===';
    RAISE NOTICE 'Old system records: %', old_count;
    RAISE NOTICE 'Migrated records: %', new_count;
    RAISE NOTICE 'Invalid records skipped: %', invalid_count;
    RAISE NOTICE 'Migration success rate: %%%', ROUND((new_count::DECIMAL / old_count::DECIMAL) * 100, 2);
END $$;

-- Bước 5: Hiển thị sample data để kiểm tra
SELECT 
    'SAMPLE MIGRATED DATA' as info,
    source_type,
    source_value,
    item_id,
    CONCAT(ROUND(drop_rate * 100, 4), '%') as drop_percentage,
    magic0,
    priority
FROM tbl_new_drops 
WHERE source_type = 'level_range'
ORDER BY priority DESC, drop_rate DESC
LIMIT 10;

-- Bước 6: Validation checks
SELECT 
    'VALIDATION CHECKS' as info,
    COUNT(*) as total_rules,
    COUNT(CASE WHEN drop_rate <= 0 OR drop_rate > 1 THEN 1 END) as invalid_rates,
    COUNT(CASE WHEN item_id <= 0 THEN 1 END) as invalid_items,
    MIN(drop_rate) as min_rate,
    MAX(drop_rate) as max_rate,
    AVG(drop_rate) as avg_rate
FROM tbl_new_drops 
WHERE source_type = 'level_range';

-- Bước 7: Drop rate distribution
SELECT 
    'DROP RATE DISTRIBUTION' as info,
    CASE 
        WHEN drop_rate >= 0.5 THEN '50%+'
        WHEN drop_rate >= 0.25 THEN '25-50%'
        WHEN drop_rate >= 0.1 THEN '10-25%'
        WHEN drop_rate >= 0.05 THEN '5-10%'
        WHEN drop_rate >= 0.01 THEN '1-5%'
        ELSE '<1%'
    END as rate_range,
    COUNT(*) as count,
    ROUND(AVG(drop_rate * 100), 2) as avg_percentage
FROM tbl_new_drops 
WHERE source_type = 'level_range'
GROUP BY 
    CASE 
        WHEN drop_rate >= 0.5 THEN '50%+'
        WHEN drop_rate >= 0.25 THEN '25-50%'
        WHEN drop_rate >= 0.1 THEN '10-25%'
        WHEN drop_rate >= 0.05 THEN '5-10%'
        WHEN drop_rate >= 0.01 THEN '1-5%'
        ELSE '<1%'
    END
ORDER BY 
    CASE 
        WHEN drop_rate >= 0.5 THEN 1
        WHEN drop_rate >= 0.25 THEN 2
        WHEN drop_rate >= 0.1 THEN 3
        WHEN drop_rate >= 0.05 THEN 4
        WHEN drop_rate >= 0.01 THEN 5
        ELSE 6
    END;

-- Bước 8: Level range coverage
SELECT 
    'LEVEL COVERAGE' as info,
    source_value as level_range,
    COUNT(*) as drop_rules,
    MIN(drop_rate) as min_rate,
    MAX(drop_rate) as max_rate,
    AVG(drop_rate) as avg_rate
FROM tbl_new_drops 
WHERE source_type = 'level_range'
GROUP BY source_value
ORDER BY 
    CAST(split_part(source_value, '-', 1) AS INTEGER),
    CAST(split_part(source_value, '-', 2) AS INTEGER);

-- Bước 9: Update configuration để enable new system
INSERT INTO tbl_drop_config (config_key, config_value, description, updated_at)
VALUES 
    ('migration_completed', 'true', 'Migration from old system completed', NOW()),
    ('migration_date', NOW()::text, 'Date when migration was completed', NOW()),
    ('old_system_backup', 'tbl_xwwl_drop_backup_' || to_char(now(), 'YYYYMMDD_HH24MISS'), 'Backup table name', NOW())
ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    updated_at = EXCLUDED.updated_at;

-- Thông báo hoàn thành
SELECT 'MIGRATION COMPLETED SUCCESSFULLY!' as status;

-- Hiển thị một số examples để verify conversion
SELECT
    'CONVERSION EXAMPLES' as info,
    'FLD_PP → Drop Rate' as conversion_type,
    '700 → 8.75%' as example_1,
    '2000 → 25%' as example_2,
    '400 → 5%' as example_3,
    '8000 → 100%' as example_4;

-- Hướng dẫn tiếp theo
SELECT 
    'NEXT STEPS' as info,
    'Run: enablenewdrop (console) or !dropenable (in-game) to activate new system' as instruction
UNION ALL
SELECT 
    'TESTING' as info,
    'Run: testdrop <npcId> <level> to test the new system' as instruction
UNION ALL
SELECT 
    'MONITORING' as info,
    'Run: dropstatus to check system status' as instruction;

-- =====================================================
-- ROLLBACK SCRIPT (nếu cần)
-- =====================================================
/*
-- Để rollback migration, chạy:

-- 1. Disable new system
UPDATE tbl_drop_config SET config_value = 'false' WHERE config_key = 'new_drop_system_enabled';

-- 2. Xóa migrated data
DELETE FROM tbl_new_drops WHERE source_type = 'level_range';

-- 3. Restore từ backup (nếu cần)
-- DROP TABLE tbl_xwwl_drop;
-- ALTER TABLE tbl_xwwl_drop_backup_YYYYMMDD_HHMMSS RENAME TO tbl_xwwl_drop;

*/
