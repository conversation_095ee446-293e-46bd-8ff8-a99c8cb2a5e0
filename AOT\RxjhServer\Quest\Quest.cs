

using System;
using System.Linq;

using RxjhServer.GroupQuest;
using static RxjhServer.GroupQuest.GroupQuestEvent;
using HeroYulgang.Helpers;
using RxjhServer.Quest;
using System.Threading.Tasks;
using RxjhServer.HelperTools;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Protos;

namespace RxjhServer;

public partial class Players
{
    public enum PlayerAction
    {
        AskNpc = 1,
        AcceptQuest = 2,
        RefuseQuest = 3,
        CancelQuest = 4,
        CompleteQuest = 5

    }

    public enum QuestResponse
    {
        MeetRequirement = 11,
        NotMeetRequirement = 12,
        NotMeetRequirementAccept = 22, // Không phù hợp điều kiện, hãy xác nhận lại điều kiện
        RefuseQuest = 31,

        CompleteQuestSuccess = 51,
        CompleteQuestUnsuccess = 52
    }
    public async Task MissionSystem(byte[] packetData, int packetSize)
    {
        try
        {
            int questId = BitConverter.ToInt16(packetData, 10);
            int action = BitConverter.ToInt16(packetData, 12);
            int stage = BitConverter.ToInt16(packetData, 14);
            int step = BitConverter.ToInt16(packetData, 16);
            HeThongNhacNho($"Nhiệm vụ {questId} - {action} - {stage}", 10, "Thiên cơ các");
            if (questId >= 12000)
            {
                HeThongNhacNho("Xử lý nhiệm vụ guild");
                await HandleGroupQuest(questId, action, stage);
                return;
            }


            var quest = QuestClass.GetQuest(questId);
            if (quest == null)
            {
                TaskPromptDataSending(questId, 12, stage);
                return;
            }
            HeThongNhacNho($"Xử lý quest: {quest.QuestName} (ID: {questId})", 10, "Thiên cơ các");
            HeThongNhacNho($"Action: {action} - stage: {stage} - step: {step}", 10, "Thiên cơ các");

            // step 1 option 0 => chọn nhiệm vụ
            // chọn nhiệm vụ sẽ kiểm tra player đã làm nhiệm vụ chưa, và đủ điều kiện nhận nhiệm vụ không
            // nhận nhiệm vụ => chuyển sang step 2
            // từ chối  => chuyển sang step 3 => nhảy thông báo chửi player

            // Có vẻ là loop qua stage thì tốt hơn, mỗi action đều sẽ loop qua tất cả các stage để xử lý
            switch (action)
            {
                // Chưa handle Step
                case (int)PlayerAction.AskNpc:
                    // TODO: Check Stage player gửi lên và Stage thực tế (không trùng nhau => cheat)
                    if (stage == 0) // AskNpc khi chưa nhận nhiệm vụ
                    {
                        var (canAccept, index) = QuestManager.CanAcceptQuest(this, questId);
                        if (canAccept)
                        {
                            TaskPromptDataSending(questId, (int)QuestResponse.MeetRequirement, stage);
                        }
                        else
                        {
                            TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement + index, stage);
                        }
                    }
                    else
                    {
                        var playerQuest = PlayerQuests[questId];
                        foreach (var stages in quest.Stages)
                        {
                            if (stages.StageId == playerQuest.CurrentStage)
                            {
                                // TODO: Check requirement
                                if (QuestManager.CanCompleteQuest(this, questId))
                                {
                                    playerQuest.CurrentStage++;
                                    TaskPromptDataSending(questId, (int)QuestResponse.MeetRequirement, playerQuest.CurrentStage);
                                    await UpdateQuestAsync(playerQuest);
                                    return;
                                }
                                else
                                {
                                    TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement, stage);
                                    return;
                                }
                                // TODO: Send dialog
                            }
                        }
                        if (playerQuest.CurrentStage == quest.Stages.Count + 1)
                        {
                            TaskPromptDataSending(questId, (int)QuestResponse.CompleteQuestSuccess, stage);
                            UpdateCharacterQuest();
                        }
                        else
                        {
                            TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement, stage);
                        }

                    }
                    break;
                case (int)PlayerAction.AcceptQuest:
                    if (stage == 0)
                    {
                        var (canAccept, index) = QuestManager.CanAcceptQuest(this, questId);
                        if (canAccept)
                        {
                            // 21 nhận nhiệm vụ trả về stage đầu tiên
                            if (AddQuest(quest, 1).Result) // 1 là stage đầu tiên
                            {
                                // Gửi reward accept
                                foreach (var reward in quest.AcceptRewards)
                                {
                                    HandleRewardItem(questId, reward);
                                }
                                TaskPromptDataSending(questId, (int)21, stage + 1);
                                UpdateCharacterQuest();
                                // Gửi thông tin đã nhận nhiệm vụ guild
                            }
                            else
                            {
                                // Không thêm được nhiệm vụ
                                TaskPromptDataSending(questId, 22 + index, stage); // 22 - thông báo Không phù hợp điều kiện, hãy kiểm tra lại
                            }
                        }
                        else
                        {
                            TaskPromptDataSending(questId, 22 + index, stage);  // 22 - thông báo Không phù hợp điều kiện, hãy kiểm tra lại
                        }
                    }
                    break;
                case (int)PlayerAction.RefuseQuest:
                    TaskPromptDataSending(questId, (int)QuestResponse.RefuseQuest, stage);
                    break;
                case (int)PlayerAction.CancelQuest:
                    if (PlayerQuests.ContainsKey(questId))
                    {
                        foreach (var reward in quest.AcceptRewards)
                        {
                            // Hiện tại chỉ hỗ trợ xóa vật phẩm nhiệm vụ, còn vật phẩm khác thì chưa
                            if (reward.Type == "item")
                            {
                                var itemInfo = World.ItemList[(int)reward.ItemId];
                                if (itemInfo == null)
                                {
                                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy vật phẩm yêu cầu: {reward.ItemId} để hoàn thành nhiệm vụ {questId} {CharacterName}");
                                    HeThongNhacNho($"Không tìm thấy vật phẩm yêu cầu: {reward.ItemId}", 7, "DEBUG");
                                    TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement, stage);
                                    return;
                                }
                                if (itemInfo.FLD_QUESTITEM == 1)
                                {
                                    var item = FindQuestItemByItemID((int)reward.ItemId);
                                    if (item != null)
                                    {
                                        item.VatPham_byte = new byte[8];
                                        GuiDi_NhiemVu_VatPham_List();
                                    }
                                    // else
                                    // {
                                    //     TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement, stage);
                                    //     UpdateCharacterQuest();
                                    //     return;
                                    // }
                                }
                                // switch ((int)reward.ItemId)
                                // {
                                //     case ItemDef.Item.Money:
                                //     case ItemDef.Item.Money2:
                                //         KiemSoatGold_SoLuong((uint)reward.Value, 0);
                                //         UpdateMoneyAndWeight();
                                //         break;
                                // }
                            }
                        }
                        PlayerQuests.TryRemove(questId, out _);
                     
                        UpdateCharacterQuest();
                    }
                    TaskPromptDataSending(questId, 41, stage);
                    break;
                case (int)PlayerAction.CompleteQuest:
                    {
                        var playerQuest = PlayerQuests[questId];
                        // Kiểm tra player có gửi thông tin hợp lệ không
                        //if (playerQuest.CurrentStage != stage)
                        //{
                        //    // Player gửi stage không hợp lệ
                        //    TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirementAccept, stage);
                        //    UpdateCharacterQuest();
                        //    break;
                        //}
                        // Kiểm tra player có đủ điều kiện hoàn thành stage không
                        if (playerQuest.CurrentStage != quest.Stages.Count + 1)
                        {
                            TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirementAccept, stage);
                            UpdateCharacterQuest();
                            break;
                        }
                        // Kiểm tra có đủ điuề kiện hoàn thành nhiệm vụ không
                        if (QuestManager.CanCompleteQuest(this, questId))
                        {
                            // TODO: Complete quest

                            var requirements = quest.CompletionRequirements;
                            foreach (var requirement in requirements)
                            {
                                if (requirement.Type == "item")
                                {
                                    var itemInfo = World.ItemList[(int)requirement.ItemId];
                                    if (itemInfo == null)
                                    {
                                        LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy vật phẩm yêu cầu: {requirement.ItemId} để hoàn thành nhiệm vụ {questId} {CharacterName}");
                                        HeThongNhacNho($"Không tìm thấy vật phẩm yêu cầu: {requirement.ItemId}", 7, "DEBUG");
                                        TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement, stage);
                                        return;
                                    }
                                    if (itemInfo.FLD_QUESTITEM == 1)
                                    {
                                        var item = FindQuestItemByItemID((int)requirement.ItemId);
                                        if (item != null)
                                        {
                                            // item.VatPham_byte = new byte[8];
                                            if (item.VatPhamSoLuong > requirement.ItemAmount)
                                            {
                                                item.VatPhamSoLuong -= (int)requirement.ItemAmount;
                                            }
                                            else
                                            {
                                                item.VatPham_byte = new byte[8];
                                                RemoveQuestItem(item);
                                            }
                                            GuiDi_NhiemVu_VatPham_List();
                                        }
                                        else
                                        {
                                            HeThongNhacNho($"Không tìm thấy vật phẩm yêu cầu: {requirement.ItemId}", 7, "DEBUG");
                                            LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy vật phẩm yêu cầu: {requirement.ItemId} để hoàn thành nhiệm vụ {questId} {CharacterName}");
                                            TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirement, stage);
                                            UpdateCharacterQuest();
                                            return;
                                        }
                                    }
                                    // TODO: kiểm tra các loại item khác
                                    switch ((int)requirement.ItemId)
                                    {
                                        case ItemDef.Item.Money:
                                        case ItemDef.Item.Money2:
                                            // KiemSoatGold_SoLuong((uint)requirement.Value, 0);
                                            // UpdateMoneyAndWeight();
                                            break;
                                    }
                                }

                            }
                            // Phát thưởng
                            var rewards = quest.Rewards;
                            foreach (var reward in rewards)
                            {
                                switch (reward.Type)
                                {
                                    case "item":
                                        bool flowControl = HandleRewardItem(questId, reward);
                                        if (!flowControl)
                                        {
                                            continue;
                                        }
                                        break;
                                    case "promotion":
                                        // Kiểm tra điều kiện thăng chức
                                        if (reward.Value - Player_Job_level == 1)
                                        {
                                            CharacterToProfession(Player_Zx, (int)reward.Value);
                                            NewLearningQigong(6, 0);
                                            if (Player_Job == 13)
                                            {
                                                ThanNuVoCongDiemSo += 5;
                                            }
                                            UpdateCharacterData(this);
                                            UpdateMoneyAndWeight();
                                            UpdateMartialArtsAndStatus();
                                        }
                                        break;
                                }

                            }
                            // end phát thưởng
                            playerQuest.CompleteTime = DateTime.Now;
                            var day = quest.QuestType switch
                            {
                                "none" => 0,
                                "daily" => 1,
                                "weekly" => 7,
                                "monthly" => 30,
                                "unlimited" => -1,
                                _ => 0
                            };
                            if (day != 0)
                                playerQuest.ResetTime = DateTime.Now.AddDays(day);
                            playerQuest.CompletionCount++;
                            await UpdateQuestAsync(playerQuest);
                            if (quest.QuestType == "unlimited")
                            {
                                // Nếu nhiệm vụ là nhiệm vụ lặp lại, thì set stage lại là 1
                                playerQuest.CurrentStage = 1;
                                UpdateCharacterQuest();
                                // PlayerQuests.TryRemove(questId, out _);
                            }
                            else
                            {
                                TaskPromptDataSending(questId, (int)QuestResponse.CompleteQuestSuccess, stage);
                            }
                        }
                        else
                        {
                            TaskPromptDataSending(questId, (int)QuestResponse.NotMeetRequirementAccept, stage);
                            UpdateCharacterQuest();
                        }
                    }
                    break;
            }
            return;
        }
        catch (Exception ex2)
        {
            LogHelper.WriteLine(LogLevel.Error, "Lỗi khi trả nhiệm vụ tại đây: ...." + ex2.Message);
        }
    }

    private void RemoveQuestItem(X_Nhiem_Vu_Vat_Pham_Loai item)
    {
        var res = Converter.HexStringToByte("aa551e00b2038200140000000000000000008ceba435000000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(item.VatPham_ID), 0, res, 0x12, 4);
        Client?.Send_Map_Data(res, res.Length);
    }

    private bool HandleRewardItem(int questId, QuestReward reward)
    {
        if (!World.ItemList.TryGetValue((int)reward.ItemId, out var itemInfo))
        {
            // Không tìm thấy vật phẩm cần thêm, báo lỗi, và mark fail cho admin
            LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy vật phẩm cần thêm cho nhiệm vụ {questId}");
            return false;
        }
        switch (reward.ItemId)
        {
            case ItemDef.QuestRewardPillItem.ExpBuff_120_90Min:
                {
                    int pillId = (int)reward.ItemId;
                    int pillTime = 90 * 60 * 1000; // 90 phút * 60 giây * 1000 mili giây = 54000000 mili giây = 54000 giây = 90 phút
                    if (AppendStatusList.TryGetValue(pillId, out StatusEffect value))
                    {
                        value.ThoiGianKetThucSuKien();
                    }
                    StatusEffect(BitConverter.GetBytes(pillId), 1, pillTime);
                    StatusEffect pill = new(this, pillTime, pillId, 1);
                    AppendStatusList.Add(pill.FLD_PID, pill);
                    FLD_NhanVat_ThemVao_PhanTramKinhNghiem += 0.2;
                }
                break;
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_2:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_3:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_4:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_5:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_6:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_7:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_8:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_9:
            case ItemDef.QuestRewardPillItem.ExpBuff_120_120Min_10:
                {
                    int pillId = (int)reward.ItemId;
                    int pillTime = 120 * 60 * 1000; // 90 phút * 60 giây * 1000 mili giây = 54000000 mili giây = 54000 giây = 90 phút
                    if (AppendStatusList.TryGetValue(pillId, out StatusEffect value))
                    {
                        value.ThoiGianKetThucSuKien();
                    }
                    StatusEffect(BitConverter.GetBytes(pillId), 1, pillTime);
                    StatusEffect pill = new(this, pillTime, pillId, 1);
                    AppendStatusList.Add(pill.FLD_PID, pill);
                    FLD_NhanVat_ThemVao_PhanTramKinhNghiem += 0.2;
                }
                break;
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_90Min:
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_90Min_2:
                {
                    int pillId = (int)reward.ItemId;
                    int pillTime = 90 * 60 * 1000; // 90 phút * 60 giây * 1000 mili giây = 54000000 mili giây = 54000 giây = 90 phút
                    if (AppendStatusList.TryGetValue(pillId, out StatusEffect value))
                    {
                        value.ThoiGianKetThucSuKien();
                    }
                    StatusEffect(BitConverter.GetBytes(pillId), 1, pillTime);
                    StatusEffect pill = new(this, pillTime, pillId, 1);
                    AppendStatusList.Add(pill.FLD_PID, pill);
                    FLD_NhanVat_ThemVao_PhanTramTraiNghiem += 0.5;
                }
                break;
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_120Min:
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_120Min_2:
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_120Min_3:
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_120Min_4:
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_120Min_5:
            case ItemDef.QuestRewardPillItem.SkillExpBuff_150_120Min_6:
                {
                    int pillId = (int)reward.ItemId;
                    int pillTime = 120 * 60 * 1000; // 90 phút * 60 giây * 1000 mili giây = 54000000 mili giây = 54000 giây = 90 phút
                    if (AppendStatusList.TryGetValue(pillId, out StatusEffect value))
                    {
                        value.ThoiGianKetThucSuKien();
                    }
                    StatusEffect(BitConverter.GetBytes(pillId), 1, pillTime);
                    StatusEffect pill = new(this, pillTime, pillId, 1);
                    AppendStatusList.Add(pill.FLD_PID, pill);
                    FLD_NhanVat_ThemVao_PhanTramTraiNghiem += 0.5;
                }
                break;

            case ItemDef.Item.VoHuanNgauNhien10_10000:
                var increase = RNG.Next(10, 10000);
                UpdateHonorPoint(increase);
                UpdateMartialArtsAndStatus();
                break;
            case ItemDef.Item.voHoangNgauNhien5_50000:
                increase = RNG.Next(5, 50000);
                Player_VoHoang += increase;
                UpdateMartialArtsAndStatus();
                break;
            case ItemDef.Item.ExpRandom1percent:
                // tăng 1% kinh nghiệm tiếp theo
                double expNeedToLevelUp = Convert.ToDouble(World.lever[Player_Level + 1] - World.lever[Player_Level]);
                double exp = expNeedToLevelUp * 0.01;
                CharacterExperience += (long)exp;
                UpdateKinhNghiemVaTraiNghiem();
                break;
            case ItemDef.Item.ExperiencePoint:
                CharacterExperience += (long)reward.Value;
                UpdateKinhNghiemVaTraiNghiem();
                break;
            case ItemDef.Item.Money:
            case ItemDef.Item.Money2:
                KiemSoatGold_SoLuong((long)reward.Value, 1);
                UpdateMoneyAndWeight();
                break;
            case ItemDef.Item.DanhVongBangHoi:
                // Switch theo qID để trao thưởng
                HeThongNhacNho("Phần thưởng danh vọng đang được hoàn thiện");
                break;
            default:
                ObtainItemFromQuest((int)reward.ItemId, (int)reward.ItemAmount);

                break;
        }

        return true;
    }

    private void ObtainItemFromQuest(int itemID, int itemAmount)
    {
        if (itemAmount <= 0) return;
        if (itemID <= 0) return;
        if (World.ItemList.TryGetValue(itemID, out var itemInfo))
        {
            if (itemInfo.FLD_QUESTITEM == 1)
            {
                SetUpQuestItems(itemID, itemAmount);
                GuiDi_NhiemVu_VatPham_List();
            }
            else
            {
                // Sử dụng ManufacturedItems để tạo item với option random sẵn
                if (itemInfo.FLD_SIDE == 0)
                {
                    for (var i = 0; i < itemAmount; i++)
                    {
                        var emptySlot = GetParcelVacancy(this);
                        if (emptySlot == -1) break;
                        ManufacturedItems(emptySlot, BitConverter.GetBytes(itemID));
                    }
                }
                else
                {
                    var emptySlot = GetParcelVacancy(this);
                    if (emptySlot == -1) return;
                    ManufacturedItems(emptySlot, BitConverter.GetBytes(itemID), itemAmount);
                    ObtainItemNotification(itemID, itemAmount);
                }
            }
        }
    }

    private void ObtainItemNotification(int itemID, int itemAmount)
    {
        var response = Converter.HexStringToByte("aa557200dd030d00680001000000476051bb990259013808af2f00000000010000000000000001000c0002250002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, response, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(itemID), 0, response, 0x16, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(itemAmount), 0, response, 10, 4);
        Client?.Send_Map_Data(response, response.Length);
    }

    private async Task<bool> HandleGroupQuest(int questId, int action, int stage)
    {
        var quest = QuestClass.GetQuest(questId);
        if (quest == null)
            {
                TaskPromptDataSending(questId, 12, stage);
                return false;
            }
        switch (questId)
        {
            case 12000:
            case 12001:
            case 12002:
            case 12003:
            case 12004:
            case 12005:
            case 12006:
            case 12007:
                switch (action)
                {
                    case 1:
                        // Chọn nhiệm vụ
                        var result = GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId);
                        if (result != 11)
                        {
                            TaskPromptDataSending(questId, result, stage);
                            break;
                        }
                        var questProcess = await GuildQuestMessage(questId, "GROUP_QUEST_PROCESS");
                        if (stage == 1)
                        {
                            if (questProcess.Success)
                            {
                                // Nếu có nhiệm vụ, kiểm tra đã thành công ngày hôm nay chưa, thành công rồi thì ko cho trả nhiệm vụ
                                if (questProcess.IsCompletedToday)
                                {
                                    TaskPromptDataSending(questId, 12, stage);
                                    break;
                                }
                                // Chưa thành công thì thông báo đang thực hiện
                                else
                                {
                                    HeThongNhacNho($"Nhiệm vụ đang được thực hiện! [{questProcess.CurrentProcess}/{questProcess.TotalProcess}]", 10, "Hệ thống nhiệm vụ");
                                    TaskPromptDataSending(questId, 12, stage);
                                    break;
                                }
                            }
                            else
                            {
                                // Không tìm thấy tiến tình nhiệm vụ, hủy nhiệm vụ
                                var res2 = await GuildQuestMessage(questId, "GROUP_QUEST_CANCEL");
                                // Hủy nhiệm vụ thành công
                                TaskPromptDataSending(questId, 12, stage);
                                UpdateCharacterQuest();
                                break;
                            }
                            // Nếu ko có nhiệm vụ, kiểm tra điều kiện nhận nhiệm vụ
                        }
                        // Nếu chưa có nhiệm vụ và đã đủ điều kiện => cho phép nhận nhiệm vụ
                        if (!questProcess.Success)
                        {
                            TaskPromptDataSending(questId, 11, stage);
                        }
                        // Nếu đã có nhiệm vụ, cần kiểm tra process xem hôm nay đã hoàn thành chưa, hoặc đang thực hiện
                        else
                        {
                            if (questProcess.IsCompletedToday)
                            {
                                TaskPromptDataSending(questId, 12, stage);
                                break;
                            }
                            else
                            {
                                HeThongNhacNho($"Nhiệm vụ đang được thực hiện! [{questProcess.CurrentProcess}/{questProcess.TotalProcess}]", 10, "Hệ thống nhiệm vụ");
                                TaskPromptDataSending(questId, 12, stage);
                                break;
                            }
                        }
                        TaskPromptDataSending(questId, result, stage);


                        break;
                    case 2:
                        // CHấp nhận nhiệm vụ, cần check có active trước không , vẫn đang kiểm tra trong DB
                        // Kiểm tra các điều kiện chấp nhận nhiệm vụ, kiểm tra process
                        if (GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId) != 11)
                        {
                            TaskPromptDataSending(questId, 22, stage);
                            break;
                        }

                        // Gửi thông tin nhận quest tới login server để tạo nhiệm vụ mới
                        var response = await GuildQuestMessage(questId, "GROUP_QUEST_ACCEPT");
                        if (!response.Success)
                        {
                            // Tạo nhiệm vụ không thành công, Hủy nhận nhiệm vụ
                            HeThongNhacNho("Tạo nhiệm vụ không thành công!", 10, "Hệ thống nhiệm vụ");
                            TaskPromptDataSending(questId, 22, stage);
                            break;
                        }
                        // Dựa vào response để quyết định tạo nhiệm vụ hay không

                        // Nên sửa lệnh gửi packet nhiệm vụ cho người chơi để gửi kèm process của group quest, ko lưu cục bộ
                        // để tất cả thành viên trong guild đều thấy nhiệm vụ 

                        // Quest được tạo thành công, gửi thông báo cho user
                        TaskPromptDataSending(questId, 21, 1);
                        UpdateCharacterQuest();
                        break;
                    case 3:
                        // Từ chối nhận nhiệm vụ
                        TaskPromptDataSending(questId, 31, stage);
                        break;
                    case 4:
                        // hủy nhiệm vụ
                        if (GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId) != 11)
                        {
                            TaskPromptDataSending(questId, 42, stage);
                            break;
                        }
                        var res = await GuildQuestMessage(questId, "GROUP_QUEST_CANCEL");
                        if (!res.Success)
                        {
                            // Hủy nhiệm vụ không thành công
                            TaskPromptDataSending(questId, 42, stage);
                            break;
                        }
                        // Hủy nhiệm vụ thành công
                        TaskPromptDataSending(questId, 41, stage);
                        UpdateCharacterQuest();
                        break;
                    case 5:
                        // Kiểm tra đủ điều kiện nhận thưởng không
                        // Cần sửa lại, CÓ thể làm tiếp nhiệm vụ của hôm qua
                        if (GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId) != 11)
                        {
                            // Kiểm tra điều kiện nhận q, ko đủ thì ko cho trả
                            TaskPromptDataSending(questId, 52, stage);
                            break;
                        }
                        var resp = await GuildQuestMessage(questId, "GROUP_QUEST_COMPLETE");
                        if (!resp.Success)
                        {
                            // Hoàn thành nhiệm vụ không thành công
                            TaskPromptDataSending(questId, 52, stage);
                            break;
                        }
                        // Hoàn thành nhiệm vụ
                        // Phát thưởng sẽ do HeroLogin đảm nhận
                        TaskPromptDataSending(questId, 51, stage);
                        UpdateCharacterQuest();
                        CurrentlyOperatingNPC = 0;
                        CurrentOperationType = 0;
                        break;
                }
                break;
            default:
                return false;
        }


        return false;
    }

    private async Task<GroupQuestMessageResponse> GuildQuestMessage(int questId, string action)
    {
        return await World.Instance.loginServerClient.GroupQuestMessageAsync(new GroupQuestMessageRequest
        {
            ClusterId = World.ClusterID,
            ServerId = World.ServerID,
            Message = action,
            QuestId = questId,
            GuildId = this.GuildId,
            CharacterName = this.CharacterName

        });
    }
}