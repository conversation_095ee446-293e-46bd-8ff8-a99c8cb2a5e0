using System;
using System.Linq;
using Akka.Actor;
using HeroYulgang.Core.Networking.Network;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using RxjhServer.Network;

namespace RxjhServer
{
	public partial class Players
	{
		public long ExtremeArenaPoints { get; internal set; }
		public bool ExtremeArenaIsDeath { get; internal set; }
		public void ExtremeArenaSetRewards(int result, long totalDamage, int killCount, int deathCount, int participantCount, ref int points, ref int stampCount)
		{
			throw new NotImplementedException();
		}

		public void ExtremeArenaCentralRevive()
		{
			throw new NotImplementedException();
		}

		public void ExtremeArenaReviveInPlace()
		{
			throw new NotImplementedException();
		}
		/// <summary>
		/// Chuyển đổi từ NetState sang ActorNetState
		/// </summary>
		/// <param name="actorNetState">Đ<PERSON><PERSON> tượng ActorNetState mới</param>
		public void MigrateToActorNetState(ActorNetState actorNetState)
		{
			try
			{
				// Cập nhật tham chiếu từ ActorNetState đến Players
				actorNetState.Player = this;

				// Cập nhật property Client
				Client = actorNetState;

				// LogHelper.WriteLine(LogLevel.Info, $"Đã chuyển đổi thành công người chơi {CharacterName} sang ActorNetState");
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi chuyển đổi sang ActorNetState: {ex.Message}");
				throw;
			}
		}
		public override void SendCurrentRangeBroadcastData(SendingClass pak, int id, int wordid)
		{
			try
			{
				// Sử dụng Grid-based broadcasting thay vì NearbyPlayers cache
				this.SendToNearbyPlayers(pak, id, wordid, true);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "Gửi dữ liệu phát sóng phạm vi hiện tại 111 ERROR: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			}
		}

		public override void SendCurrentRangeBroadcastData(byte[] data, int length, bool skipBs = false)
		{
			try
			{
				// Sử dụng Grid-based broadcasting thay vì NearbyPlayers cache
				this.SendToNearbyPlayers(data, length, true);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "Gửi dữ liệu phát sóng phạm vi hiện tại ERROR: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			}
		}

		public void Logout()
		{
			try
			{

				if (!isholdlogin)
				{

					GameDb.SetUserOffline(AccountID);
					GameDb.SetCharacterOnline(AccountID, CharacterName, 0);
					GameDb.DeleteRecoveryMacAddressLogout(CharacterName);
				}
				isholdlogin = false;
				if (!verifyVersion)
				{
					LogHelper.WriteLine(LogLevel.Error, "_connectionSucceeded Log out call for " + AccountID);
					if (World.allConnectedChars.ContainsKey(SessionID))
					{
						World.allConnectedChars.TryRemove(SessionID, out _);
					}
					return;
				}
				if (IsInLobby)
					return;

				SaveCharacterData();
				IsInLobby = true;
				if (World.allConnectedChars.TryGetValue(SessionID, out var value2))
				{
					LogHelper.WriteLine(LogLevel.Info, " LogOut Found player" + AccountID);
					if (MapID == 801 && World.WhetherTheCurrentLineIsSilver == 1)
					{
						World.conn.Transmit("DECREASE_FACTION_WAR|" + CharacterName + "|" + TheLucChien_PhePhai);
						if (string.Compare(TheLucChien_PhePhai, "CHINH_PHAI") == 0)
						{
							if (World.TheLucChien_ChinhPhai_SoNguoi > 0)
							{
								World.TheLucChien_ChinhPhai_SoNguoi--;
							}
						}
						else if (string.Compare(TheLucChien_PhePhai, "TA_PHAI") == 0 && World.TheLucChien_TaPhai_SoNguoi > 0)
						{
							World.TheLucChien_TaPhai_SoNguoi--;
						}
						World.conn.Transmit("FACTION_WAR_TOTAL|" + World.TheLucChien_ChinhPhai_SoNguoi + "|" + World.TheLucChien_TaPhai_SoNguoi);
					}
					var flag = false;
					var matchingItem = World.DiDong.FirstOrDefault(i => i.Rxjh_Map == MapID);
					if (matchingItem != null)
					{
						flag = true;
					}
					if (!flag && MapID != 1201)
					{
						MapID = 101;
						AOIExtensions.UpdateAOIPosition(this, 575f, 1565f, MapID);
					}
					if (MapID != 7001)
					{
						if (MapID == 7101)
						{
							if (World.HuyetChien != null && World.HuyetChien.KetThuc == 0)
							{
								if (World.HuyetChien.BangChienChuPhuong.DangKy_BangPhaiID == GuildId)
								{
									if (World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.ContainsKey(SessionID))
									{
										if (GangCharacterLevel == 6)
										{
											World.HuyetChien.ChuPhuong_DiemSo = 0;
											World.HuyetChien.KhachHang_DiemSo = 0;
											World.HuyetChien.KetThuc = 2;
											World.HuyetChien.Dispose();
										}
										else
										{
											World.HuyetChien.ChuPhuong_DiemSo--;
											World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.Remove(SessionID);
										}
										CloseUp = 0;
										HelpStartPrompt(1, 0);
									}
								}
								else if (World.HuyetChien.BangChienKhachHang.DangKy_BangPhaiID == GuildId && World.HuyetChien.BangChienKhachHang.DanhSachUngVien.ContainsKey(SessionID))
								{
									if (GangCharacterLevel == 6)
									{
										World.HuyetChien.ChuPhuong_DiemSo = 0;
										World.HuyetChien.KhachHang_DiemSo = 0;
										World.HuyetChien.KetThuc = 2;
										World.HuyetChien.Dispose();
									}
									else
									{
										World.HuyetChien.KhachHang_DiemSo--;
										World.HuyetChien.BangChienKhachHang.DanhSachUngVien.Remove(SessionID);
									}
									CloseUp = 0;
									HelpStartPrompt(1, 0);
								}
							}
							Mobile(529f, 1528f, 15f, 101, 0);
						}
						else if (MapID == 7301 && World.BangChien != null)
						{
							Mobile(415f, 1528f, 15f, 101, 0);
							HelpStartPrompt(0, 0);
						}
					}
					// Nếu đổi nhân vật thì không xóa khỏi World, chỉ set player  = null
					// if (IsJoinWorld)
					// {
					// 	// World.allConnectedChars.TryRemove(SessionID, out _);
					// }
					// else
					// {
					// 	this.CharacterName = "";
					// }

					// Remove player from AOI system
					this.RemoveFromAOI();
					LogHelper.WriteLine(LogLevel.Debug, $"Player {CharacterName} removed from AOI system on logout");

					PlayerLeaveMap(this.MapID);
					// if (!IsJoinWorld)
					// {
					// 	return;
					// }
					if (FLD_Couple != null && FLD_Couple.Length != 0)
					{
						var players = World.KiemTra_Ten_NguoiChoi(FLD_Couple);
						players?.UpdateCoupleSystem(1, CharacterName, players.NhanCuoiKhacChu, players.GiaiTruQuanHe_Countdown, DateTime.Now);
					}
					ClearTheNewListOfAdditionalStatus();
					ClearTheAppendStatusList();
					AbnormalStatusList();
					if (TeamID != 0 && World.WToDoi.TryGetValue(TeamID, out var value3))
					{
						value3.LeaveParty(this, 1);
					}
					if (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa)
					{
						if (CuaHangCaNhan.StoreType == 1)
						{
							CloseShop();
						}
						else if (CuaHangCaNhan.StoreType == 2)
						{
							YuanbaoPersonalStoreClosed();
						}
					}
					if (InTheShop && InTheShopID != 0)
					{
						var characterData = GetCharacterData(InTheShopID);
						if (characterData != null && characterData.CuaHangCaNhan != null)
						{
							if (characterData.CuaHangCaNhan.StoreType == 1)
							{
								OutOfShop(InTheShopID);
							}
							else if (characterData.CuaHangCaNhan.StoreType == 2)
							{
								YuanbaoPersonalStoreIsOut(InTheShopID);
							}
						}
					}
					if (CharacterBeast != null)
					{
						ClearTheBeastState();
					}
					if (GiaoDich != null && GiaoDich.GiaoDichBenTrong)
					{
						CloseTransaction(0, 6);
						BanAccount(72, AccountID, "Thoat GD");
					}
					if (value2.GiaoDich != null && value2.GiaoDich.GiaoDichBenTrong)
					{
						value2.CloseTransaction(0, 6);
						value2.BanAccount(72, value2.AccountID, "Thoat GD");
					}
					if (CharacterPKMode != 0)
					{
						SwitchPkMode(0);
					}
					// UpdateCharacterData(this);
					// GetUpdatedCharacterData(this);
					// UpdateBroadcastCharacterData();
					GameDb.SetCharacterOnline(AccountID, CharacterName, 0);
					GameDb.DeleteRecoveryMacAddressLogout(CharacterName);
					Clear();
					//Hooks_PlayerMove.OnPlayerLogout(this);
					//LogHelper.WriteLine(LogLevel.Error, "Log Out [" + AccountID + "][" + CharacterName + "] " + Client.ToString());
				}
				else
				{
					LogHelper.WriteLine(LogLevel.Error, "Không thấy nhân vật " + AccountID);
				}
				if (DchPlayerTimer != null)
				{
					DchPlayerTimer.Enabled = false;
					DchPlayerTimer.Close();
					DchPlayerTimer.Dispose();
					DchPlayerTimer = null;
				}
				if (TlcPlayerTimer != null)
				{
					TlcPlayerTimer.Enabled = false;
					TlcPlayerTimer.Close();
					TlcPlayerTimer.Dispose();
					TlcPlayerTimer = null;
				}
				if (CheckToaDo != null)
				{
					CheckToaDo.Enabled = false;
					CheckToaDo.Close();
					CheckToaDo.Dispose();
					CheckToaDo = null;
				}
				if (Automatic_Coordinates != null)
				{
					Automatic_Coordinates.Enabled = false;
					Automatic_Coordinates.Close();
					Automatic_Coordinates.Dispose();
					Automatic_Coordinates = null;
				}

				ShortcutBar?.Clear();
				if (AutomaticRecovery != null)
				{
					AutomaticRecovery.Enabled = false;
					AutomaticRecovery.Close();
					AutomaticRecovery.Dispose();
					AutomaticRecovery = null;
				}
				if (MobileMapTimer != null)
				{
					MobileMapTimer.Enabled = false;
					MobileMapTimer.Close();
					MobileMapTimer.Dispose();
				}
				if (InvincibleTimeCounter != null)
				{
					InvincibleTimeCounter.Enabled = false;
					InvincibleTimeCounter.Close();
					InvincibleTimeCounter.Dispose();
				}
				if (RecoveryTimeCounter != null)
				{
					RecoveryTimeCounter.Enabled = false;
					RecoveryTimeCounter.Close();
					RecoveryTimeCounter.Dispose();
					RecoveryTimeCounter = null;
				}
				if (RecoveryCheckBugGold != null)
				{
					RecoveryCheckBugGold.Enabled = false;
					RecoveryCheckBugGold.Close();
					RecoveryCheckBugGold.Dispose();
					RecoveryCheckBugGold = null;
				}
				if (Recovery_Exp_ALL != null)
				{
					Recovery_Exp_ALL.Enabled = false;
					Recovery_Exp_ALL.Close();
					Recovery_Exp_ALL.Dispose();
					Recovery_Exp_ALL = null;
				}
				if (Recovery_CheckLogin != null)
				{
					Recovery_CheckLogin.Enabled = false;
					Recovery_CheckLogin.Close();
					Recovery_CheckLogin.Dispose();
					Recovery_CheckLogin = null;
				}
				if (Recovery_2_CheckLogin != null)
				{
					Recovery_2_CheckLogin.Enabled = false;
					Recovery_2_CheckLogin.Close();
					Recovery_2_CheckLogin.Dispose();
					Recovery_2_CheckLogin = null;
				}
				if (Check_Item_HetHanSuDung != null)
				{
					Check_Item_HetHanSuDung.Enabled = false;
					Check_Item_HetHanSuDung.Close();
					Check_Item_HetHanSuDung.Dispose();
					Check_Item_HetHanSuDung = null;
				}
				if (Recovery_UnCheck_DCH != null)
				{
					Recovery_UnCheck_DCH.Enabled = false;
					Recovery_UnCheck_DCH.Close();
					Recovery_UnCheck_DCH.Dispose();
					Recovery_UnCheck_DCH = null;
				}
				if (Recovery_CheckPill_TTTP != null)
				{
					Recovery_CheckPill_TTTP.Enabled = false;
					Recovery_CheckPill_TTTP.Close();
					Recovery_CheckPill_TTTP.Dispose();
					Recovery_CheckPill_TTTP = null;
				}
				CharacterPKMode = 0;
				if (PartyThaoPhatToDoiIdDragon != 0)
				{
					Event_Dragon_ToDoi_Loai.Logout_Event(this, null, 0);
				}
			}
			catch (Exception ex)
			{
				World.allConnectedChars.TryRemove(SessionID, out _);
				LogHelper.WriteLine(LogLevel.Error, "Logout() User Lỗi: [" + AccountID + "]-[" + CharacterName + "] - " + ex.StackTrace);
			}
		}


		public bool Logoin()
		{
			try
			{
				// if (World.allConnectedChars.ContainsKey(SessionID))
				// {
				// 	LogHelper.WriteLine(LogLevel.Error, "SessionID đã tồn tại: " + SessionID);
				// 	return false;
				// }
				// World.allConnectedChars.Add(SessionID, this);
				LogHelper.WriteLine(LogLevel.Debug, "Vào game [" + AccountID + "] - [" + CharacterName + "]");
				CheckToaDo = new System.Timers.Timer(6000.0);
				CheckToaDo.Elapsed += MoveAll;
				CheckToaDo.AutoReset = true;
				CheckToaDo.Enabled = true;
				var macByIp = GetMacByIp(Client.ToString());
				GameDb.LoginRecord(AccountID, CharacterName, macByIp, "Login", MacAddress);
				if (World.Check_LoginAcc_Recovery != 0)
				{
					GameDb.LoginRecordMac(AccountID, CharacterName, macByIp, "Login", MacAddress, World.ServerID);
				}
				IsJoinWorld = true;

				// Thiết lập tham chiếu đến ClientActor chỉ khi sử dụng Akka networking
				var networkingConfig = HeroYulgang.Core.ConfigManager.Instance.NetworkingSettings;
				if (networkingConfig.UseAkkaNetworking)
				{
					try
					{
						// Lấy tham chiếu đến ActorSystem
						var actorSystem = HeroYulgang.Core.Networking.Core.NetworkingSystem.Instance.ActorSystem;

						// Tìm ClientActor tương ứng với session này
						var clientActorPath = $"/user/tcpManager/client-{SessionID}";
						var clientActorSelection = actorSystem.ActorSelection(clientActorPath);

						// Gửi tin nhắn để lấy tham chiếu
						clientActorSelection.Tell(new Identify(Guid.NewGuid()), Akka.Actor.ActorRefs.Nobody);

						LogHelper.WriteLine(LogLevel.Debug, $"[Akka] Đã gửi yêu cầu xác định ClientActor cho người chơi {CharacterName}");
					}
					catch (Exception ex)
					{
						LogHelper.WriteLine(LogLevel.Error, $"[Akka] Không thể thiết lập tham chiếu đến ClientActor: {ex.Message}");
					}
				}
				else
				{
					LogHelper.WriteLine(LogLevel.Debug, $"[NetCore] Bỏ qua thiết lập ClientActor cho người chơi {CharacterName} (sử dụng NetCoreServer)");
				}

				//Hooks_PlayerMove.OnPlayerLogin(this);
				return true;
			}
			catch (Exception ex)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Logoin()error: [" + AccountID + "]-[" + CharacterName + "][" + SessionID + "]" + ex.Message);
				return false;
			}
		}
		public void Dispose()
		{
			try
			{
				StopAllTimers();
				if (DchPlayerTimer != null)
				{
					DchPlayerTimer.Enabled = false;
					DchPlayerTimer.Close();
					DchPlayerTimer.Dispose();
					DchPlayerTimer = null;
				}
				if (TlcPlayerTimer != null)
				{
					TlcPlayerTimer.Enabled = false;
					TlcPlayerTimer.Close();
					TlcPlayerTimer.Dispose();
					TlcPlayerTimer = null;
				}
				if (CheckToaDo != null)
				{
					CheckToaDo.Enabled = false;
					CheckToaDo.Close();
					CheckToaDo.Dispose();
					CheckToaDo = null;
				}
				allChars?.Clear();
				AttackList?.Clear();

				NewMartialArtsCombos?.Clear();
				if (CuaHangCaNhan != null)
				{
					CuaHangCaNhan = null;
				}
				if (GiaoDich != null)
				{
					GiaoDich = null;
				}

				NgocLienHoan?.Clear();
				if (DatDuoc_BangPhaiHuyHieu_ID != null)
				{
					DatDuoc_BangPhaiHuyHieu_ID.Clear();
					DatDuoc_BangPhaiHuyHieu_ID = null;
				}
				if (NearbyPlayers != null)
				{
					NearbyPlayers.Clear();
					// PlayerList.Dispose();
					NearbyPlayers = null;
				}

				this.RemoveFromAOI();
				if (ListOfGroundItems != null)
				{
					ListOfGroundItems.Clear();
					// ListOfGroundItems.Dispose();
					ListOfGroundItems = null;
				}
				if (AppendStatusList != null)
				{
					AppendStatusList.Clear();
					AppendStatusList.Dispose();
					AppendStatusList = null;
				}
				if (AppendStatusNewList != null)
				{
					AppendStatusNewList.Clear();
					AppendStatusNewList.Dispose();
					AppendStatusNewList = null;
				}
				if (TrangThai_BatThuong != null)
				{
					TrangThai_BatThuong.Clear();
					TrangThai_BatThuong.Dispose();
					TrangThai_BatThuong = null;
				}
				if (ThanNu_TrangThai_BatThuong != null)
				{
					ThanNu_TrangThai_BatThuong.Clear();
					ThanNu_TrangThai_BatThuong.Dispose();
					ThanNu_TrangThai_BatThuong = null;
				}
				if (TrangThai_PhongThu_BatThuong != null)
				{
					TrangThai_PhongThu_BatThuong.Clear();
					TrangThai_PhongThu_BatThuong.Dispose();
					TrangThai_PhongThu_BatThuong = null;
				}
				if (TrangThai_TanCong_BatThuong != null)
				{
					TrangThai_TanCong_BatThuong.Clear();
					TrangThai_TanCong_BatThuong.Dispose();
					TrangThai_TanCong_BatThuong = null;
				}
				if (TrangThai_XanhLam_BatThuong != null)
				{
					TrangThai_XanhLam_BatThuong.Clear();
					TrangThai_XanhLam_BatThuong.Dispose();
					TrangThai_XanhLam_BatThuong = null;
				}
				if (TrangThai_MatMau_BatThuong != null)
				{
					TrangThai_MatMau_BatThuong.Clear();
					TrangThai_MatMau_BatThuong.Dispose();
					TrangThai_MatMau_BatThuong = null;
				}
				if (_hopThanhVatPhamTable != null)
				{
					_hopThanhVatPhamTable.Clear();
					_hopThanhVatPhamTable = null;
				}
				if (CharacterBeast != null)
				{
					ClearTheBeastState();
				}
				if (QuestList != null)
				{
					QuestList.Clear();
					QuestList = null;
				}
				if (PublicDrugs != null)
				{
					PublicDrugs.Clear();
					PublicDrugs = null;
				}
				if (PhanKhiCong != null)
				{
					PhanKhiCong.Clear();
					PhanKhiCong = null;
				}
				if (DanhSach_TruyenThu != null)
				{
					DanhSach_TruyenThu.Clear();
					DanhSach_TruyenThu = null;
				}
				if (tem != null)
				{
					tem.Clear();
					tem = null;
				}
				if (AutomaticRecovery != null)
				{
					AutomaticRecovery.Enabled = false;
					AutomaticRecovery.Close();
					AutomaticRecovery.Dispose();
					AutomaticRecovery = null;
					AutomaticRecovery = null;
				}
				if (PreliminaryApplicationCeremonyTimer != null)
				{
					PreliminaryApplicationCeremonyTimer.Enabled = false;
					PreliminaryApplicationCeremonyTimer.Close();
					PreliminaryApplicationCeremonyTimer.Dispose();
					PreliminaryApplicationCeremonyTimer = null;
				}
				if (_mission != null)
				{
					_mission.Dispose();
					_mission = null;
				}
				_disposed = true;
				if (MobileMapTimer != null)
				{
					MobileMapTimer.Enabled = false;
					MobileMapTimer.Close();
					MobileMapTimer.Dispose();
					MobileMapTimer = null;
				}
				if (InvincibleTimeCounter != null)
				{
					InvincibleTimeCounter.Enabled = false;
					InvincibleTimeCounter.Close();
					InvincibleTimeCounter.Dispose();
					InvincibleTimeCounter = null;
				}
				if (RecoveryTimeCounter != null)
				{
					RecoveryTimeCounter.Enabled = false;
					RecoveryTimeCounter.Close();
					RecoveryTimeCounter.Dispose();
					RecoveryTimeCounter = null;
				}
				if (RecoveryCheckBugGold != null)
				{
					RecoveryCheckBugGold.Enabled = false;
					RecoveryCheckBugGold.Close();
					RecoveryCheckBugGold.Dispose();
					RecoveryCheckBugGold = null;
				}
				if (Recovery_Exp_ALL != null)
				{
					Recovery_Exp_ALL.Enabled = false;
					Recovery_Exp_ALL.Close();
					Recovery_Exp_ALL.Dispose();
					Recovery_Exp_ALL = null;
				}
				if (Recovery_CheckLogin != null)
				{
					Recovery_CheckLogin.Enabled = false;
					Recovery_CheckLogin.Close();
					Recovery_CheckLogin.Dispose();
					Recovery_CheckLogin = null;
				}
				if (Recovery_2_CheckLogin != null)
				{
					Recovery_2_CheckLogin.Enabled = false;
					Recovery_2_CheckLogin.Close();
					Recovery_2_CheckLogin.Dispose();
					Recovery_2_CheckLogin = null;
				}
				if (Check_Item_HetHanSuDung != null)
				{
					Check_Item_HetHanSuDung.Enabled = false;
					Check_Item_HetHanSuDung.Close();
					Check_Item_HetHanSuDung.Dispose();
					Check_Item_HetHanSuDung = null;
				}
				if (Recovery_UnCheck_DCH != null)
				{
					Recovery_UnCheck_DCH.Enabled = false;
					Recovery_UnCheck_DCH.Close();
					Recovery_UnCheck_DCH.Dispose();
					Recovery_UnCheck_DCH = null;
				}
				if (Recovery_CheckPill_TTTP != null)
				{
					Recovery_CheckPill_TTTP.Enabled = false;
					Recovery_CheckPill_TTTP.Close();
					Recovery_CheckPill_TTTP.Dispose();
					Recovery_CheckPill_TTTP = null;
				}
				if (MapID == 801)
				{
					if (TheLucChien_PhePhai == "TA_PHAI")
					{
						World.TheLucChien_TaPhai_SoNguoi--;
					}
					else if (TheLucChien_PhePhai == "CHINH_PHAI")
					{
						World.TheLucChien_ChinhPhai_SoNguoi--;
					}
				}
				GameDb.SetUserOffline(AccountID);
				GameDb.SetCharacterOnline(AccountID, CharacterName, 0);
				GameDb.DeleteRecoveryMacAddressLogout(CharacterName);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "người sử dụng SoLieu giải phóng Dispose() error: [" + ex.Message + "]");
			}
		}

		private void Dispose(bool disposing)
		{
			if (_disposed)
			{
			}
			_disposed = true;
		}

	}
}
