-- Reset script for yghero_game database tables
-- Script reset cho các bảng database yghero_game
-- This script will truncate all tables and reset sequences
-- Script này sẽ truncate tất cả các bảng và reset sequences

-- ========================================
-- WARNING / CẢNH BÁO
-- ========================================
-- This script will DELETE ALL DATA in the specified tables
-- Script này sẽ XÓA TẤT CẢ DỮ LIỆU trong các bảng được chỉ định
-- Make sure to backup your data before running this script
-- H<PERSON>y chắc chắn backup dữ liệu trước khi chạy script này

-- ========================================
-- DISABLE FOREIGN KEY CHECKS (if any)
-- TẮT KIỂM TRA FOREIGN KEY (nếu có)
-- ========================================
SET session_replication_role = replica;

-- ========================================
-- TRUNCATE TABLES AND RESTART SEQUENCES
-- TRUNCATE CÁC BẢNG VÀ RESTART SEQUENCES
-- ========================================

-- Game database tables
TRUNCATE TABLE "bachbaocacrecord" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "bangchien_tiendatcuoc" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "congthanhchien_thanhchu" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "drugrecord" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "eventtop_dch" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "eventtop" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "exchangecharacter" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "giftcode" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "itemrecord" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "log_deleteitem" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "log_thelucchien" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "loginrecord" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "loginrecord_mac" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "logpk" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "logshop" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "logshopvohuan" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "syntheticrecord" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_faction_quest_progress" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_group_quest_contribution" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_group_quest_contribution_log" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_group_quest_history" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_group_quest" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_guild_quest_progress" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_sudosolieu" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_truyenthuhethong" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_vinhduhethong" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_char" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_cw" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_cwarehouse" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_guild" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_guildmember" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_pklog" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_publicwarehouse" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_pvp" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_rosetop" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "tbl_xwwl_warehouse" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "thienmathancung_danhsach" RESTART IDENTITY CASCADE;
TRUNCATE TABLE "vinhdubangphaixephang" RESTART IDENTITY CASCADE;
truncate table tbl_player_attendance restart identity cascade;
truncate table tbl_player_cumulative_reward_log restart identity cascade;
truncate table tbl_parties restart identity cascade;
truncate table tbl_party_members restart identity cascade;

-- ========================================
-- RESET SEQUENCES TO START FROM 1
-- RESET CÁC SEQUENCE VỀ BẮT ĐẦU TỪ 1
-- ========================================

SELECT setval('bachbaocacrecord_id_seq', 1, false);
SELECT setval('bangchien_tiendatcuoc_id_seq', 1, false);
SELECT setval('congthanhchien_thanhchu_id_seq', 1, false);
SELECT setval('drugrecord_id_seq', 1, false);
SELECT setval('eventtop_dch_id_seq', 1, false);
SELECT setval('eventtop_id_seq', 1, false);
SELECT setval('exchangecharacter_id_seq', 1, false);
SELECT setval('giftcode_id_seq', 1, false);
SELECT setval('itemrecord_id_seq', 1, false);
SELECT setval('log_deleteitem_id_seq', 1, false);
SELECT setval('log_thelucchien_id_seq', 1, false);
SELECT setval('loginrecord_id_seq', 1, false);
SELECT setval('loginrecord_mac_id_seq', 1, false);
SELECT setval('logpk_id_seq', 1, false);
SELECT setval('logshop_id_seq', 1, false);
SELECT setval('logshopvohuan_id_seq', 1, false);
SELECT setval('syntheticrecord_id_seq', 1, false);
SELECT setval('tbl_faction_quest_progress_id_seq', 1, false);
SELECT setval('tbl_group_quest_contribution_id_seq', 1, false);
SELECT setval('tbl_group_quest_contribution_log_id_seq', 1, false);
SELECT setval('tbl_group_quest_history_id_seq', 1, false);
SELECT setval('tbl_group_quest_id_seq', 1, false);
SELECT setval('tbl_guild_quest_progress_id_seq', 1, false);
SELECT setval('tbl_sudosolieu_id_seq', 1, false);
SELECT setval('tbl_truyenthuhethong_id_seq', 1, false);
SELECT setval('tbl_vinhduhethong_id_seq', 1, false);
SELECT setval('tbl_xwwl_char_id_seq', 1, false);
SELECT setval('tbl_xwwl_cw_id_seq', 1, false);
SELECT setval('tbl_xwwl_cwarehouse_id_seq', 1, false);
SELECT setval('tbl_xwwl_guild_id_seq', 1, false);
SELECT setval('tbl_xwwl_guildmember_id_seq', 1, false);
SELECT setval('tbl_xwwl_pklog_id_seq', 1, false);
SELECT setval('tbl_xwwl_publicwarehouse_id_seq', 1, false);
SELECT setval('tbl_xwwl_pvp_id_seq', 1, false);
SELECT setval('tbl_xwwl_rosetop_id_seq', 1, false);
SELECT setval('tbl_xwwl_warehouse_id_seq', 1, false);
SELECT setval('thienmathancung_danhsach_id_seq', 1, false);
SELECT setval('vinhdubangphaixephang_id_seq', 1, false);

-- ========================================
-- RE-ENABLE FOREIGN KEY CHECKS
-- BẬT LẠI KIỂM TRA FOREIGN KEY
-- ========================================
SET session_replication_role = DEFAULT;

-- ========================================
-- VERIFICATION
-- XÁC MINH
-- ========================================

-- Check that all tables are empty
SELECT 
    schemaname,
    tablename,
    n_tup_ins as total_inserts,
    n_tup_upd as total_updates,
    n_tup_del as total_deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples
FROM pg_stat_user_tables 
WHERE tablename IN (
    'bachbaocacrecord', 'bangchien_tiendatcuoc', 'congthanhchien_thanhchu', 'drugrecord',
    'eventtop_dch', 'eventtop', 'exchangecharacter', 'giftcode', 'itemrecord',
    'log_deleteitem', 'log_thelucchien', 'loginrecord', 'loginrecord_mac', 'logpk',
    'logshop', 'logshopvohuan', 'syntheticrecord', 'tbl_faction_quest_progress',
    'tbl_group_quest_contribution', 'tbl_group_quest_contribution_log', 'tbl_group_quest_history',
    'tbl_group_quest', 'tbl_guild_quest_progress', 'tbl_sudosolieu', 'tbl_truyenthuhethong',
    'tbl_vinhduhethong', 'tbl_xwwl_char', 'tbl_xwwl_cw', 'tbl_xwwl_cwarehouse',
    'tbl_xwwl_guild', 'tbl_xwwl_guildmember', 'tbl_xwwl_pklog', 'tbl_xwwl_publicwarehouse',
    'tbl_xwwl_pvp', 'tbl_xwwl_rosetop', 'tbl_xwwl_warehouse', 'thienmathancung_danhsach',
    'vinhdubangphaixephang'
)
ORDER BY tablename;

-- Check sequence values
SELECT schemaname, sequencename, last_value, start_value, increment_by
FROM pg_sequences 
WHERE sequencename LIKE '%_id_seq'
ORDER BY sequencename;

SELECT 'Reset completed successfully for yghero_game database' as status;
