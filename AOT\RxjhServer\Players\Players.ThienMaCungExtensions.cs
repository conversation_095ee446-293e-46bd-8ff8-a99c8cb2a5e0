﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	public void ThienMaThanCungThongBaoBatDau()
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000100400220001C0066000E4C035301600007000A00000001000120054002010807A008E03E00000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}

	public void GuiDi_Thu_Thanh_<PERSON>ong(int 守城还是攻城, string 门派名)
	{
		try
		{
			var text = "AA5586007B055301800007000900000001009E49000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000B400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
			var array = Converter.HexStringToByte(text);
			var array2 = Converter.TangHoaKetHon(门派名);
			Buffer.BlockCopy(array2, 0, array, 74, array2.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(守城还是攻城), 0, array, 12, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}

	public void ThienMaThanCungCuaChinhThanhDaMoRa()
	{
		try
		{
			var array = Converter.HexStringToByte("AA550B000000D40405002E4000000055AA");
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}

	public void VoHuan_DatDuoc_NhacNho_CongThanhChien(int 数量)
	{
		var text = "AA550E001302D218080033000000F401000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(数量), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void ThienMaThanCungCuaThanhDong_DaMo()
	{
		try
		{
			var array = Converter.HexStringToByte("AA550B000000D40405002F4000000055AA");
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}

	public void ThienMaThanCungPhoTuongKichSat()
	{
		//var dBToDataTable = DBA.GetDBToDataTable($"select * from [TBL_XWWL_Guild]  where  G_Name='{GuildName}'");
		var guild = GameDb.FindGuild(GuildName).Result;
		//DBA.ExeSqlCommand(string.Format("INSERT INTO CongThanhChien_ThanhChu (TenThanhChu,CongThanhChien_TenBang,BangPhaiID)values('{0}','{1}',{2})", dBToDataTable.Rows[0]["G_Master"].ToString(), GuildName, GuildId)).GetAwaiter().GetResult();
		GameDb.InsertSiege(GuildName, guild.g_master, GuildId);
		//dBToDataTable.Dispose();
	}

	public void ThienMaThanCungPhoTuongKichSat_DiDong(Players nhanVat)
	{
		//var dBToDataTable = DBA.GetDBToDataTable($"select  *  from  [CongThanhChien_ThanhChu]  ");
		var dBToDataTable = GameDb.FindAllSiegeParticipants().Result;
		var text = dBToDataTable[0].congthanhchien_tenbang.ToString();
		if (nhanVat.MapID == 42001)
		{
			if (nhanVat.GuildName == text)
			{
				nhanVat.DemonMobile(-427f, -13f, 15f, 42001);
			}
			else
			{
				nhanVat.DemonMobile(-431f, -681f, 15f, 42001);
			}
		}
	}

	public void ThienMaThanCungCongThanhChienThangPacket(Players nhanVat)
	{
		try
		{
			var array = Converter.HexStringToByte("AA553A000100400223001F0066000C33015301600007000B000000012003E02D020D0000000000000000000000000000000000E00F40000000000000000055AA");
			var bytes = Encoding.Default.GetBytes(nhanVat.GuildName);
			Buffer.BlockCopy(bytes, 0, array, 34, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(nhanVat.SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
				SendCurrentRangeBroadcastData(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void ThienMaThanCungThuThanhThangLoiKetThucPacket(Players nhanVat)
	{
		try
		{
			var array = Converter.HexStringToByte("AA5566000B01530160000700090000000100A946000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			//var dBToDataTable = DBA.GetDBToDataTable($"select  *  from  [CongThanhChien_ThanhChu]  ");
			var dBToDataTable = GameDb.FindAllSiegeParticipants().Result;
			var bytes = Encoding.Default.GetBytes(dBToDataTable[0].congthanhchien_tenbang.ToString());
			Buffer.BlockCopy(bytes, 0, array, 74, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(nhanVat.SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
				SendCurrentRangeBroadcastData(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public async Task ThienMaThanCungBieuTuongHienThi(Players players, int nhanSo)
	{
		try
		{
			if (World.jlMsg == 1)
			{
				LogHelper.WriteLine(0, "Players chỉ CongThanhChienNguoiChoi");
			}
			foreach (var value in World.CongThanhSoLieu_list.Values)
			{
				using SendingClass sendingClass = new();
				sendingClass.Write2(nhanSo);
				sendingClass.Write2(0);
				for (var i = 0; i < nhanSo; i++)
				{
					sendingClass.Write2(players.SessionID);
					sendingClass.Write2(0);
					sendingClass.Write(2);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write2(0);
					sendingClass.Write(0);
					sendingClass.Write(40);
					sendingClass.Write2(0);
					sendingClass.Write(0);
					sendingClass.Write4(-1);
					sendingClass.Write4(0);
					sendingClass.Write2(players.GuildId);
					sendingClass.Write2(3);
					var dataTable = await GameDb.FindGuild(players.GuildName);
					if (dataTable.g_master.ToString() != players.CharacterName)
					{
						sendingClass.Write(0);
					}
					else
					{
						sendingClass.Write(1);
					}
				}
				if (players.Client != null)
				{
					players.Client.SendPak(sendingClass, 2129, SessionID);
					players.SendCurrentRangeBroadcastData(sendingClass, 2129, SessionID);
				}
			}
		}
		catch (Exception)
		{
		}
	}

	public void TlcBieuTuong(Players players, int nhanSo)
	{
		try
		{
			if (World.jlMsg == 1)
			{
				LogHelper.WriteLine(0, "Players chỉ CongThanhChienNguoiChoi");
			}
			using SendingClass sendingClass = new();
			sendingClass.Write2(nhanSo);
			sendingClass.Write2(0);
			for (var i = 0; i < nhanSo; i++)
			{
				sendingClass.Write2(players.SessionID);
				sendingClass.Write2(0);
				sendingClass.Write(2);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
				sendingClass.Write(0);
				sendingClass.Write(40);
				sendingClass.Write2(0);
				sendingClass.Write(0);
				sendingClass.Write4(-1);
				sendingClass.Write4(0);
				sendingClass.Write2(players.GuildId);
				sendingClass.Write2(3);
				sendingClass.Write(1);
			}
			if (players.Client != null)
			{
				players.Client.SendPak(sendingClass, 2129, SessionID);
				players.SendCurrentRangeBroadcastData(sendingClass, 2129, SessionID);
			}
		}
		catch (Exception)
		{
		}
	}

	public async Task ThienMaThanCungThongTin(byte[] packetData, Players players)
	{
		var text = "AA550E011000CA04080107000000330000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000100000001000000010000000000000055AA";
		var array = Converter.HexStringToByte(text);
		var dataTable = await GameDb.LoadGuildAlliance(World.NguoiChiemGiu_Den_Tenma);
		if (dataTable != null)
		{
			var num = 0;
			for (var i = 0; i < dataTable.Count; i++)
			{
				var text2 = dataTable[i].g_name;
				if (text2 != World.NguoiChiemGiu_Den_Tenma)
				{
					var array2 = Converter.TangHoaKetHon(text2);
					Buffer.BlockCopy(array2, 0, array, 18 + 24 * num, array2.Length);
					num++;
				}
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 270, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void DragonEvent(byte[] packetData, int packetSize)
	{
		var text = Converter.ToString(packetData);
		var num = BitConverter.ToInt16(packetData, 12);
		if (num == 61)
		{
			var array = Converter.HexStringToByte("AA55B2002C010105A40008003D0000000100090000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007F912B6100000000B070F25D3015000010300000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		if (num == 30)
		{
			var array2 = Converter.HexStringToByte("AA55B2002C010105A40008001E000100010001000000000000008C0000000000000009000000000000000A0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000636C617373313300000000000000000000000000AA00000046B5050D3015000026F50000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
			Client?.Send_Map_Data(array2, array2.Length);
			var array3 = Converter.HexStringToByte("AA55B2002C010105A400080020000100010009000000000000000D00000000000000AA0000000000000001000000000000000A000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000636C61737331330000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
			Client?.Send_Map_Data(array3, array3.Length);
		}
		if (num == 31)
		{
		}
		if (num == 53)
		{
			Mobile(29f, -600f, 15f, 43001, 0);
		}
	}

	public void ThienMaThanCungBangXepHang(byte[] packetData, int packetSize)
	{
		foreach (var value3 in World.CongThanhSoLieu_list.Values)
		{
			var year = value3.ThienMaCongThanhThoiGian.ToUniversalTime().Year;
			var month = value3.ThienMaCongThanhThoiGian.ToUniversalTime().Month;
			var day = value3.ThienMaCongThanhThoiGian.ToUniversalTime().Day;
			var array = Converter.HexStringToByte("AA552E00A404C20428000700000010000000000000000000000000000000000000000A3B34010000000000000000113B340155AA");
			var bytes = Encoding.Default.GetBytes(value3.CongThanhChien_TenBang);
			var value = year * 1000 + month * 100 + day;
			var value2 = year * 1000 + month * 100 + (day + 2);
			Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 34, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array, 46, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
	}

	public void ThienMaThanCungBangXepHang(byte[] packetData, Players players)
	{
		var text = "AA552E001000C2042800070000001500000000000000000000000000000000000000A53C340100000000030000007F3D340155AA";
		var array = Converter.HexStringToByte(text);
		var array2 = Converter.TangHoaKetHon(World.NguoiChiemGiu_Den_Tenma);
		Buffer.BlockCopy(array2, 0, array, 18, array2.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(World.BanDau_ChiemLinh_Ngay), 0, array, 34, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(Converter.DateTimeToString(DateTime.Now.AddDays(1.0)))), 0, array, 46, 4);
		if (players.MonPhai_LienMinh_MinhChu == "")
		{
			if (KiemTra_BangPhai_Xin_LienMinh(players.GuildName) == World.NguoiChiemGiu_Den_Tenma)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 42, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 42, 4);
			}
		}
		else if (players.MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
		{
			if (players.GuildName == players.MonPhai_LienMinh_MinhChu)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 42, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(21), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 42, 4);
			}
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 42, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}

	public void GuiDiXungQuanhPlayer_CongThanhChien_BenTrongEvent()
	{
		if (MapID != 42001)
		{
			return;
		}
		var text = "AA556D0072050851670003000000";
		var array = Converter.HexStringToByte(text);
		var text2 = "55AA";
		var array2 = Converter.HexStringToByte(text2);
		var text3 = "7205000001000000000000000000000000000000FFFFFFFF00000000D0791D0101";
		var array3 = Converter.HexStringToByte(text3);
		var list = TraTim_PhamVi_Player(300);
		var array4 = new byte[array.Length + array3.Length * list.Count + array2.Length];
		Buffer.BlockCopy(array, 0, array4, 0, array.Length);
		array4[array4.Length - 2] = 85;
		array4[array4.Length - 1] = 170;
		for (var i = 0; i < list.Count; i++)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(list[i].SessionID), 0, array3, 0, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(list[i].GuildId), 0, array3, 28, 4);
			if (list[i].MonPhai_LienMinh_MinhChu == World.ThienMa_ThanCung_TheLuc_ChiemLinh)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array3, 4, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array3, 4, 4);
			}
			if (list[i].GangCharacterLevel == 6)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array3, 32, 1);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array3, 32, 1);
			}
			Buffer.BlockCopy(array3, 0, array4, 14 + i * array3.Length, array3.Length);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(list.Count), 0, array4, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(array3.Length * list.Count + 4), 0, array4, 8, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(array3.Length * list.Count + 10), 0, array4, 2, 2);
		Client?.Send_Map_Data(array4, array4.Length);
	}

	public void ThienMaVeThanh(byte[] packetData, int packetSize)
	{
		DemonMobile(420f, 1740f, 15f, 101);
	}
	
	public static int XinMonPhaiLienMinh(Players player, string 盟主门派)
	{
		foreach (var item in World.MonPhaiLienMinhTrangThai)
		{
			if (item.XinMonPhaiDanhDu == player.GuildName)
			{
				return -1;
			}
		}
		X_Mon_Phai_Lien_Minh_Trang_Thai xMonPhaiLienMinhTrangThai = new();
		xMonPhaiLienMinhTrangThai.XinMonPhaiDanhDu = player.GuildName;
		xMonPhaiLienMinhTrangThai.MinhChu_MonPhai_DanhDu = 盟主门派;
		World.MonPhaiLienMinhTrangThai.Add(xMonPhaiLienMinhTrangThai);
		return 0;
	}

	public void ThienMaThanCung_MoiThamGia_SilverCoin(byte[] packetData, int packetSize)
	{
		World.conn.Transmit("GET_SERVER_LIST|" + AccountID + "|" + OriginalServerSerialNumber + "|" + OriginalServerIP + "|" + OriginalServerPort + "|" + OriginalServerID);
		Thread.Sleep(1000);
		if (World.CoMo_ThiTruongTraoDoiTienXu == 1 && MapID != 1201)
		{
			MobileSwitchingScreen();
			ThienMa_ChangeLineMove(10f, 10f, 15f, 1201);
		}
	}

	public void ThienMaThanCung_MoiThamGia(byte[] packetData, int packetSize)
	{
		// var dBToDataTable = DBA.GetDBToDataTable($"select  *  from  [CongThanhChien_ThanhChu]  ");
		// if (dBToDataTable.Rows.Count > 0 && dBToDataTable != null)
		// {
		// 	if (dBToDataTable.Rows[0]["CongThanhChien_TenBang"].ToString() == GuildName)
		// 	{
		// 		World.DangKyDanhSach_NguoiChoiCongThanhChien.Add(this);
		// 		DemonMobile(-427f, -13f, 15f, 42001);
		// 	}
		// 	else if (GuildName == string.Empty)
		// 	{
		// 		HeThongNhacNho("Đại hiệp chưa gia nhập môn phái, xin hãy gia nhập rồi mới tham gia!", 10, "Thiên cơ các");
		// 	}
		// 	else
		// 	{
		// 		World.DangKyDanhSach_NguoiChoiCongThanhChien.Add(this);
		// 		DemonMobile(-431f, -681f, 15f, 42001);
		// 	}
		// 	GuiDi_CongThanhChien_ThoiGian_ConLai((int)CongThanhChien.KhiTienLenTrinh_KetThuc_ThoiGian.Subtract(DateTime.Now).TotalSeconds);
		// }
		// else
		// {
		// 	if (GuildName == string.Empty)
		// 	{
		// 		HeThongNhacNho("Đại hiệp chưa gia nhập môn phái, xin hãy gia nhập rồi mới tham gia!", 10, "Thiên cơ các");
		// 	}
		// 	else
		// 	{
		// 		World.DangKyDanhSach_NguoiChoiCongThanhChien.Add(this);
		// 		DemonMobile(-431f, -681f, 15f, 42001);
		// 	}
		// 	GuiDi_CongThanhChien_ThoiGian_ConLai((int)CongThanhChien.KhiTienLenTrinh_KetThuc_ThoiGian.Subtract(DateTime.Now).TotalSeconds);
		// }
		// dBToDataTable.Dispose();
	}

}
