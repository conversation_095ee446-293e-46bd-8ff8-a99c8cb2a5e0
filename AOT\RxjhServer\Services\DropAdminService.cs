using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Public;

namespace RxjhServer.Services
{
    /// <summary>
    /// Admin service for managing drop system through in-game interface
    /// </summary>
    public static class DropAdminService
    {
        /// <summary>
        /// Show drop management menu to admin player
        /// </summary>
        /// <param name="player">Admin player</param>
        public static void ShowDropManagementMenu(Players player)
        {
            if (player == null || player.GMMode != 8)
            {
                return;
            }

            try
            {
                player.HeThongNhacNho("=== Drop System Management ===", 10, "Admin");
                player.HeThongNhacNho("1. !dropstatus - System status", 10, "Admin");
                player.HeThongNhacNho("2. !dropenable/!dropdisable - Toggle system", 10, "Admin");
                player.HeThong<PERSON>ha<PERSON><PERSON>ho("3. !dropmultiplier <value> - Set multiplier", 10, "Admin");
                player.HeThongNhacNho("4. !dropadditem <type> <value> <itemId> <rate>", 10, "Admin");
                player.HeThongNhacNho("5. !dropremoveitem <id> - Remove drop rule", 10, "Admin");
                player.HeThongNhacNho("6. !droplistitem <type> - List drop rules", 10, "Admin");
                player.HeThongNhacNho("7. !dropbackup - Backup current config", 10, "Admin");
                player.HeThongNhacNho("8. !droprestore - Restore from backup", 10, "Admin");
                player.HeThongNhacNho("===============================", 10, "Admin");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error showing drop management menu: {ex.Message}");
            }
        }

        /// <summary>
        /// Add a new drop item rule
        /// </summary>
        /// <param name="player">Admin player</param>
        /// <param name="sourceType">Source type (level_range, npc_specific, quest_based)</param>
        /// <param name="sourceValue">Source value (e.g., "1-10", "15100", "1001")</param>
        /// <param name="itemId">Item ID to drop</param>
        /// <param name="dropRate">Drop rate (0.000001 to 1.000000)</param>
        /// <param name="priority">Priority (optional, default 100)</param>
        /// <returns>Success status</returns>
        public static async Task<bool> AddDropItem(Players player, string sourceType, string sourceValue, 
            int itemId, decimal dropRate, int priority = 100)
        {
            if (player == null || player.GMMode != 8)
            {
                return false;
            }

            try
            {
                // Validate parameters
                if (!IsValidSourceType(sourceType))
                {
                    player.HeThongNhacNho($"Invalid source type: {sourceType}. Use: level_range, npc_specific, quest_based", 20, "Admin");
                    return false;
                }

                if (dropRate <= 0 || dropRate > 1)
                {
                    player.HeThongNhacNho($"Invalid drop rate: {dropRate}. Must be between 0.000001 and 1.000000", 20, "Admin");
                    return false;
                }

                if (itemId <= 0)
                {
                    player.HeThongNhacNho($"Invalid item ID: {itemId}", 20, "Admin");
                    return false;
                }

                // Check if drop rule already exists
                var existing = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.source_type == sourceType && 
                               d.source_value == sourceValue && 
                               d.item_id == itemId)
                    .FirstAsync();

                if (existing != null)
                {
                    player.HeThongNhacNho($"Drop rule already exists for {sourceType}:{sourceValue} item {itemId}", 20, "Admin");
                    return false;
                }

                // Create new drop rule
                var newDrop = new tbl_new_drops
                {
                    source_type = sourceType,
                    source_value = sourceValue,
                    item_id = itemId,
                    drop_rate = dropRate,
                    quantity_min = 1,
                    quantity_max = 1,
                    magic0 = 0,
                    magic1 = 0,
                    magic2 = 0,
                    magic3 = 0,
                    magic4 = 0,
                    expire_days = 0,
                    is_active = true,
                    priority = priority,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };

                var result = await PublicDb._freeSql.Insert(newDrop).ExecuteAffrowsAsync();

                if (result > 0)
                {
                    // Refresh drop configuration
                    PublicDb.LoadNewDrops();
                    
                    player.HeThongNhacNho($"✓ Added drop rule: {sourceType}:{sourceValue} → Item {itemId} ({dropRate:P4})", 10, "Admin");
                    LogHelper.WriteLine(LogLevel.Info, $"Admin {player.CharacterName} added drop rule: {sourceType}:{sourceValue} → Item {itemId} ({dropRate:P4})");
                    return true;
                }
                else
                {
                    player.HeThongNhacNho("✗ Failed to add drop rule", 20, "Admin");
                    return false;
                }
            }
            catch (Exception ex)
            {
                player.HeThongNhacNho($"✗ Error adding drop rule: {ex.Message}", 20, "Admin");
                LogHelper.WriteLine(LogLevel.Error, $"Error adding drop rule: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a drop item rule by ID
        /// </summary>
        /// <param name="player">Admin player</param>
        /// <param name="dropId">Drop rule ID to remove</param>
        /// <returns>Success status</returns>
        public static async Task<bool> RemoveDropItem(Players player, int dropId)
        {
            if (player == null || player.GMMode != 8)
            {
                return false;
            }

            try
            {
                var dropRule = await PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.id == dropId)
                    .FirstAsync();

                if (dropRule == null)
                {
                    player.HeThongNhacNho($"Drop rule with ID {dropId} not found", 20, "Admin");
                    return false;
                }

                var result = await PublicDb._freeSql.Delete<tbl_new_drops>()
                    .Where(d => d.id == dropId)
                    .ExecuteAffrowsAsync();

                if (result > 0)
                {
                    // Refresh drop configuration
                    PublicDb.LoadNewDrops();
                    
                    player.HeThongNhacNho($"✓ Removed drop rule: {dropRule.source_type}:{dropRule.source_value} → Item {dropRule.item_id}", 10, "Admin");
                    LogHelper.WriteLine(LogLevel.Info, $"Admin {player.CharacterName} removed drop rule ID {dropId}");
                    return true;
                }
                else
                {
                    player.HeThongNhacNho("✗ Failed to remove drop rule", 20, "Admin");
                    return false;
                }
            }
            catch (Exception ex)
            {
                player.HeThongNhacNho($"✗ Error removing drop rule: {ex.Message}", 20, "Admin");
                LogHelper.WriteLine(LogLevel.Error, $"Error removing drop rule: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// List drop items by source type
        /// </summary>
        /// <param name="player">Admin player</param>
        /// <param name="sourceType">Source type to filter by (optional)</param>
        /// <param name="limit">Maximum number of items to show</param>
        public static async Task ListDropItems(Players player, string sourceType = null, int limit = 20)
        {
            if (player == null || player.GMMode != 8)
            {
                return;
            }

            try
            {
                var query = PublicDb._freeSql.Select<tbl_new_drops>()
                    .Where(d => d.is_active == true);

                if (!string.IsNullOrEmpty(sourceType))
                {
                    query = query.Where(d => d.source_type == sourceType);
                }

                var drops = await query
                    .OrderBy(d => d.source_type)
                    .ThenBy(d => d.priority)
                    .ThenBy(d => d.source_value)
                    .Take(limit)
                    .ToListAsync();

                if (!drops.Any())
                {
                    player.HeThongNhacNho("No drop rules found", 10, "Admin");
                    return;
                }

                player.HeThongNhacNho($"=== Drop Rules ({drops.Count}) ===", 10, "Admin");
                
                foreach (var drop in drops)
                {
                    var ratePercent = (drop.drop_rate * 100).ToString("F4");
                    player.HeThongNhacNho($"ID:{drop.id} {drop.source_type}:{drop.source_value} → Item {drop.item_id} ({ratePercent}%)", 10, "Admin");
                }

                if (drops.Count >= limit)
                {
                    player.HeThongNhacNho($"... (showing first {limit} results)", 10, "Admin");
                }
            }
            catch (Exception ex)
            {
                player.HeThongNhacNho($"✗ Error listing drop items: {ex.Message}", 20, "Admin");
                LogHelper.WriteLine(LogLevel.Error, $"Error listing drop items: {ex.Message}");
            }
        }

        /// <summary>
        /// Update drop rate for existing rule
        /// </summary>
        /// <param name="player">Admin player</param>
        /// <param name="dropId">Drop rule ID</param>
        /// <param name="newRate">New drop rate</param>
        /// <returns>Success status</returns>
        public static async Task<bool> UpdateDropRate(Players player, int dropId, decimal newRate)
        {
            if (player == null || player.GMMode != 8)
            {
                return false;
            }

            try
            {
                if (newRate <= 0 || newRate > 1)
                {
                    player.HeThongNhacNho($"Invalid drop rate: {newRate}. Must be between 0.000001 and 1.000000", 20, "Admin");
                    return false;
                }

                var result = await PublicDb._freeSql.Update<tbl_new_drops>()
                    .Set(d => d.drop_rate, newRate)
                    .Set(d => d.updated_at, DateTime.Now)
                    .Where(d => d.id == dropId)
                    .ExecuteAffrowsAsync();

                if (result > 0)
                {
                    // Refresh drop configuration
                    PublicDb.LoadNewDrops();
                    
                    player.HeThongNhacNho($"✓ Updated drop rate for rule ID {dropId} to {newRate:P4}", 10, "Admin");
                    LogHelper.WriteLine(LogLevel.Info, $"Admin {player.CharacterName} updated drop rate for rule ID {dropId} to {newRate:P4}");
                    return true;
                }
                else
                {
                    player.HeThongNhacNho($"Drop rule with ID {dropId} not found", 20, "Admin");
                    return false;
                }
            }
            catch (Exception ex)
            {
                player.HeThongNhacNho($"✗ Error updating drop rate: {ex.Message}", 20, "Admin");
                LogHelper.WriteLine(LogLevel.Error, $"Error updating drop rate: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Backup current drop configuration
        /// </summary>
        /// <param name="player">Admin player</param>
        /// <returns>Success status</returns>
        public static async Task<bool> BackupDropConfiguration(Players player)
        {
            if (player == null || player.GMMode != 8)
            {
                return false;
            }

            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupTableName = $"tbl_new_drops_backup_{timestamp}";

                // Create backup table
                var sql = $@"
                    CREATE TABLE {backupTableName} AS 
                    SELECT * FROM tbl_new_drops;
                    
                    INSERT INTO tbl_drop_config (config_key, config_value, description, updated_at)
                    VALUES ('last_backup_table', '{backupTableName}', 'Last backup table name', NOW())
                    ON CONFLICT (config_key) DO UPDATE SET 
                        config_value = EXCLUDED.config_value,
                        updated_at = EXCLUDED.updated_at;
                ";

                await PublicDb._freeSql.Ado.ExecuteNonQueryAsync(sql);

                player.HeThongNhacNho($"✓ Drop configuration backed up to {backupTableName}", 10, "Admin");
                LogHelper.WriteLine(LogLevel.Info, $"Admin {player.CharacterName} created drop backup: {backupTableName}");
                return true;
            }
            catch (Exception ex)
            {
                player.HeThongNhacNho($"✗ Error creating backup: {ex.Message}", 20, "Admin");
                LogHelper.WriteLine(LogLevel.Error, $"Error creating drop backup: {ex.Message}");
                return false;
            }
        }

        #region Helper Methods

        /// <summary>
        /// Validate source type
        /// </summary>
        private static bool IsValidSourceType(string sourceType)
        {
            return sourceType switch
            {
                "level_range" or "npc_specific" or "quest_based" => true,
                _ => false
            };
        }

        /// <summary>
        /// Parse level range string
        /// </summary>
        private static bool TryParseLevelRange(string rangeValue, out int min, out int max)
        {
            min = max = 0;

            if (string.IsNullOrEmpty(rangeValue))
                return false;

            var parts = rangeValue.Split('-');
            if (parts.Length != 2)
                return false;

            return int.TryParse(parts[0], out min) && int.TryParse(parts[1], out max) && min <= max;
        }

        #endregion
    }
}
