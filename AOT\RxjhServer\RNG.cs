using System;
using System.Security.Cryptography;

namespace RxjhServer;

public static class RNG
{
	private static RandomNumberGenerator rngcryptoServiceProvider_0 = RandomNumberGenerator.Create(); // Use RandomNumberGenerator.Create() for cryptographically secure random numbers

	private static byte[] rb = new byte[4];

	public static int Next()
	{
		rngcryptoServiceProvider_0.GetBytes(rb);
		var num = BitConverter.ToInt32(rb, 0);
		if (num < 0)
		{
			num = -num;
		}
		return num;
	}

	public static int Next(int int_0)
	{
		if (int_0 <= 0)
		{
			return 0;
		}

		rngcryptoServiceProvider_0.GetBytes(rb);
		var num = Math.Abs(BitConverter.ToInt32(rb, 0)) % int_0;
		return num;
	}

	public static int Next(int int_0, int int_1)
	{
		// Đ<PERSON>m bảo int_0 <= int_1
		if (int_0 > int_1)
		{
			(int_0, int_1) = (int_1, int_0);
		}

		// Nếu range = 0, trả về giá trị đó
		if (int_0 == int_1)
		{
			return int_0;
		}

		int range = int_1 - int_0;
		return Next(range) + int_0;
	}
	public static long Next(long start, long end)
	{
		// Đảm bảo start <= end
		if (start > end)
		{
			(start, end) = (end, start);
		}

		// Nếu range = 0, trả về giá trị đó
		if (start == end)
		{
			return start;
		}

		rngcryptoServiceProvider_0.GetBytes(rb);
		long range = end - start;
		long num = Math.Abs(BitConverter.ToInt32(rb, 0));
		long result = start + (num % range);

		return result;
	}
}
