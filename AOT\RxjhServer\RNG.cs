using System;
using System.Security.Cryptography;

namespace RxjhServer;

public static class RNG
{
	private static RandomNumberGenerator rngcryptoServiceProvider_0 = RandomNumberGenerator.Create(); // Use RandomNumberGenerator.Create() for cryptographically secure random numbers

	private static byte[] rb = new byte[4];

	public static int Next()
	{
		rngcryptoServiceProvider_0.GetBytes(rb);
		var num = BitConverter.ToInt32(rb, 0);
		if (num < 0)
		{
			num = -num;
		}
		return num;
	}

	public static int Next(int int_0)
	{
		if (int_0 <= 0)
		{
			return 0;
		}

		rngcryptoServiceProvider_0.GetBytes(rb);
		var num = Math.Abs(BitConverter.ToInt32(rb, 0)) % int_0;
		return num;
	}

	public static int Next(int int_0, int int_1)
	{
		// Đ<PERSON>m bảo int_0 <= int_1
		if (int_0 > int_1)
		{
			(int_0, int_1) = (int_1, int_0);
		}

		// Nếu range = 0, trả về giá trị đó
		if (int_0 == int_1)
		{
			return int_0;
		}

		int range = int_1 - int_0;
		return Next(range) + int_0;
	}
	public static long Next(long start, long end)
	{
		// Đảm bảo start <= end
		if (start > end)
		{
			(start, end) = (end, start);
		}

		// Nếu range = 0, trả về giá trị đó
		if (start == end)
		{
			return start;
		}

		rngcryptoServiceProvider_0.GetBytes(rb);
		long range = end - start;
		long num = Math.Abs(BitConverter.ToInt32(rb, 0));
		long result = start + (num % range);

		return result;
	}

	/// <summary>
	/// Generate a random double between 0.0 and 1.0 (exclusive)
	/// Used for new drop system with decimal rates
	/// </summary>
	public static double NextDouble()
	{
		// Use 8 bytes for better precision
		byte[] buffer = new byte[8];
		rngcryptoServiceProvider_0.GetBytes(buffer);

		// Convert to ulong and normalize to 0.0 - 1.0 range
		ulong randomValue = BitConverter.ToUInt64(buffer, 0);

		// Divide by max ulong value to get 0.0 - 1.0 range
		// Use double precision for better accuracy
		return (double)randomValue / ulong.MaxValue;
	}

	/// <summary>
	/// Generate a random decimal between 0.0 and 1.0 (exclusive)
	/// Higher precision version for critical drop calculations
	/// </summary>
	public static decimal NextDecimal()
	{
		// Use 16 bytes for maximum precision
		byte[] buffer = new byte[16];
		rngcryptoServiceProvider_0.GetBytes(buffer);

		// Create decimal from random bytes
		int[] bits = new int[4];
		bits[0] = BitConverter.ToInt32(buffer, 0);
		bits[1] = BitConverter.ToInt32(buffer, 4);
		bits[2] = BitConverter.ToInt32(buffer, 8);
		bits[3] = BitConverter.ToInt32(buffer, 12);

		// Clear sign bit and set scale to maximum precision
		bits[3] = (bits[3] & 0x7FFFFFFF) | (28 << 16); // 28 decimal places

		try
		{
			decimal randomDecimal = new decimal(bits);
			// Normalize to 0.0 - 1.0 range
			return randomDecimal / decimal.MaxValue;
		}
		catch
		{
			// Fallback to double conversion if decimal construction fails
			return (decimal)NextDouble();
		}
	}

	/// <summary>
	/// Generate a random double between min and max (inclusive)
	/// </summary>
	public static double NextDouble(double min, double max)
	{
		if (min >= max) return min;

		double range = max - min;
		return min + (NextDouble() * range);
	}

	/// <summary>
	/// Generate a random decimal between min and max (inclusive)
	/// </summary>
	public static decimal NextDecimal(decimal min, decimal max)
	{
		if (min >= max) return min;

		decimal range = max - min;
		return min + (NextDecimal() * range);
	}

	/// <summary>
	/// Test if a random roll succeeds against a given probability
	/// </summary>
	/// <param name="probability">Probability between 0.0 and 1.0</param>
	/// <returns>True if the roll succeeds</returns>
	public static bool TestProbability(double probability)
	{
		if (probability <= 0.0) return false;
		if (probability >= 1.0) return true;

		return NextDouble() < probability;
	}

	/// <summary>
	/// Test if a random roll succeeds against a given probability (decimal version)
	/// </summary>
	/// <param name="probability">Probability between 0.0 and 1.0</param>
	/// <returns>True if the roll succeeds</returns>
	public static bool TestProbability(decimal probability)
	{
		if (probability <= 0m) return false;
		if (probability >= 1m) return true;

		return NextDecimal() < probability;
	}
}
