﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using Newtonsoft.Json;
using RxjhServer.HelperTools;

namespace RxjhServer;

public partial class Players
{


    public void BachBao(byte[] PacketData, int PacketSize)
    {
        var array = new byte[4];
        System.Buffer.BlockCopy(PacketData, 10, array, 0, 1);
        switch (BitConverter.ToInt32(array, 0))
        {
            case 2:
                {
                    var array2 = Converter.HexStringToByte("AA5507007100D50001000255AA");
                    System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
                    Client?.Send_Map_Data(array2, array2.Length);
                    break;
                }
            case 1:
                {
                    var array2 = Converter.HexStringToByte("aa5500003d01d500080006010000603b000055aa");
                    System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
                    Client?.Send_Map_Data(array2, array2.Length);
                    break;
                }
        }
    }

    public void BachBaoNew(byte[] PacketData, int PacketSize) // 660
    {
        try
        {
            var typeByte = PacketData[10]; // Directly access the byte you're interested in
            int typeInt = typeByte; // Implicit conversion from byte to int
            if (typeInt != 3) throw new("BachBaoNew not 3");

            SendingClass packetDataClass = new();

            var noParentCount = 0;
            List<World.X_WebShop_Category> rowsWithoutParent = new();
            List<World.X_WebShop_Category> rowsWithParent = new();

            foreach (var row in World.WebShopCategoryList.Values)
                if (row.PARENTID == 0) // Checking for null parent
                {
                    noParentCount++;
                    rowsWithoutParent.Add(row);
                }
                else
                {
                    rowsWithParent.Add(row);
                }

            var index = 1;
            // Write the number of items with no parent
            //  LogHelper.WriteLine(LogLevel.Debug, "Total Parent " + noParentCount);
            packetDataClass.Write4(noParentCount);
            // Process items without parent
            foreach (var row in rowsWithoutParent)
            {
                packetDataClass.Write4(0);
                packetDataClass.Write4(index);
                packetDataClass.Write4(row.ID);
                index++;
                // packetDataClass.WriteString("Test",32);
                packetDataClass.WriteStringCut(Unitoccp1258(row.NAME), 32, Encoding.GetEncoding(1252));
            }

            // Process items with parent
            packetDataClass.Write4(rowsWithParent.Count);
            index = 1;
            foreach (var row in rowsWithParent)
            {
                packetDataClass.Write4(index);
                packetDataClass.Write4(row.ID);
                index++;
                packetDataClass.Write4(row.PARENTID);
                // packetDataClass.WriteString("Test",32);
                packetDataClass.WriteStringCut(Unitoccp1258(row.NAME), 32, Encoding.GetEncoding(1252));
            }
            packetDataClass.Write4(0);
            // Send packet 640
            Client?.SendPak(packetDataClass, 32770, SessionID);

            // Send Cash 653
            // 653
            UpdateCash();
        }
        catch (Exception Ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "BachBaoNew Error " + Ex.Message);
        }
    }

    public void UpdateCash()
    {
        var array2 = Converter.HexStringToByte("aa551800b5078d0210000100000000000000000000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_RXPIONT), 0, array2, 14, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_RXPIONTX), 0, array2, 18, 4);
        Client?.Send_Map_Data(array2, array2.Length);
    }

    public void AutoCheckOut(int auto = 0)
    {
        var res = Converter.HexStringToByte("aa5512009d0295020800030000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
        Buffer.BlockCopy(BitConverter.GetBytes(0), 0, res, 14, 2);
        Client?.Send_Map_Data(res, res.Length);

    }

    public void BachBaoNew_ShopData(byte[] PacketData, int PacketSize) // 641
    {
        //SendTest();
        //return;
        var check = 0;
        try
        {
            int typeInt = PacketData[10];
            int subInt = PacketData[14];

            var pageByte = PacketData[18];
            int pageInt = pageByte;

            var itemsPerPage = 12; // Items per page
            var offsetItem = pageInt * itemsPerPage;

            List<int> parentId = new()
            {
                typeInt
            };
            foreach (var row in World.WebShopCategoryList.Values)
            {
                if (subInt == 0)
                {
                    if (typeInt == row.PARENTID) parentId.Add(row.ID);
                }
                else if (subInt == row.ID)
                {
                    parentId.Add(row.ID);
                }
            }

            int requestType = BitConverter.ToInt16(PacketData, 6);


            List<X_Bach_Bao_Cac_Loai> items = new();
            switch (requestType)
            {
                case 645:
                    var searchQueryByte = new byte[14];
                    System.Buffer.BlockCopy(PacketData, 26, searchQueryByte, 0, 14);
                    var actualLength = Array.IndexOf(searchQueryByte, (byte)0);
                    if (actualLength == -1)
                        actualLength = 14;

                    var searchQuery = Encoding.ASCII.GetString(searchQueryByte, 0, actualLength).TrimEnd('\0');
                    var normalizedSearchQuery = NormalizeString(searchQuery);

                    foreach (var row in World.WebShopItemList.Values)
                    {
                        var normalizedName = NormalizeString(row.NAME);
                        var normalizedDesc = NormalizeString(row.DESC);

                        if (normalizedName.Contains(normalizedSearchQuery) ||
                            normalizedDesc.Contains(normalizedSearchQuery))
                            items.Add(row);
                    }

                    break;
                default:
                    foreach (var row in World.WebShopItemList.Values)
                        if (parentId.Contains(row.CATEGORY_ID))
                            items.Add(row);

                    break;
            }


            // Manual Pagination
            var totalItems = items.Count;
            var endIndex = Math.Min(offsetItem + itemsPerPage, totalItems);
            var totalPages = totalItems / itemsPerPage;
            var currentItemCount = endIndex - offsetItem; // Calculate the actual number of items in this page

            var packetDataClass = CreateCashShopList(typeInt, pageInt, items, offsetItem, endIndex, totalPages,
                currentItemCount);
            // 642
            Client?.SendPak(packetDataClass, 33282, SessionID);
            AutoCheckOut(0);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "BachBaoNew Error offset" + check + " " + ex.Message);
        }
    }


    private SendingClass CreateCashShopList(int typeInt, int pageInt, List<X_Bach_Bao_Cac_Loai> items,
        int startIndex, int endIndex, int totalPages, int currentItemCount)
    {
        SendingClass packetDataClass = new();
        packetDataClass.Write4(typeInt);
        packetDataClass.Write4(0);
        packetDataClass.Write4(pageInt);
        packetDataClass.Write4(totalPages); // Total number of items without pagination
        packetDataClass.Write4(currentItemCount); // Correctly set the number of items on this page
        for (var i = startIndex; i < endIndex; i++)
        {
            var row = items[i];
            try
            {
                var PRODUCT_CODE = row.ID.ToString();
                var FLD_ID = row.PID;
                var FLD_NAME = Unitoccp1258(row.NAME);
                var FLD_TYPE = Convert.ToInt32(row.CATEGORY_ID);
                var FLD_PRICE = Convert.ToInt32(row.PRICE);

                var FLD_OLD_PRICE = DBNull.Value.Equals(row.PRICE_OLD) ? FLD_PRICE : Convert.ToInt32(row.PRICE_OLD);
                var FLD_NUMBER = Convert.ToInt32(row.NUMBER);

                packetDataClass.WriteStringCut(PRODUCT_CODE, 16);
                packetDataClass.Write4(FLD_ID);
                packetDataClass.Write4(0);
                packetDataClass.WriteStringCut(FLD_NAME, 32, Encoding.GetEncoding(1252));
                packetDataClass.Write4(FLD_NUMBER);
                packetDataClass.Write4(FLD_OLD_PRICE);
                packetDataClass.Write4(FLD_PRICE);
                packetDataClass.Write4(FLD_TYPE);
                packetDataClass.Write4(0); //sub type
                packetDataClass.Write(new byte[84]);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing row {i}: {ex.Message}");
            }
        }

        return packetDataClass;
    }

    public static string NormalizeString(string text)
    {
        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();

        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark) stringBuilder.Append(c);
        }

        // Remove non-alphanumeric characters and convert to lowercase
        var alphanumericString = Regex.Replace(stringBuilder.ToString(), "[^a-zA-Z0-9]", string.Empty);
        return alphanumericString.Normalize(NormalizationForm.FormC).ToLower();
    }

    public void BachBaoNew_ItemDetail(byte[] PacketData, int PacketSize)
    {
        try
        {
            var productIdByte = new byte[14];
            System.Buffer.BlockCopy(PacketData, 10, productIdByte, 0, 14);
            var productId = Encoding.ASCII.GetString(productIdByte).TrimEnd('\0');
            World.WebShopItemList.TryGetValue(productId, out var row);
            var itemName = Unitoccp1258(row.NAME);
            var itemDesc = Unitoccp1258(row.DESC);

            var FLD_PRICE = Convert.ToInt32(row.PRICE);
            var FLD_OLD_PRICE = row.PRICE_OLD != 0 ? Convert.ToInt32(row.PRICE_OLD) : FLD_PRICE;
            var FLD_NUMBER = Convert.ToInt32(row.NUMBER);
            var CATEGORY_ID = Convert.ToInt32(row.CATEGORY_ID);

            SendingClass packetDataClass = new();
            packetDataClass.WriteStringCut(productId, 16);
            packetDataClass.Write4(row.PID);
            packetDataClass.Write4(0);
            packetDataClass.WriteStringCut(itemName, 32, Encoding.GetEncoding(1252));
            packetDataClass.WriteStringCut(itemDesc, 4096, Encoding.GetEncoding(1252));

            packetDataClass.Write4(FLD_NUMBER);
            packetDataClass.Write4(FLD_OLD_PRICE);
            packetDataClass.Write4(FLD_PRICE);
            packetDataClass.Write4(CATEGORY_ID);
            packetDataClass.Write4(CATEGORY_ID); // Số lượng
            packetDataClass.Write2(0);
            packetDataClass.Write1(0);
            packetDataClass.Write1(1);
            packetDataClass.Write(new byte[91]);

            Client?.SendPak(packetDataClass, 33794, SessionID);
        }
        catch (Exception Ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "BachBaoNew_ItemDetail Error: " + Ex.Message);
        }
    }

    public void CoinShopRequest(byte[] packetData, int size)
    {
        // var res = Converter.HexStringToByte("aa5516009d023f050c00decf9a3b000000000000000055aa");
        // System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, res, 4, 2);
        // System.Buffer.BlockCopy(BitConverter.GetBytes(World.CoinCurrency), 0, res, 10, 4);
        // Client?.Send_Map_Data(res, res.Length);
        SendingClass w = new();
        w.Write4(World.CoinCurrency);
        w.Write4(0);
        w.Write4(GoldCoin);
        Client?.SendPak(w, 16133, SessionID);
    }

    public async void BachBaonew_BuyItem(byte[] PacketData, int PacketSize)
    {
        // Type 647
        try
        {

            var typeByte = PacketData[10];
            int amount = typeByte;
            if (amount <= 0)
            {
                HeThongNhacNho("Không thể mua ít hơn 1 số lượng!");
                Send652();
                throw new($"Không thể mua ít hơn 1 số lượng {CharacterName}");
            }

            var productIdByte = new byte[14];
            System.Buffer.BlockCopy(PacketData, 14, productIdByte, 0, 14);
            var productId = Encoding.ASCII.GetString(productIdByte).TrimEnd('\0').Replace(" ", "");
            if (!World.WebShopItemList.TryGetValue(productId, out var item))
            {
                HeThongNhacNho("!Không tìm thấy vật phẩm cần mua! Liên hệ Admin!!!");
                throw new($"!Không tìm thấy vật phẩm cần mua! {CharacterName}");
            }
            var beforeCash = FLD_RXPIONT;
            var coinItem = FindItemByItemID(World.CoinCurrency);
            if (item.CATEGORY_ID == 10)
            {
                if (coinItem == null)
                {
                    sendCashShopMessage(0x45);
                    return;
                }
                coinItem.Lock_Move = true;
                beforeCash = coinItem.GetVatPhamSoLuong;
            }
            var total = amount * item.PRICE;
            if (beforeCash < total)
            {
                //SendNotificationPopUp($"!Không đủ Cash! Cần : {total} Cash");
                HeThongNhacNho($"!Không đủ Cash! Cần : {total} Cash", 7, "CASHSHOP");
                sendCashShopMessage(0x45);
                return;
            }

            // Get Empty Inventory Slot
            var parcelVacancy = GetParcelVacancy(this);
            if (parcelVacancy == -1)
            {
                HeThongNhacNho("Thùng đồ không còn chỗ trống");
                sendCashShopMessage(0x19);
                throw new("Thùng đồ không còn slot trống");
            }


            var FLD_MAGIC0 = item.MAGIC0;
            switch (item.PID)
            {
                case ItemDef.Item.HanNgocThachRandom:
                    FLD_MAGIC0 = RNG.Next(200002, 200010);
                    break;
                case ItemDef.Item.KimCuongThachRandom:
                    FLD_MAGIC0 = RNG.Next(100002, 100010);
                    break;
                case ItemDef.Item.SoCapKyNgocThach:
                    FLD_MAGIC0 = RNG.Next(1, 22);
                    break;
                case ItemDef.Item.TrungCapKyNgocThach:
                    FLD_MAGIC0 = RNG.Next(23, 51);
                    break;
                case ItemDef.Item.CaoCapKyNgocThach:
                    FLD_MAGIC0 = RNG.Next(70, 81);
                    break;
                case ItemDef.Item.KimCuongThach_DaKich:
                    FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                    break;
                case ItemDef.Item.KimCuongThach_VoCong:
                    FLD_MAGIC0 = RNG.Next(700002, 700010);
                    break;
                case ItemDef.Item.ThuocTinhThachRandom:
                    FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                    break;
            }

            CheckTheNumberOfIngotsInBaibaoge();
            if (item.CATEGORY_ID == 10)
            {
                if (beforeCash - total > 0)
                {
                    coinItem.VatPhamSoLuong = BitConverter.GetBytes(beforeCash - total);
                }
                else
                {
                    // Xóa item vàng nếu không còn
                    coinItem.VatPham_byte = new byte[World.Item_Db_Byte_Length];
                }
                Init_Item_In_Bag();
            }
            else
            {
                KiemSoatNguyenBao_SoLuong(total, 0);
                Save_NguyenBaoData();
            }
            var res = await BBGDb.InsertCashShopLog("REQUEST", item.NAME, CharacterName, amount, total, item.ID, productId);
            if (res == -1)
            {
                HeThongNhacNho("Có lỗi xảy ra khi mua vật phẩm.Vui lòng liên hệ ADMIN");
                Send652();
                return;
            }

            if (amount > 1)
            {
                for (var i = 0; i < amount; i++)
                {
                    var parcelVacancy2 = GetParcelVacancy(this);
                    FLD_MAGIC0 = item.PID switch
                    {
                        ItemDef.Item.HanNgocThachRandom => RNG.Next(200002, 200010),
                        ItemDef.Item.KimCuongThachRandom => RNG.Next(100002, 100010),
                        ItemDef.Item.SoCapKyNgocThach => RNG.Next(1, 22),
                        ItemDef.Item.TrungCapKyNgocThach => RNG.Next(23, 51),
                        ItemDef.Item.CaoCapKyNgocThach => RNG.Next(70, 81),
                        ItemDef.Item.KimCuongThach_DaKich => RNG.Next(1000002, 1000010),
                        ItemDef.Item.KimCuongThach_VoCong => RNG.Next(700002, 700010),
                        ItemDef.Item.ThuocTinhThachRandom => int.Parse("200" + RNG.Next(0, 6) + "000"),
                        _ => FLD_MAGIC0
                    };

                    AddItem_ThuocTinh_int(item.PID, parcelVacancy2, 1, FLD_MAGIC0, item.MAGIC1, item.MAGIC2,
                        item.MAGIC3, item.MAGIC4, item.ThucTinh, item.TrungCapHon, item.TienHoa, item.KhoaLai,
                        item.NgaySuDung);
                    // Working
                    UpdateCash();
                    send648();
                }
            }
            else
            {
                AddItem_ThuocTinh_int(item.PID, parcelVacancy, 1, FLD_MAGIC0, item.MAGIC1, item.MAGIC2,
                    item.MAGIC3, item.MAGIC4, item.ThucTinh, item.TrungCapHon, item.TienHoa, item.KhoaLai,
                    item.NgaySuDung);
                HeThongNhacNho(
                    "Nhận thành công vật phẩm [" + ItmeClass.DatDuocVatPhamTen_XungHao(item.PID) + "]", 7, "Thiên cơ các");
                // Working
                UpdateCash();
                send648();
            }

            //RxjhClass.UpdateBachBaoNewRecord(res, "SUCCESS");
            await BBGDb.UpdateCashShopLog(res, "SUCCESS");
            UpdateCharacterData(this);
            //35842
        }
        catch (Exception Ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "BachBaonew_BuyItem Error: " + Ex.Message);
        }
    }

    public async Task BachBaonew_BuyMultiItem(byte[] PacketData, int PacketSize)
    {
        try
        {
            int totalAmount = 0;
            var beforeCash = FLD_RXPIONT;
            int totalBuyCash = 0;
            var packet = Converter.HexStringToByte("AA550000A10000009D02D200BF280000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
            for (var i = 0; i < 10; i++)
            {
                var offset = 10 + i * 20;
                var amount = PacketData[offset];
                totalAmount += amount;
                if (amount <= 0)
                {
                    //packet.Write(new byte[20]);
                    continue;
                }
                var productIdByte = new byte[offset + 4];
                System.Buffer.BlockCopy(PacketData, offset + 4, productIdByte, 0, 14);
                System.Buffer.BlockCopy(PacketData, offset + 4, packet, 16 + i * 20, 14);
                var productId = Encoding.ASCII.GetString(productIdByte).TrimEnd('\0');

                if (!World.WebShopItemList.TryGetValue(productId, out var item))
                {
                    HeThongNhacNho("!Không tìm thấy vật phẩm cần mua! Liên hệ Admin!!!");
                    throw new Exception($"!Không tìm thấy vật phẩm cần mua! {CharacterName}");
                }
                totalBuyCash += amount * item.PRICE;
            }
            if (totalAmount > GetParcelVacancyNumber())
            {
                System.Buffer.BlockCopy(BitConverter.GetBytes(0x19), 0, packet, 12, 1);
                Client?.Send(packet, packet.Length);
                return;
            }
            if (totalBuyCash > beforeCash)
            {
                HeThongNhacNho($"Cần {totalBuyCash}@ mới có thể mua vật phẩm", 10, "Cashshop");
                sendCashShopMessage(0x45);
                //System.Buffer.BlockCopy(BitConverter.GetBytes(0x45), 0, packet, 12, 1);
                //Client?.Send(packet, packet.Length);
                return;
            }
            System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, packet, 12, 2);
            SendingClass response = new();
            for (var i = 0; i < 10; i++)
            {
                var offset = 10 + i * 20;
                var amount = PacketData[offset];
                if (amount <= 0)
                {
                    response.Write(new byte[21]);
                    continue;
                }
                response.Write4(1);
                var productIdByte = new byte[offset + 4];
                System.Buffer.BlockCopy(PacketData, offset + 4, productIdByte, 0, 14);
                var productId = Encoding.ASCII.GetString(productIdByte).TrimEnd('\0');
                response.WriteString(productId, 17);
                await CashShop_BuyAnItem(amount, productId);
            }
            System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 4, 2);
            //Client?.SendPacketX(response, 669, CharacterFullServerID);
            Client?.SendPak(response, 40194, SessionID);
            //send648();
            //Client?.Send(packet, packet.Length);
            UpdateCash();
            //sendCashShopMessage(0x45);
            UpdateCharacterData(this);
        }
        catch (Exception e)
        {
            LogHelper.WriteLine(LogLevel.Error, "CashShop Buy Multi item error " + e.Message);
        }
    }

    private async Task<bool> CashShop_BuyAnItem(int amount, string productId)
    {
        try
        {
            var beforeCash = FLD_RXPIONT;

            if (!World.WebShopItemList.TryGetValue(productId, out var item))
            {
                HeThongNhacNho("!Không tìm thấy vật phẩm cần mua! Liên hệ Admin!!!");
                throw new Exception($"!Không tìm thấy vật phẩm cần mua! {CharacterName}");
            }

            return await PaymentByCash(amount, productId, item);
        }
        catch (Exception e)
        {
            LogHelper.WriteLine(LogLevel.Error, "CashShop_BuyAnTiem Error " + e.Message);
            return false;
        }
    }
    private async Task<bool> PaymentByCash(int amount, string productId, X_Bach_Bao_Cac_Loai item)
    {
        var total = amount * item.PRICE;
        if (FLD_RXPIONT < total)
        {
            HeThongNhacNho($"!Không đủ TCoin! Cần : {total} TCoin", 7, "CASHSHOP");
            sendCashShopMessage(0x45);
            return false;
        }

        var parcelVacancy = GetParcelVacancy(this);
        if (parcelVacancy == -1)
        {
            HeThongNhacNho("Thùng đồ không còn chỗ trống");
            sendCashShopMessage(0x19);
            throw new Exception("Thùng đồ không còn slot trống");
        }

        var totalSpace = GetParcelVacancyNumber();

        if (totalSpace < amount)
        {
            HeThongNhacNho($"Cần {amount} chỗ trống trong túi đồ");
            sendCashShopMessage(0x19);
            throw new Exception("Túi đồ không còn slot trống");
        }

        var FLD_MAGIC0 = item.MAGIC0;
        switch (item.PID)
        {
            case ItemDef.Item.HanNgocThachRandom:
                FLD_MAGIC0 = RNG.Next(200002, 200010);
                break;
            case ItemDef.Item.KimCuongThachRandom:
                FLD_MAGIC0 = RNG.Next(100002, 100010);
                break;
            case ItemDef.Item.SoCapKyNgocThach:
                FLD_MAGIC0 = RNG.Next(1, 22);
                break;
            case ItemDef.Item.TrungCapKyNgocThach:
                FLD_MAGIC0 = RNG.Next(23, 51);
                break;
            case ItemDef.Item.CaoCapKyNgocThach:
                FLD_MAGIC0 = RNG.Next(70, 81);
                break;
            case ItemDef.Item.KimCuongThach_DaKich:
                FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                break;
            case ItemDef.Item.KimCuongThach_VoCong:
                FLD_MAGIC0 = RNG.Next(700002, 700010);
                break;
            case ItemDef.Item.ThuocTinhThachRandom:
                FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                break;
        }

        CheckTheNumberOfIngotsInBaibaoge();
        KiemSoatNguyenBao_SoLuong((int)total, 0);
        Save_NguyenBaoData();
        var res = await BBGDb.InsertCashShopLog("REQUEST", item.NAME, CharacterName, amount, total, item.ID, productId);
        if (res == -1)
        {
            HeThongNhacNho("Có lỗi xảy ra khi mua vật phẩm.Vui lòng liên hệ ADMIN");
            Send652();
            return false;
        }

        if (amount > 1)
        {
            for (var i = 0; i < amount; i++)
            {
                var parcelVacancy2 = GetParcelVacancy(this);
                FLD_MAGIC0 = item.PID switch
                {
                    ItemDef.Item.HanNgocThachRandom => RNG.Next(200002, 200010),
                    ItemDef.Item.KimCuongThachRandom => RNG.Next(100002, 100010),
                    ItemDef.Item.SoCapKyNgocThach => RNG.Next(1, 22),
                    ItemDef.Item.TrungCapKyNgocThach => RNG.Next(23, 51),
                    ItemDef.Item.CaoCapKyNgocThach => RNG.Next(70, 81),
                    ItemDef.Item.KimCuongThach_DaKich => RNG.Next(1000002, 1000010),
                    ItemDef.Item.KimCuongThach_VoCong => RNG.Next(700002, 700010),
                    ItemDef.Item.ThuocTinhThachRandom => int.Parse("200" + RNG.Next(0, 6) + "000"),
                    _ => FLD_MAGIC0
                };

                AddItem_ThuocTinh_int(item.PID, parcelVacancy2, 1, FLD_MAGIC0, item.MAGIC1, item.MAGIC2,
                    item.MAGIC3, item.MAGIC4, item.ThucTinh, item.TrungCapHon, item.TienHoa, item.KhoaLai,
                    item.NgaySuDung);
            }
        }
        else
        {
            AddItem_ThuocTinh_int(item.PID, parcelVacancy, 1, FLD_MAGIC0, item.MAGIC1, item.MAGIC2,
                item.MAGIC3, item.MAGIC4, item.ThucTinh, item.TrungCapHon, item.TienHoa, item.KhoaLai,
                item.NgaySuDung);
            HeThongNhacNho(
                "Nhận thành công vật phẩm [" + ItmeClass.DatDuocVatPhamTen_XungHao(item.PID) + "]");
        }

        await BBGDb.UpdateCashShopLog(res, "SUCCESS");
        return true;
    }

    public async void BachBaonew_History_Grid(byte[] data)
    {
        try
        {
            // Get page number from packet data (0-based)
            var pageInt = data[0x12];

            // Get cash shop successful purchases using BBGDb with pagination
            var gridResult = await BBGDb.GetCashShopSuccessfulPurchases(CharacterName, pageInt, 12);

            // Create packet data
            SendingClass packetDataClass = new();
            packetDataClass.Write4(0);
            packetDataClass.Write4(0);
            packetDataClass.Write4(pageInt);
            packetDataClass.Write4(gridResult.TotalPages); // Total pages
            packetDataClass.Write4(gridResult.CurrentItemCount); // Items on current page

            // Write item data
            foreach (var item in gridResult.Data)
            {
                try
                {
                    packetDataClass.WriteStringCut(item.ProductCode, 16, Encoding.ASCII);
                    packetDataClass.Write4(item.ItemId);
                    packetDataClass.Write4(0);
                    packetDataClass.WriteStringCut(Unitoccp1258(item.ItemName), 32, Encoding.GetEncoding(1252));
                    packetDataClass.Write4(item.Number);
                    packetDataClass.Write4(item.OldPrice);
                    packetDataClass.Write4(item.Price);
                    packetDataClass.Write4(item.ItemType);
                    packetDataClass.Write4(0); // sub type
                    packetDataClass.Write(new byte[84]);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error processing grid item {item.ProductCode}: {ex.Message}");
                }
            }

            // Send packet (642)
            Client?.SendPak(packetDataClass, 36866, SessionID);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"BachBaonew_History_Grid Error: {ex.Message}");
        }
    }

    public async void BachBaoNew_History(byte[] PacketData)
    {
        var offset = 2;
        try
        {
            int page = PacketData[0x68 - offset];
            int date = PacketData[0x64 - offset];
            var typeByte = new byte[16];
            System.Buffer.BlockCopy(PacketData, 0xC - offset, typeByte, 0, 16);

            var commandByte = new byte[16];
            System.Buffer.BlockCopy(PacketData, 0x20 - offset, commandByte, 0, 16);

            var type = Encoding.GetEncoding(1252)
                .GetString(typeByte)
                .TrimEnd('\0')
                .Trim()
                .ToLower();
            var command = Encoding.GetEncoding(1252).GetString(commandByte).TrimEnd('\0')
                .Trim()
                .ToLower();

            LogHelper.WriteLine(LogLevel.Error, $"{type} {command} {page} {date}");
            byte[] array;
            switch (type)
            {
                case "myshop":
                    // LogHelper.WriteLine(LogLevel.Error, "Send Myshop");
                    switch (command)
                    {
                        case "buy":
                            // Get cash shop purchase history using BBGDb
                            var purchaseHistory = await BBGDb.GetCashShopPurchaseHistory(CharacterName, date, page, 10);

                            // Convert to the format expected by the client
                            var formatedData = new List<Dictionary<string, object>>();
                            foreach (var purchase in purchaseHistory.Data)
                            {
                                var rowDict = new Dictionary<string, object>
                                {
                                    { "rownum", purchase.RowNum },
                                    { "item_name", purchase.ItemName },
                                    { "item_cnt", purchase.ItemCount },
                                    { "buycash", purchase.BuyCash },
                                    { "buydate", purchase.BuyDate },
                                    { "cashtype", purchase.CashType },
                                    { "dtype", purchase.DType },
                                    { "state", purchase.State },
                                    { "buynum", purchase.BuyNum }
                                };
                                formatedData.Add(rowDict);
                            }

                            var jsonResult = JsonConvert.SerializeObject(new { data = formatedData });
                            SendingClass packet = new();
                            packet.Write4(0);
                            packet.Write(new byte[32]);
                            packet.Write4(0);
                            packet.Write4(page);
                            packet.Write4(date);
                            packet.Write1(0x52);
                            packet.Write1(0x6);
                            packet.Write2(0);
                            packet.Write4(purchaseHistory.TotalCount); // total item
                            packet.Write(Encoding.GetEncoding(1252).GetBytes(jsonResult));
                            Client?.SendPak(packet, 40706, SessionID);

                            break;
                    }

                    break;
                case "present":
                    LogHelper.WriteLine(LogLevel.Error, "Send present");
                    array = Converter.HexStringToByte(
                        "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");
                    System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
                    Client?.Send_Map_Data(array, array.Length);
                    break;
                default:
                    LogHelper.WriteLine(LogLevel.Error, $"Default {type}");
                    break;
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "Bachbaonew_History Error: " + ex.Message);
        }
    }

    public async void CheckTheNumberOfIngotsInBaibaoge()
    {
        // var text =
        //     "SELECT FLD_SEX, FLD_RXPIONT, FLD_RXPIONTX, FLD_VIP, FLD_VIPTIM, FLD_COIN FROM [TBL_ACCOUNT] WHERE FLD_ID = @Userid";
        // var sqlParameter_ = new SqlParameter[1]
        // {
        //     new("@Userid", SqlDbType.VarChar, 30) { Value = AccountID }
        // };
        // var dBToDataTable = DBA.GetDBToDataTable(text, sqlParameter_, "rxjhaccount");
        var account = await AccountDb.FindAccount(AccountID);
        FLD_RXPIONT = Math.Max(0, (int)account.fld_rxpiont);
        FLD_RXPIONTX = Math.Max(0, (int)account.fld_rxpiontx);
        FLD_Coin = Math.Max(0, (int)account.fld_coin);
        //if (FLD_RXPIONT > World.GioiHan_TongSoNguyenBao_1TaiKhoan)
        //{
        //    LogHelper.WriteLine(LogLevel.Debug,
        //        "Tổng số Cash của người chơi vượt quá Thiên cơ các cho phép CaoNhat[" + Userid + "]-[" +
        //        UserName + "]  [Cash tổng cộng：" + FLD_RXPIONT + "] [Thiên cơ các cho phép CaoNhatSoLuong：" +
        //        World.GioiHan_TongSoNguyenBao_1TaiKhoan + "]");
        //    switch (World.HoatDong_PhatHienPhoi)
        //    {
        //        case 2:
        //            Title(112, Userid, "CashVuotQuaSoLuong");
        //            break;
        //        case 1:
        //            FLD_RXPIONT = 0;
        //            Save_NguyenBaoData();
        //            break;
        //    }
        //}
    }

    public string PakTreasureCourtBuyAndSellThings(int VatPham_ID, int VatPhamSoLuong, int yuanbao, int LoaiHinh,
        int FLD_MAGIC0, int FLD_MAGIC1, int FLD_MAGIC2, int FLD_MAGIC3, int FLD_MAGIC4, int TrungCapHon, int ThucTinh,
        int TienHoa, int KhoaLai, int NgaySuDung)
    {
        try
        {
            var fLD_RXPIONT = FLD_RXPIONT;
            //if (World.Cho_Phep_Mo_Class_Den_So_Luong <= 0)
            //{
            //    HeThongNhacNho("Máy chủ tạm khóa mua vật phẩm!", 7);
            //    return "KhoaMua";
            //}

            if (!World.BachBaoCat_ThuocTinhVatPhamClassList.TryGetValue(VatPham_ID, out var value)) return "购买错误";

            if (value.PID != VatPham_ID)
            {
                LogHelper.WriteLine(LogLevel.Debug,
                    "Sửa đổi bất hợp pháp PacketTitle 5_Baobaoge MuaBan[" + AccountID + "][" + CharacterName +
                    "]  CharacterFullServerID=[" + SessionID + "]  [" + Client + "]");
                if (Client != null)
                {
                    // logo.kickid("Disconnect: 102");
                    Client.Dispose();
                }

                return "你已经被封号请联系管理";
            }

            if (value.TYPE != LoaiHinh)
            {
                LogHelper.WriteLine(LogLevel.Debug,
                    "Sửa đổi bất hợp pháp PacketTitle 3_Baobaoge MuaBan[" + AccountID + "][" + CharacterName +
                    "]  CharacterFullServerID=[" + SessionID + "]  [" + Client + "]");
                if (Client != null)
                {
                    // logo.kickid("Disconnect: 103");
                    Client.Dispose();
                }

                return "你已经被封号请联系管理";
            }

            if (VatPhamSoLuong == 1)
            {
                if (yuanbao != value.PRICE)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        "Sửa đổi bất hợp pháp PacketTitle 2_Baobaoge MuaBan[" + AccountID + "][" + CharacterName +
                        "]  CharacterFullServerID=[" + SessionID + "]  [" + Client +
                        "]");
                    if (Client != null)
                    {
                        // logo.kickid("Disconnect: 104");
                        Client.Dispose();
                    }

                    return "购买错误";
                }
            }
            else if (yuanbao != value.PRICE * VatPhamSoLuong)
            {
                LogHelper.WriteLine(LogLevel.Debug,
                    "Sửa đổi bất hợp pháp PacketTitle 1_Baobaoge MuaBan[" + AccountID + "][" + CharacterName +
                    "]  CharacterFullServerID=[" + SessionID + "]  [" + Client + "]");
                if (Client != null)
                {
                    // logo.kickid("Disconnect: 105");
                    Client.Dispose();
                }

                return "购买错误";
            }

            if (VatPhamSoLuong >= 1 && yuanbao >= 0)
            {
                var parcelVacancy = GetParcelVacancy(this);
                if (parcelVacancy == -1) return "没有空位了";

                CheckTheNumberOfIngotsInBaibaoge();
                if ((VatPhamSoLuong >= 1 && yuanbao > 0 && FLD_RXPIONT >= yuanbao) ||
                    (VatPhamSoLuong >= 1 && yuanbao > 0 && FLD_RXPIONTX >= yuanbao) ||
                    (VatPhamSoLuong >= 1 && yuanbao > 0 && FLD_Coin >= yuanbao))
                {
                    switch (VatPham_ID)
                    {
                        case ItemDef.Item.HanNgocThachRandom:
                            FLD_MAGIC0 = RNG.Next(200002, 200010);
                            break;
                        case ItemDef.Item.KimCuongThachRandom:
                            FLD_MAGIC0 = RNG.Next(100002, 100010);
                            break;
                        case ItemDef.Item.SoCapKyNgocThach:
                            FLD_MAGIC0 = RNG.Next(1, 22);
                            break;
                        case ItemDef.Item.TrungCapKyNgocThach:
                            FLD_MAGIC0 = RNG.Next(23, 51);
                            break;
                        case ItemDef.Item.CaoCapKyNgocThach:
                            FLD_MAGIC0 = RNG.Next(70, 81);
                            break;
                        case ItemDef.Item.KimCuongThach_DaKich:
                            FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                            break;
                        case ItemDef.Item.KimCuongThach_VoCong:
                            FLD_MAGIC0 = RNG.Next(700002, 700010);
                            break;
                        case ItemDef.Item.ThuocTinhThachRandom:
                            FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                            break;
                    }

                    if (VatPhamSoLuong > 1)
                    {
                        for (var i = 0; i < VatPhamSoLuong; i++)
                        {
                            var parcelVacancy2 = GetParcelVacancy(this);
                            switch (VatPham_ID)
                            {
                                case 800000012:
                                    FLD_MAGIC0 = RNG.Next(200002, 200010);
                                    break;
                                case 800000011:
                                    FLD_MAGIC0 = RNG.Next(100002, 100010);
                                    break;
                                case ItemDef.Item.SoCapKyNgocThach:
                                    FLD_MAGIC0 = RNG.Next(1, 22);
                                    break;
                                case ItemDef.Item.TrungCapKyNgocThach:
                                    FLD_MAGIC0 = RNG.Next(23, 51);
                                    break;
                                case ItemDef.Item.CaoCapKyNgocThach:
                                    FLD_MAGIC0 = RNG.Next(70, 81);
                                    break;
                                case 800000025:
                                    FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                                    break;
                                case ItemDef.Item.KimCuongThach_VoCong:
                                    FLD_MAGIC0 = RNG.Next(700002, 700010);
                                    break;
                                case ItemDef.Item.ThuocTinhThachRandom:
                                    FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                                    break;
                            }

                            AddItem_ThuocTinh_int(VatPham_ID, parcelVacancy2, 1, FLD_MAGIC0, FLD_MAGIC1,
                                FLD_MAGIC2, FLD_MAGIC3, FLD_MAGIC4, ThucTinh, TrungCapHon, TienHoa, KhoaLai,
                                NgaySuDung);
                        }
                    }
                    else
                    {
                        AddItem_ThuocTinh_int(VatPham_ID, parcelVacancy, 1, FLD_MAGIC0, FLD_MAGIC1, FLD_MAGIC2,
                            FLD_MAGIC3, FLD_MAGIC4, ThucTinh, TrungCapHon, TienHoa, KhoaLai, NgaySuDung);
                        HeThongNhacNho(
                            "Nhận thành công vật phẩm [" + ItmeClass.DatDuocVatPhamTen_XungHao(VatPham_ID) + "]",
                            7, "Thiên cơ các");
                    }

                    Save_NguyenBaoData();
                    return "PurchaseSuccessful";
                }

                return "元宝不够了";
            }

            return "购买错误";
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error,
                "PakTreasureCourtBuyAndSellThings  error  [" + AccountID + "][" + CharacterName + "]  " + ex.Message);
            return "购买错误";
        }
    }

    public void Send652_2()
    {
        var array = Converter.HexStringToByte("aa55000099058c020c008802000000000000bf28000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void send648()
    {
        var array = Converter.HexStringToByte("aa5500009e018802150001000000f05989c170140000287c90d0721400000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void sendCashShopMessage(int type)
    {
        // type 0x45 : Khong du cash
        var array = Converter.HexStringToByte("aa550000b4018c020c0088020000000000004528000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 0x12, 1);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void Send652()
    {
        var array = Converter.HexStringToByte("aa55000099058c020c008802000000000000bf28000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void Send652_NoEmptySlot()
    {
        // "aa5500009e0100008c020c0088020000000000001928000055aa";
        var array = Converter.HexStringToByte("aa5500009e018c020c0088020000000000001928000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void SendNotificationPopUp(string message)
    {
        try
        {
            var array = Converter.HexStringToByte(
                "AA55BC0000006600B4000A003A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FFFFFFFF6500000000000000000000000000000055AA");
            System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
            var messageByte = Encoding.GetEncoding(1252).GetBytes(Unitoccp1258(message));
            System.Buffer.BlockCopy(messageByte, 0, array, 34, message.Length);
            Client?.Send_Map_Data(array, array.Length);
        }
        catch (Exception Ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "Failed to send notification " + Ex.Message);
        }
    }


}