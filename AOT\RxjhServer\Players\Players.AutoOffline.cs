using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    

	public static int Auto_Attack_PhamVi_NPC(Players play, float float0, float float1)
	{
		try
		{
			if (play.NearbyNpcs != null)
			{
				List<NpcClass> list = new();
				foreach (var value in play.NearbyNpcs.Values)
				{
					if (PhamVi_CongKich_NPC(play, World.Offline_TreoMay_PhamVi, value, float0, float1) && !value.NPCDeath && value.IsNpc != 1 && value.Rxjh_HP >= 1L && !value.NPCDeath)
					{
						list.Add(value);
					}
				}
				if (list.Count > 0)
				{
					var result = 0;
					var num = World.Offline_TreoMay_PhamVi;
					foreach (var item in list)
					{
						var num2 = item.Rxjh_X - play.PosX;
						var num3 = item.Rxjh_Y - play.PosY;
						var num4 = (float)Math.Sqrt(num2 * num2 + num3 * num3);
						if ((int)num4 < num)
						{
							num = (int)num4;
							result = item.NPC_SessionID;
						}
					}
					list.Clear();
					return result;
				}
				list.Clear();
			}
			return play.OffLine_TreoMay_CongKich_QuaiVat;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Auto Offline CongKich NPC MucTieu   error：" + ex.ToString());
			return 0;
		}
	}

	public static void Auto_PickUp_Item_Packet(Players playe, long dmwpid)
	{
		var text = "AA5517002C010B000800C676600000000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(dmwpid), 0, array, 10, 8);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array, 4, 2);
		playe.PickUpItems(array, array.Length);
	}

	public static long Auto_TuDong_Nhat_VatPham(Players player)
	{
		var result = 0L;
		List<GroundItem> list = new();
		Random random = new();
		foreach (var value in World.GroundItemList.Values)
		{
			try
			{
				if (player.FindGroundItems(100, value) && value.ItemPriority != null && value.ItemPriority.CharacterName == player.CharacterName)
				{
					list.Add(value);
				}
			}
			catch (Exception)
			{
				throw;
			}
		}
		if (list.Count > 0)
		{
			var index = random.Next(0, list.Count);
			result = list[index].Item.GetItemGlobal_ID;
			list.Clear();
			return result;
		}
		list.Clear();
		return result;
	}

	public static bool PhamVi_CongKich_NPC(Players player, int int75, NpcClass npcClass0, float float0, float float1)
	{
		if (npcClass0.Rxjh_Map != player.MapID)
		{
			return false;
		}
		if (player.MapID == 7101)
		{
			int75 = 1000;
		}
		var num = npcClass0.Rxjh_X - float0;
		var num2 = npcClass0.Rxjh_Y - float1;
		float num3 = (int)Math.Sqrt(num * num + num2 * num2);
		return num3 <= int75;
	}
	public int Auto_Attack_NPC_Target(Players player, float float0, float float1)
	{
		if (player.NearbyNpcs != null)
		{
			var num = 50;
			List<NpcClass> list = new();
			foreach (var value in player.NearbyNpcs.Values)
			{
				if (Auto_PhamVi_Attack_NPC(player, num, value, float0, float1) && !value.NPCDeath && value.IsNpc != 1 && value.Rxjh_HP > 0)
				{
					list.Add(value);
				}
			}
			if (list.Count > 0)
			{
				var result = 0;
				foreach (var item in list)
				{
					var num2 = item.Rxjh_X - player.PosX;
					var num3 = item.Rxjh_Y - player.PosY;
					var num4 = (float)Math.Sqrt(num2 * num2 + num3 * num3);
					if ((int)num4 < num)
					{
						num = (int)num4;
						result = item.NPC_SessionID;
					}
				}
				list.Clear();
				return result;
			}
			list.Clear();
		}
		return 0;
	}

	public long OfflineAutoNhatItem()
	{
		try
		{
			var result = 0L;
			List<GroundItem> list = new();
			Random random = new();
			foreach (var value in World.GroundItemList.Values)
			{
				try
				{
					if (FindGroundItems(50, value) && value.ItemPriority != null && value.ItemPriority.CharacterName == CharacterName)
					{
						list.Add(value);
					}
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Lỗi TraTim PhamVi Drop VatPham - " + ex.Message);
					throw;
				}
			}
			if (list.Count > 0)
			{
				var index = random.Next(0, list.Count);
				result = list[index].Item.GetItemGlobal_ID;
				list.Clear();
				return result;
			}
			list.Clear();
			return result;
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Offline Auto Nhat Item - " + ex2.Message);
			throw;
		}
	}

	public bool Auto_PhamVi_Attack_NPC_Old(int int75, NpcClass npcClass0, float float0, float float1)
	{
		if (npcClass0.Rxjh_Map != MapID)
		{
			return false;
		}
		if (MapID == 7101)
		{
			int75 = 1000;
		}
		var num = npcClass0.Rxjh_X - float0;
		var num2 = npcClass0.Rxjh_Y - float1;
		float num3 = (int)Math.Sqrt(num * num + num2 * num2);
		return num3 <= int75;
	}

	public bool Auto_PhamVi_Attack_NPC(Players player, int int75, NpcClass npcClass0, float float0, float float1)
	{
		if (npcClass0.Rxjh_Map != player.MapID)
		{
			return false;
		}
		if (player.MapID == 7101)
		{
			int75 = 1000;
		}
		var num = npcClass0.Rxjh_X - float0;
		var num2 = npcClass0.Rxjh_Y - float1;
		float num3 = (int)Math.Sqrt(num * num + num2 * num2);
		return num3 <= int75;
	}

	public bool Auto_DiDong_PhamVi(int int75)
	{
		if (Offline_TreoMay_BanDo != MapID)
		{
			return false;
		}
		if (MapID == 7101)
		{
			int75 = 1000;
		}
		var num = PosX - Offline_TreoMay_ToaDo_X;
		var num2 = PosY - Offline_TreoMay_ToaDo_Y;
		float num3 = (int)Math.Sqrt(num * num + num2 * num2);
		return num3 <= int75;
	}

	public static void Auto_Offline_Move(Players player, float x, float y, float jl)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E002E010700280002000000F91DC7426177ACC3978E3C44F91DC74200007041978E3C4401050000000000005C28000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(x), 0, array, 14, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, array, 18, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(y), 0, array, 22, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(player.PosX), 0, array, 26, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(player.PosZ), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(player.PosY), 0, array, 34, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(player.Character_KhinhCong), 0, array, 39, 1);
			Buffer.BlockCopy(BitConverter.GetBytes(jl), 0, array, 42, 4);
			player.CharacterMove(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Auto Offline Move - " + ex.Message);
		}
	}

	public void Auto_SuDungPill()
	{
		var num = 0;
		try
		{
			if (!AppendStatusList.ContainsKey(1008000055) && GetPacket_VatPhamViTri(1008000055) != -1)
			{
				Packet_SuDungPill(this, 1008000055);
			}
			if (!AppendStatusList.ContainsKey(1008002063) && GetPacket_VatPhamViTri(1008002063) != -1)
			{
				Packet_SuDungPill(this, 1008002063);
			}
			else if (!AppendStatusList.ContainsKey(1008001100) && GetPacket_VatPhamViTri(1008001100) != -1)
			{
				Packet_SuDungPill(this, 1008001100);
			}
			else if (!AppendStatusList.ContainsKey(1008000240) && GetPacket_VatPhamViTri(1008000240) != -1)
			{
				Packet_SuDungPill(this, 1008000240);
			}
			else if (!AppendStatusList.ContainsKey(1008000250) && GetPacket_VatPhamViTri(1008000250) != -1)
			{
				Packet_SuDungPill(this, 1008000250);
			}
			else if (!AppendStatusList.ContainsKey(1008000305) && GetPacket_VatPhamViTri(1008000305) != -1)
			{
				Packet_SuDungPill(this, 1008000305);
			}
			else if (!AppendStatusList.ContainsKey(1008000325) && GetPacket_VatPhamViTri(1008000325) != -1)
			{
				Packet_SuDungPill(this, 1008000325);
			}
			else if (!TitleDrug.ContainsKey(1008001480) && GetPacket_VatPhamViTri(1008001480) != -1)
			{
				Packet_SuDungPill(this, 1008001480);
			}
			else if (!TitleDrug.ContainsKey(1008001478) && GetPacket_VatPhamViTri(1008001478) != -1)
			{
				Packet_SuDungPill(this, 1008001478);
			}
			else if (!TitleDrug.ContainsKey(999000580) && GetPacket_VatPhamViTri(999000580) != -1)
			{
				Packet_SuDungPill(this, 999000580);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Auto sử dụng PILL tại num: [" + num + "] - " + ex.Message);
		}
	}

	public static void Packet_SuDungPill(Players player, int pid)
	{
		try
		{
			var packetVatPhamViTri = player.GetPacket_VatPhamViTri(pid);
			if (packetVatPhamViTri >= 0)
			{
				var array = Converter.HexStringToByte("AA5522002C013A001C0001000037DC143C00000000000000000000000000010000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(packetVatPhamViTri), 0, array, 11, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(pid), 0, array, 14, 4);
				player.OpenItem(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Packet SuDung Pill - " + ex.Message);
		}
	}
	
	private void AutoOffline_Reload_Player(Players player)
	{
		try
		{
			if (player.Player_Level > 30 && player.Player_Level < 60 && player.MapID != 1301)
			{
				Auto_Offline_BanDo_Mobile(player, 3);
			}
			else if (player.Player_Level >= 60 && player.Player_Level < 115 && player.MapID != 6001)
			{
				Auto_Offline_BanDo_Mobile(player, 4);
			}
			else if (player.Player_Level >= 115 && player.Player_Level < 130 && player.MapID != 25100)
			{
				Auto_Offline_BanDo_Mobile(player, 6);
			}
			else if (player.Player_Level >= 130 && player.Player_Level < 150 && player.MapID != 26000)
			{
				Auto_Offline_BanDo_Mobile(player, 8);
			}
			else if (player.Player_Level >= 150 && player.Player_Level < 255 && player.MapID != 26100)
			{
				Auto_Offline_BanDo_Mobile(player, 9);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi AutoOffline_Reload_Player - " + ex.Message);
		}
	}

	private static void Auto_Offline_BanDo_Mobile(Players player, int soLuongQuay)
	{
		try
		{
			NpcClass npcClass = null;
			switch (soLuongQuay)
			{
				case 2:
				case 3:
					{
						var index5 = RNG.Next(1, World.Chuyen_BanDo_2_3.Count);
						npcClass = World.Chuyen_BanDo_2_3[index5];
						World.Chuyen_BanDo_2_3.Remove(npcClass);
						break;
					}
				case 4:
				case 5:
					{
						var index4 = RNG.Next(1, World.Chuyen_BanDo_4_5.Count);
						npcClass = World.Chuyen_BanDo_4_5[index4];
						World.Chuyen_BanDo_4_5.Remove(npcClass);
						break;
					}
				case 6:
				case 7:
					{
						var index3 = RNG.Next(1, World.Chuyen_BanDo_6_7.Count);
						npcClass = World.Chuyen_BanDo_6_7[index3];
						World.Chuyen_BanDo_6_7.Remove(npcClass);
						break;
					}
				case 8:
					{
						var index2 = RNG.Next(1, World.Chuyen_BanDo_8.Count);
						npcClass = World.Chuyen_BanDo_8[index2];
						World.Chuyen_BanDo_8.Remove(npcClass);
						break;
					}
				case 9:
					{
						var index = RNG.Next(1, World.Chuyen_BanDo_9.Count);
						npcClass = World.Chuyen_BanDo_9[index];
						World.Chuyen_BanDo_9.Remove(npcClass);
						break;
					}
			}
			if (npcClass != null)
			{
				player.Offline_TreoMay_ToaDo_X = (int)npcClass.Rxjh_X;
				player.Offline_TreoMay_ToaDo_Y = (int)npcClass.Rxjh_Y;
				player.Offline_TreoMay_BanDo = npcClass.Rxjh_Map;
				player.Mobile(npcClass.Rxjh_X, npcClass.Rxjh_Y, 15f, npcClass.Rxjh_Map, 0);
				player.GetReviewScopeNpc();
				LogHelper.WriteLine(LogLevel.Debug, "Auto Offline chuyển bản đồ:[" + player.CharacterName + "]tọa độ X:" + npcClass.Rxjh_X + "  tọa độ Y:" + npcClass.Rxjh_Y + "  bản đồ:" + npcClass.Rxjh_Map);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Auto_Offline_BanDo_Mobile - " + ex.Message);
		}
	}
	
	public static void TLC_AUTO_KICK_PLAYER(string username)
	{
		try
		{
			foreach (var value3 in World.allConnectedChars.Values)
			{
				var theLucChienChinhPhaiDiemSo = World.TheLucChien_ChinhPhai_DiemSo;
				var theLucChienTaPhaiDiemSo = World.TheLucChien_TaPhai_DiemSo;
				if (!(value3.CharacterName == username) || !World.EventTop.TryGetValue(value3.CharacterName, out var value) || value.TuVongSoLuong <= 0)
				{
					continue;
				}
				if (World.EventTop.TryGetValue(value3.CharacterName, out value))
				{
					if (value.TuVongSoLuong > 0)
					{
						if (value3.TheLucChien_PhePhai == "TA_PHAI")
						{
							World.TheLucChien_ChinhPhai_SoNguoi--;
							World.TheLucChien_ChinhPhai_DiemSo -= value.TuVongSoLuong / 2;
						}
						else if (value3.TheLucChien_PhePhai == "CHINH_PHAI")
						{
							World.TheLucChien_TaPhai_SoNguoi--;
							World.TheLucChien_TaPhai_DiemSo -= value.TuVongSoLuong / 2;
						}
					}
					if (value.GietNguoiSoLuong > 0)
					{
						if (value3.TheLucChien_PhePhai == "TA_PHAI")
						{
							World.TheLucChien_TaPhai_SoNguoi--;
							World.TheLucChien_TaPhai_DiemSo -= value.GietNguoiSoLuong / 2;
						}
						else if (value3.TheLucChien_PhePhai == "CHINH_PHAI")
						{
							World.TheLucChien_ChinhPhai_SoNguoi--;
							World.TheLucChien_ChinhPhai_DiemSo -= value.GietNguoiSoLuong / 2;
						}
					}
					if (value3.Tao_GietNguoi_Trong_TLC.Count > 0)
					{
						foreach (var item in value3.Tao_GietNguoi_Trong_TLC.Select((EventTopClass x) => x.TenNhanVat).Distinct())
						{
							if (World.EventTop.TryGetValue(item, out var value2))
							{
								value2.GietNguoiSoLuong -= value3.Tao_GietNguoi_Trong_TLC.Where((EventTopClass x) => x.TenNhanVat == item).Count();
							}
						}
						value3.Tao_GietNguoi_Trong_TLC.Clear();
					}
					World.EventTop.Remove(username);
					var txt = "[" + value3.CharacterName + "] -Kill:[" + value.GietNguoiSoLuong + "] -Die:[" + value.TuVongSoLuong + "]-----Diem9Truoc:[" + theLucChienChinhPhaiDiemSo + "]-Sau:[" + World.TheLucChien_ChinhPhai_DiemSo + "]-----DiemTaTruoc:[" + theLucChienTaPhaiDiemSo + "]-Sau:[" + World.TheLucChien_TaPhai_DiemSo + "][";
					// logo.Log_Kick_TheLucChien(txt);
				}
				if (value3.NhanVat_HP <= 0 || value3.PlayerTuVong)
				{
					value3.NhanVat_HP = value3.CharacterMax_HP;
					value3.CapNhat_HP_MP_SP();
					value3.PlayerTuVong = false;
					value3.ReviveMove(value3.PosX, value3.PosY, value3.PosZ, value3.MapID);
				}
				value3.Tao_GietNguoi_Trong_TLC.Clear();
				EventClass.GUI_DI_THE_LUC_CHIEN_THAM_DU_FEED_MANG_BI_KICK_RA_TIN_TUC(value3);
				EventClass.KET_THUC_THE_LUC_CHIEN_NHAC_NHO(value3);
				if (value3.Client != null)
				{
					var text = "Nhân vật [" + value3.CharacterName + "] bị kick khỏi TLC vì giết [" + value.GietNguoiSoLuong + "] - chết [" + value.TuVongSoLuong + "] mạng";
					World.GuiThongBao(text);
					value.TuVongSoLuong = 0;
					value.GietNguoiSoLuong = 0;
					value3.Mobile(529f, 1528f, 15f, 101, 0);
				}
			}
		}
		catch
		{
		}
	}

	public void Time_KTTX_Tranh_Trung_Time()
	{
		// var num = RNG.Next(20, 60);
		// if (Player_Job == 6 && CurrentlyActiveSkill_ID != 0)
		// {
		// 	Auto_Offline_Timer = new System.Timers.Timer(1500.0);
		// 	return;
		// }
		// switch (num)
		// {
		// 	case 20:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2000.0);
		// 		break;
		// 	case 21:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2005.0);
		// 		break;
		// 	case 22:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2010.0);
		// 		break;
		// 	case 23:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2015.0);
		// 		break;
		// 	case 24:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2020.0);
		// 		break;
		// 	case 25:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2025.0);
		// 		break;
		// 	case 26:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2030.0);
		// 		break;
		// 	case 27:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2035.0);
		// 		break;
		// 	case 28:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2040.0);
		// 		break;
		// 	case 29:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2045.0);
		// 		break;
		// 	case 30:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2050.0);
		// 		break;
		// 	case 31:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2055.0);
		// 		break;
		// 	case 32:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2060.0);
		// 		break;
		// 	case 33:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2065.0);
		// 		break;
		// 	case 34:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2070.0);
		// 		break;
		// 	case 35:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2075.0);
		// 		break;
		// 	case 36:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2080.0);
		// 		break;
		// 	case 37:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2085.0);
		// 		break;
		// 	case 38:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2090.0);
		// 		break;
		// 	case 39:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2095.0);
		// 		break;
		// 	case 40:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2110.0);
		// 		break;
		// 	case 41:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2105.0);
		// 		break;
		// 	case 42:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2110.0);
		// 		break;
		// 	case 43:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2115.0);
		// 		break;
		// 	case 44:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2120.0);
		// 		break;
		// 	case 45:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2125.0);
		// 		break;
		// 	case 46:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2130.0);
		// 		break;
		// 	case 47:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2135.0);
		// 		break;
		// 	case 48:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2140.0);
		// 		break;
		// 	case 49:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2145.0);
		// 		break;
		// 	case 50:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2150.0);
		// 		break;
		// 	case 51:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2155.0);
		// 		break;
		// 	case 52:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2160.0);
		// 		break;
		// 	case 53:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2165.0);
		// 		break;
		// 	case 54:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2170.0);
		// 		break;
		// 	case 55:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2175.0);
		// 		break;
		// 	case 56:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2180.0);
		// 		break;
		// 	case 57:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2185.0);
		// 		break;
		// 	case 58:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2190.0);
		// 		break;
		// 	case 59:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2195.0);
		// 		break;
		// 	case 60:
		// 		Auto_Offline_Timer = new System.Timers.Timer(2200.0);
		// 		break;
		// }
	}

	public void KTTX_Time_Pill_Loai_2()
	{
		if (!TitleDrug.ContainsKey(1008001512))
		{
			var dateTime = DateTime.Now.AddMinutes(1.0);
			var now = DateTime.Now;
			var thoiGian = Convert.ToUInt32(dateTime.ToString("yyMMddHHmm"));
			var timeSpan = dateTime - now;
			PillItem xXungHaoDuocPhamLoai = new();
			xXungHaoDuocPhamLoai.DuocPhamID = 1008001512;
			xXungHaoDuocPhamLoai.ThoiGian = thoiGian;
			TitleDrug.Add(xXungHaoDuocPhamLoai.DuocPhamID, xXungHaoDuocPhamLoai);
			NewDrugEffects(1008001512, 1, xXungHaoDuocPhamLoai.ThoiGian, (uint)timeSpan.TotalMinutes);
			UpdateMartialArtsAndStatus();
			// Auto_Offline_Timer = new System.Timers.Timer(2000.0);
			// Auto_Offline_Timer.Elapsed += Auto_Offline_Function;
			// Auto_Offline_Timer.AutoReset = true;
			// Auto_Offline_Timer.Enabled = true;
		}
	}

}
