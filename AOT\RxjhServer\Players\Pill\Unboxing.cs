﻿using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RxjhServer;

public partial class Players
{
    
    public void OpenItem(byte[] packetData, int length)
	{
		var num = 0;
		if (NhanVat_HP <= 0)
		{
			return;
		}
		PacketModification(packetData, packetData.Length);
		if (OpenWarehouse || (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa))
		{
			return;
		}
		try
		{
			num = 1;
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			var itemId = BitConverter.ToInt32(array, 0);
			int bagId = packetData[10];
			int positionID = packetData[11];
			BitConverter.ToInt32(packetData, 26);
			var num5 = 20000;
			if (Item_In_Bag[positionID].VatPham_KhoaLai && itemId != 1008000188 && itemId != 1008001021 && itemId != 1008001022 && itemId != 1008001023 && itemId != 1008001027 && itemId != 1008001028 && itemId != 1008001480)
			{
				itemId -= num5;
			}
			switch (bagId)
			{
				case 60:
					if (positionID != 14 || CharacterBeast == null)
					{
						var itmeClass = World.ItemList[BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[positionID].VatPham_ID, 0)];
						if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[4].VatPham_ID, 0) == 0)
						{
							CharacterBeast.ThuCungVaTrangBi[itmeClass.FLD_RESIDE2 - 1].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[positionID].VatPham_byte;
							CharacterBeast.ThuCung_Thanh_TrangBi[positionID].VatPham_byte = new byte[World.Item_Db_Byte_Length];
							ChangeEquipmentLocation(bagId, positionID, bagId - 1, itmeClass.FLD_RESIDE2 - 1, CharacterBeast.ThuCungVaTrangBi[4].VatPham_byte, 1);
							UpdateTheWeightOfTheBeast();
							UpdateTheSpiritBeastExperienceAndTrainExperience();
							CharacterBeast.CalculateBasicData();
							UpdateSpiritBeastHP_MP_SP();
							UpdateSpiritBeastMartialArtsAndStatus();
							UpdateCharacterData(this);
						}
					}
					break;
				case 1:
					if (Item_In_Bag[positionID].GetVatPham_ID == 8000008 || Item_In_Bag[positionID].GetVatPham_ID == 8000007)
					{
						if (!GetAbnormalState(26) && !GetAbnormalState(27) && !GetAbnormalState(28) && checkkepskill)
						{
							if (Player_Job == 4)
							{
								if (CurrentlyActiveSkill_ID != 0)
								{
									var num7 = (int)DateTime.Now.Subtract(time_PK_Cung).TotalMilliseconds;
									if (num7 > World.Time_Delay_Item_8000008_Cung)
									{
										time_PK_Cung = DateTime.Now;
										WalkingState(BitConverter.GetBytes(1), 1);
									}
								}
							}
							else if (Player_Job == 6)
							{
								if (CurrentlyActiveSkill_ID != 0)
								{
									var num8 = (int)DateTime.Now.Subtract(time_PK_ThichKhach).TotalMilliseconds;
									if (num8 > World.Time_Delay_Item_8000008_ThichKhach)
									{
										time_PK_ThichKhach = DateTime.Now;
										WalkingState(BitConverter.GetBytes(1), 1);
									}
								}
							}
							else
							{
								var num9 = (int)DateTime.Now.Subtract(time_PK_ConLai).TotalMilliseconds;
								if (num9 > World.Time_Delay_Item_8000008_Con_Lai)
								{
									time_PK_ConLai = DateTime.Now;
									WalkingState(BitConverter.GetBytes(1), 1);
								}
							}
						}
						else
						{
							HeThongNhacNho("Trạng thái hiện tại không thể sử dụng!", 10, "Thiên cơ các");
						}
					}
					else if (BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) == 1600101)
					{
						if (Player_Bien_Hinh_ID == 0)
						{
							Player_Bien_Hinh_ID = 1600101;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
						else
						{
							Player_Bien_Hinh_ID = 0;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
					}
					else if (BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) == 1600102)
					{
						if (Player_Bien_Hinh_ID == 0)
						{
							Player_Bien_Hinh_ID = 1600102;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
						else
						{
							Player_Bien_Hinh_ID = 0;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
					}
					else if (BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) == 1600103)
					{
						if (Player_Bien_Hinh_ID == 0)
						{
							Player_Bien_Hinh_ID = 1600103;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
						else
						{
							Player_Bien_Hinh_ID = 0;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
					}
					else if (BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) == 1600104)
					{
						if (Player_Bien_Hinh_ID == 0)
						{
							Player_Bien_Hinh_ID = 1600104;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
						else
						{
							Player_Bien_Hinh_ID = 0;
							UpdateCharacterData(this);
							// GetUpdatedCharacterData(this);
							UpdateBroadcastCharacterData();
						}
					}
					else
					{
						if ((BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) != itemId || Item_In_Bag[positionID].Lock_Move) && (BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) < 1000002051 || BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0) > 1000002070))
						{
							break;
						}
						var itmeClass2 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0)];
						if (itmeClass2 != null)
						{
							if (itmeClass2.FLD_RESIDE2 == 1)
							{
								if (itmeClass2.FLD_LEVEL <= Player_Level && (itmeClass2.FLD_ZX == 0 || itmeClass2.FLD_ZX == Player_Zx) && (itmeClass2.FLD_RESIDE1 == 0 || itmeClass2.FLD_RESIDE1 == Player_Job) && (itmeClass2.FLD_JOB_LEVEL == 0 || itmeClass2.FLD_JOB_LEVEL <= Player_Job_level) && (itmeClass2.FLD_SEX == 0 || itmeClass2.FLD_SEX == Player_Sex) && (itmeClass2.FLD_XWJD < 1 || itmeClass2.FLD_XWJD <= VoHuanGiaiDoan))
								{
									var itmeClass3 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0)];
									if (BitConverter.ToInt32(Item_Wear[itmeClass3.FLD_RESIDE2 - 1].VatPham_ID, 0) == 0)
									{
										Item_Wear[itmeClass3.FLD_RESIDE2 - 1].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
										Item_In_Bag[positionID].VatPham_byte = new byte[World.Item_Db_Byte_Length];
										ChangeEquipmentLocation(bagId, positionID, 0, itmeClass3.FLD_RESIDE2 - 1, Item_Wear[itmeClass3.FLD_RESIDE2 - 1].VatPham_byte, 1);
										CalculateCharacterEquipmentData();
										UpdateEquipmentEffects();
										UpdateMartialArtsAndStatus();
										UpdateMoneyAndWeight();
										CapNhat_HP_MP_SP();
									}
								}
							}
							else if (itmeClass2.FLD_RESIDE2 == 13)
							{
								//if (Player_Job != 4 && Player_Job != 11)
								//{
								//if (itmeClass2.FLD_PID >= 1000002051 && itmeClass2.FLD_PID <= 1000002090)
								//{
								var itmeClass4 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0)];
								if (BitConverter.ToInt32(Item_Wear[12].VatPham_ID, 0) == 0)
								{
									Item_In_Bag[positionID].Lock_Move = true;
									Item_Wear[12].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
									Item_In_Bag[positionID].VatPham_byte = new byte[World.Item_Db_Byte_Length];
									ChangeEquipmentLocation(bagId, positionID, 0, 12, Item_Wear[12].VatPham_byte, 1);
									CalculateCharacterEquipmentData();
									UpdateEquipmentEffects();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
									Item_Wear[12].Lock_Move = false;
								}
								else
								{
									Item_Wear[12].Lock_Move = true;
									Item_In_Bag[positionID].Lock_Move = true;
									var tempItem = Item_Wear[12].VatPham_byte;
									Item_Wear[12].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
									Item_In_Bag[positionID].VatPham_byte = tempItem;
									ChangeEquipmentLocation(bagId, positionID, 0, 12, Item_Wear[12].VatPham_byte, 1);
									CalculateCharacterEquipmentData();
									UpdateEquipmentEffects();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
									Item_Wear[12].Lock_Move = false;
									Item_In_Bag[positionID].Lock_Move = false;
								}

								//}
								//	else
								//	{
								//		HeThongNhacNho("Bảo vật không hợp lệ với nhân vật này!", 10, "Thiên cơ các");
								//	}
								//}
								//else
								//{
								//	HeThongNhacNho("Bảo vật không dành cho Cung và Diệu Yến!", 10, "Thiên cơ các");
								//}
							}
							else if (itmeClass2.FLD_RESIDE2 == 17)
							{
								Unboxing(packetData);
							}
							else if (itmeClass2.FLD_RESIDE2 == 19)
							{
								QigongBook(packetData);
							}
							else if (itmeClass2.FLD_RESIDE2 == 20)
							{
								TurnOnItemTrigger(packetData);
							}
							else if (itmeClass2.FLD_RESIDE2 == 1792)
							{
								MartialArtsBook(packetData);
							}
							else if (itmeClass2.FLD_RESIDE2 == 31)
							{
								var itmeClass5 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0)];
								ItmeClass value;
								if (BitConverter.ToInt32(Item_Wear[16].VatPham_ID, 0) == 0)
								{
									Item_Wear[16].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
									Item_In_Bag[positionID].VatPham_byte = new byte[World.Item_Db_Byte_Length];
									ChangeEquipmentLocation(bagId, positionID, 0, 16, Item_Wear[16].VatPham_byte, 1);
									CalculateCharacterEquipmentData();
									UpdateEquipmentEffects();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
								}
								else if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0), out value) && itmeClass2.FLD_RESIDE2 == value.FLD_RESIDE2)
								{
									var vatPhamByte = Item_Wear[16].VatPham_byte;
									Item_Wear[16].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
									Item_In_Bag[positionID].VatPham_byte = vatPhamByte;
									ChangeEquipmentLocation(bagId, positionID, 0, 16, Item_Wear[16].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[positionID].VatPhamSoLuong, 0));
									CalculateCharacterEquipmentData();
									UpdateEquipmentEffects();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
								}
							}
							// TODO Update WIng slot
							else if (itmeClass2.FLD_RESIDE2 == 9999)
							{
								var itmeClass5 = World.ItemList[BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0)];
								ItmeClass value;
								if (BitConverter.ToInt32(Item_Wear[17].VatPham_ID, 0) == 0)
								{
									Item_Wear[17].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
									Item_In_Bag[positionID].VatPham_byte = new byte[World.Item_Db_Byte_Length];
									ChangeEquipmentLocation(bagId, positionID, 0, 17, Item_Wear[17].VatPham_byte, 1);
									CalculateCharacterEquipmentData();
									UpdateEquipmentEffects();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
								}
								else if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[positionID].VatPham_ID, 0), out value) && itmeClass2.FLD_RESIDE2 == value.FLD_RESIDE2)
								{
									var vatPhamByte = Item_Wear[16].VatPham_byte;
									Item_Wear[17].VatPham_byte = Item_In_Bag[positionID].VatPham_byte;
									Item_In_Bag[positionID].VatPham_byte = vatPhamByte;
									ChangeEquipmentLocation(bagId, positionID, 0, 17, Item_Wear[17].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[positionID].VatPhamSoLuong, 0));
									CalculateCharacterEquipmentData();
									UpdateEquipmentEffects();
									UpdateMartialArtsAndStatus();
									UpdateMoneyAndWeight();
									CapNhat_HP_MP_SP();
								}
							}
							else if (World.PillClass.TryGetValue(itmeClass2.FLD_PID, out var value))
							{
								Use_Pill(packetData, value);
							}
							else
							{
								num = 6;
								TakeMedicine(packetData);
							}
						}
						else
						{
							LogHelper.WriteLine(LogLevel.Error, "itmeClass2 Vô giá trị !! [" + AccountID + "][" + CharacterName + "]");
						}
					}
					break;
				case 0:
					{
						if (positionID == 14 && CharacterBeast != null)
						{
							break;
						}
						num = 7;
						var num6 = 0;
						while (true)
						{
							if (num6 >= 36)
							{
								return;
							}
							if (BitConverter.ToInt32(Item_In_Bag[num6].VatPham_ID, 0) == 0)
							{
								break;
							}
							num6++;
						}
						Item_In_Bag[num6].VatPham_byte = Item_Wear[positionID].VatPham_byte;
						Item_Wear[positionID].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						ChangeEquipmentLocation(bagId, positionID, 1, num6, Item_In_Bag[num6].VatPham_byte, 1);
						UpdateEquipmentEffects();
						CalculateCharacterEquipmentData();
						UpdateMartialArtsAndStatus();
						UpdateMoneyAndWeight();
						CapNhat_HP_MP_SP();
						break;
					}
			}
		}
		catch (Exception ex)
		{
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 14, array2, 0, 4);
			LogHelper.WriteLine(LogLevel.Error, "Mở Hộp lỗi num: [" + num + "] - ID hộp：[" + BitConverter.ToInt32(array2, 0) + "] [" + AccountID + "]-[" + CharacterName + "]-[" + ex.Message);
		}
	}

	public void Unboxing(byte[] packetData)
	{
		try
		{
			if ((int)DateTime.Now.Subtract(_moRuongThoiGian).TotalMilliseconds < 500)
			{
				return;
			}
			_moRuongThoiGian = DateTime.Now;
			int num = packetData[11];
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			var num2 = BitConverter.ToInt32(array, 0);
			if (Item_In_Bag[num].VatPham_KhoaLai)
			{
				num2 -= 20000;
			}
			if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != num2 || num2 == 0)
			{
				return;
			}
			using (new Lock(World.MoRuong_Lock, "MoRuong_Lock"))
			{
				var open = OpenClass.GetOpen(num2, Player_Job, Player_Zx, this);
				if (open == null || open.FLD_PIDX == 0)
				{
					HeThongNhacNho("Bảo vật chưa thể sử dụng vào thời điểm này! UnBoxing", 10, "Thiên cơ các");
					// TakeMedicine(packetData);
					return;
				}
				if (!World.ItemList.TryGetValue(open.FLD_PIDX, out var value))
				{
					LogHelper.WriteLine(LogLevel.Error, "Mở hộp lỗi [" + open.FLD_PID + "][" + open.FLD_NAME + "] mở [" + open.FLD_PIDX + "][" + open.FLD_NAMEX + "][" + AccountID + "][" + CharacterName + "]");
					return;
				}
				var num3 = open.FLD_MAGIC1;
				var value2 = open.FLD_MAGIC2;
				if (value.FLD_RESIDE2 == 16)
				{
					if (num3 == 0)
					{
						switch (value.FLD_PID)
						{
							case ItemDef.Item.KimCuongThach:
							case ItemDef.Item.HanNgocThach:
							case ItemDef.Item.KimCuongThachRandom:
							case ItemDef.Item.HanNgocThachRandom:
							case ItemDef.Item.NhietHuyetThach:
							case ItemDef.Item.KimCuongThachCaoCap:
							case ItemDef.Item.HanNgocThachCaoCap:
							case ItemDef.Item.ThuocTinhThach:
							case *********:
							case *********:
							case *********:
							case *********:
							case *********:
							case ItemDef.Item.KimCuongThachSieuCap:
							case ItemDef.Item.HanNgocThachSieuCap:
							case **********:
							case ItemDef.Item.KimCuongThachHonNguyen:
							case ItemDef.Item.HanNgocThachHonNguyen:
								num3 = World.GetValue(value.FLD_PID, 6);
								break;
						}
					}
				}
				else
				{
					switch (value.FLD_PID)
					{
						case ItemDef.TapHonThach.HaCapTapHonChau:
							num3 = RNG.Next(0, 1000);
							value2 = RNG.Next(10, 50);
							break;
						case ItemDef.TapHonThach.TrungCapTapHonCha:
							num3 = RNG.Next(0, 1000);
							value2 = RNG.Next(100, 150);
							break;
						case ItemDef.TapHonThach.ThuongCapTapHonChau:
							num3 = RNG.Next(0, 1000);
							value2 = RNG.Next(400, 699);
							break;
						case ItemDef.TapHonThach.TuLinhTapHonChau:
							num3 = RNG.Next(0, 1000);
							value2 = RNG.Next(2000, 2499);
							break;
					}
				}
				SubtractItem(num, 1);
				var array2 = new byte[56];
				Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, array2, 0, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array2, 4, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(open.FLD_MAGIC3), 0, array2, 8, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(open.FLD_MAGIC4), 0, array2, 12, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(open.FLD_MAGIC5), 0, array2, 16, 4);
				if (open.FLD_TrungCapPhuHon > 0)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array2, 22, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(open.FLD_TrungCapPhuHon), 0, array2, 24, 4);
				}
				Buffer.BlockCopy(BitConverter.GetBytes(open.FLD_ThucTinh), 0, array2, 46, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(open.FLD_TienHoa), 0, array2, 52, 4);
				var khoaLai = false;
				if (open.FLD_BD > 0 || value.FLD_LOCK > 0 || Item_In_Bag[num].Lock_Move)
				{
					khoaLai = true;
				}
				var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
				num = GetParcelVacancyPosition();
				if (num != -1)
				{
					AddItems_Lock(bytes, BitConverter.GetBytes(open.FLD_PIDX), num, BitConverter.GetBytes(open.FLD_NUMBER), array2, khoaLai, open.FLD_DAYS);
					HeThongNhacNho("Mở hộp [" + open.FLD_NAME + "] nhận được [" + open.FLD_NAMEX + "]", 10, "Thiên cơ các");
					if (World.CoHayKo_Drop_CoMoThongBao != 0 && open.CoMoThongBao > 0)
					{
						if (!Client.TreoMay)
						{
							var text = "Đại hiệp [" + CharacterName + "] vận dụng kỳ tài, mở thành công rương báu [" + open.FLD_NAME + "] tại [Kênh " + World.ServerID + "], đoạt được trân phẩm [" + open.FLD_NAMEX + "]. Thiên hạ thán phục, bảo vật lại lộ diện giang hồ!";
							World.conn.Transmit("PK_MESSAGE|" + 22 + "|" + text);
						}
						var text2 = "[" + AccountID + "]-[" + CharacterName + "]-1:[" + open.FLD_PID + "]-2:[" + open.FLD_PIDX + "]-3:[" + open.FLD_NAME + "]-4:[" + open.FLD_NAMEX + "]-5:[" + BitConverter.ToInt32(array, 0) + "]";
						// logo.Log_Mo_Hop_OPEN(text2);
					}
					if (num2 == ********** || num2 == **********)
					{
						FLD_PVP_Piont = 1;
					}
					if (num2 == World.Log_Hop_Code_Event)
					{
						if (World.Event_MoHop_SoLuong != 0)
						{
							FLD_NUMBER_OPEN++;
							HeThongNhacNho("Số lượng mở hộp sự kiện: [" + FLD_NUMBER_OPEN + "]", 10, "Thiên cơ các");
						}
						else
						{
							FLD_NUMBER_OPEN = 0;
						}
						var text3 = "ID:[" + AccountID + "][" + open.FLD_PIDX + "][" + open.FLD_NAMEX + "] - Global_ID[" + BitConverter.ToInt64(bytes, 0) + "] - ID hop[" + BitConverter.ToInt32(array, 0) + "] - Item ra[" + value.FLD_PID + "]";
						// logo.Log_Hop_Event(text3, UserName);
					}
					if (num2 == **********)
					{
						Ban_Phao_Hoa(**********, SessionID);
					}

					//RxjhClass.DropRecord(AccountID, CharacterName, BitConverter.ToInt64(bytes, 0), value.FLD_PID, value.ItmeNAME, value.FLD_MAGIC0, value.FLD_MAGIC1, value.FLD_MAGIC2, value.FLD_MAGIC3, value.FLD_MAGIC4, MapID, (int)PosX, (int)PosY, "开箱");
					//GameDb.DropRecord(AccountID, CharacterName, BitConverter.ToInt64(bytes, 0), value.FLD_PID, 1, MapID, (int)PosX, (int)PosY);
				}
				else
				{
					HeThongNhacNho("Hành trang không đủ chỗ trống!", 10, "Thiên cơ các");
				}
			}
		}
		catch (Exception ex)
		{
			var array3 = new byte[4];
			Buffer.BlockCopy(packetData, 14, array3, 0, 4);
			LogHelper.WriteLine(LogLevel.Error, "Mở hộp error !! [" + AccountID + "][" + CharacterName + "] ID hộp mới là：" + BitConverter.ToInt32(array3, 0) + ex.Message);
		}
	}
	
	public void QigongBook(byte[] packetData)
	{
		try
		{
			if (PlayerTuVong)
			{
				return;
			}
			int num = packetData[11];
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			Buffer.BlockCopy(packetData, 26, array2, 0, 4);
			var num2 = BitConverter.ToInt32(array, 0);
			BitConverter.ToInt32(array2, 0);
			if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != num2 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0 || !World.ItemList.TryGetValue(num2, out var value))
			{
				return;
			}
			foreach (var value3 in World.ThangThienKhiCongList.Values)
			{
				if (value3.VatPham_ID != num2)
				{
					continue;
				}
				if (value.FLD_LEVEL > Player_Level || (value.FLD_ZX != 0 && value.FLD_ZX != Player_Zx) || (value.FLD_RESIDE1 != 0 && value.FLD_RESIDE1 != Player_Job) || (value.FLD_JOB_LEVEL != 0 && value.FLD_JOB_LEVEL > Player_Job_level))
				{
					break;
				}
				if (value.FLD_NEED_MONEY > 0 && Player_Money < value.FLD_NEED_MONEY)
				{
					HeThongNhacNho("Ngân lượng trò chơi không đủ!", 10, "Thiên cơ các");
				}
				else
				{
					if (value.FLD_NEED_FIGHTEXP > 0 && SkillExperience < value.FLD_NEED_FIGHTEXP)
					{
						HeThongNhacNho($"Kinh nghiệm võ công không đủ!", 10, "Thiên cơ các");
						LogHelper.WriteLine(LogLevel.Debug, "Kinh nghiệm võ công không đủ !![" + value3.KhiCongTen + "] [" + AccountID + "][" + CharacterName + "] Cần: " + value.FLD_NEED_FIGHTEXP + " Có: " + SkillExperience);
						break;
					}
					switch (Player_Job)
					{
						case 1:
							if (value3.NhanVatNgheNghiep1 == 0)
							{
								return;
							}
							break;
						case 2:
							if (value3.NhanVatNgheNghiep2 == 0)
							{
								return;
							}
							break;
						case 3:
							if (value3.NhanVatNgheNghiep3 == 0)
							{
								return;
							}
							break;
						case 4:
							if (value3.NhanVatNgheNghiep4 == 0)
							{
								return;
							}
							break;
						case 5:
							if (value3.NhanVatNgheNghiep5 == 0)
							{
								return;
							}
							break;
						case 6:
							if (value3.NhanVatNgheNghiep6 == 0)
							{
								return;
							}
							break;
						case 7:
							if (value3.NhanVatNgheNghiep7 == 0)
							{
								return;
							}
							break;
						case 8:
							if (value3.NhanVatNgheNghiep8 == 0)
							{
								return;
							}
							break;
						case 9:
							if (value3.NhanVatNgheNghiep9 == 0)
							{
								return;
							}
							break;
						case 10:
							if (value3.NhanVatNgheNghiep10 == 0)
							{
								return;
							}
							break;
						case 11:
							if (value3.NhanVatNgheNghiep11 == 0)
							{
								return;
							}
							break;
						case 12:
							if (value3.NhanVatNgheNghiep12 == 0)
							{
								return;
							}
							break;
						case 13:
							if (value3.NhanVatNgheNghiep13 == 0)
							{
								return;
							}
							break;
					}
					X_Thang_Thien_Khi_Cong_Loai xThangThienKhiCongLoai = new();
					xThangThienKhiCongLoai.KhiCongID = value3.KhiCongID;
					if (!GetSTQG(xThangThienKhiCongLoai.KhiCongID))
					{
						if (xThangThienKhiCongLoai.KhiCongID == 380 || xThangThienKhiCongLoai.KhiCongID == 381 || xThangThienKhiCongLoai.KhiCongID == 382 || xThangThienKhiCongLoai.KhiCongID == 383 || xThangThienKhiCongLoai.KhiCongID == 384 || xThangThienKhiCongLoai.KhiCongID == 385 || xThangThienKhiCongLoai.KhiCongID == 386)
						{
							var num3 = 0;
							X_Thang_Thien_Khi_Cong_Loai value2;
							var flag = ThangThienKhiCong.TryGetValue(380, out value2);
							if (value2 != null)
							{
								num3++;
							}
							var flag2 = ThangThienKhiCong.TryGetValue(381, out value2);
							if (value2 != null)
							{
								num3++;
							}
							var flag3 = ThangThienKhiCong.TryGetValue(382, out value2);
							if (value2 != null)
							{
								num3++;
							}
							var flag4 = ThangThienKhiCong.TryGetValue(383, out value2);
							if (value2 != null)
							{
								num3++;
							}
							var flag5 = ThangThienKhiCong.TryGetValue(384, out value2);
							if (value2 != null)
							{
								num3++;
							}
							var flag6 = ThangThienKhiCong.TryGetValue(385, out value2);
							if (value2 != null)
							{
								num3++;
							}
							var flag7 = ThangThienKhiCong.TryGetValue(386, out value2);
							if (value2 != null)
							{
								num3++;
							}
							if (num3 > 4)
							{
								HeThongNhacNho("Đã học đủ 5 loại khí công thăng thiên cấp 115, không thể học thêm!", 10, "Thiên cơ các");
								break;
							}
							ThangThienKhiCong.Add(xThangThienKhiCongLoai.KhiCongID, xThangThienKhiCongLoai);
							ItemUse(1, num, 1);
							Player_Money -= value.FLD_NEED_MONEY;
							SkillExperience -= value.FLD_NEED_FIGHTEXP;
							LearningSkillsTips();
							UpdateMartialArtsAndStatus();
							UpdateMoneyAndWeight();
						}
						else
						{
							ThangThienKhiCong.Add(xThangThienKhiCongLoai.KhiCongID, xThangThienKhiCongLoai);
							ItemUse(1, num, 1);
							Player_Money -= value.FLD_NEED_MONEY;
							SkillExperience -= value.FLD_NEED_FIGHTEXP;
							LearningSkillsTips();
							UpdateMartialArtsAndStatus();
							UpdateMoneyAndWeight();
						}
					}
					else
					{
						HeThongNhacNho("Đại hiệp đã lĩnh hội khí công này rồi!!", 10, "Thiên cơ các");
					}
				}
				break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Sách Khí Công - Lỗi: [" + AccountID + "][" + CharacterName + "] " + ex.Message);
		}
	}
	
	public void MartialArtsBook(byte[] packetData)
	{
		try
		{
			if (PlayerTuVong || NhanVatKhoa_Chat)
			{
				return;
			}
			var b = packetData[10];
			int num = packetData[11];
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			Buffer.BlockCopy(packetData, 22, array2, 0, 4);
			var num2 = BitConverter.ToInt32(array, 0);
			BitConverter.ToInt32(array2, 0);
			if (b == 60)
			{
				if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[num].VatPham_ID, 0) != num2 || BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[num].VatPham_ID, 0) == 0)
				{
					return;
				}
			}
			else if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != num2 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0)
			{
				return;
			}
			if ((!World.ItemList.TryGetValue(num2, out var value) || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != 0) && value.FLD_LEVEL <= Player_Level && (value.FLD_ZX == 0 || value.FLD_ZX == Player_Zx) && (value.FLD_RESIDE1 == 0 || value.FLD_RESIDE1 == Player_Job || (value.FLD_RESIDE1 == 2 && Player_Job == 9)) && (value.FLD_JOB_LEVEL == 0 || value.FLD_JOB_LEVEL <= Player_Job_level) && (value.FLD_NEED_FIGHTEXP <= 0 || SkillExperience >= value.FLD_NEED_FIGHTEXP))
			{
				if (value.FLD_NEED_MONEY > 0 && Player_Money < value.FLD_NEED_MONEY)
				{
					HeThongNhacNho("Ngân lượng không đủ!", 10, "Thiên cơ các");
					return;
				}
				if (value.FLD_NEED_FIGHTEXP > 0 && SkillExperience < value.FLD_NEED_FIGHTEXP)
				{
					HeThongNhacNho("Kinh nghiệm võ công không đủ!", 10, "Thiên cơ các");
					LogHelper.WriteLine(LogLevel.Debug, "Kinh nghiệm võ công không đủ !![" + value.ItmeNAME + "] [" + AccountID + "][" + CharacterName + "] Cần: " + value.FLD_NEED_FIGHTEXP + " Có: " + SkillExperience);

					return;
				}
				switch (num2)
				{
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
					case **********:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 19))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 20);
						break;
					case **********:
					case **********:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 20))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 20);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
						break;
					case **********:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 24))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 24);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 25);
						break;
					case 1000002035:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 29))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 29);
						break;
					case 1000002034:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 26);
						break;
					case 1000002040:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 25);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 26);
						break;
					case 1000002039:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 25);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 26);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 27);
						break;
					case 1000002041:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 21))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 26);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 27);
						break;
					case 1000001320:
					case 1000001321:
					case 1000001322:
					case 1000001323:
					case 1000001324:
					case 1000001325:
					case 1000001326:
					case 1000001327:
					case 1000001328:
					case 1000001329:
					case 1000001330:
					case 1000001331:
					case 1000001332:
					case 1000001333:
					case 1000001337:
					case 1000001338:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 16))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 16);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						break;
					case 1000001334:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 19))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
						break;
					case 1000001335:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 19))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 19);
						break;
					case 1000001336:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 22))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
						break;
					case 1000001339:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 21))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
						break;
					case 1000001340:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 21))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
						break;
					case 1000001341:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 23))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 24);
						break;
					case 1000000300:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000301:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000302:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000303:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000304:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000305:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000313:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 17);
						break;
					case 1000000314:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 18))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 18);
						break;
					case 1000000315:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 21))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 21);
						break;
					case 1000000316:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 22))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 22);
						break;
					case 1000000317:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 23))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 23);
						break;
					case 1000000318:
						{
							var wg2 = X_Vo_Cong_Loai.GetWg(801401);
							if (wg2 != null)
							{
								if ((wg2.FLD_ZX != 0 && Player_Zx != wg2.FLD_ZX) || (wg2.FLD_JOB != 0 && Player_Job != wg2.FLD_JOB) || (wg2.FLD_JOBLEVEL != 0 && Player_Job_level < wg2.FLD_JOBLEVEL) || (wg2.FLD_LEVEL != 0 && Player_Level < wg2.FLD_LEVEL))
								{
									return;
								}
								VoCongMoi[wg2.FLD_VoCongLoaiHinh, wg2.FLD_INDEX] = new X_Vo_Cong_Loai(wg2.FLD_PID);
							}
							break;
						}
					case 1000000320:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 9))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 9);
						break;
					case 1000000200:
						{
							if (FLD_Couple.Length == 0)
							{
								return;
							}
							if (FLD_loveDegreeLevel > 10)
							{
								HeThongNhacNho("Tình yêu vượt cấp 10 là hành vi phá hoại giang hồ!", 10, "Thiên cơ các");
								return;
							}
							if (WhetherMarried != 1)
							{
								return;
							}
							var key2 = 0;
							if (Player_Job == 1)
							{
								key2 = 100301;
							}
							else if (Player_Job == 2)
							{
								key2 = 200301;
							}
							else if (Player_Job == 3)
							{
								key2 = 300301;
							}
							else if (Player_Job == 4)
							{
								key2 = 400301;
							}
							else if (Player_Job == 5)
							{
								key2 = 500301;
							}
							else if (Player_Job == 6)
							{
								key2 = 800301;
							}
							else if (Player_Job == 7)
							{
								key2 = 900301;
							}
							else if (Player_Job == 8)
							{
								key2 = 1000301;
							}
							else if (Player_Job == 9)
							{
								key2 = 2000301;
							}
							else if (Player_Job == 10)
							{
								key2 = 3000301;
							}
							else if (Player_Job == 11)
							{
								key2 = 4000301;
							}
							else if (Player_Job == 12)
							{
								key2 = 5000301;
							}
							else if (Player_Job == 13)
							{
								key2 = 6000301;
							}
							if (World.MagicList.TryGetValue(key2, out var value3))
							{
								VoCongMoi[value3.FLD_VoCongLoaiHinh, value3.FLD_INDEX] = new X_Vo_Cong_Loai(value3.FLD_PID);
								CalculateMartialArtsAttackPowerOfHusbandAndWifeData();
							}
							break;
						}
					case 1000000213:
						{
							if (FLD_Couple.Length == 0)
							{
								return;
							}
							if (FLD_loveDegreeLevel > 10)
							{
								HeThongNhacNho("Tình yêu vượt cấp 10 là hành vi phá hoại giang hồ!", 10, "Thiên cơ các");
								return;
							}
							if (WhetherMarried != 1)
							{
								return;
							}
							var key = 0;
							if (Player_Sex == 1)
							{
								key = 601201;
							}
							else if (Player_Sex == 2)
							{
								key = 601202;
							}
							if (World.MagicList.TryGetValue(key, out var value2))
							{
								VoCongMoi[value2.FLD_VoCongLoaiHinh, value2.FLD_INDEX] = new X_Vo_Cong_Loai(value2.FLD_PID);
								CalculateMartialArtsAttackPowerOfHusbandAndWifeData();
							}
							break;
						}
					case 1000000217:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000218:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000219:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000220:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000221:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000222:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000223:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000224:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000225:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000226:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000227:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000228:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000229:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000230:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000231:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000232:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000233:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000234:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000235:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000236:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000237:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000238:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000239:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000240:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000241:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000242:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000243:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000244:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000245:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000246:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000247:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 5);
						break;
					case 1000000248:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 5);
						break;
					case 1000000249:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
						break;
					case 1000001003:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 23))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 23);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 24);
						break;
					case 1000001004:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 19))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 19);
						break;
					case 1000001005:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 9))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 9);
						break;
					case 1000001006:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 12))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 12);
						break;
					case 1000001007:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 14))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 14);
						break;
					case 1000001008:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 17);
						break;
					case 1000001009:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 5);
						break;
					case 1000001010:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 15))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 15);
						break;
					case 1000001013:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 16))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 16);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						break;
					case 1000001046:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
						break;
					case 1000001047:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 18))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
						break;
					case 1000001049:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 25);
						break;
					case 1000001050:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
						break;
					case 1000000564:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 19))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 19);
						break;
					case 1000000565:
						{
							var wg3 = X_Vo_Cong_Loai.GetWg(1010701);
							if (wg3 == null || (wg3.FLD_ZX != 0 && Player_Zx != wg3.FLD_ZX) || (wg3.FLD_JOB != 0 && Player_Job != wg3.FLD_JOB) || (wg3.FLD_JOBLEVEL != 0 && Player_Job_level < wg3.FLD_JOBLEVEL) || (wg3.FLD_LEVEL != 0 && Player_Level < wg3.FLD_LEVEL))
							{
								return;
							}
							VoCongMoi[wg3.FLD_VoCongLoaiHinh, wg3.FLD_INDEX] = new X_Vo_Cong_Loai(wg3.FLD_PID);
							VoCongMoi[wg3.FLD_VoCongLoaiHinh, wg3.FLD_INDEX].VoCong_DangCap = 1;
							break;
						}
					case 1000000566:
						{
							var wg = X_Vo_Cong_Loai.GetWg(1020701);
							if (wg == null || (wg.FLD_ZX != 0 && Player_Zx != wg.FLD_ZX) || (wg.FLD_JOB != 0 && Player_Job != wg.FLD_JOB) || (wg.FLD_JOBLEVEL != 0 && Player_Job_level < wg.FLD_JOBLEVEL) || (wg.FLD_LEVEL != 0 && Player_Level < wg.FLD_LEVEL))
							{
								return;
							}
							VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX] = new X_Vo_Cong_Loai(wg.FLD_PID);
							VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX].VoCong_DangCap = 1;
							break;
						}
					case 1000000567:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 10);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 24);
						break;
					case 1000000568:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 11);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
						break;
					case 1000000569:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 9))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 9);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 12);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 14);
						break;
					case 1000000570:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 10))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 10);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 11);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 12);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 14);
						break;
					case 1000000488:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000489:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000490:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000491:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000000492:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000000493:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000001223:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 17);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 18);
						break;
					case 1000001164:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 3);
						break;
					case 1000001165:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 6);
						break;
					case 1000001166:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 9))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 9);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
						break;
					case 1000001167:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
						break;
					case 1000001205:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						break;
					case 1000001206:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 20))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 20);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
						break;
					case 1000001191:
					case 1000001192:
					case 1000001193:
					case 1000001194:
					case 1000001195:
					case 1000001196:
					case 1000001197:
					case 1000001198:
					case 1000001199:
					case 1000001200:
					case 1000001201:
					case 1000001202:
					case 1000001203:
					case 1000001204:
					case 1000001207:
					case 1000001208:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
						break;
					case 1000001209:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
						break;
					case 1000001210:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
						break;
					case 1000001100:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000001101:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						break;
					case 1000001102:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000001103:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						break;
					case 1000001104:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000001105:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						break;
					case 1000000336:
					case 1000000337:
					case 1000000338:
					case 1000000339:
					case 1000000340:
					case 1000000341:
					case 1000000342:
					case 1000000343:
					case 1000000344:
					case 1000000345:
					case 1000000346:
					case 1000000347:
					case 1000000494:
					case 1000000497:
					case 1000001106:
					case 1000001107:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 2))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
						break;
					case 1000000388:
					case 1000000389:
					case 1000000390:
					case 1000000391:
					case 1000000392:
					case 1000000393:
					case 1000000394:
					case 1000000395:
					case 1000000396:
					case 1000000397:
					case 1000000398:
					case 1000000399:
					case 1000000495:
					case 1000000498:
					case 1000001108:
					case 1000001109:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 4) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 4);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
						break;
					case 1000000464:
					case 1000000465:
					case 1000000466:
					case 1000000467:
					case 1000000468:
					case 1000000469:
					case 1000000470:
					case 1000000471:
					case 1000000472:
					case 1000000473:
					case 1000000474:
					case 1000000475:
					case 1000000496:
					case 1000000499:
					case 1000001110:
					case 1000001111:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 7) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 8))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 7);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 8);
						break;
					case 1000001032:
					case 1000001033:
					case 1000001034:
					case 1000001035:
					case 1000001036:
					case 1000001037:
					case 1000001038:
					case 1000001039:
					case 1000001040:
					case 1000001041:
					case 1000001042:
					case 1000001043:
					case 1000001044:
					case 1000001045:
					case 1000001112:
					case 1000001113:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 10) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 11))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 11);
						break;
					case 1000001532:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 3);
						break;
					case 1000001533:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 6);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 7);
						break;
					case 1000001534:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 9))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 9);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 11);
						break;
					case 1000001535:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 15);
						break;
					case 1000001284:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
						break;
					case 1000001285:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 28);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 29);
						break;
					case 1000001286:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 4))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 4);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
						break;
					case 1000001287:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 7))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 7);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 8);
						break;
					case 1000001288:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 10))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 11);
						break;
					case 1000001289:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
						break;
					case 1000001231:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
						break;
					case 1000001235:
						if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
						{
							return;
						}
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
						X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
						break;
				}
				ItemUse(1, num, 1);
				SkillExperience -= value.FLD_NEED_FIGHTEXP;
				Player_Money -= value.FLD_NEED_MONEY;
				LearningSkillsTips();
				UpdateMartialArtsAndStatus();
				UpdateMoneyAndWeight();
				UpdateKinhNghiemVaTraiNghiem();
			}
			else if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != 0)
			{
				if (value.FLD_LEVEL > Player_Level)
				{
					HeThongNhacNho("Cấp độ chưa đủ để lĩnh hội võ công!!", 20, "Thiên cơ các");
				}
				else if (value.FLD_ZX != Player_Zx)
				{
					HeThongNhacNho("Thế lực không phù hợp để học võ công này!!", 20, "Thiên cơ các");
				}
				else if (value.FLD_RESIDE1 != Player_Job)
				{
					HeThongNhacNho("Bí tịch này không dùng được cho nhân vật của đại hiệp!", 20, "Thiên cơ các");
				}
				else if (value.FLD_JOB_LEVEL > Player_Job_level)
				{
					HeThongNhacNho("Chưa đủ thăng chức hoặc thăng thiên để lĩnh hội!", 20, "Thiên cơ các");
				}
				else if (value.FLD_NEED_FIGHTEXP <= 0)
				{
					HeThongNhacNho("Điểm kỹ năng không đủ để học võ công!!", 20, "Thiên cơ các");
				}
				else if (SkillExperience < value.FLD_NEED_FIGHTEXP)
				{
					HeThongNhacNho("Điểm kỹ năng không đủ để học võ công!!", 20, "Thiên cơ các");
				}
				else
				{
					HeThongNhacNho("Chưa đủ điều kiện để lĩnh hội võ công này!!", 20, "Thiên cơ các");
				}
			}
		}
		catch
		{
		}
	}
		public void XoaBoThoLinhPhu(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			var array2 = new byte[15];
			Buffer.BlockCopy(packetData, 10, array2, 0, 15);
			Buffer.BlockCopy(packetData, 24, array, 0, 2);
			ThoLinhPhu_ToaDo.Remove(BitConverter.ToInt32(array, 0));
			var array3 = Converter.HexStringToByte("AA55310000000F10230001000A003132333400000000000000000000006500000054A8034400007041342FCB44000000000000000055AA");
			Buffer.BlockCopy(array, 0, array3, 12, 2);
			Buffer.BlockCopy(array2, 0, array3, 14, array2.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
			Client?.Send_Map_Data(array3, array3.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "xóa bỏ ThoLinhPhu error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SaveEarthTalisman(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			if (World.WhetherTheCurrentLineIsSilver == 1)
			{
				HeThongNhacNho("Tại quảng trường ngân tệ, không thể sử dụng Thổ Linh Phù!");
				return;
			}
			var array = World.BanDoKhoa_Chat.Split(';');
			if (array.Length >= 1)
			{
				for (var i = 0; i < array.Length; i++)
				{
					if (int.Parse(array[i]) == MapID)
					{
						return;
					}
				}
			}
			var array2 = new byte[4];
			var array3 = new byte[15];
			for (var j = 0; j < 15 && packetData[10 + j] != 0; j++)
			{
				array3[j] = packetData[10 + j];
			}
			var rxjhName = Encoding.Default.GetString(array3).Replace("\0", string.Empty);
			Buffer.BlockCopy(packetData, 25, array2, 0, 2);
			X_Toa_Do_Class xToaDoClass = new(PosX, PosY, PosZ, MapID);
			xToaDoClass.Rxjh_name = rxjhName;
			if (ThoLinhPhu_ToaDo.ContainsKey(BitConverter.ToInt32(array2, 0)))
			{
				ThoLinhPhu_ToaDo.Remove(BitConverter.ToInt32(array2, 0));
			}
			ThoLinhPhu_ToaDo.Add(BitConverter.ToInt32(array2, 0), xToaDoClass);
			SendAndSaveEarthenTalismanData(array3, array2, xToaDoClass);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Save ThoLinhPhu error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SendAndSaveEarthenTalismanData(byte[] name, byte[] saveId, X_Toa_Do_Class toaDo)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55310000000D10230001000A003132333400000000000000000000006500000054A8034400007041342FCB44000000000000000055AA");
			Buffer.BlockCopy(saveId, 0, array, 12, 2);
			Buffer.BlockCopy(name, 0, array, 14, name.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(toaDo.Rxjh_Map), 0, array, 29, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(toaDo.Rxjh_X), 0, array, 33, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(toaDo.Rxjh_Z), 0, array, 37, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(toaDo.Rxjh_Y), 0, array, 41, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Transmit Save ThoLinhPhuSoLieu error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SendEarthenTalismanData()
	{
		var array = Converter.HexStringToByte("************************");
		var array2 = Converter.HexStringToByte("000000000000000055AA");
		var array3 = new byte[ThoLinhPhu_ToaDo.Count * 33 + array.Length + array2.Length];
		Buffer.BlockCopy(array, 0, array3, 0, array.Length);
		Buffer.BlockCopy(array2, 0, array3, array3.Length - array2.Length, array2.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(2 + ThoLinhPhu_ToaDo.Count * 33), 0, array3, 8, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(16 + ThoLinhPhu_ToaDo.Count * 33), 0, array3, 2, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(ThoLinhPhu_ToaDo.Count), 0, array3, 10, 1);
		var num = 0;
		foreach (DictionaryEntry item in ThoLinhPhu_ToaDo)
		{
			var xToaDoClass = (X_Toa_Do_Class)item.Value;
			var value = (int)item.Key;
			var bytes = Encoding.Default.GetBytes(xToaDoClass.Rxjh_name);
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array3, 12 + num * 33, 2);
			Buffer.BlockCopy(bytes, 0, array3, 14 + num * 33, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(xToaDoClass.Rxjh_Map), 0, array3, 29 + num * 33, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(xToaDoClass.Rxjh_X), 0, array3, 33 + num * 33, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(xToaDoClass.Rxjh_Z), 0, array3, 37 + num * 33, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(xToaDoClass.Rxjh_Y), 0, array3, 41 + num * 33, 4);
			num++;
		}
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
		Client?.Send_Map_Data(array3, array3.Length);
	}

	public void SuDung_ThoLinhPhu(byte[] packetData, int length)
	{
		try
		{
			if (GMMode == 0)
			{
				if (PlayerTuVong || OpenWarehouse)
				{
					return;
				}
				if (DateTime.Now.Subtract(UseDatuTime).TotalSeconds < 5.0)
				{
					HeThongNhacNho("Thổ Linh Phù mỗi 5 giây chỉ được thi triển một lần!");
					return;
				}
				if (World.WhetherTheCurrentLineIsSilver == 1)
				{
					HeThongNhacNho("Mời đại hiệp đến Quảng Trường Tiền Tệ để dịch chuyển về Huyền Bột Phái!", 10, "Thiên cơ các");
					return;
				}
			}
			UseDatuTime = DateTime.Now;
			PacketModification(packetData, length);
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 10, array, 0, 2);
			Buffer.BlockCopy(packetData, 12, array2, 0, 2);
			var dst = Converter.HexStringToByte("AA552200020308101C0001000000000000000000000000000000000000000000000000000A0055AA");
			var array3 = Converter.HexStringToByte("AA552B002C0108101C0001000A003E44030000000000C399053C00000000010018943577010000000000000000000055AA");
			var xToaDoClass = (X_Toa_Do_Class)ThoLinhPhu_ToaDo[BitConverter.ToInt32(array2, 0)];
			if (xToaDoClass == null && GMMode == 0)
			{
				if (xToaDoClass.Rxjh_Map == 801 || xToaDoClass.Rxjh_Map == 1001 || xToaDoClass.Rxjh_Map == 40101 || xToaDoClass.Rxjh_Map == 9001 || xToaDoClass.Rxjh_Map == 9101 || xToaDoClass.Rxjh_Map == 9201 || xToaDoClass.Rxjh_Map == 42001 || xToaDoClass.Rxjh_Map == 42101 || xToaDoClass.Rxjh_Map == 32001 || xToaDoClass.Rxjh_Map == 30000 || xToaDoClass.Rxjh_Map == 30100 || xToaDoClass.Rxjh_Map == 30200 || xToaDoClass.Rxjh_Map == 30300 || xToaDoClass.Rxjh_Map == 32101 || xToaDoClass.Rxjh_Map == 32102 || xToaDoClass.Rxjh_Map == 32103 || xToaDoClass.Rxjh_Map == 32104 || xToaDoClass.Rxjh_Map == 32105 || xToaDoClass.Rxjh_Map == 32106 || xToaDoClass.Rxjh_Map == 32107 || xToaDoClass.Rxjh_Map == 32108 || xToaDoClass.Rxjh_Map == 32109)
				{
					HeThongNhacNho("Bản đồ này không cho phép sử dụng bảo vật hay bí thuật!!");
					return;
				}
				if (xToaDoClass.Rxjh_Map == 30000 || xToaDoClass.Rxjh_Map == 30100 || xToaDoClass.Rxjh_Map == 30200 || xToaDoClass.Rxjh_Map == 30300)
				{
					if (RemainingTimeOfTrainingMap <= 0)
					{
						HeThongNhacNho("Dịch chuyển biến mất, thời gian luyện công tại bản đồ đã cạn!");
						return;
					}
					FBtime = DateTime.Now;
					HeThongNhacNho("Thời gian bắt đầu tính, sau [" + RemainingTimeOfTrainingMap + "] khắc, đại hiệp sẽ tự động được truyền tống về Huyền Bột Phái!");
				}
			}
			if (xToaDoClass.Rxjh_Map == World.HuyenBotPhai || xToaDoClass.Rxjh_Map == World.TamTaQuan || xToaDoClass.Rxjh_Map == World.LieuChinhQuan || xToaDoClass.Rxjh_Map == World.LieuThienPhu || xToaDoClass.Rxjh_Map == World.NamMinhHieu || xToaDoClass.Rxjh_Map == World.ThanVoMon || xToaDoClass.Rxjh_Map == World.TungNguyetQuan || xToaDoClass.Rxjh_Map == World.BachVoQuan || xToaDoClass.Rxjh_Map == World.BacHaiBangCung || xToaDoClass.Rxjh_Map == World.NamLam || xToaDoClass.Rxjh_Map == World.HoHapCoc || xToaDoClass.Rxjh_Map == World.XichThienGioi || xToaDoClass.Rxjh_Map == World.ThienDuSon || xToaDoClass.Rxjh_Map == World.ThanhDiaKiemHoang || (xToaDoClass.Rxjh_Map >= World.KhuLuyenTap1 && xToaDoClass.Rxjh_Map <= World.KhuLuyenTap9) || xToaDoClass.Rxjh_Map == World.PhongThanKhau)
			{
				var array4 = new byte[4];
				var array5 = new byte[8];
				var dst2 = new byte[4];
				var num = BitConverter.ToInt32(array, 0);
				var value = BitConverter.ToInt32(array2, 0);
				if (Item_In_Bag[num].GetVatPham_ID == 1007000003 || Item_In_Bag[num].GetVatPham_ID == 1007000008 || Item_In_Bag[num].GetVatPham_ID == 1007000020 || Item_In_Bag[num].GetVatPham_ID == 1007000021 || Item_In_Bag[num].GetVatPham_ID == 1007000023)
				{
					if (num >= 0)
					{
						if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1007000003 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1007000008 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1007000020 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1007000021 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1007000023)
						{
							Buffer.BlockCopy(Item_In_Bag[num].VatPham_byte, 16, array4, 0, 4);
							Buffer.BlockCopy(Item_In_Bag[num].VatPham_byte, 8, dst2, 0, 4);
							Buffer.BlockCopy(Item_In_Bag[num].VatPham_byte, 0, array5, 0, 8);
							var num2 = BitConverter.ToInt32(array4, 0) - 2000000000 - 1;
							if (num2 <= 0)
							{
								Buffer.BlockCopy(BitConverter.GetBytes(2000000000), 0, array3, 32, 4);
							}
							else
							{
								Buffer.BlockCopy(BitConverter.GetBytes(num2 + 2000000000), 0, array3, 32, 4);
								Buffer.BlockCopy(BitConverter.GetBytes(num2 + 2000000000), 0, Item_In_Bag[num].VatPham_byte, 16, 4);
							}
							Buffer.BlockCopy(array5, 0, array3, 14, 8);
							Buffer.BlockCopy(array, 0, array3, 12, 2);
							Buffer.BlockCopy(Item_In_Bag[num].VatPham_ID, 0, array3, 22, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
							Client?.Send_Map_Data(array3, array3.Length);
							if (num2 <= 0)
							{
								SubtractItem(num, 1);
							}
						}
					}
					else
					{
						Buffer.BlockCopy(BitConverter.GetBytes(value), 0, dst, 36, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, dst, 4, 2);
						Client?.Send_Map_Data(array3, array3.Length);
					}
					if (MapID == 801 || MapID == 1001 || MapID == 40101)
					{
						HeThongNhacNho("Bản đồ này không cho phép sử dụng bảo vật hay bí thuật!!");
						return;
					}
					if (xToaDoClass != null)
					{
						Mobile(xToaDoClass.Rxjh_X, xToaDoClass.Rxjh_Y, xToaDoClass.Rxjh_Z, xToaDoClass.Rxjh_Map, 0);
					}
					GetTheReviewRangePlayers();
					GetReviewScopeNpc();
					ScanGroundItems();
				}
				else if (Item_In_Bag[num].GetVatPham_ID == **********)
				{
					HeThongNhacNho("Bảo vật này không thể thi triển được!!");
				}
				else
				{
					HeThongNhacNho("Bí thuật này không thể sử dụng trong hoàn cảnh hiện tại!!");
				}
			}
			else
			{
				HeThongNhacNho("Bản đồ đang bị phong tỏa, không thể hành động!!", 20, "Thiên cơ các");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "sử dụng ThoLinhPhu error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SetPublicPills()
	{
		foreach (var value in PublicDrugs.Values)
		{
			SetPublicPills(value);
		}
	}

	public void SetPublicPills(X_Cong_Huu_Duoc_Pham_Loai yp)
	{
		try
		{
			var array = Converter.HexStringToByte("AA5518002C01F9000A00037C17765E00FFFFFFFF000000000000000055AA");
			var dateTime = new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(yp.ThoiGian);
			if (dateTime > DateTime.Now)
			{
				switch (yp.DuocPhamID)
				{
					case **********:
						Buffer.BlockCopy(BitConverter.GetBytes(11), 0, array, 10, 1);
						break;
					case **********:
						Buffer.BlockCopy(BitConverter.GetBytes(12), 0, array, 10, 1);
						break;
					case **********:
						Buffer.BlockCopy(BitConverter.GetBytes(13), 0, array, 10, 1);
						break;
					case **********:
						Buffer.BlockCopy(BitConverter.GetBytes(28), 0, array, 10, 1);
						break;
					case **********:
						Buffer.BlockCopy(BitConverter.GetBytes(29), 0, array, 10, 1);
						break;
					case 1008000063:
						Buffer.BlockCopy(BitConverter.GetBytes(30), 0, array, 10, 1);
						break;
					case 1008000027:
						Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 10, 1);
						break;
					case 1008000028:
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 10, 1);
						break;
					case 1008000029:
						Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 10, 1);
						break;
					case 1008000141:
						Buffer.BlockCopy(BitConverter.GetBytes(37), 0, array, 10, 1);
						break;
					case 1008000140:
						Buffer.BlockCopy(BitConverter.GetBytes(36), 0, array, 10, 1);
						break;
					case 1008000124:
						Buffer.BlockCopy(BitConverter.GetBytes(35), 0, array, 10, 1);
						break;
					case 1008000312:
						Buffer.BlockCopy(BitConverter.GetBytes(70), 0, array, 10, 1);
						break;
					case 1008000311:
						Buffer.BlockCopy(BitConverter.GetBytes(69), 0, array, 10, 1);
						break;
					default:
						Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 10, 1);
						break;
					case 1008000877:
						Buffer.BlockCopy(BitConverter.GetBytes(69), 0, array, 10, 1);
						break;
					case **********:
						Buffer.BlockCopy(BitConverter.GetBytes(37), 0, array, 10, 1);
						break;
					case **********:
						break;
				}
				Buffer.BlockCopy(BitConverter.GetBytes(yp.ThoiGian), 0, array, 11, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "VatPham Chí Tôn Phù ALL error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

}
