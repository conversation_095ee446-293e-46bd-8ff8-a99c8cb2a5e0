-- Complete script to add auto-increment primary keys for BBG database tables
-- <PERSON><PERSON><PERSON> hoàn chỉnh để thêm auto-increment primary key cho các bảng BBG database
-- Generated automatically from entity analysis
-- <PERSON><PERSON><PERSON><PERSON> tạo tự động từ phân tích entity

-- ========================================
-- SECTION 1: CREATE SEQUENCES
-- PHẦN 1: TẠO CÁC SEQUENCE
-- ========================================

CREATE SEQUENCE IF NOT EXISTS "cash_shop_log_id_seq";
CREATE SEQUENCE IF NOT EXISTS "cashshop_id_seq";
CREATE SEQUENCE IF NOT EXISTS "giftcodelog_id_seq";
CREATE SEQUENCE IF NOT EXISTS "item_vong_quay_id_seq";
CREATE SEQUENCE IF NOT EXISTS "itmeclss_id_seq";
CREATE SEQUENCE IF NOT EXISTS "mailcod_id_seq";
CREATE SEQUENCE IF NOT EXISTS "marketplace_id_seq";
CREATE SEQUENCE IF NOT EXISTS "order_detail_id_seq";
CREATE SEQUENCE IF NOT EXISTS "shopcategory_id_seq";
CREATE SEQUENCE IF NOT EXISTS "vong_quay_result_id_seq";

-- ========================================
-- SECTION 2: SET SEQUENCE VALUES AND ATTACH TO ID COLUMNS
-- PHẦN 2: THIẾT LẬP GIÁ TRỊ SEQUENCE VÀ GẮN VÀO CÁC CỘT ID
-- ========================================

-- Table: cash_shop_log
SELECT setval('cash_shop_log_id_seq', COALESCE((SELECT MAX("id") FROM "cash_shop_log"), 0) + 1, false);
ALTER TABLE "cash_shop_log"
   ALTER COLUMN "id" SET DEFAULT nextval('cash_shop_log_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "cash_shop_log_id_seq" OWNED BY "cash_shop_log"."id";
SELECT 'Setup completed for cash_shop_log' as status;

-- Table: cashshop
SELECT setval('cashshop_id_seq', COALESCE((SELECT MAX("id") FROM "cashshop"), 0) + 1, false);
ALTER TABLE "cashshop"
   ALTER COLUMN "id" SET DEFAULT nextval('cashshop_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "cashshop_id_seq" OWNED BY "cashshop"."id";
SELECT 'Setup completed for cashshop' as status;

-- Table: giftcodelog
SELECT setval('giftcodelog_id_seq', COALESCE((SELECT MAX("id") FROM "giftcodelog"), 0) + 1, false);
ALTER TABLE "giftcodelog"
   ALTER COLUMN "id" SET DEFAULT nextval('giftcodelog_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "giftcodelog_id_seq" OWNED BY "giftcodelog"."id";
SELECT 'Setup completed for giftcodelog' as status;

-- Table: item_vong_quay
SELECT setval('item_vong_quay_id_seq', COALESCE((SELECT MAX("id") FROM "item_vong_quay"), 0) + 1, false);
ALTER TABLE "item_vong_quay"
   ALTER COLUMN "id" SET DEFAULT nextval('item_vong_quay_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "item_vong_quay_id_seq" OWNED BY "item_vong_quay"."id";
SELECT 'Setup completed for item_vong_quay' as status;

-- Table: itmeclss
SELECT setval('itmeclss_id_seq', COALESCE((SELECT MAX("id") FROM "itmeclss"), 0) + 1, false);
ALTER TABLE "itmeclss"
   ALTER COLUMN "id" SET DEFAULT nextval('itmeclss_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "itmeclss_id_seq" OWNED BY "itmeclss"."id";
SELECT 'Setup completed for itmeclss' as status;

-- Table: mailcod
SELECT setval('mailcod_id_seq', COALESCE((SELECT MAX("id") FROM "mailcod"), 0) + 1, false);
ALTER TABLE "mailcod"
   ALTER COLUMN "id" SET DEFAULT nextval('mailcod_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "mailcod_id_seq" OWNED BY "mailcod"."id";
SELECT 'Setup completed for mailcod' as status;

-- Table: marketplace
SELECT setval('marketplace_id_seq', COALESCE((SELECT MAX("id") FROM "marketplace"), 0) + 1, false);
ALTER TABLE "marketplace"
   ALTER COLUMN "id" SET DEFAULT nextval('marketplace_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "marketplace_id_seq" OWNED BY "marketplace"."id";
SELECT 'Setup completed for marketplace' as status;

-- Table: order_detail
SELECT setval('order_detail_id_seq', COALESCE((SELECT MAX("id") FROM "order_detail"), 0) + 1, false);
ALTER TABLE "order_detail"
   ALTER COLUMN "id" SET DEFAULT nextval('order_detail_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "order_detail_id_seq" OWNED BY "order_detail"."id";
SELECT 'Setup completed for order_detail' as status;

-- Table: shopcategory
SELECT setval('shopcategory_id_seq', COALESCE((SELECT MAX("id") FROM "shopcategory"), 0) + 1, false);
ALTER TABLE "shopcategory"
   ALTER COLUMN "id" SET DEFAULT nextval('shopcategory_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "shopcategory_id_seq" OWNED BY "shopcategory"."id";
SELECT 'Setup completed for shopcategory' as status;

-- Table: vong_quay_result
SELECT setval('vong_quay_result_id_seq', COALESCE((SELECT MAX("id") FROM "vong_quay_result"), 0) + 1, false);
ALTER TABLE "vong_quay_result"
   ALTER COLUMN "id" SET DEFAULT nextval('vong_quay_result_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "vong_quay_result_id_seq" OWNED BY "vong_quay_result"."id";
SELECT 'Setup completed for vong_quay_result' as status;

-- ========================================
-- VERIFICATION QUERIES
-- CÁC TRUY VẤN XÁC MINH
-- ========================================

-- Check all sequences created
SELECT schemaname, sequencename, last_value, start_value, increment_by
FROM pg_sequences 
WHERE sequencename LIKE '%_id_seq'
ORDER BY sequencename;

-- Check column defaults for BBG tables
SELECT 
    table_name,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN (
    'cash_shop_log', 'cashshop', 'giftcodelog', 'item_vong_quay', 'itmeclss',
    'mailcod', 'marketplace', 'order_detail', 'shopcategory', 'vong_quay_result'
) AND column_name = 'id'
ORDER BY table_name;
