-- =====================================================
-- HeroYulgang - New Drop System Database Tables
-- =====================================================

-- Table: tbl_new_drops
-- Purpose: Enhanced drop system with decimal rates and flexible source types
CREATE TABLE IF NOT EXISTS tbl_new_drops (
    id SERIAL PRIMARY KEY,

    -- Drop Source (unified approach)
    source_type VARCHAR(20) NOT NULL,     -- 'level_range', 'npc_specific', 'quest_based'
    source_value VARCHAR(50) NOT NULL,    -- '1-20', '15100', '789'

    -- Item Definition
    item_id INTEGER NOT NULL,
    drop_rate DECIMAL(8,6) NOT NULL,      -- 0.000001 to 1.000000 (true percentage)
    quantity_min INTEGER DEFAULT 1,
    quantity_max INTEGER DEFAULT 1,

    -- Magic Properties (same as old system)
    magic0 INTEGER DEFAULT 0,
    magic1 INTEGER DEFAULT 0,
    magic2 INTEGER DEFAULT 0,
    magic3 INTEGER DEFAULT 0,
    magic4 INTEGER DEFAULT 0,

    -- Enhanced Controls
    expire_days INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 100,         -- Higher = more priority

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    -- Constraints
    CONSTRAINT chk_drop_rate CHECK (drop_rate >= 0.000001 AND drop_rate <= 1.000000),
    CONSTRAINT chk_quantity CHECK (quantity_min >= 1 AND quantity_max >= quantity_min),
    CONSTRAINT chk_source_type CHECK (source_type IN ('level_range', 'npc_specific', 'quest_based'))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_new_drops_source_active 
    ON tbl_new_drops (source_type, source_value, is_active);
CREATE INDEX IF NOT EXISTS idx_new_drops_item_rate 
    ON tbl_new_drops (item_id, drop_rate);
CREATE INDEX IF NOT EXISTS idx_new_drops_priority 
    ON tbl_new_drops (priority DESC);

-- =====================================================

-- Table: tbl_drop_config
-- Purpose: Runtime configuration for drop system
CREATE TABLE IF NOT EXISTS tbl_drop_config (
    config_key VARCHAR(50) PRIMARY KEY,
    config_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Default configuration values
INSERT INTO tbl_drop_config (config_key, config_value, description) VALUES
('new_drop_system_enabled', 'false', 'Enable new drop system (true/false)'),
('global_drop_multiplier', '1.0', 'Global drop rate multiplier (decimal)'),
('debug_drop_logging', 'false', 'Enable detailed drop logging (true/false)'),
('max_drops_per_kill', '5', 'Maximum number of items that can drop from one kill'),
('level_gap_penalty', '0.1', 'Drop rate penalty per level difference'),
('team_drop_bonus', '1.2', 'Drop rate bonus for team kills')
ON CONFLICT (config_key) DO NOTHING;

-- =====================================================

-- Migration helper view
-- Purpose: Compare old vs new drop rates
CREATE OR REPLACE VIEW v_drop_rate_comparison AS
SELECT 
    'OLD_SYSTEM' as system_type,
    CONCAT(fld_level1, '-', fld_level2) as level_range,
    fld_pid as item_id,
    fld_pp as old_pp_value,
    ROUND(CAST(fld_pp AS DECIMAL) / 8000.0, 6) as equivalent_decimal_rate,
    CONCAT(ROUND(CAST(fld_pp AS DECIMAL) / 8000.0 * 100, 4), '%') as percentage_display
FROM tbl_xwwl_drop 
WHERE fld_pp > 0

UNION ALL

SELECT 
    'NEW_SYSTEM' as system_type,
    source_value as level_range,
    item_id,
    CAST(drop_rate * 8000 AS INTEGER) as old_pp_value,
    drop_rate as equivalent_decimal_rate,
    CONCAT(ROUND(drop_rate * 100, 4), '%') as percentage_display
FROM tbl_new_drops 
WHERE source_type = 'level_range' AND is_active = true;

-- =====================================================

-- Example data for testing
-- Uncomment to insert test data

/*
-- Example: Level-based drops (converted from old system)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, magic0, priority) VALUES
('level_range', '1-10', 100001, 0.125000, 50, 100),   -- 12.5% chance, basic item
('level_range', '1-10', 100002, 0.050000, 75, 90),    -- 5% chance, better item
('level_range', '11-20', 100003, 0.100000, 100, 100), -- 10% chance, mid-tier item
('level_range', '21-30', 100004, 0.075000, 125, 100); -- 7.5% chance, higher-tier item

-- Example: NPC-specific drops (boss drops)
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, magic0, magic1, priority) VALUES
('npc_specific', '15100', 200001, 0.250000, 200, 150, 200), -- 25% chance from specific boss
('npc_specific', '15100', 200002, 0.100000, 250, 200, 190), -- 10% chance from same boss
('npc_specific', '15236', 200003, 0.500000, 300, 250, 200); -- 50% chance from another boss

-- Example: Quest-based drops
INSERT INTO tbl_new_drops (source_type, source_value, item_id, drop_rate, magic0, priority) VALUES
('quest_based', '1001', 300001, 1.000000, 100, 300), -- 100% chance when quest 1001 is active
('quest_based', '1002', 300002, 0.750000, 150, 300); -- 75% chance when quest 1002 is active
*/

-- =====================================================

-- Utility functions

-- Function to convert old FLD_PP to decimal rate
CREATE OR REPLACE FUNCTION convert_pp_to_decimal(pp_value INTEGER)
RETURNS DECIMAL(8,6) AS $$
BEGIN
    IF pp_value <= 0 THEN
        RETURN 0.000001;
    END IF;
    
    RETURN LEAST(CAST(pp_value AS DECIMAL) / 8000.0, 1.000000);
END;
$$ LANGUAGE plpgsql;

-- Function to convert decimal rate to old FLD_PP
CREATE OR REPLACE FUNCTION convert_decimal_to_pp(decimal_rate DECIMAL)
RETURNS INTEGER AS $$
BEGIN
    RETURN GREATEST(1, LEAST(8000, CAST(decimal_rate * 8000 AS INTEGER)));
END;
$$ LANGUAGE plpgsql;

-- =====================================================

COMMENT ON TABLE tbl_new_drops IS 'Enhanced drop system with decimal rates and flexible source types';
COMMENT ON TABLE tbl_drop_config IS 'Runtime configuration for drop system';
COMMENT ON VIEW v_drop_rate_comparison IS 'Compare old vs new drop rates for migration';
