using System;
using System.Timers;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class X_Them_Vao_Trang_Thai_New_Loai : IDisposable
{
	public System.Timers.Timer npcyd;

	public DateTime time;

	public Players Play;

	private int _FLD_PID;

	private int _SoLuong;

	private int _SoLuongLoaiHinh;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public int SoLuong
	{
		get
		{
			return _SoLuong;
		}
		set
		{
			_SoLuong = value;
		}
	}

	public int SoLuongLoaiHinh
	{
		get
		{
			return _SoLuongLoaiHinh;
		}
		set
		{
			_SoLuongLoaiHinh = value;
		}
	}

	public int FLD_sj => getsj();

	public int getsj()
	{
		return (int)time.Subtract(DateTime.Now).TotalMilliseconds;
	}

	public void Dispose()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		Play = null;
	}

	public X_Them_Vao_Trang_Thai_New_Loai(Players Play_, int VatPham_ID, int ThoiGian, int SoLuong, int SoLuongLoaiHinh)
	{
		FLD_PID = VatPham_ID;
		this.SoLuong = SoLuong;
		this.SoLuongLoaiHinh = SoLuongLoaiHinh;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien2;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		ThoiGianKetThucSuKien();
	}

	public void ThoiGianKetThucSuKien()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (Play == null)
		{
			Dispose();
			return;
		}
		if (!Play.Exiting && Play.Client.Running)
		{
			try
			{
				switch (FLD_PID)
				{
				case 1:
					if (SoLuongLoaiHinh == 2)
					{
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.01 * SoLuong);
						if (Play.FLD_ThemVaoTiLePhanTram_CongKich < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
						}
					}
					else if (SoLuongLoaiHinh == 1)
					{
						Play.FLD_NhanVat_ThemVao_CongKich -= SoLuong;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
					}
					Play.UpdateMartialArtsAndStatus();
					break;
				case 2:
					if (SoLuongLoaiHinh == 2)
					{
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.01 * SoLuong);
						if (Play.FLD_ThemVaoTiLePhanTram_PhongNgu < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
						}
					}
					else if (SoLuongLoaiHinh == 1)
					{
						Play.FLD_NhanVat_ThemVao_PhongNgu -= SoLuong;
						if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0)
						{
							Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
						}
					}
					Play.UpdateMartialArtsAndStatus();
					break;
				case 3:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.01 * SoLuong;
						if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
						}
					}
					else if (SoLuongLoaiHinh == 1)
					{
						Play.CharactersToAddMax_HP -= SoLuong;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
					}
					if (Play.NhanVat_HP > Play.CharacterMax_HP)
					{
						Play.NhanVat_HP = Play.CharacterMax_HP;
					}
					Play.CapNhat_HP_MP_SP();
					break;
				case 4:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_ThemVaoTiLePhanTram_MPCaoNhat -= 0.01 * SoLuong;
						if (Play.FLD_ThemVaoTiLePhanTram_MPCaoNhat < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_MPCaoNhat = 0.0;
						}
					}
					else if (SoLuongLoaiHinh == 1)
					{
						Play.CharactersToAddMax_MP -= SoLuong;
						if (Play.CharactersToAddMax_MP < 0)
						{
							Play.CharactersToAddMax_MP = 0;
						}
					}
					if (Play.NhanVat_MP > Play.CharacterMax_MP)
					{
						Play.NhanVat_MP = Play.CharacterMax_MP;
					}
					Play.CapNhat_HP_MP_SP();
					break;
				case 5:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.01 * SoLuong;
						if (Play.FLD_ThemVaoTiLePhanTram_TrungDich < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
					}
					else if (SoLuongLoaiHinh == 1)
					{
						Play.FLD_NhanVat_ThemVao_TrungDich -= SoLuong;
						if (Play.FLD_NhanVat_ThemVao_TrungDich < 0)
						{
							Play.FLD_NhanVat_ThemVao_TrungDich = 0;
						}
						Play.UpdateMartialArtsAndStatus();
					}
					break;
				case 6:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0)
						{
							Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
					}
					else if (SoLuongLoaiHinh == 1)
					{
						Play.FLD_NhanVat_ThemVao_NeTranh -= SoLuong;
						if (Play.FLD_NhanVat_ThemVao_NeTranh < 0)
						{
							Play.FLD_NhanVat_ThemVao_NeTranh = 0;
						}
						Play.UpdateMartialArtsAndStatus();
					}
					break;
				case 7:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
					}
					Play.UpdateMartialArtsAndStatus();
					break;
				case 8:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
					}
					Play.UpdateMartialArtsAndStatus();
					break;
				case 9:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
					}
					break;
				case 10:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram -= 0.01 * SoLuong;
					}
					break;
				case 12:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 0.01 * SoLuong;
					}
					break;
				case 13:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.01 * SoLuong;
					}
					break;
				case 14:
					Play.FLD_NhanVat_ThemVao_KhiCong -= SoLuong;
					Play.UpdateKhiCong();
					Play.UpdateMartialArtsAndStatus();
					break;
				case 15:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
						}
					}
					break;
				case 16:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan = 0.0;
						}
					}
					break;
				case 17:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
						}
					}
					break;
				case 18:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_KinhNghiem_Party -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_Party < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_Party = 0.0;
						}
					}
					break;
				case 19:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_KinhNghiem_KetHon -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_KetHon < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.0;
						}
					}
					break;
				case 20:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_KinhNghiem_Bonus -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_Bonus < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_Bonus = 0.0;
						}
					}
					break;
				case 21:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TanThu -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TanThu < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TanThu = 0.0;
						}
					}
					break;
				case 22:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_ThemVao_Exp_CTP -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_ThemVao_Exp_CTP < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_Exp_CTP = 0.0;
						}
					}
					break;
				case 23:
					if (SoLuongLoaiHinh == 2)
					{
						Play.FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1022 -= 0.01 * SoLuong;
						if (Play.FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1022 < 0.0)
						{
							Play.FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1022 = 0.0;
						}
					}
					Play.UpdateMartialArtsAndStatus();
					break;
				case 24:
					Play.FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 -= SoLuong;
					Play.UpdateKhiCong();
					Play.UpdateMartialArtsAndStatus();
					break;
				}
				if (Play.AppendStatusNewList != null)
				{
					Play.AppendStatusNewList.Remove(FLD_PID);
				}
				Play.StateEffectsNew(FLD_PID, 0, FLD_sj, SoLuong, SoLuongLoaiHinh);
				Dispose();
				return;
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "thêm vào TrangThaiNew tốt bụng ThoiGianKetThucSuKien error：[" + FLD_PID + "]" + ex);
				return;
			}
			finally
			{
				Dispose();
			}
		}
		if (Play.AppendStatusNewList != null)
		{
			Play.AppendStatusNewList.Clear();
		}
		Dispose();
	}
}
