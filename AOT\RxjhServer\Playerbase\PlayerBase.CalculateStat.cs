﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer
{
    public partial class PlayersBes : <PERSON>_<PERSON><PERSON>_Cong_Thuoc_Tinh
    {
        
	public void TinhToan_NhanVatCoBan_DuLieu3()
	{
		var player_Level = Player_Level;
		var num = Player_Level;
		var characterExperience = CharacterExperience;
		var num2 = CharacterGreatestExperience;
		CharacterGreatestExperience = (long)World.lever[Player_Level];
		var num3 = 0;
		while (Player_Level < World.GioiHan_Level_CaoNhat)
		{
			if (characterExperience >= num2)
			{
				if (num3 <= 270 && Client.Running)
				{
					num++;
					num2 = (long)World.lever[num];
					num3++;
					continue;
				}
				return;
			}
			if (CharacterExperience - Convert.ToInt64(World.lever[Player_Level - 1]) < 1.0)
			{
				CharacterExperience = Convert.ToInt64(World.lever[Player_Level - 1]);
				num--;
				var num4 = World.lever[num];
			}
			break;
		}
		if (num - player_Level != 0)
		{
			TinhToan_NhanVatCoBan_DuLieu();
			var players = World.FindPlayerBySession(SessionID);
			if (players != null)
			{
				players.CapNhat_HP_MP_SP();
				players.UpdateMartialArtsAndStatus();
			}
			SaveGangData();
		}
	}

	public void TinhToan_NhanVatCoBan_DuLieu()
	{
		if (Player_Level > World.GioiHan_Level_CaoNhat)
		{
			Player_Level = World.GioiHan_Level_CaoNhat;
		}
		CharacterGreatestExperience = (long)World.lever[Player_Level];
		var player_Level = Player_Level;
		while (Player_Level < World.GioiHan_Level_CaoNhat)
		{
			if (Client == null || !Client.Running)
			{
				return;
			}
			if (CharacterExperience < CharacterGreatestExperience)
			{
				if (Player_Level != 1 && CharacterExperience - Convert.ToInt64(World.lever[Player_Level - 1]) < 1.0)
				{
					Player_Level--;
					CharacterGreatestExperience = (long)World.lever[Player_Level];
				}
				break;
			}
			Player_Level++;
			CharacterGreatestExperience = (long)World.lever[Player_Level];
			// restore hp/mp
			NhanVat_HP = CharacterMax_HP;
			NhanVat_MP = CharacterMax_MP;
		}
		if (Player_Level - player_Level > 0)
		{
			LevelUpNoti(1);
			var num = Player_Level - player_Level;
			if (num > 0)
			{
				for (var i = 0; i < num; i++)
				{
					if (Player_Level <= 34)
					{
						Player_Qigong_point++;
					}
					else if (Player_Level > 34 && Player_Level < 115)
					{
						Player_Qigong_point += 2;
					}
					else if (Player_Level >= 115)
					{
						Player_Qigong_point += 2;
					}
				}
			}
		}
		else if (Player_Level - player_Level < 0)
		{
			var num2 = player_Level - Player_Level;
			if (num2 > 0)
			{
				for (var j = 0; j < num2; j++)
				{
					if (Player_Level <= 34)
					{
						Player_Qigong_point--;
					}
					else
					{
						if (Player_Level > 34 && Player_Level < 115)
						{
							if (Player_Qigong_point >= 2)
							{
								Player_Qigong_point -= 2;
								continue;
							}
							for (var k = 0; k < 12; k++)
							{
								if (KhiCong[k].KhiCong_SoLuong >= 2)
								{
									KhiCong[k].KhiCong_SoLuong -= 2;
									KhoiTaoKhiCong();
									break;
								}
							}
							continue;
						}
						if (Player_Level >= 115)
						{
							if (Player_Qigong_point >= 2)
							{
								Player_Qigong_point -= 2;
								continue;
							}
							for (var l = 0; l < 12; l++)
							{
								if (KhiCong[l].KhiCong_SoLuong >= 3)
								{
									KhiCong[l].KhiCong_SoLuong -= 3;
									KhoiTaoKhiCong();
									break;
								}
							}
							continue;
						}
					}
					if (Player_Qigong_point >= 1)
					{
						Player_Qigong_point--;
						continue;
					}
					for (var m = 0; m < 5; m++)
					{
						if (KhiCong[m].KhiCong_SoLuong >= 1)
						{
							KhiCong[m].KhiCong_SoLuong--;
							KhoiTaoKhiCong();
							break;
						}
					}
				}
			}
		}
		CharacterMax_SP = 100 + Player_Level * 10;
		int_51 = 1000 + 100 * Player_Level;
		CharacterIsBasicallyTheLargest_HP = 0;
		CharacterIsBasicallyTheLargest_MP = 0;
		CharacterIsBasicallyTheLargest_Barrier = 0;
		FLD_Tam = 0;
		FLD_Than = 0;
		FLD_TrungDich = 0;
		FLD_NeTranh = 0;
		FLD_Luc = 0;
		FLD_The = 0;
		FLD_CongKichThapNhat = 0;
		FLD_LonNhatCongKich = 0;
		FLD_CongKich = 0;
		FLD_PhongNgu = 0;
		switch (Player_Job)
		{
			case 1:
				{
					var num7 = Player_Level;
					if (Player_Level >= 130)
					{
						num7 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 145 + (num7 - 1) * 12;
					CharacterIsBasicallyTheLargest_MP = 116 + (num7 - 1) * 2;
					FLD_CongKich = 8;
					FLD_PhongNgu = 15;
					FLD_TrungDich = 2 + (int)((num7 - 1) / 2.9 + 0.5);
					FLD_NeTranh = 4 + (int)((num7 - 1) / 1.9 + 0.5);
					FLD_Tam = 8 + (num7 - 1);
					FLD_Luc = 8;
					FLD_The = 15;
					FLD_Than = 9 + (num7 - 1);
					for (var num8 = 2; num8 <= num7; num8++)
					{
						if (num8 % 2 == 0)
						{
							FLD_Luc++;
							FLD_The++;
							FLD_CongKich++;
							FLD_PhongNgu++;
						}
						else
						{
							FLD_Luc += 2;
							FLD_The += 2;
							FLD_CongKich += 2;
							FLD_PhongNgu += 2;
						}
					}
					if (Player_Job_level >= 6 && Player_Level >= 115)
					{
						FLD_TrungDich += 17;
						FLD_NeTranh += 27;
						FLD_Tam += 50;
						FLD_Luc += 76;
						FLD_The += 77;
						FLD_Than += 54;
					}
					break;
				}
			case 2:
				{
					var num15 = Player_Level;
					if (Player_Level >= 130)
					{
						num15 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num15 - 1) * 9;
					CharacterIsBasicallyTheLargest_MP = 118;
					FLD_CongKich = 11 + (num15 - 1) * 2;
					FLD_PhongNgu = 11 + num15 - 1;
					FLD_TrungDich = 2 + (num15 - 1);
					FLD_NeTranh = 4 + (num15 - 1);
					FLD_Tam = 9;
					FLD_Luc = 11 + (num15 - 1) * 2;
					FLD_The = 11 + (num15 - 1);
					FLD_Than = 9 + (num15 - 1) * 2;
					for (var num16 = 2; num16 <= num15; num16++)
					{
						if (num16 % 2 == 0)
						{
							FLD_Tam++;
							CharacterIsBasicallyTheLargest_MP += 2;
						}
						else
						{
							FLD_Tam += 2;
							CharacterIsBasicallyTheLargest_MP += 4;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 41;
						FLD_NeTranh += 67;
						FLD_Tam += 75;
						FLD_Luc += 100;
						FLD_The += 52;
						FLD_Than += 99;
					}
					break;
				}
			case 3:
				{
					var num11 = Player_Level;
					if (Player_Level >= 130)
					{
						num11 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num11 - 1) * 7;
					CharacterIsBasicallyTheLargest_MP = 118 + (num11 - 1) * 2;
					FLD_CongKich = 13 + (num11 - 1) * 3;
					FLD_PhongNgu = 11 + (num11 - 1);
					FLD_TrungDich = 2 + (int)((num11 - 1) / 3.4 + 0.5);
					FLD_NeTranh = 3 + (int)((num11 - 1) / 2.0 + 0.5);
					FLD_Tam = 9 + (num11 - 1);
					FLD_Luc = 13 + (num11 - 1) * 3;
					FLD_The = 11 + (num11 - 1);
					FLD_Than = 7 + (num11 - 1);
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 12;
						FLD_NeTranh += 27;
						FLD_Tam += 51;
						FLD_Luc += 147;
						FLD_The += 52;
						FLD_Than += 53;
					}
					break;
				}
			case 4:
				{
					var num19 = Player_Level;
					if (Player_Level >= 130)
					{
						num19 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num19 - 1) * 6;
					CharacterIsBasicallyTheLargest_MP = 118 + (num19 - 1) * 4;
					FLD_CongKich = 11;
					FLD_PhongNgu = 9 + (num19 - 1);
					FLD_TrungDich = 3 + (num19 - 1);
					FLD_NeTranh = 5;
					FLD_Tam = 9 + (num19 - 1) * 2;
					FLD_Luc = 11;
					FLD_The = 9 + (num19 - 1);
					FLD_Than = 11 + (num19 - 1) * 3;
					for (var num20 = 2; num20 <= num19; num20++)
					{
						if (num20 % 2 == 0)
						{
							FLD_NeTranh += 2;
							FLD_Luc += 2;
							FLD_CongKich += 2;
						}
						else
						{
							FLD_NeTranh++;
							FLD_Luc += 3;
							FLD_CongKich += 3;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 31;
						FLD_NeTranh += 71;
						FLD_Tam += 99;
						FLD_Luc += 118;
						FLD_The += 49;
						FLD_Than += 143;
					}
					break;
				}
			case 5:
				{
					var num21 = Player_Level;
					if (Player_Level >= 130)
					{
						num21 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 124 + (num21 - 1) * 7;
					CharacterIsBasicallyTheLargest_MP = 136;
					FLD_CongKich = 7 + (int)((num21 - 1) * 2.1);
					FLD_PhongNgu = 8 + (num21 - 1);
					FLD_TrungDich = 2 + (int)(num21 / 4.0 + 0.5);
					FLD_NeTranh = 3 + (int)(num21 / 3.0 + 0.5);
					FLD_Tam = 18;
					FLD_Luc = 7 + (num21 - 1) * 2;
					FLD_The = 8 + (num21 - 1);
					FLD_Than = 7 + (num21 - 1);
					for (var num22 = 2; num22 <= num21; num22++)
					{
						if (num22 % 2 == 0)
						{
							CharacterIsBasicallyTheLargest_MP += 6;
							FLD_Tam += 3;
						}
						else
						{
							CharacterIsBasicallyTheLargest_MP += 8;
							FLD_Tam += 4;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 21;
						FLD_NeTranh += 46;
						FLD_Tam += 169;
						FLD_Luc += 96;
						FLD_The += 48;
						FLD_Than += 53;
					}
					break;
				}
			case 6:
				{
					var num12 = Player_Level;
					if (Player_Level >= 130)
					{
						num12 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num12 - 1) * 9;
					CharacterIsBasicallyTheLargest_MP = 114;
					FLD_CongKich = 10 + (int)((num12 - 1) * 2.1);
					FLD_PhongNgu = 9 + (num12 - 1);
					FLD_TrungDich = 4 + (num12 - 1);
					FLD_NeTranh = 7 + (num12 - 1) * 2;
					FLD_Tam = 7 + (num12 - 1) * 2;
					FLD_Luc = 10 + (num12 - 1) * 2;
					FLD_The = 9 + (num12 - 1);
					FLD_Than = 14;
					for (var num13 = 2; num13 <= num12; num13++)
					{
						if (num13 % 2 == 0)
						{
							CharacterIsBasicallyTheLargest_MP += 2;
							FLD_Than += 3;
						}
						else
						{
							CharacterIsBasicallyTheLargest_MP += 4;
							FLD_Than += 4;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 56;
						FLD_NeTranh += 83;
						FLD_Tam += 99;
						FLD_Luc += 102;
						FLD_The += 51;
						FLD_Than += 168;
					}
					break;
				}
			case 7:
				{
					var num4 = Player_Level;
					if (Player_Level >= 130)
					{
						num4 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = ((Player_Level > 2) ? (124 + (num4 - 2) * 6) : 124);
					CharacterIsBasicallyTheLargest_MP = 136 + (num4 - 1) * 8;
					FLD_CongKich = 7 + num4 * 2;
					FLD_PhongNgu = ((num4 != 1) ? (7 + (num4 - 2)) : 8);
					FLD_TrungDich = 2 + (int)((num4 - 1) / 3.5 + 0.5);
					FLD_NeTranh = 4 + (int)((num4 - 1) / 2.0 + 0.5);
					FLD_Tam = 18 + (num4 - 1) * 4;
					FLD_Luc = 7 + (num4 - 1) * 2;
					switch (num4)
					{
						default:
							FLD_The = 8 + (num4 - 3);
							break;
						case 2:
							FLD_The = 7;
							break;
						case 1:
							FLD_The = 8;
							break;
					}
					FLD_Than = 7 + (num4 - 1);
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 22;
						FLD_NeTranh += 46;
						FLD_Tam += 191;
						FLD_Luc += 101;
						FLD_The += 50;
						FLD_Than += 53;
					}
					break;
				}
			case 8:
				{
					var num5 = Player_Level;
					if (Player_Level >= 130)
					{
						num5 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 145 + (num5 - 1) * 12;
					CharacterIsBasicallyTheLargest_MP = 116 + (num5 - 1) * 2;
					FLD_CongKich = 8;
					FLD_PhongNgu = 15;
					FLD_TrungDich = 2 + (int)((num5 - 1) / 3.0 + 0.5);
					FLD_NeTranh = 4 + (int)((num5 - 1) / 3.0 + 0.5);
					FLD_Tam = 8 + (num5 - 1);
					FLD_Luc = 8;
					FLD_The = 15;
					FLD_Than = 9 + (num5 - 1);
					for (var num6 = 2; num6 <= num5; num6++)
					{
						if (num6 % 2 == 0)
						{
							FLD_Luc++;
							FLD_The++;
							FLD_CongKich++;
							FLD_PhongNgu++;
						}
						else
						{
							FLD_Luc += 2;
							FLD_The += 2;
							FLD_CongKich += 2;
							FLD_PhongNgu += 2;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 18;
						FLD_NeTranh += 57;
						FLD_Tam += 50;
						FLD_Luc += 76;
						FLD_The += 77;
						FLD_Than += 54;
					}
					break;
				}
			case 9:
				{
					var num9 = Player_Level;
					if (Player_Level >= 130)
					{
						num9 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num9 - 1) * 9;
					CharacterIsBasicallyTheLargest_MP = 118;
					FLD_CongKich = 11 + (num9 - 1) * 2;
					FLD_PhongNgu = 11 + (num9 - 1);
					FLD_TrungDich = 2;
					FLD_NeTranh = 4 + (num9 - 1);
					FLD_Tam = 9;
					FLD_Luc = 11 + (num9 - 1) * 2;
					FLD_The = 11 + (num9 - 1);
					FLD_Than = 9 + (num9 - 1) * 2;
					for (var num10 = 2; num10 <= num9; num10++)
					{
						if (num10 % 2 == 0)
						{
							CharacterIsBasicallyTheLargest_MP += 2;
							FLD_TrungDich++;
							FLD_Tam++;
						}
						else
						{
							FLD_Tam += 2;
							CharacterIsBasicallyTheLargest_MP += 4;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 41;
						FLD_NeTranh += 67;
						FLD_Tam += 75;
						FLD_Luc += 100;
						FLD_The += 52;
						FLD_Than += 99;
					}
					break;
				}
			case 10:
				{
					var num17 = Player_Level;
					if (Player_Level >= 130)
					{
						num17 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 145 + (num17 - 1) * 12;
					CharacterIsBasicallyTheLargest_MP = 114 + (num17 - 1) * 2;
					FLD_CongKich = 9;
					FLD_PhongNgu = 16 + (num17 - 1) * 2;
					FLD_TrungDich = 2 + (int)((num17 - 1) / 3.0 + 0.5);
					FLD_NeTranh = 4 + (int)((num17 - 1) / 2.0 + 0.5);
					FLD_Tam = 7 + (num17 - 1);
					FLD_Luc = 9;
					FLD_The = 16 + (num17 - 1) * 2;
					FLD_Than = 8;
					for (var num18 = 2; num18 <= num17; num18++)
					{
						if (num18 % 2 == 0)
						{
							FLD_Luc++;
							FLD_Than++;
							FLD_CongKich++;
						}
						else
						{
							FLD_Luc += 2;
							FLD_Than += 2;
							FLD_CongKich += 2;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 37;
						FLD_NeTranh += 67;
						FLD_Tam += 50;
						FLD_Luc += 75;
						FLD_Than += 74;
					}
					break;
				}
			case 11:
				{
					var num23 = Player_Level;
					if (Player_Level >= 130)
					{
						num23 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num23 - 1) * 4;
					CharacterIsBasicallyTheLargest_MP = 118 + (num23 - 1) * 3;
					CharacterIsBasicallyTheLargest_Barrier = 145 + (int)((num23 - 1) * 14.07563 + 0.5);
					FLD_CongKich = 11;
					FLD_PhongNgu = 9 + (num23 - 1);
					FLD_TrungDich = 3 + (int)((num23 - 1) / 3.0 + 0.5);
					FLD_NeTranh = 5 + (int)((num23 - 1) / 2.0 + 0.5);
					FLD_Tam = 9 + (num23 - 1);
					FLD_Luc = 11 + (int)((num23 - 1) / 2.0 + 0.5);
					FLD_The = 9 + (num23 - 1);
					FLD_Than = 11 + (int)((num23 - 1) / 3.0 + 0.5);
					for (var num24 = 2; num24 <= num23; num24++)
					{
						if (num24 % 2 == 0)
						{
							FLD_CongKich++;
						}
						else
						{
							FLD_CongKich += 2;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 12;
						FLD_NeTranh += 36;
						FLD_Tam += 52;
						FLD_Luc += 190;
						FLD_The += 47;
						FLD_Than += 133;
					}
					break;
				}
			case 12:
				{
					var num14 = Player_Level;
					if (Player_Level >= 130)
					{
						num14 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 133 + (num14 - 1) * 7;
					CharacterIsBasicallyTheLargest_MP = 118 + (num14 - 1) * 2;
					FLD_CongKich = 13 + (int)((num14 - 1) * 1.5);
					FLD_PhongNgu = 11 + (num14 - 1);
					FLD_TrungDich = 2 + (int)((num14 - 1) / 3.4 + 0.5);
					FLD_NeTranh = 3 + (int)((num14 - 1) / 2.0 + 0.5);
					FLD_Tam = 9 + (num14 - 1);
					FLD_Luc = 13 + (num14 - 1) * 3;
					FLD_The = 11 + (num14 - 1);
					FLD_Than = 7 + (num14 - 1);
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 12;
						FLD_NeTranh += 27;
						FLD_Tam += 51;
						FLD_Luc += 147;
						FLD_The += 52;
						FLD_Than += 53;
					}
					break;
				}
			case 13:
				{
					var num3 = Player_Level;
					if (Player_Level >= 130)
					{
						num3 += 5;
					}
					CharacterIsBasicallyTheLargest_HP = 124 + (num3 - 1) * 7;
					CharacterIsBasicallyTheLargest_MP = 136;
					FLD_CongKich = 7 + (int)((num3 - 1) * 1.5);
					FLD_PhongNgu = 8 + (num3 - 1);
					FLD_TrungDich = 2 + (int)(num3 / 4.0 + 0.5);
					FLD_NeTranh = 3 + (int)(num3 / 3.0 + 0.5);
					FLD_Tam = 18;
					FLD_Luc = 7 + (num3 - 1) * 2;
					FLD_The = 8 + (num3 - 1);
					FLD_Than = 7 + (num3 - 1);
					for (var n = 2; n <= num3; n++)
					{
						if (n % 2 == 0)
						{
							CharacterIsBasicallyTheLargest_MP += 6;
							FLD_Tam += 3;
						}
						else
						{
							CharacterIsBasicallyTheLargest_MP += 8;
							FLD_Tam += 4;
						}
					}
					if (Player_Level >= 115 && Player_Job_level >= 6)
					{
						FLD_TrungDich += 21;
						FLD_NeTranh += 46;
						FLD_Tam += 169;
						FLD_Luc += 96;
						FLD_The += 48;
						FLD_Than += 53;
					}
					break;
				}
		}
		FLD_CongKichThapNhat = FLD_CongKich;
		switch (Player_Job_level)
		{
			case 1:
				if (Player_Job == 11)
				{
					FLD_CongKich += 5;
					FLD_PhongNgu += 5;
					CharacterIsBasicallyTheLargest_HP += 25;
					CharacterIsBasicallyTheLargest_MP += 25;
				}
				else
				{
					FLD_CongKich += 5;
					FLD_PhongNgu += 5;
					CharacterIsBasicallyTheLargest_HP += 50;
					CharacterIsBasicallyTheLargest_MP += 50;
				}
				break;
			case 2:
				if (Player_Job == 11)
				{
					FLD_CongKich += 15;
					FLD_PhongNgu += 15;
					CharacterIsBasicallyTheLargest_HP += 75;
					CharacterIsBasicallyTheLargest_MP += 75;
				}
				else
				{
					FLD_CongKich += 15;
					FLD_PhongNgu += 15;
					CharacterIsBasicallyTheLargest_HP += 150;
					CharacterIsBasicallyTheLargest_MP += 150;
				}
				break;
			case 3:
				if (Player_Job == 11)
				{
					FLD_CongKich += 30;
					FLD_PhongNgu += 30;
					CharacterIsBasicallyTheLargest_HP += 175;
					CharacterIsBasicallyTheLargest_MP += 175;
				}
				else
				{
					FLD_CongKich += 30;
					FLD_PhongNgu += 30;
					CharacterIsBasicallyTheLargest_HP += 350;
					CharacterIsBasicallyTheLargest_MP += 350;
				}
				break;
			case 4:
				if (Player_Job == 1)
				{
					FLD_CongKich += 37;
					FLD_PhongNgu += 54;
					CharacterIsBasicallyTheLargest_HP += 613;
					CharacterIsBasicallyTheLargest_MP += 649;
				}
				else if (Player_Job == 2)
				{
					FLD_CongKich += 40;
					FLD_PhongNgu += 47;
					CharacterIsBasicallyTheLargest_HP += 613;
					CharacterIsBasicallyTheLargest_MP += 624;
				}
				else if (Player_Job == 3)
				{
					FLD_CongKich += 50;
					FLD_PhongNgu += 50;
					CharacterIsBasicallyTheLargest_HP += 650;
					CharacterIsBasicallyTheLargest_MP += 650;
				}
				else if (Player_Job == 4)
				{
					FLD_CongKich += 43;
					FLD_PhongNgu += 45;
					CharacterIsBasicallyTheLargest_HP += 650;
					CharacterIsBasicallyTheLargest_MP += 636;
				}
				else if (Player_Job == 5)
				{
					FLD_CongKich += 35;
					FLD_PhongNgu += 41;
					CharacterIsBasicallyTheLargest_HP += 577;
					CharacterIsBasicallyTheLargest_MP += 750;
				}
				else if (Player_Job == 6)
				{
					FLD_CongKich += 40;
					FLD_PhongNgu += 42;
					CharacterIsBasicallyTheLargest_HP += 577;
					CharacterIsBasicallyTheLargest_MP += 745;
				}
				else if (Player_Job == 7)
				{
					FLD_CongKich += 40;
					FLD_PhongNgu += 43;
					CharacterIsBasicallyTheLargest_HP += 598;
					CharacterIsBasicallyTheLargest_MP += 762;
				}
				else if (Player_Job == 8)
				{
					FLD_CongKich += 37;
					FLD_PhongNgu += 54;
					CharacterIsBasicallyTheLargest_HP += 613;
					CharacterIsBasicallyTheLargest_MP += 649;
				}
				else if (Player_Job == 9)
				{
					FLD_CongKich += 40;
					FLD_PhongNgu += 47;
					CharacterIsBasicallyTheLargest_HP += 613;
					CharacterIsBasicallyTheLargest_MP += 624;
				}
				else if (Player_Job == 10)
				{
					FLD_CongKich += 40;
					FLD_PhongNgu += 59;
					CharacterIsBasicallyTheLargest_HP += 638;
					CharacterIsBasicallyTheLargest_MP += 649;
				}
				else if (Player_Job == 11)
				{
					FLD_CongKich += 37;
					FLD_PhongNgu += 32;
					CharacterIsBasicallyTheLargest_HP += 332;
					CharacterIsBasicallyTheLargest_MP += 594;
				}
				else if (Player_Job == 12)
				{
					FLD_CongKich += 50;
					FLD_PhongNgu += 50;
					CharacterIsBasicallyTheLargest_HP += 650;
					CharacterIsBasicallyTheLargest_MP += 650;
				}
				else if (Player_Job == 13)
				{
					FLD_CongKich += 35;
					FLD_PhongNgu += 41;
					CharacterIsBasicallyTheLargest_HP += 577;
					CharacterIsBasicallyTheLargest_MP += 750;
				}
				break;
			case 5:
				if (Player_Job == 1)
				{
					FLD_CongKich += 70;
					FLD_PhongNgu += 97;
					CharacterIsBasicallyTheLargest_HP += 1223;
					CharacterIsBasicallyTheLargest_MP += 1177;
				}
				else if (Player_Job == 2)
				{
					FLD_CongKich += 76;
					FLD_PhongNgu += 84;
					CharacterIsBasicallyTheLargest_HP += 1223;
					CharacterIsBasicallyTheLargest_MP += 1130;
				}
				else if (Player_Job == 3)
				{
					FLD_CongKich += 94;
					FLD_PhongNgu += 90;
					CharacterIsBasicallyTheLargest_HP += 1296;
					CharacterIsBasicallyTheLargest_MP += 1178;
				}
				else if (Player_Job == 4)
				{
					FLD_CongKich += 81;
					FLD_PhongNgu += 82;
					CharacterIsBasicallyTheLargest_HP += 1296;
					CharacterIsBasicallyTheLargest_MP += 1152;
				}
				else if (Player_Job == 5)
				{
					FLD_CongKich += 66;
					FLD_PhongNgu += 74;
					CharacterIsBasicallyTheLargest_HP += 1150;
					CharacterIsBasicallyTheLargest_MP += 1359;
				}
				else if (Player_Job == 6)
				{
					FLD_CongKich += 75;
					FLD_PhongNgu += 75;
					CharacterIsBasicallyTheLargest_HP += 1150;
					CharacterIsBasicallyTheLargest_MP += 1350;
				}
				else if (Player_Job == 7)
				{
					FLD_CongKich += 76;
					FLD_PhongNgu += 76;
					CharacterIsBasicallyTheLargest_HP += 1193;
					CharacterIsBasicallyTheLargest_MP += 1380;
				}
				else if (Player_Job == 8)
				{
					FLD_CongKich += 70;
					FLD_PhongNgu += 97;
					CharacterIsBasicallyTheLargest_HP += 1223;
					CharacterIsBasicallyTheLargest_MP += 1176;
				}
				else if (Player_Job == 9)
				{
					FLD_CongKich += 76;
					FLD_PhongNgu += 84;
					CharacterIsBasicallyTheLargest_HP += 1223;
					CharacterIsBasicallyTheLargest_MP += 1130;
				}
				else if (Player_Job == 10)
				{
					FLD_CongKich += 76;
					FLD_PhongNgu += 105;
					CharacterIsBasicallyTheLargest_HP += 1272;
					CharacterIsBasicallyTheLargest_MP += 1177;
				}
				else if (Player_Job == 11)
				{
					FLD_CongKich += 69;
					FLD_PhongNgu += 57;
					CharacterIsBasicallyTheLargest_HP += 662;
					CharacterIsBasicallyTheLargest_MP += 1076;
				}
				else if (Player_Job == 12)
				{
					FLD_CongKich += 94;
					FLD_PhongNgu += 90;
					CharacterIsBasicallyTheLargest_HP += 1296;
					CharacterIsBasicallyTheLargest_MP += 1178;
				}
				else if (Player_Job == 13)
				{
					FLD_CongKich += 66;
					FLD_PhongNgu += 74;
					CharacterIsBasicallyTheLargest_HP += 1150;
					CharacterIsBasicallyTheLargest_MP += 1359;
				}
				break;
			case 6:
				if (Player_Job == 1)
				{
					FLD_CongKich += 207;
					FLD_PhongNgu += 195;
					CharacterIsBasicallyTheLargest_HP += 2280;
					CharacterIsBasicallyTheLargest_MP += 2411;
				}
				else if (Player_Job == 2)
				{
					FLD_CongKich += 223;
					FLD_PhongNgu += 169;
					CharacterIsBasicallyTheLargest_HP += 2280;
					CharacterIsBasicallyTheLargest_MP += 2314;
				}
				else if (Player_Job == 3)
				{
					FLD_CongKich += 235;
					FLD_PhongNgu += 180;
					CharacterIsBasicallyTheLargest_HP += 2417;
					CharacterIsBasicallyTheLargest_MP += 2413;
				}
				else if (Player_Job == 4)
				{
					FLD_CongKich += 238;
					FLD_PhongNgu += 163;
					CharacterIsBasicallyTheLargest_HP += 2417;
					CharacterIsBasicallyTheLargest_MP += 2361;
				}
				else if (Player_Job == 5)
				{
					FLD_CongKich += 194;
					FLD_PhongNgu += 148;
					CharacterIsBasicallyTheLargest_HP += 2144;
					CharacterIsBasicallyTheLargest_MP += 2785;
				}
				else if (Player_Job == 6)
				{
					FLD_CongKich += 220;
					FLD_PhongNgu += 150;
					CharacterIsBasicallyTheLargest_HP += 2144;
					CharacterIsBasicallyTheLargest_MP += 2766;
				}
				else if (Player_Job == 7)
				{
					FLD_CongKich += 222;
					FLD_PhongNgu += 153;
					CharacterIsBasicallyTheLargest_HP += 2225;
					CharacterIsBasicallyTheLargest_MP += 2827;
				}
				else if (Player_Job == 8)
				{
					FLD_CongKich += 207;
					FLD_PhongNgu += 195;
					CharacterIsBasicallyTheLargest_HP += 2280;
					CharacterIsBasicallyTheLargest_MP += 2411;
				}
				else if (Player_Job == 9)
				{
					FLD_CongKich += 223;
					FLD_PhongNgu += 169;
					CharacterIsBasicallyTheLargest_HP += 2280;
					CharacterIsBasicallyTheLargest_MP += 2314;
				}
				else if (Player_Job == 10)
				{
					FLD_CongKich += 223;
					FLD_PhongNgu += 211;
					CharacterIsBasicallyTheLargest_HP += 2371;
					CharacterIsBasicallyTheLargest_MP += 2411;
				}
				else if (Player_Job == 11)
				{
					FLD_CongKich += 202;
					FLD_PhongNgu += 114;
					CharacterIsBasicallyTheLargest_HP += 1234;
					CharacterIsBasicallyTheLargest_MP += 2204;
				}
				else if (Player_Job == 12)
				{
					FLD_CongKich += 235;
					FLD_PhongNgu += 180;
					CharacterIsBasicallyTheLargest_HP += 2417;
					CharacterIsBasicallyTheLargest_MP += 2413;
				}
				else if (Player_Job == 13)
				{
					FLD_CongKich += 194;
					FLD_PhongNgu += 148;
					CharacterIsBasicallyTheLargest_HP += 2144;
					CharacterIsBasicallyTheLargest_MP += 2785;
				}
				break;
			case 7:
				switch (Player_Job)
				{
					case 1:
						CharacterIsBasicallyTheLargest_HP += 2510;
						CharacterIsBasicallyTheLargest_MP += 2500;
						FLD_CongKich += 246;
						FLD_PhongNgu += 267;
						break;
					case 2:
						FLD_CongKich += 265;
						FLD_PhongNgu += 232;
						CharacterIsBasicallyTheLargest_HP += 2510;
						CharacterIsBasicallyTheLargest_MP += 2400;
						break;
					case 3:
						CharacterIsBasicallyTheLargest_HP += 2660;
						CharacterIsBasicallyTheLargest_MP += 2502;
						FLD_CongKich += 275;
						FLD_PhongNgu += 247;
						break;
					case 4:
						CharacterIsBasicallyTheLargest_HP += 2660;
						CharacterIsBasicallyTheLargest_MP += 2448;
						FLD_CongKich += 283;
						FLD_PhongNgu += 224;
						break;
					case 5:
						CharacterIsBasicallyTheLargest_HP += 2360;
						CharacterIsBasicallyTheLargest_MP += 2888;
						FLD_CongKich += 231;
						FLD_PhongNgu += 203;
						break;
					case 6:
						CharacterIsBasicallyTheLargest_HP += 2360;
						CharacterIsBasicallyTheLargest_MP += 2868;
						FLD_CongKich += 262;
						FLD_PhongNgu += 206;
						break;
					case 7:
						CharacterIsBasicallyTheLargest_HP += 2449;
						CharacterIsBasicallyTheLargest_MP += 2932;
						FLD_CongKich += 264;
						FLD_PhongNgu += 210;
						break;
					case 8:
						CharacterIsBasicallyTheLargest_HP += 2510;
						CharacterIsBasicallyTheLargest_MP += 2500;
						FLD_CongKich += 246;
						FLD_PhongNgu += 267;
						break;
					case 9:
						CharacterIsBasicallyTheLargest_HP += 2510;
						CharacterIsBasicallyTheLargest_MP += 2400;
						FLD_CongKich += 265;
						FLD_PhongNgu += 232;
						break;
					case 10:
						CharacterIsBasicallyTheLargest_HP += 2610;
						CharacterIsBasicallyTheLargest_MP += 2500;
						FLD_CongKich += 265;
						FLD_PhongNgu += 289;
						break;
					case 11:
						CharacterIsBasicallyTheLargest_HP += 1358;
						CharacterIsBasicallyTheLargest_MP += 2285;
						FLD_CongKich += 241;
						FLD_PhongNgu += 157;
						break;
					case 12:
						CharacterIsBasicallyTheLargest_HP += 2660;
						CharacterIsBasicallyTheLargest_MP += 2502;
						FLD_CongKich += 275;
						FLD_PhongNgu += 247;
						break;
					case 13:
						CharacterIsBasicallyTheLargest_HP += 2360;
						CharacterIsBasicallyTheLargest_MP += 2888;
						FLD_CongKich += 231;
						FLD_PhongNgu += 203;
						break;
				}
				break;
			case 8:
				switch (Player_Job)
				{
					case 1:
						CharacterIsBasicallyTheLargest_HP += 3910;
						CharacterIsBasicallyTheLargest_MP += 3500;
						FLD_CongKich += 346;
						FLD_PhongNgu += 382;
						break;
					case 2:
						CharacterIsBasicallyTheLargest_HP += 3910;
						CharacterIsBasicallyTheLargest_MP += 3360;
						FLD_CongKich += 373;
						FLD_PhongNgu += 332;
						break;
					case 3:
						CharacterIsBasicallyTheLargest_HP += 4144;
						CharacterIsBasicallyTheLargest_MP += 3503;
						FLD_CongKich += 383;
						FLD_PhongNgu += 353;
						break;
					case 4:
						CharacterIsBasicallyTheLargest_HP += 4144;
						CharacterIsBasicallyTheLargest_MP += 3427;
						FLD_CongKich += 398;
						FLD_PhongNgu += 320;
						break;
					case 5:
						CharacterIsBasicallyTheLargest_HP += 3676;
						CharacterIsBasicallyTheLargest_MP += 4043;
						FLD_CongKich += 325;
						FLD_PhongNgu += 290;
						break;
					case 6:
						CharacterIsBasicallyTheLargest_HP += 3676;
						CharacterIsBasicallyTheLargest_MP += 4015;
						FLD_CongKich += 369;
						FLD_PhongNgu += 295;
						break;
					case 7:
						CharacterIsBasicallyTheLargest_HP += 3815;
						CharacterIsBasicallyTheLargest_MP += 4105;
						FLD_CongKich += 371;
						FLD_PhongNgu += 300;
						break;
					case 8:
						CharacterIsBasicallyTheLargest_HP += 3910;
						CharacterIsBasicallyTheLargest_MP += 3500;
						FLD_CongKich += 346;
						FLD_PhongNgu += 382;
						break;
					case 9:
						CharacterIsBasicallyTheLargest_HP += 3910;
						CharacterIsBasicallyTheLargest_MP += 3360;
						FLD_CongKich += 373;
						FLD_PhongNgu += 332;
						break;
					case 10:
						CharacterIsBasicallyTheLargest_HP += 4066;
						CharacterIsBasicallyTheLargest_MP += 3500;
						FLD_CongKich += 373;
						FLD_PhongNgu += 413;
						break;
					case 11:
						CharacterIsBasicallyTheLargest_HP += 2115;
						CharacterIsBasicallyTheLargest_MP += 3199;
						FLD_CongKich += 398;
						FLD_PhongNgu += 225;
						break;
					case 12:
						CharacterIsBasicallyTheLargest_HP += 4144;
						CharacterIsBasicallyTheLargest_MP += 3503;
						FLD_CongKich += 383;
						FLD_PhongNgu += 353;
						break;
					case 13:
						CharacterIsBasicallyTheLargest_HP += 3676;
						CharacterIsBasicallyTheLargest_MP += 4043;
						FLD_CongKich += 325;
						FLD_PhongNgu += 290;
						break;
				}
				break;
			case 9:
				switch (Player_Job)
				{
					case 1:
						CharacterIsBasicallyTheLargest_HP += 6091;
						CharacterIsBasicallyTheLargest_MP += 4900;
						FLD_CongKich += 487;
						FLD_PhongNgu += 547;
						break;
					case 2:
						CharacterIsBasicallyTheLargest_HP += 6091;
						CharacterIsBasicallyTheLargest_MP += 4704;
						FLD_CongKich += 524;
						FLD_PhongNgu += 475;
						break;
					case 3:
						CharacterIsBasicallyTheLargest_HP += 6455;
						CharacterIsBasicallyTheLargest_MP += 4904;
						FLD_CongKich += 550;
						FLD_PhongNgu += 506;
						break;
					case 4:
						CharacterIsBasicallyTheLargest_HP += 6455;
						CharacterIsBasicallyTheLargest_MP += 4798;
						FLD_CongKich += 560;
						FLD_PhongNgu += 459;
						break;
					case 5:
						CharacterIsBasicallyTheLargest_HP += 5727;
						CharacterIsBasicallyTheLargest_MP += 5660;
						FLD_CongKich += 457;
						FLD_PhongNgu += 416;
						break;
					case 6:
						CharacterIsBasicallyTheLargest_HP += 5727;
						CharacterIsBasicallyTheLargest_MP += 5621;
						FLD_CongKich += 518;
						FLD_PhongNgu += 422;
						break;
					case 7:
						CharacterIsBasicallyTheLargest_HP += 5943;
						CharacterIsBasicallyTheLargest_MP += 5747;
						FLD_CongKich += 522;
						FLD_PhongNgu += 430;
						break;
					case 8:
						CharacterIsBasicallyTheLargest_HP += 6091;
						CharacterIsBasicallyTheLargest_MP += 4900;
						FLD_CongKich += 487;
						FLD_PhongNgu += 547;
						break;
					case 9:
						CharacterIsBasicallyTheLargest_HP += 6091;
						CharacterIsBasicallyTheLargest_MP += 4704;
						FLD_CongKich += 524;
						FLD_PhongNgu += 475;
						break;
					case 10:
						CharacterIsBasicallyTheLargest_HP += 6334;
						CharacterIsBasicallyTheLargest_MP += 4900;
						FLD_CongKich += 524;
						FLD_PhongNgu += 592;
						break;
					case 11:
						CharacterIsBasicallyTheLargest_HP += 3295;
						CharacterIsBasicallyTheLargest_MP += 4479;
						FLD_CongKich += 560;
						FLD_PhongNgu += 321;
						break;
					case 12:
						CharacterIsBasicallyTheLargest_HP += 6455;
						CharacterIsBasicallyTheLargest_MP += 4904;
						FLD_CongKich += 550;
						FLD_PhongNgu += 506;
						break;
					case 13:
						CharacterIsBasicallyTheLargest_HP += 5727;
						CharacterIsBasicallyTheLargest_MP += 5660;
						FLD_CongKich += 457;
						FLD_PhongNgu += 416;
						break;
				}
				break;
			case 10:
				switch (Player_Job)
				{
					case 1:
						CharacterIsBasicallyTheLargest_HP += 9488;
						CharacterIsBasicallyTheLargest_MP += 6860;
						FLD_CongKich += 684;
						FLD_PhongNgu += 782;
						break;
					case 2:
						CharacterIsBasicallyTheLargest_HP += 9488;
						CharacterIsBasicallyTheLargest_MP += 6586;
						FLD_CongKich += 737;
						FLD_PhongNgu += 679;
						break;
					case 3:
						CharacterIsBasicallyTheLargest_HP += 10055;
						CharacterIsBasicallyTheLargest_MP += 6865;
						FLD_CongKich += 750;
						FLD_PhongNgu += 723;
						break;
					case 4:
						CharacterIsBasicallyTheLargest_HP += 10055;
						CharacterIsBasicallyTheLargest_MP += 6717;
						FLD_CongKich += 787;
						FLD_PhongNgu += 656;
						break;
					case 5:
						CharacterIsBasicallyTheLargest_HP += 8921;
						CharacterIsBasicallyTheLargest_MP += 7925;
						FLD_CongKich += 643;
						FLD_PhongNgu += 594;
						break;
					case 6:
						CharacterIsBasicallyTheLargest_HP += 8921;
						CharacterIsBasicallyTheLargest_MP += 7870;
						FLD_CongKich += 729;
						FLD_PhongNgu += 603;
						break;
					case 7:
						CharacterIsBasicallyTheLargest_HP += 9258;
						CharacterIsBasicallyTheLargest_MP += 8045;
						FLD_CongKich += 735;
						FLD_PhongNgu += 615;
						break;
					case 8:
						CharacterIsBasicallyTheLargest_HP += 9488;
						CharacterIsBasicallyTheLargest_MP += 6860;
						FLD_CongKich += 684;
						FLD_PhongNgu += 782;
						break;
					case 9:
						CharacterIsBasicallyTheLargest_HP += 9488;
						CharacterIsBasicallyTheLargest_MP += 6586;
						FLD_CongKich += 737;
						FLD_PhongNgu += 679;
						break;
					case 10:
						CharacterIsBasicallyTheLargest_HP += 9866;
						CharacterIsBasicallyTheLargest_MP += 6860;
						FLD_CongKich += 737;
						FLD_PhongNgu += 846;
						break;
					case 11:
						CharacterIsBasicallyTheLargest_HP += 5133;
						CharacterIsBasicallyTheLargest_MP += 6270;
						FLD_CongKich += 780;
						FLD_PhongNgu += 460;
						break;
					case 12:
						CharacterIsBasicallyTheLargest_HP += 10055;
						CharacterIsBasicallyTheLargest_MP += 6865;
						FLD_CongKich += 750;
						FLD_PhongNgu += 723;
						break;
					case 13:
						CharacterIsBasicallyTheLargest_HP += 8921;
						CharacterIsBasicallyTheLargest_MP += 7925;
						FLD_CongKich += 643;
						FLD_PhongNgu += 594;
						break;
				}
				break;
			case 11:
				switch (Player_Job)
				{
					case 1:
						CharacterIsBasicallyTheLargest_HP += 11860;
						CharacterIsBasicallyTheLargest_MP += 8860;
						FLD_CongKich += 820;
						FLD_PhongNgu += 908;
						break;
					case 2:
						CharacterIsBasicallyTheLargest_HP += 11860;
						CharacterIsBasicallyTheLargest_MP += 8586;
						FLD_CongKich += 884;
						FLD_PhongNgu += 814;
						break;
					case 3:
						CharacterIsBasicallyTheLargest_HP += 12568;
						CharacterIsBasicallyTheLargest_MP += 8865;
						FLD_CongKich += 900;
						FLD_PhongNgu += 837;
						break;
					case 4:
						CharacterIsBasicallyTheLargest_HP += 12055;
						CharacterIsBasicallyTheLargest_MP += 8717;
						FLD_CongKich += 944;
						FLD_PhongNgu += 787;
						break;
					case 5:
						CharacterIsBasicallyTheLargest_HP += 11151;
						CharacterIsBasicallyTheLargest_MP += 9925;
						FLD_CongKich += 771;
						FLD_PhongNgu += 712;
						break;
					case 6:
						CharacterIsBasicallyTheLargest_HP += 11151;
						CharacterIsBasicallyTheLargest_MP += 9870;
						FLD_CongKich += 874;
						FLD_PhongNgu += 723;
						break;
					case 7:
						CharacterIsBasicallyTheLargest_HP += 11572;
						CharacterIsBasicallyTheLargest_MP += 10045;
						FLD_CongKich += 882;
						FLD_PhongNgu += 738;
						break;
					case 8:
						CharacterIsBasicallyTheLargest_HP += 11860;
						CharacterIsBasicallyTheLargest_MP += 8860;
						FLD_CongKich += 855;
						FLD_PhongNgu += 888;
						break;
					case 9:
						CharacterIsBasicallyTheLargest_HP += 11860;
						CharacterIsBasicallyTheLargest_MP += 8586;
						FLD_CongKich += 884;
						FLD_PhongNgu += 814;
						break;
					case 10:
						CharacterIsBasicallyTheLargest_HP += 12332;
						CharacterIsBasicallyTheLargest_MP += 8860;
						FLD_CongKich += 884;
						FLD_PhongNgu += 965;
						break;
					case 11:
						CharacterIsBasicallyTheLargest_HP += 6416;
						CharacterIsBasicallyTheLargest_MP += 8270;
						FLD_CongKich += 936;
						FLD_PhongNgu += 552;
						break;
					case 12:
						CharacterIsBasicallyTheLargest_HP += 12568;
						CharacterIsBasicallyTheLargest_MP += 8865;
						FLD_CongKich += 900;
						FLD_PhongNgu += 867;
						break;
					case 13:
						CharacterIsBasicallyTheLargest_HP += 11151;
						CharacterIsBasicallyTheLargest_MP += 9925;
						FLD_CongKich += 771;
						FLD_PhongNgu += 762;
						break;
				}
				break;
			case 12:
				switch (Player_Job)
				{
					case 1:
						CharacterIsBasicallyTheLargest_HP += 12860;
						CharacterIsBasicallyTheLargest_MP += 9860;
						FLD_CongKich += 920;
						FLD_PhongNgu += 908;
						break;
					case 2:
						CharacterIsBasicallyTheLargest_HP += 12860;
						CharacterIsBasicallyTheLargest_MP += 9586;
						FLD_CongKich += 984;
						FLD_PhongNgu += 914;
						break;
					case 3:
						CharacterIsBasicallyTheLargest_HP += 13568;
						CharacterIsBasicallyTheLargest_MP += 9865;
						FLD_CongKich += 1000;
						FLD_PhongNgu += 937;
						break;
					case 4:
						CharacterIsBasicallyTheLargest_HP += 13055;
						CharacterIsBasicallyTheLargest_MP += 9717;
						FLD_CongKich += 944;
						FLD_PhongNgu += 787;
						break;
					case 5:
						CharacterIsBasicallyTheLargest_HP += 12151;
						CharacterIsBasicallyTheLargest_MP += 10925;
						FLD_CongKich += 871;
						FLD_PhongNgu += 812;
						break;
					case 6:
						CharacterIsBasicallyTheLargest_HP += 12151;
						CharacterIsBasicallyTheLargest_MP += 10870;
						FLD_CongKich += 974;
						FLD_PhongNgu += 823;
						break;
					case 7:
						CharacterIsBasicallyTheLargest_HP += 12572;
						CharacterIsBasicallyTheLargest_MP += 11045;
						FLD_CongKich += 982;
						FLD_PhongNgu += 838;
						break;
					case 8:
						CharacterIsBasicallyTheLargest_HP += 13860;
						CharacterIsBasicallyTheLargest_MP += 10860;
						FLD_CongKich += 1055;
						FLD_PhongNgu += 1088;
						break;
					case 9:
						CharacterIsBasicallyTheLargest_HP += 12860;
						CharacterIsBasicallyTheLargest_MP += 9586;
						FLD_CongKich += 984;
						FLD_PhongNgu += 914;
						break;
					case 10:
						CharacterIsBasicallyTheLargest_HP += 13332;
						CharacterIsBasicallyTheLargest_MP += 9860;
						FLD_CongKich += 984;
						FLD_PhongNgu += 1065;
						break;
					case 11:
						CharacterIsBasicallyTheLargest_HP += 7416;
						CharacterIsBasicallyTheLargest_MP += 9270;
						FLD_CongKich += 1036;
						FLD_PhongNgu += 652;
						break;
					case 12:
						CharacterIsBasicallyTheLargest_HP += 13568;
						CharacterIsBasicallyTheLargest_MP += 9865;
						FLD_CongKich += 1000;
						FLD_PhongNgu += 967;
						break;
					case 13:
						CharacterIsBasicallyTheLargest_HP += 12151;
						CharacterIsBasicallyTheLargest_MP += 10925;
						FLD_CongKich += 871;
						FLD_PhongNgu += 862;
						break;
				}
				break;
		}
		FLD_LonNhatCongKich = FLD_CongKich;
		CalculateCharacterEquipmentData();
		CalculateCharacterProductionLevel();
	}

	public void UpdateKinhNghiemVaTraiNghiem()
	{
		try
		{
			double expNeedToLevelUp = Convert.ToInt64(World.lever[Player_Level]) - Convert.ToInt64(World.lever[Player_Level - 1]);
			double currentExpToLevelUp = CharacterExperience - Convert.ToInt64(World.lever[Player_Level - 1]);
			if (currentExpToLevelUp < 1.0)
			{
				CharacterExperience = Convert.ToInt64(World.lever[Player_Level - 1]);
				currentExpToLevelUp = 0.0;
			}
			SendingClass sendingClass = new();
			sendingClass.Write8((long)currentExpToLevelUp);
			sendingClass.Write8((long)expNeedToLevelUp);
			sendingClass.Write4(0);
			if (SkillExperience >= 2000000000)
			{
				SkillExperience = 2000000000;
			}
			else if (SkillExperience <= 0)
			{
				SkillExperience = 0;
			}
			// HeThongNhacNho($"Player_ExpErience {SkillExperience}");
			sendingClass.Write4(SkillExperience);
			sendingClass.Write2(FLD_LoaiSanXuat);
			sendingClass.Write2(FLD_LevelSanXuat);
			sendingClass.Write4(FLD_TrangBi_DoiPhuong_BiThuong_Giam);
			if (Player_Job_level >= 6)
			{
				if (ThangThienLichLuyen_KinhNghiem < 0)
				{
					ThangThienLichLuyen_KinhNghiem = 0;
				}
				ThangThienLichLuyen_KinhNghiem += ThangThienLichLuyen_TangKinhNghiemNhanDuoc_HienTai;
				if (ThangThienLichLuyen_KinhNghiem >= 50000000)
				{
					var parcelVacancyPosition = GetParcelVacancyPosition();
					if (parcelVacancyPosition != -1)
					{
						ThangThienLichLuyen_KinhNghiem -= 50000000;
						IncreaseItem3(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000714), parcelVacancyPosition, BitConverter.GetBytes(1), new byte[56]);
					}
					else
					{
						HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
					}
				}
				sendingClass.Write4(ThangThienLichLuyen_KinhNghiem);
				sendingClass.Write4(ThangThienLichLuyen_TangKinhNghiemNhanDuoc_HienTai);
			}
			else
			{
				sendingClass.Write4(0);
				sendingClass.Write4(0);
			}
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			ThangThienLichLuyen_TangKinhNghiemNhanDuoc_HienTai = 0;
			Client?.SendPak(sendingClass, 27136, SessionID);
		}
		catch
		{
		}
	}

	public void UpdateMoneyAndWeight()
	{
		// HeThongNhacNho("Money: " + Player_Money);
		if (Player_Money < 0)
		{
			Player_Money = 0L;
		}
		int_50 = 0;
		for (var i = 0; i < 36; i++)
		{
			int_50 += Item_In_Bag[i].VatPham_TongTrongLuong;
		}
		SendingClass sendingClass = new();
		sendingClass.Write8(Player_Money);
		sendingClass.Write((float)CharacterCurrentWeight);
		sendingClass.Write((float)TheTotalWeightOfTheCharacter);
		if (Client != null)
		{
			Client.SendPak(sendingClass, 31744, SessionID);
		}
	}

	public void KhoiTaoKhiCong()
	{
		SendingClass sendingClass = new();
		sendingClass.Write8(Player_Money);
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		sendingClass.Write2(0);
		for (var i = 0; i < 15; i++)
		{
			if (i < 12)
			{
				sendingClass.Write2(KhiCong[i].KhiCongID);
				if (KhiCong[i].KhiCongID != 0)
				{
					if (KhiCong[i].KhiCong_SoLuong != 0)
					{
						var maxKhiCong_Tren1KhiCong = World.MaxKhiCong_Tren1KhiCong;
						var num = KhiCong[i].KhiCong_SoLuong + FLD_TrangBi_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong + FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + NhanVat_WX_BUFF_KhiCong + (int)NhanGiaTri_TangRieng_CuaKhiCong(i) + FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
						if (num > maxKhiCong_Tren1KhiCong)
						{
							num = maxKhiCong_Tren1KhiCong;
						}
						sendingClass.Write2(num);
					}
					else
					{
						sendingClass.Write2(0);
					}
				}
				else
				{
					sendingClass.Write2(0);
				}
			}
			else
			{
				sendingClass.Write4(0);
			}
		}
		sendingClass.Write2(0);
		sendingClass.Write2(65520);
		sendingClass.Write2(7);
		sendingClass.Write4(0);
		sendingClass.Write2(Player_Extra_Money_Level);
		sendingClass.Write2(0);
		if (Client != null)
		{
			Client.SendPak(sendingClass, 27648, SessionID);
		}
	}
		
	public void CalculateCharacterProductionLevel()
	{
		var fLD_LevelSanXuat = FLD_LevelSanXuat;
		FLD_LevelSanXuat = ((FLD_TrinhDoSanXuat >= 800) ? 8 : ((FLD_TrinhDoSanXuat >= 700) ? 7 : ((FLD_TrinhDoSanXuat >= 600) ? 6 : ((FLD_TrinhDoSanXuat >= 500) ? 5 : ((FLD_TrinhDoSanXuat >= 400) ? 4 : ((FLD_TrinhDoSanXuat >= 300) ? 3 : ((FLD_TrinhDoSanXuat < 100) ? 1 : 2)))))));
		if (FLD_LevelSanXuat > fLD_LevelSanXuat)
		{
			UpdateProductionSystem();
		}
	}

		public int CalculateTheDailyAmountOfMartialArts(int Player_Job_Level)
	{
		if (MapID != 101)
		{
			if (World.week != 0 && World.week != 6)
			{
				if (Player_Job_Level < 2)
				{
					return 0;
				}
				return Player_Job_Level switch
				{
					2 => World.GioiHanVoHuanMoiNgay_Cap2,
					3 => World.GioiHanVoHuanMoiNgay_Cap3,
					4 => World.GioiHanVoHuanMoiNgay_Cap4,
					5 => World.GioiHanVoHuanMoiNgay_Cap5,
					6 => World.GioiHanVoHuanMoiNgay_Cap6,
					7 => World.GioiHanVoHuanMoiNgay_Cap7,
					8 => World.GioiHanVoHuanMoiNgay_Cap8,
					9 => World.GioiHanVoHuanMoiNgay_Cap9,
					10 => World.GioiHanVoHuanMoiNgay_Cap10,
					11 => World.GioiHanVoHuanMoiNgay_Cap11,
					_ => 0,
				};
			}
			return World.LuongVoHuan_CuoiTuan;
		}
		return 1000000;
	}



    }

}
