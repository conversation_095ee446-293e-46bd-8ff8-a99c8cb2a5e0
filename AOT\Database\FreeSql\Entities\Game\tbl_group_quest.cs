﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_group_quest {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_group_quest_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string questname { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string questdesc { get; set; }

		[JsonProperty]
		public int? questtype { get; set; }

		[JsonProperty]
		public int? targettype { get; set; }

		[JsonProperty]
		public int? targetid { get; set; }

		[JsonProperty]
		public int? targetcount { get; set; }

		[JsonProperty]
		public int? requiredlevel { get; set; }

		[JsonProperty]
		public int? rewardexp { get; set; }

		[JsonProperty]
		public long? rewardmoney { get; set; }

		[JsonProperty]
		public int? rewarditem { get; set; }

		[JsonProperty]
		public int? rewarditemcount { get; set; }

		[JsonProperty]
		public int? resettype { get; set; }

		[JsonProperty]
		public bool? isactive { get; set; }

		[JsonProperty]
		public DateTime? createdate { get; set; }

	}

}
