


using System;
using RxjhServer.HelperTools;

namespace RxjhServer.PacketBuilder.Player
{
    public static class CommonBuilder
    {
        public static void RemindersForStrengtheningSpiritPets(Players player,int id, int id2)
        {
            var array = Converter.HexStringToByte("AA551900E2007A0314000200C4090000000000000300460000000000000055AA");
            Buffer.BlockCopy(BitConverter.GetBytes(id2), 0, array, 10, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 12, 4);
            Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
            player.Client?.SendMultiplePackage(array, array.Length);
        }
    }
}