-- Complete script to add auto-increment primary keys for Public database tables
-- <PERSON>rip<PERSON> hoàn chỉnh để thêm auto-increment primary key cho các bảng Public database
-- Generated automatically from entity analysis
-- <PERSON><PERSON><PERSON><PERSON> tạo tự động từ phân tích entity

-- ========================================
-- SECTION 1: CREATE SEQUENCES
-- PHẦN 1: TẠO CÁC SEQUENCE
-- ========================================

CREATE SEQUENCE IF NOT EXISTS "cheduocvatphamdanhsach_id_seq";
CREATE SEQUENCE IF NOT EXISTS "chetacvatphamdanhsach_id_seq";
CREATE SEQUENCE IF NOT EXISTS "dangcapbanthuong_id_seq";
CREATE SEQUENCE IF NOT EXISTS "giftcode_rewards_id_seq";
CREATE SEQUENCE IF NOT EXISTS "giftcode_id_seq";
CREATE SEQUENCE IF NOT EXISTS "kiemtrathietbi_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_itemoption_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_upgrade_item_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_gg_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_monster_set_base_fld_index_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_sell_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_stone_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_vome_id_seq";
CREATE SEQUENCE IF NOT EXISTS "thangthienkhicong_id_seq";
CREATE SEQUENCE IF NOT EXISTS "vatphamtraodoi_id_seq";
CREATE SEQUENCE IF NOT EXISTS "xwwl_kill_id_seq";

-- ========================================
-- SECTION 2: SET SEQUENCE VALUES AND ATTACH TO ID COLUMNS
-- PHẦN 2: THIẾT LẬP GIÁ TRỊ SEQUENCE VÀ GẮN VÀO CÁC CỘT ID
-- ========================================

-- Table: cheduocvatphamdanhsach
SELECT setval('cheduocvatphamdanhsach_id_seq', COALESCE((SELECT MAX("id") FROM "cheduocvatphamdanhsach"), 0) + 1, false);
ALTER TABLE "cheduocvatphamdanhsach"
   ALTER COLUMN "id" SET DEFAULT nextval('cheduocvatphamdanhsach_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "cheduocvatphamdanhsach_id_seq" OWNED BY "cheduocvatphamdanhsach"."id";
SELECT 'Setup completed for cheduocvatphamdanhsach' as status;

-- Table: chetacvatphamdanhsach
SELECT setval('chetacvatphamdanhsach_id_seq', COALESCE((SELECT MAX("id") FROM "chetacvatphamdanhsach"), 0) + 1, false);
ALTER TABLE "chetacvatphamdanhsach"
   ALTER COLUMN "id" SET DEFAULT nextval('chetacvatphamdanhsach_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "chetacvatphamdanhsach_id_seq" OWNED BY "chetacvatphamdanhsach"."id";
SELECT 'Setup completed for chetacvatphamdanhsach' as status;

-- Table: dangcapbanthuong
SELECT setval('dangcapbanthuong_id_seq', COALESCE((SELECT MAX("id") FROM "dangcapbanthuong"), 0) + 1, false);
ALTER TABLE "dangcapbanthuong"
   ALTER COLUMN "id" SET DEFAULT nextval('dangcapbanthuong_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "dangcapbanthuong_id_seq" OWNED BY "dangcapbanthuong"."id";
SELECT 'Setup completed for dangcapbanthuong' as status;

-- Table: giftcode_rewards
SELECT setval('giftcode_rewards_id_seq', COALESCE((SELECT MAX("id") FROM "giftcode_rewards"), 0) + 1, false);
ALTER TABLE "giftcode_rewards"
   ALTER COLUMN "id" SET DEFAULT nextval('giftcode_rewards_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "giftcode_rewards_id_seq" OWNED BY "giftcode_rewards"."id";
SELECT 'Setup completed for giftcode_rewards' as status;

-- Table: giftcode
SELECT setval('giftcode_id_seq', COALESCE((SELECT MAX("id") FROM "giftcode"), 0) + 1, false);
ALTER TABLE "giftcode"
   ALTER COLUMN "id" SET DEFAULT nextval('giftcode_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "giftcode_id_seq" OWNED BY "giftcode"."id";
SELECT 'Setup completed for giftcode' as status;

-- Table: kiemtrathietbi
SELECT setval('kiemtrathietbi_id_seq', COALESCE((SELECT MAX("id") FROM "kiemtrathietbi"), 0) + 1, false);
ALTER TABLE "kiemtrathietbi"
   ALTER COLUMN "id" SET DEFAULT nextval('kiemtrathietbi_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "kiemtrathietbi_id_seq" OWNED BY "kiemtrathietbi"."id";
SELECT 'Setup completed for kiemtrathietbi' as status;

-- Table: tbl_itemoption
SELECT setval('tbl_itemoption_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_itemoption"), 0) + 1, false);
ALTER TABLE "tbl_itemoption"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_itemoption_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_itemoption_id_seq" OWNED BY "tbl_itemoption"."id";
SELECT 'Setup completed for tbl_itemoption' as status;

-- Table: tbl_upgrade_item
SELECT setval('tbl_upgrade_item_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_upgrade_item"), 0) + 1, false);
ALTER TABLE "tbl_upgrade_item"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_upgrade_item_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_upgrade_item_id_seq" OWNED BY "tbl_upgrade_item"."id";
SELECT 'Setup completed for tbl_upgrade_item' as status;

-- Table: tbl_xwwl_gg
SELECT setval('tbl_xwwl_gg_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_xwwl_gg"), 0) + 1, false);
ALTER TABLE "tbl_xwwl_gg"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_gg_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_gg_id_seq" OWNED BY "tbl_xwwl_gg"."id";
SELECT 'Setup completed for tbl_xwwl_gg' as status;

-- Table: tbl_xwwl_monster_set_base (Note: uses fld_index column)
SELECT setval('tbl_xwwl_monster_set_base_fld_index_seq', COALESCE((SELECT MAX("fld_index") FROM "tbl_xwwl_monster_set_base"), 0) + 1, false);
ALTER TABLE "tbl_xwwl_monster_set_base"
   ALTER COLUMN "fld_index" SET DEFAULT nextval('tbl_xwwl_monster_set_base_fld_index_seq'),
   ALTER COLUMN "fld_index" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_monster_set_base_fld_index_seq" OWNED BY "tbl_xwwl_monster_set_base"."fld_index";
SELECT 'Setup completed for tbl_xwwl_monster_set_base' as status;

-- Table: tbl_xwwl_sell
SELECT setval('tbl_xwwl_sell_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_xwwl_sell"), 0) + 1, false);
ALTER TABLE "tbl_xwwl_sell"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_sell_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_sell_id_seq" OWNED BY "tbl_xwwl_sell"."id";
SELECT 'Setup completed for tbl_xwwl_sell' as status;

-- Table: tbl_xwwl_stone
SELECT setval('tbl_xwwl_stone_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_xwwl_stone"), 0) + 1, false);
ALTER TABLE "tbl_xwwl_stone"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_stone_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_stone_id_seq" OWNED BY "tbl_xwwl_stone"."id";
SELECT 'Setup completed for tbl_xwwl_stone' as status;

-- Table: tbl_xwwl_vome
SELECT setval('tbl_xwwl_vome_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_xwwl_vome"), 0) + 1, false);
ALTER TABLE "tbl_xwwl_vome"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_vome_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_vome_id_seq" OWNED BY "tbl_xwwl_vome"."id";
SELECT 'Setup completed for tbl_xwwl_vome' as status;

-- Table: thangthienkhicong
SELECT setval('thangthienkhicong_id_seq', COALESCE((SELECT MAX("id") FROM "thangthienkhicong"), 0) + 1, false);
ALTER TABLE "thangthienkhicong"
   ALTER COLUMN "id" SET DEFAULT nextval('thangthienkhicong_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "thangthienkhicong_id_seq" OWNED BY "thangthienkhicong"."id";
SELECT 'Setup completed for thangthienkhicong' as status;

-- Table: vatphamtraodoi
SELECT setval('vatphamtraodoi_id_seq', COALESCE((SELECT MAX("id") FROM "vatphamtraodoi"), 0) + 1, false);
ALTER TABLE "vatphamtraodoi"
   ALTER COLUMN "id" SET DEFAULT nextval('vatphamtraodoi_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "vatphamtraodoi_id_seq" OWNED BY "vatphamtraodoi"."id";
SELECT 'Setup completed for vatphamtraodoi' as status;

-- Table: xwwl_kill
SELECT setval('xwwl_kill_id_seq', COALESCE((SELECT MAX("id") FROM "xwwl_kill"), 0) + 1, false);
ALTER TABLE "xwwl_kill"
   ALTER COLUMN "id" SET DEFAULT nextval('xwwl_kill_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "xwwl_kill_id_seq" OWNED BY "xwwl_kill"."id";
SELECT 'Setup completed for xwwl_kill' as status;

-- ========================================
-- VERIFICATION QUERIES
-- CÁC TRUY VẤN XÁC MINH
-- ========================================

-- Check all sequences created
SELECT schemaname, sequencename, last_value, start_value, increment_by
FROM pg_sequences
WHERE sequencename LIKE '%_id_seq' OR sequencename LIKE '%_fld_index_seq'
ORDER BY sequencename;

-- Check column defaults for Public tables
SELECT
    table_name,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns
WHERE table_name IN (
    'cheduocvatphamdanhsach', 'chetacvatphamdanhsach', 'dangcapbanthuong', 'giftcode_rewards',
    'giftcode', 'kiemtrathietbi', 'tbl_itemoption', 'tbl_upgrade_item', 'tbl_xwwl_gg',
    'tbl_xwwl_monster_set_base', 'tbl_xwwl_sell', 'tbl_xwwl_stone', 'tbl_xwwl_vome',
    'thangthienkhicong', 'vatphamtraodoi', 'xwwl_kill'
) AND (column_name = 'id' OR column_name = 'fld_index')
ORDER BY table_name;
