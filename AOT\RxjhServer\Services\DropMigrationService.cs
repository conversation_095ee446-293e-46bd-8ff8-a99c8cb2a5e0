using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Public;

namespace RxjhServer.Services
{
    /// <summary>
    /// Service for migrating drop data from old system to new system
    /// </summary>
    public static class DropMigrationService
    {
        /// <summary>
        /// Migrate all drop data from old system to new system
        /// </summary>
        /// <param name="dryRun">If true, only simulate migration without actual changes</param>
        /// <returns>Migration result summary</returns>
        public static async Task<MigrationResult> MigrateAllDropData(bool dryRun = true)
        {
            var result = new MigrationResult();
            
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Starting Drop Migration (DryRun: {dryRun}) ===");

                // Migrate normal drops
                var normalResult = await MigrateNormalDrops(dryRun);
                result.Merge(normalResult);

                // Migrate boss drops
                var bossResult = await MigrateBossDrops(dryRun);
                result.Merge(bossResult);

                // Migrate DCH drops
                var dchResult = await MigrateDCHDrops(dryRun);
                result.Merge(dchResult);

                // Migrate GS drops
                var gsResult = await MigrateGSDrops(dryRun);
                result.Merge(gsResult);

                LogHelper.WriteLine(LogLevel.Info, $"=== Migration Complete ===");
                LogHelper.WriteLine(LogLevel.Info, $"Total Processed: {result.TotalProcessed}");
                LogHelper.WriteLine(LogLevel.Info, $"Successfully Migrated: {result.SuccessfulMigrations}");
                LogHelper.WriteLine(LogLevel.Info, $"Skipped: {result.SkippedItems}");
                LogHelper.WriteLine(LogLevel.Info, $"Errors: {result.Errors.Count}");

                if (result.Errors.Any())
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Migration Errors:");
                    foreach (var error in result.Errors.Take(10)) // Show first 10 errors
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"  - {error}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Migration failed: {ex.Message}");
                result.Errors.Add($"Migration failed: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Migrate normal drops from tbl_xwwl_drop
        /// </summary>
        private static async Task<MigrationResult> MigrateNormalDrops(bool dryRun)
        {
            var result = new MigrationResult();
            
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Migrating normal drops...");

                var oldDrops = await PublicDb._freeSql.Select<tbl_xwwl_drop>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .ToListAsync();

                foreach (var oldDrop in oldDrops)
                {
                    result.TotalProcessed++;

                    try
                    {
                        // Convert old drop to new format
                        var newDrop = ConvertToNewDrop(oldDrop, "level_range");
                        
                        if (newDrop == null)
                        {
                            result.SkippedItems++;
                            continue;
                        }

                        if (!dryRun)
                        {
                            // Check if already exists
                            var existing = await PublicDb._freeSql.Select<tbl_new_drops>()
                                .Where(d => d.source_type == newDrop.source_type && 
                                           d.source_value == newDrop.source_value && 
                                           d.item_id == newDrop.item_id)
                                .FirstAsync();

                            if (existing == null)
                            {
                                await PublicDb._freeSql.Insert(newDrop).ExecuteAffrowsAsync();
                                result.SuccessfulMigrations++;
                            }
                            else
                            {
                                result.SkippedItems++;
                            }
                        }
                        else
                        {
                            result.SuccessfulMigrations++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Error migrating normal drop {oldDrop.fld_pid}: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Normal drops migration: {result.SuccessfulMigrations} migrated, {result.SkippedItems} skipped");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error in MigrateNormalDrops: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Migrate boss drops from tbl_xwwl_bossdrop
        /// </summary>
        private static async Task<MigrationResult> MigrateBossDrops(bool dryRun)
        {
            var result = new MigrationResult();
            
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Migrating boss drops...");

                var oldDrops = await PublicDb._freeSql.Select<tbl_xwwl_bossdrop>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .ToListAsync();

                foreach (var oldDrop in oldDrops)
                {
                    result.TotalProcessed++;

                    try
                    {
                        // For boss drops, we need to determine which NPCs should drop these items
                        // This is a simplified approach - you may need to customize based on your data
                        var newDrop = ConvertBossDropToNewDrop(oldDrop);
                        
                        if (newDrop == null)
                        {
                            result.SkippedItems++;
                            continue;
                        }

                        if (!dryRun)
                        {
                            var existing = await PublicDb._freeSql.Select<tbl_new_drops>()
                                .Where(d => d.source_type == newDrop.source_type && 
                                           d.source_value == newDrop.source_value && 
                                           d.item_id == newDrop.item_id)
                                .FirstAsync();

                            if (existing == null)
                            {
                                await PublicDb._freeSql.Insert(newDrop).ExecuteAffrowsAsync();
                                result.SuccessfulMigrations++;
                            }
                            else
                            {
                                result.SkippedItems++;
                            }
                        }
                        else
                        {
                            result.SuccessfulMigrations++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Error migrating boss drop {oldDrop.fld_pid}: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Boss drops migration: {result.SuccessfulMigrations} migrated, {result.SkippedItems} skipped");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error in MigrateBossDrops: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Migrate DCH drops from tbl_xwwl_drop_dch
        /// </summary>
        private static async Task<MigrationResult> MigrateDCHDrops(bool dryRun)
        {
            var result = new MigrationResult();
            
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Migrating DCH drops...");

                var oldDrops = await PublicDb._freeSql.Select<tbl_xwwl_drop_dch>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .ToListAsync();

                foreach (var oldDrop in oldDrops)
                {
                    result.TotalProcessed++;

                    try
                    {
                        var newDrop = ConvertToNewDrop(oldDrop, "level_range", priority: 150); // Higher priority for DCH
                        
                        if (newDrop == null)
                        {
                            result.SkippedItems++;
                            continue;
                        }

                        if (!dryRun)
                        {
                            var existing = await PublicDb._freeSql.Select<tbl_new_drops>()
                                .Where(d => d.source_type == newDrop.source_type && 
                                           d.source_value == newDrop.source_value && 
                                           d.item_id == newDrop.item_id)
                                .FirstAsync();

                            if (existing == null)
                            {
                                await PublicDb._freeSql.Insert(newDrop).ExecuteAffrowsAsync();
                                result.SuccessfulMigrations++;
                            }
                            else
                            {
                                result.SkippedItems++;
                            }
                        }
                        else
                        {
                            result.SuccessfulMigrations++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Error migrating DCH drop {oldDrop.fld_pid}: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"DCH drops migration: {result.SuccessfulMigrations} migrated, {result.SkippedItems} skipped");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error in MigrateDCHDrops: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Migrate GS drops from tbl_xwwl_drop_gs
        /// </summary>
        private static async Task<MigrationResult> MigrateGSDrops(bool dryRun)
        {
            var result = new MigrationResult();
            
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Migrating GS drops...");

                var oldDrops = await PublicDb._freeSql.Select<tbl_xwwl_drop_gs>()
                    .Where(d => d.fld_pp > 0 && d.fld_pid > 0)
                    .ToListAsync();

                foreach (var oldDrop in oldDrops)
                {
                    result.TotalProcessed++;

                    try
                    {
                        var newDrop = ConvertToNewDrop(oldDrop, "level_range", priority: 120); // Medium priority for GS
                        
                        if (newDrop == null)
                        {
                            result.SkippedItems++;
                            continue;
                        }

                        if (!dryRun)
                        {
                            var existing = await PublicDb._freeSql.Select<tbl_new_drops>()
                                .Where(d => d.source_type == newDrop.source_type && 
                                           d.source_value == newDrop.source_value && 
                                           d.item_id == newDrop.item_id)
                                .FirstAsync();

                            if (existing == null)
                            {
                                await PublicDb._freeSql.Insert(newDrop).ExecuteAffrowsAsync();
                                result.SuccessfulMigrations++;
                            }
                            else
                            {
                                result.SkippedItems++;
                            }
                        }
                        else
                        {
                            result.SuccessfulMigrations++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Error migrating GS drop {oldDrop.fld_pid}: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"GS drops migration: {result.SuccessfulMigrations} migrated, {result.SkippedItems} skipped");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error in MigrateGSDrops: {ex.Message}");
            }

            return result;
        }

        #region Helper Methods

        /// <summary>
        /// Convert old drop format to new drop format
        /// </summary>
        private static tbl_new_drops ConvertToNewDrop(dynamic oldDrop, string sourceType, int priority = 100)
        {
            try
            {
                // Validate required fields
                if (oldDrop.fld_pid == null || oldDrop.fld_pp == null || 
                    oldDrop.fld_level1 == null || oldDrop.fld_level2 == null)
                {
                    return null;
                }

                var itemId = (int)oldDrop.fld_pid;
                var pp = (int)oldDrop.fld_pp;
                var level1 = (int)oldDrop.fld_level1;
                var level2 = (int)oldDrop.fld_level2;

                // Skip invalid data
                if (itemId <= 0 || pp <= 0 || level1 < 1 || level2 < level1 || level2 > 300)
                {
                    return null;
                }

                // Convert FLD_PP to decimal rate
                // Old logic: random(1-8000), drop if random <= FLD_PP
                // Real rate = FLD_PP / 8000
                var dropRate = Math.Min(1.0m, Math.Max(0.000001m, (decimal)pp / 8000.0m));

                return new tbl_new_drops
                {
                    source_type = sourceType,
                    source_value = $"{level1}-{level2}",
                    item_id = itemId,
                    drop_rate = dropRate,
                    quantity_min = 1,
                    quantity_max = 1,
                    magic0 = oldDrop.fld_magic0 ?? 0,
                    magic1 = oldDrop.fld_magic1 ?? 0,
                    magic2 = oldDrop.fld_magic2 ?? 0,
                    magic3 = oldDrop.fld_magic3 ?? 0,
                    magic4 = oldDrop.fld_magic4 ?? 0,
                    expire_days = oldDrop.fld_days ?? 0,
                    is_active = true,
                    priority = priority,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error converting drop: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Convert boss drop to new format with NPC-specific source
        /// </summary>
        private static tbl_new_drops ConvertBossDropToNewDrop(tbl_xwwl_bossdrop oldDrop)
        {
            try
            {
                // For boss drops, we'll use level_range for now
                // You may want to customize this to use npc_specific if you have NPC mapping data
                var level1 = oldDrop.fld_level1 ?? 1;
                var level2 = oldDrop.fld_level2 ?? 100;
                var pp = oldDrop.fld_pp ?? 0;

                if (pp <= 0 || level1 < 1 || level2 < level1)
                {
                    return null;
                }

                var dropRate = Math.Min(1.0m, Math.Max(0.000001m, (decimal)pp / 8000.0m));

                return new tbl_new_drops
                {
                    source_type = "level_range", // Could be "npc_specific" if you have NPC mapping
                    source_value = $"{level1}-{level2}",
                    item_id = oldDrop.fld_pid ?? 0,
                    drop_rate = dropRate,
                    quantity_min = 1,
                    quantity_max = 1,
                    magic0 = oldDrop.fld_magic0 ?? 0,
                    magic1 = oldDrop.fld_magic1 ?? 0,
                    magic2 = oldDrop.fld_magic2 ?? 0,
                    magic3 = oldDrop.fld_magic3 ?? 0,
                    magic4 = oldDrop.fld_magic4 ?? 0,
                    expire_days = oldDrop.fld_days ?? 0,
                    is_active = true,
                    priority = 200, // Higher priority for boss drops
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error converting boss drop: {ex.Message}");
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// Migration result summary
    /// </summary>
    public class MigrationResult
    {
        public int TotalProcessed { get; set; }
        public int SuccessfulMigrations { get; set; }
        public int SkippedItems { get; set; }
        public List<string> Errors { get; set; } = new List<string>();

        public void Merge(MigrationResult other)
        {
            TotalProcessed += other.TotalProcessed;
            SuccessfulMigrations += other.SuccessfulMigrations;
            SkippedItems += other.SkippedItems;
            Errors.AddRange(other.Errors);
        }
    }
}
