using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Core;
using Microsoft.Extensions.Logging;
using LogLevel = HeroYulgang.Helpers.LogLevel;

namespace HeroYulgang.Services
{


    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties | DynamicallyAccessedMemberTypes.PublicMethods)]
    public class LogEntry
    {
        private DateTime _timestamp;
        private LogLevel _level;
        private string _message = string.Empty;

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                _timestamp = value;
            }
        }

        public LogLevel Level
        {
            get => _level;
            set
            {
                _level = value;
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                _message = value;
            }
        }



        public string LevelName => Level switch
        {
            LogLevel.Debug => "DBG",
            LogLevel.Info => "INF",
            LogLevel.Warning => "WAR",
            LogLevel.Error => "ERR",
            LogLevel.Fatal => "FTL",
            _ => Level.ToString()
        };

        public string FormattedMessage => $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] [{LevelName}] {Message}";


    }

    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties | DynamicallyAccessedMemberTypes.PublicMethods)]
    public class Logger
    {
        private static Logger? _instance;
        private readonly List<LogEntry> _logs = new();
        private readonly ILogger _logger;
        private const int MAX_LOG_ENTRIES = 1000;
        private readonly string _logFilePath;
        private readonly string _errorLogFilePath;

        public static Logger Instance => _instance ??= new Logger();

        public IReadOnlyList<LogEntry> Logs => _logs;

        private Logger()
        {
            // Không sử dụng Microsoft.Extensions.Logging console output nữa
            // Chúng ta sẽ tự format console output
            var factory = LoggerFactory.Create(builder =>
            {
                // Chỉ tạo logger factory mà không add console provider
            });
            _logger = factory.CreateLogger<Logger>();

            _logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", $"log-{DateTime.Now:yyyyMMdd}.txt");
            _errorLogFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", $"error-{DateTime.Now:yyyyMMdd}.txt");

            Directory.CreateDirectory(Path.GetDirectoryName(_logFilePath)!);

            _batchUpdateTimer = new System.Threading.Timer(BatchUpdateUI, null, 100, 100);
        }

        private readonly Queue<LogEntry> _pendingLogs = new();
        private readonly object _pendingLogsLock = new();
        private readonly System.Threading.Timer _batchUpdateTimer;
        private volatile bool _isUpdatingUI = false;

        public void Log(LogLevel level, string message)
        {
            try
            {
                var timestamp = DateTime.Now;
                var entry = new LogEntry
                {
                    Timestamp = timestamp,
                    Level = level,
                    Message = message
                };

                Task.Run(() =>
                {
                    try
                    {
                        // Format log message theo yêu cầu: [ngày tháng][debug] : message
                        var formattedMessage = $"[{timestamp:yyyy-MM-dd HH:mm:ss:fff}][{entry.LevelName}] : {message}";

                        // Ghi log vào console với màu sắc tương ứng
                        switch (level)
                        {
                            case LogLevel.Debug:
                                if (!ConfigManager.Instance.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase)) return;
                                Console.ForegroundColor = ConsoleColor.Gray;
                                Console.WriteLine(formattedMessage);
                                Console.ResetColor();
                                break;
                            case LogLevel.Info:
                                Console.ForegroundColor = ConsoleColor.White;
                                Console.WriteLine(formattedMessage);
                                Console.ResetColor();
                                break;
                            case LogLevel.Warning:
                                Console.ForegroundColor = ConsoleColor.Yellow;
                                Console.WriteLine(formattedMessage);
                                Console.ResetColor();
                                break;
                            case LogLevel.Error:
                                Console.ForegroundColor = ConsoleColor.Red;
                                Console.WriteLine(formattedMessage);
                                Console.ResetColor();
                                break;
                            case LogLevel.Fatal:
                                Console.ForegroundColor = ConsoleColor.DarkRed;
                                Console.WriteLine(formattedMessage);
                                Console.ResetColor();
                                break;
                        }

                        // Ghi log vào file với cùng format
                        lock (_pendingLogsLock)
                        {
                            var logMessage = $"{formattedMessage}\n";
                            File.AppendAllText(_logFilePath, logMessage);
                            if (level == LogLevel.Error || level == LogLevel.Fatal)
                            {
                                File.AppendAllText(_errorLogFilePath, logMessage);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Logging error: {ex.Message}");
                    }
                });

                lock (_pendingLogsLock)
                {
                    _pendingLogs.Enqueue(entry);
                    while (_pendingLogs.Count > MAX_LOG_ENTRIES * 2)
                    {
                        _pendingLogs.Dequeue();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi ghi log: {ex.Message}");
            }
        }

        private void BatchUpdateUI(object? state)
        {
            if (_isUpdatingUI) return;

            try
            {
                _isUpdatingUI = true;

                List<LogEntry> logsToAdd = new();
                lock (_pendingLogsLock)
                {
                    int count = Math.Min(_pendingLogs.Count, 50);
                    for (int i = 0; i < count; i++)
                    {
                        if (_pendingLogs.Count > 0)
                        {
                            logsToAdd.Add(_pendingLogs.Dequeue());
                        }
                    }
                }

                if (logsToAdd.Count > 0)
                {
                    foreach (var entry in logsToAdd)
                    {
                        _logs.Add(entry);
                    }
                    TrimLogsIfNeeded();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Batch update error: {ex.Message}");
            }
            finally
            {
                _isUpdatingUI = false;
            }
        }

        public void Debug(string message) => Log(LogLevel.Debug, message);
        public void Info(string message) => Log(LogLevel.Info, message);
        public void Warning(string message) => Log(LogLevel.Warning, message);
        public void Error(string message) => Log(LogLevel.Error, message);
        public void Fatal(string message) => Log(LogLevel.Fatal, message);

        // Debug method to check logging
        public void TestLogging()
        {
            Console.WriteLine($"[DEBUG] Logger instance created. Logs count: {_logs.Count}");
            Console.WriteLine($"[DEBUG] Pending logs count: {_pendingLogs.Count}");

            // Add a test log
            Log(LogLevel.Info, "Test log entry for debugging");

            Console.WriteLine($"[DEBUG] After test log - Logs count: {_logs.Count}, Pending: {_pendingLogs.Count}");
        }

        public void Clear()
        {
            try
            {
                _logs.Clear();
                _logger.LogInformation("Log đã được xóa");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi xóa log: {ex.Message}");
            }
        }

        private void TrimLogsIfNeeded()
        {
            while (_logs.Count > MAX_LOG_ENTRIES)
            {
                _logs.RemoveAt(0);
            }
        }

        public static string GetCurrentLogFilePath(bool errorLogOnly = false)
        {
            var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            var today = DateTime.Now;
            return Path.Combine(logDirectory, errorLogOnly ? $"error-{today:yyyyMMdd}.txt" : $"log-{today:yyyyMMdd}.txt");
        }

        public static string GetCurrentErrorLogFilePath()
        {
            return GetCurrentLogFilePath(true);
        }

        public static bool ErrorLogFileExists()
        {
            return File.Exists(GetCurrentErrorLogFilePath());
        }

        public static long GetErrorLogFileSize()
        {
            var filePath = GetCurrentErrorLogFilePath();
            return File.Exists(filePath) ? new FileInfo(filePath).Length : 0;
        }

        public static List<string> GetAllErrorLogFiles()
        {
            var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            if (!Directory.Exists(logDirectory))
                return new List<string>();

            return Directory.GetFiles(logDirectory, "error-*.txt")
                           .OrderByDescending(f => f)
                           .ToList();
        }

        public static void TestErrorLogging()
        {
            var logger = Logger.Instance;

            logger.Error("Đây là một log Error test - Kết nối database thất bại");
            logger.Error("Đây là một log Error test - Không thể đọc file cấu hình");
            logger.Error("Đây là một log Error test - API call timeout");

            logger.Fatal("Đây là một log Fatal test - Hệ thống crash nghiêm trọng");
            logger.Fatal("Đây là một log Fatal test - Out of memory exception");

            logger.Info("Đây là log Info - sẽ không xuất hiện trong error log file");
            logger.Warning("Đây là log Warning - sẽ không xuất hiện trong error log file");
            logger.Debug("Đây là log Debug - sẽ không xuất hiện trong error log file");
        }

        public static void CloseAndFlush()
        {
            // Không cần flush trong trường hợp này
        }


    }
}