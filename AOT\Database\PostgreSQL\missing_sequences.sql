-- Complete script to add auto-increment primary keys for Game database tables
-- <PERSON><PERSON><PERSON> hoàn chỉnh để thêm auto-increment primary key cho các bảng Game database
-- Generated automatically from entity analysis
-- <PERSON><PERSON><PERSON><PERSON> tạo tự động từ phân tích entity

-- ========================================
-- SECTION 1: CREATE SEQUENCES
-- PHẦN 1: TẠO CÁC SEQUENCE
-- ========================================

CREATE SEQUENCE IF NOT EXISTS "bachbaocacrecord_id_seq";
CREATE SEQUENCE IF NOT EXISTS "bangchien_tiendatcuoc_id_seq";
CREATE SEQUENCE IF NOT EXISTS "congthanhchien_thanhchu_id_seq";
CREATE SEQUENCE IF NOT EXISTS "drugrecord_id_seq";
CREATE SEQUENCE IF NOT EXISTS "eventtop_dch_id_seq";
CREATE SEQUENCE IF NOT EXISTS "eventtop_id_seq";
CREATE SEQUENCE IF NOT EXISTS "exchangecharacter_id_seq";
CREATE SEQUENCE IF NOT EXISTS "giftcode_id_seq";
CREATE SEQUENCE IF NOT EXISTS "itemrecord_id_seq";
CREATE SEQUENCE IF NOT EXISTS "log_deleteitem_id_seq";
CREATE SEQUENCE IF NOT EXISTS "log_thelucchien_id_seq";
CREATE SEQUENCE IF NOT EXISTS "loginrecord_id_seq";
CREATE SEQUENCE IF NOT EXISTS "loginrecord_mac_id_seq";
CREATE SEQUENCE IF NOT EXISTS "logpk_id_seq";
CREATE SEQUENCE IF NOT EXISTS "logshop_id_seq";
CREATE SEQUENCE IF NOT EXISTS "logshopvohuan_id_seq";
-- syntheticrecord_id_seq already exists, skipping
CREATE SEQUENCE IF NOT EXISTS "tbl_faction_quest_progress_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_group_quest_contribution_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_group_quest_contribution_log_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_group_quest_history_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_group_quest_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_guild_quest_progress_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_sudosolieu_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_truyenthuhethong_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_vinhduhethong_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_char_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_cw_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_cwarehouse_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_guild_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_guildmember_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_pklog_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_publicwarehouse_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_pvp_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_rosetop_id_seq";
CREATE SEQUENCE IF NOT EXISTS "tbl_xwwl_warehouse_id_seq";
CREATE SEQUENCE IF NOT EXISTS "thienmathancung_danhsach_id_seq";
CREATE SEQUENCE IF NOT EXISTS "vinhdubangphaixephang_id_seq";

-- ========================================
-- SECTION 2: SET SEQUENCE VALUES AND ATTACH TO ID COLUMNS
-- PHẦN 2: THIẾT LẬP GIÁ TRỊ SEQUENCE VÀ GẮN VÀO CÁC CỘT ID
-- ========================================

-- Table: bachbaocacrecord
SELECT setval('bachbaocacrecord_id_seq', COALESCE((SELECT MAX("id") FROM "bachbaocacrecord"), 0) + 1, false);
ALTER TABLE "bachbaocacrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('bachbaocacrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "bachbaocacrecord_id_seq" OWNED BY "bachbaocacrecord"."id";
SELECT 'Setup completed for bachbaocacrecord' as status;

-- Table: bangchien_tiendatcuoc
SELECT setval('bangchien_tiendatcuoc_id_seq', COALESCE((SELECT MAX("id") FROM "bangchien_tiendatcuoc"), 0) + 1, false);
ALTER TABLE "bangchien_tiendatcuoc"
   ALTER COLUMN "id" SET DEFAULT nextval('bangchien_tiendatcuoc_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "bangchien_tiendatcuoc_id_seq" OWNED BY "bangchien_tiendatcuoc"."id";
SELECT 'Setup completed for bangchien_tiendatcuoc' as status;

-- Table: congthanhchien_thanhchu
SELECT setval('congthanhchien_thanhchu_id_seq', COALESCE((SELECT MAX("id") FROM "congthanhchien_thanhchu"), 0) + 1, false);
ALTER TABLE "congthanhchien_thanhchu"
   ALTER COLUMN "id" SET DEFAULT nextval('congthanhchien_thanhchu_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "congthanhchien_thanhchu_id_seq" OWNED BY "congthanhchien_thanhchu"."id";
SELECT 'Setup completed for congthanhchien_thanhchu' as status;

-- Table: drugrecord
SELECT setval('drugrecord_id_seq', COALESCE((SELECT MAX("id") FROM "drugrecord"), 0) + 1, false);
ALTER TABLE "drugrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('drugrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "drugrecord_id_seq" OWNED BY "drugrecord"."id";
SELECT 'Setup completed for drugrecord' as status;

-- Table: eventtop_dch
SELECT setval('eventtop_dch_id_seq', COALESCE((SELECT MAX("id") FROM "eventtop_dch"), 0) + 1, false);
ALTER TABLE "eventtop_dch"
   ALTER COLUMN "id" SET DEFAULT nextval('eventtop_dch_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "eventtop_dch_id_seq" OWNED BY "eventtop_dch"."id";
SELECT 'Setup completed for eventtop_dch' as status;

-- Table: eventtop
SELECT setval('eventtop_id_seq', COALESCE((SELECT MAX("id") FROM "eventtop"), 0) + 1, false);
ALTER TABLE "eventtop"
   ALTER COLUMN "id" SET DEFAULT nextval('eventtop_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "eventtop_id_seq" OWNED BY "eventtop"."id";
SELECT 'Setup completed for eventtop' as status;

-- Table: exchangecharacter
SELECT setval('exchangecharacter_id_seq', COALESCE((SELECT MAX("id") FROM "exchangecharacter"), 0) + 1, false);
ALTER TABLE "exchangecharacter"
   ALTER COLUMN "id" SET DEFAULT nextval('exchangecharacter_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "exchangecharacter_id_seq" OWNED BY "exchangecharacter"."id";
SELECT 'Setup completed for exchangecharacter' as status;

-- Table: giftcode
SELECT setval('giftcode_id_seq', COALESCE((SELECT MAX("id") FROM "giftcode"), 0) + 1, false);
ALTER TABLE "giftcode"
   ALTER COLUMN "id" SET DEFAULT nextval('giftcode_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "giftcode_id_seq" OWNED BY "giftcode"."id";
SELECT 'Setup completed for giftcode' as status;

-- Table: itemrecord
SELECT setval('itemrecord_id_seq', COALESCE((SELECT MAX("id") FROM "itemrecord"), 0) + 1, false);
ALTER TABLE "itemrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('itemrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "itemrecord_id_seq" OWNED BY "itemrecord"."id";
SELECT 'Setup completed for itemrecord' as status;

-- Table: log_deleteitem
SELECT setval('log_deleteitem_id_seq', COALESCE((SELECT MAX("id") FROM "log_deleteitem"), 0) + 1, false);
ALTER TABLE "log_deleteitem"
   ALTER COLUMN "id" SET DEFAULT nextval('log_deleteitem_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "log_deleteitem_id_seq" OWNED BY "log_deleteitem"."id";
SELECT 'Setup completed for log_deleteitem' as status;

-- Table: log_thelucchien
SELECT setval('log_thelucchien_id_seq', COALESCE((SELECT MAX("id") FROM "log_thelucchien"), 0) + 1, false);
ALTER TABLE "log_thelucchien"
   ALTER COLUMN "id" SET DEFAULT nextval('log_thelucchien_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "log_thelucchien_id_seq" OWNED BY "log_thelucchien"."id";
SELECT 'Setup completed for log_thelucchien' as status;

-- Table: loginrecord
SELECT setval('loginrecord_id_seq', COALESCE((SELECT MAX("id") FROM "loginrecord"), 0) + 1, false);
ALTER TABLE "loginrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('loginrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "loginrecord_id_seq" OWNED BY "loginrecord"."id";
SELECT 'Setup completed for loginrecord' as status;

-- Table: loginrecord_mac
SELECT setval('loginrecord_mac_id_seq', COALESCE((SELECT MAX("id") FROM "loginrecord_mac"), 0) + 1, false);
ALTER TABLE "loginrecord_mac"
   ALTER COLUMN "id" SET DEFAULT nextval('loginrecord_mac_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "loginrecord_mac_id_seq" OWNED BY "loginrecord_mac"."id";
SELECT 'Setup completed for loginrecord_mac' as status;

-- Table: logpk
SELECT setval('logpk_id_seq', COALESCE((SELECT MAX("id") FROM "logpk"), 0) + 1, false);
ALTER TABLE "logpk"
   ALTER COLUMN "id" SET DEFAULT nextval('logpk_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "logpk_id_seq" OWNED BY "logpk"."id";
SELECT 'Setup completed for logpk' as status;

-- Table: logshop
SELECT setval('logshop_id_seq', COALESCE((SELECT MAX("id") FROM "logshop"), 0) + 1, false);
ALTER TABLE "logshop"
   ALTER COLUMN "id" SET DEFAULT nextval('logshop_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "logshop_id_seq" OWNED BY "logshop"."id";
SELECT 'Setup completed for logshop' as status;

-- Table: logshopvohuan
SELECT setval('logshopvohuan_id_seq', COALESCE((SELECT MAX("id") FROM "logshopvohuan"), 0) + 1, false);
ALTER TABLE "logshopvohuan"
   ALTER COLUMN "id" SET DEFAULT nextval('logshopvohuan_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "logshopvohuan_id_seq" OWNED BY "logshopvohuan"."id";
SELECT 'Setup completed for logshopvohuan' as status;

-- syntheticrecord already has sequence setup, skipping

-- Table: tbl_faction_quest_progress
SELECT setval('tbl_faction_quest_progress_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_faction_quest_progress"), 0) + 1, false);
ALTER TABLE "tbl_faction_quest_progress"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_faction_quest_progress_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_faction_quest_progress_id_seq" OWNED BY "tbl_faction_quest_progress"."id";
SELECT 'Setup completed for tbl_faction_quest_progress' as status;

-- Table: tbl_group_quest_contribution
ALTER TABLE "tbl_group_quest_contribution"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_group_quest_contribution_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_group_quest_contribution_id_seq" OWNED BY "tbl_group_quest_contribution"."id";
SELECT 'Setup completed for tbl_group_quest_contribution' as status;

-- Table: tbl_group_quest_contribution_log
ALTER TABLE "tbl_group_quest_contribution_log"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_group_quest_contribution_log_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_group_quest_contribution_log_id_seq" OWNED BY "tbl_group_quest_contribution_log"."id";
SELECT 'Setup completed for tbl_group_quest_contribution_log' as status;

-- Table: tbl_group_quest_history
ALTER TABLE "tbl_group_quest_history"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_group_quest_history_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_group_quest_history_id_seq" OWNED BY "tbl_group_quest_history"."id";
SELECT 'Setup completed for tbl_group_quest_history' as status;

-- Table: tbl_group_quest
ALTER TABLE "tbl_group_quest"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_group_quest_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_group_quest_id_seq" OWNED BY "tbl_group_quest"."id";
SELECT 'Setup completed for tbl_group_quest' as status;

-- Table: tbl_guild_quest_progress
ALTER TABLE "tbl_guild_quest_progress"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_guild_quest_progress_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_guild_quest_progress_id_seq" OWNED BY "tbl_guild_quest_progress"."id";
SELECT 'Setup completed for tbl_guild_quest_progress' as status;

-- Table: tbl_sudosolieu
ALTER TABLE "tbl_sudosolieu"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_sudosolieu_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_sudosolieu_id_seq" OWNED BY "tbl_sudosolieu"."id";
SELECT 'Setup completed for tbl_sudosolieu' as status;

-- Table: tbl_truyenthuhethong
ALTER TABLE "tbl_truyenthuhethong"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_truyenthuhethong_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_truyenthuhethong_id_seq" OWNED BY "tbl_truyenthuhethong"."id";
SELECT 'Setup completed for tbl_truyenthuhethong' as status;

-- Table: tbl_vinhduhethong
ALTER TABLE "tbl_vinhduhethong"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_vinhduhethong_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_vinhduhethong_id_seq" OWNED BY "tbl_vinhduhethong"."id";
SELECT 'Setup completed for tbl_vinhduhethong' as status;

-- Table: tbl_xwwl_char
ALTER TABLE "tbl_xwwl_char"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_char_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_char_id_seq" OWNED BY "tbl_xwwl_char"."id";
SELECT 'Setup completed for tbl_xwwl_char' as status;

-- Table: tbl_xwwl_cw
ALTER TABLE "tbl_xwwl_cw"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_cw_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_cw_id_seq" OWNED BY "tbl_xwwl_cw"."id";
SELECT 'Setup completed for tbl_xwwl_cw' as status;

-- Table: tbl_xwwl_cwarehouse
ALTER TABLE "tbl_xwwl_cwarehouse"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_cwarehouse_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_cwarehouse_id_seq" OWNED BY "tbl_xwwl_cwarehouse"."id";
SELECT 'Setup completed for tbl_xwwl_cwarehouse' as status;

-- Table: tbl_xwwl_guild
ALTER TABLE "tbl_xwwl_guild"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_guild_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_guild_id_seq" OWNED BY "tbl_xwwl_guild"."id";
SELECT 'Setup completed for tbl_xwwl_guild' as status;

-- Table: tbl_xwwl_guildmember
ALTER TABLE "tbl_xwwl_guildmember"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_guildmember_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_guildmember_id_seq" OWNED BY "tbl_xwwl_guildmember"."id";
SELECT 'Setup completed for tbl_xwwl_guildmember' as status;

-- Table: tbl_xwwl_pklog
ALTER TABLE "tbl_xwwl_pklog"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_pklog_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_pklog_id_seq" OWNED BY "tbl_xwwl_pklog"."id";
SELECT 'Setup completed for tbl_xwwl_pklog' as status;

-- Table: tbl_xwwl_publicwarehouse
ALTER TABLE "tbl_xwwl_publicwarehouse"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_publicwarehouse_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_publicwarehouse_id_seq" OWNED BY "tbl_xwwl_publicwarehouse"."id";
SELECT 'Setup completed for tbl_xwwl_publicwarehouse' as status;

-- Table: tbl_xwwl_pvp
ALTER TABLE "tbl_xwwl_pvp"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_pvp_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_pvp_id_seq" OWNED BY "tbl_xwwl_pvp"."id";
SELECT 'Setup completed for tbl_xwwl_pvp' as status;

-- Table: tbl_xwwl_rosetop
ALTER TABLE "tbl_xwwl_rosetop"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_rosetop_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_rosetop_id_seq" OWNED BY "tbl_xwwl_rosetop"."id";
SELECT 'Setup completed for tbl_xwwl_rosetop' as status;

-- Table: tbl_xwwl_warehouse
ALTER TABLE "tbl_xwwl_warehouse"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_xwwl_warehouse_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_xwwl_warehouse_id_seq" OWNED BY "tbl_xwwl_warehouse"."id";
SELECT 'Setup completed for tbl_xwwl_warehouse' as status;

-- Table: thienmathancung_danhsach
ALTER TABLE "thienmathancung_danhsach"
   ALTER COLUMN "id" SET DEFAULT nextval('thienmathancung_danhsach_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "thienmathancung_danhsach_id_seq" OWNED BY "thienmathancung_danhsach"."id";
SELECT 'Setup completed for thienmathancung_danhsach' as status;

-- Table: vinhdubangphaixephang
ALTER TABLE "vinhdubangphaixephang"
   ALTER COLUMN "id" SET DEFAULT nextval('vinhdubangphaixephang_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "vinhdubangphaixephang_id_seq" OWNED BY "vinhdubangphaixephang"."id";
SELECT 'Setup completed for vinhdubangphaixephang' as status;

-- ========================================
-- VERIFICATION QUERIES
-- CÁC TRUY VẤN XÁC MINH
-- ========================================

-- Check all sequences created
SELECT schemaname, sequencename, last_value, start_value, increment_by
FROM pg_sequences
WHERE sequencename LIKE '%_id_seq'
ORDER BY sequencename;

-- Check column defaults for Game tables
SELECT
    table_name,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns
WHERE table_name IN (
    'bachbaocacrecord', 'bangchien_tiendatcuoc', 'congthanhchien_thanhchu', 'drugrecord',
    'eventtop_dch', 'eventtop', 'exchangecharacter', 'giftcode', 'itemrecord',
    'log_deleteitem', 'log_thelucchien', 'loginrecord', 'loginrecord_mac', 'logpk',
    'logshop', 'logshopvohuan', 'syntheticrecord', 'tbl_faction_quest_progress',
    'tbl_group_quest_contribution', 'tbl_group_quest_contribution_log', 'tbl_group_quest_history',
    'tbl_group_quest', 'tbl_guild_quest_progress', 'tbl_sudosolieu', 'tbl_truyenthuhethong',
    'tbl_vinhduhethong', 'tbl_xwwl_char', 'tbl_xwwl_cw', 'tbl_xwwl_cwarehouse',
    'tbl_xwwl_guild', 'tbl_xwwl_guildmember', 'tbl_xwwl_pklog', 'tbl_xwwl_publicwarehouse',
    'tbl_xwwl_pvp', 'tbl_xwwl_rosetop', 'tbl_xwwl_warehouse', 'thienmathancung_danhsach',
    'vinhdubangphaixephang'
) AND column_name = 'id'
ORDER BY table_name;
