using System;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer.Systems
{
    /// <summary>
    /// Attack Limiter System - Manages attack cooldown and damage multipliers
    /// Hệ thống giới hạn tấn công - <PERSON>u<PERSON>n lý thời gian hồi chiêu và hệ số sát thương
    /// </summary>
    public static class AttackLimiterSystem
    {

        /// <summary>
        /// Configuration for attack limiter
        /// C<PERSON><PERSON> hình cho hệ thống giới hạn tấn công
        /// </summary>
        public class AttackLimiterConfig
        {
            public int BaseCooldown { get; set; } = 1000; // Base cooldown in milliseconds
            public int MaxBonusTime { get; set; } = 1500; // Time to reach max bonus
            public int MinDamagePercent { get; set; } = 10; // Minimum damage percentage
            public int MaxDamagePercent { get; set; } = 120; // Maximum damage percentage
        }

        /// <summary>
        /// Calculate damage multiplier based on time since last attack
        /// T<PERSON><PERSON> hệ số sát thương dựa trên thời gian từ lần tấn công cuối
        /// </summary>
        /// <param name="timeSinceLastAttack">Time in milliseconds since last attack</param>
        /// <param name="config">Attack limiter configuration</param>
        /// <returns>Damage multiplier (0.1 to 1.2)</returns>
        public static double CalculateDamageMultiplier(int timeSinceLastAttack, AttackLimiterConfig config)
        {
            try
            {
                // If attack is at or after base cooldown, calculate bonus
                if (timeSinceLastAttack >= config.BaseCooldown)
                {
                    // Calculate bonus damage up to MaxBonusTime
                    if (timeSinceLastAttack >= config.MaxBonusTime)
                    {
                        return config.MaxDamagePercent / 100.0; // Max bonus (120%)
                    }
                    
                    // Linear interpolation between 100% and max bonus
                    double bonusRange = config.MaxBonusTime - config.BaseCooldown;
                    double timeInBonusRange = timeSinceLastAttack - config.BaseCooldown;
                    double bonusPercent = (timeInBonusRange / bonusRange) * (config.MaxDamagePercent - 100);
                    
                    return (100 + bonusPercent) / 100.0;
                }
                else
                {
                    // Calculate penalty for attacking too fast
                    double penaltyPercent = (double)timeSinceLastAttack / config.BaseCooldown;
                    double damagePercent = config.MinDamagePercent + (penaltyPercent * (100 - config.MinDamagePercent));
                    
                    return Math.Max(damagePercent / 100.0, config.MinDamagePercent / 100.0);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AttackLimiterSystem.CalculateDamageMultiplier error: {ex.Message}");
                return 1.0; // Return normal damage on error
            }
        }

        /// <summary>
        /// Get effective base cooldown for player considering special abilities
        /// Lấy thời gian hồi chiêu hiệu quả cho người chơi có tính đến kỹ năng đặc biệt
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>Effective base cooldown in milliseconds</returns>
        public static int GetEffectiveBaseCooldown(Players player)
        {
            try
            {
                var config = JobConfig.GetConfigForJob(player.Player_Job);
                int baseCooldown = config.BaseCooldown;

                // Job 4 (Cung) special handling for TamThanNgungTu
                if (player.Player_Job == 4 && player.IsTriggerTamThanNgungTu)
                {
                    // When TamThanNgungTu is active, reduce base cooldown by 50percent
                    baseCooldown = (int)(baseCooldown * 0.5);
                }
                // Job 6 (Ninja) special handling for attack speed
                else if (player.Player_Job == 6)
                {
                    // Apply attack speed modifiers
                    if (player.AppendStatusList != null && player.GetAddState(801201))
                    {
                        // When speed buff is active, adjust cooldown based on attack speed
                        double speedMultiplier = 100.0 / player.FLD_CongKichTocDo;
                        baseCooldown = (int)(baseCooldown * speedMultiplier);
                    }
                    
                    // When NINJA_TamThanNgungTu is active, further reduce cooldown
                    if (player.IsTriggerTamThanNgungTu)
                    {
                        double speedMultiplier = 100.0 / player.FLD_CongKichTocDo;
                        baseCooldown = (int)(baseCooldown*0.5 * speedMultiplier);
                    }
                }

                return Math.Max(baseCooldown, 100); // Minimum 100ms cooldown
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AttackLimiterSystem.GetEffectiveBaseCooldown error: {ex.Message}");
                return 1000; // Return default cooldown on error
            }
        }

        /// <summary>
        /// Apply attack limiter to damage for monster attacks only
        /// Áp dụng giới hạn tấn công cho sát thương chỉ khi tấn công quái vật
        /// </summary>
        /// <param name="player">Player performing the attack</param>
        /// <param name="originalDamage">Original damage value</param>
        /// <param name="targetId">Target ID (must be >= 10000 for monsters)</param>
        /// <returns>Modified damage value</returns>
        public static double ApplyAttackLimiter(Players player, double originalDamage, int targetId)
        {
            try
            {
                // chi co tac dung voi quai va khi enable
                if (targetId < 10000 || !World.AttackLimiter_Enabled)
                {
                    return originalDamage;
                }

                // tinh thoi gian tan cong lan cuoi
                int timeSinceLastAttack = (int)DateTime.Now.Subtract(player._lastAttackTime).TotalMilliseconds;
                
                // Get effective base cooldown for this player
                int effectiveBaseCooldown = GetEffectiveBaseCooldown(player);
                
                // Create temporary config with effective cooldown
                var config = JobConfig.GetConfigForJob(player.Player_Job);
                var effectiveConfig = new AttackLimiterConfig
                {
                    BaseCooldown = effectiveBaseCooldown,
                    MaxBonusTime = config.MaxBonusTime,
                    MinDamagePercent = config.MinDamagePercent,
                    MaxDamagePercent = config.MaxDamagePercent
                };

                // Calculate damage multiplier
                double multiplier = CalculateDamageMultiplier(timeSinceLastAttack, effectiveConfig);
                
                // Apply multiplier to damage
                double modifiedDamage = originalDamage * multiplier;

                // Log for debugging (can be removed in production)
                //if (multiplier != 1.0)
                //{
                //    LogHelper.WriteLine(LogLevel.Debug, 
                //        $"AttackLimiter: Player {player.CharacterName} (Job {player.Player_Job}) " +
                //        $"TimeSinceLastAttack: {timeSinceLastAttack}ms, " +
                //        $"EffectiveCooldown: {effectiveBaseCooldown}ms, " +
                //        $"Multiplier: {multiplier:F2}, " +
                //        $"Damage: {originalDamage:F0} -> {modifiedDamage:F0}");
                //}
                if (multiplier < 1.0)
                {
                    player.HeThongNhacNho($"Đại hiệp tấn công quá nhanh, sát thương giảm {multiplier*100}%");
                }

                return modifiedDamage;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AttackLimiterSystem.ApplyAttackLimiter error: {ex.Message}");
                return originalDamage; // Return original damage on error
            }
        }
    }
}
